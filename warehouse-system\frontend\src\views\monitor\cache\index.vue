<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header"><span><i class="el-icon-monitor"></i> 缓存统计</span></div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf"><div class="cell">缓存类型</div></td>
                  <td class="el-table__cell is-leaf"><div class="cell">内存缓存</div></td>
                  <td class="el-table__cell is-leaf"><div class="cell">缓存数量</div></td>
                  <td class="el-table__cell is-leaf"><div class="cell">{{ cacheNames.length }}</div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24">
        <el-card style="margin-top: 10px">
          <div slot="header">
            <span><i class="el-icon-data-line"></i> 缓存列表</span>
            <el-button
              style="float: right; margin-left: 10px"
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="!showClearAll"
              @click="handleClearAllCache"
              v-hasPermi="['monitor:cache:list']"
            >清理全部</el-button>
          </div>

          <el-table
            v-loading="loading"
            :data="cacheList"
            style="width: 100%"
          >
            <el-table-column label="缓存名称" align="center" prop="cacheName" :show-overflow-tooltip="true" />
            <el-table-column label="缓存描述" align="center" prop="remark" :show-overflow-tooltip="true" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleCacheValue(scope.row)"
                  v-hasPermi="['monitor:cache:list']"
                >缓存内容</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleClearCache(scope.row)"
                  v-hasPermi="['monitor:cache:list']"
                >清理</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 缓存内容详情 -->
    <el-dialog :title="cacheDialog.title" :visible.sync="cacheDialog.visible" width="60%" append-to-body>
      <el-table :data="cacheDialog.cacheValue" v-loading="cacheDialog.loading">
        <el-table-column prop="key" label="键名" :show-overflow-tooltip="true" />
        <el-table-column prop="value" label="值" :show-overflow-tooltip="true" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cacheDialog.visible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { list, getNames, getValue, clearCacheName, clearCacheKey, clearAll } from "@/api/monitor/cache";

export default {
  name: "Cache",
  data() {
    return {
      loading: true,
      cacheNames: [],
      cacheList: [],
      showClearAll: false,
      cacheDialog: {
        title: "",
        visible: false,
        loading: false,
        cacheValue: []
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      list().then(response => {
        this.cacheList = response.rows;
        this.loading = false;
        this.showClearAll = true;
      });
    },
    handleCacheValue(row) {
      getNames(row.cacheName).then(response => {
        const cacheNames = response.data;
        this.cacheDialog.cacheValue = [];
        if (cacheNames.length > 0) {
          cacheNames.forEach(key => {
            getValue(row.cacheName, key).then(res => {
              this.cacheDialog.cacheValue.push({ key: key, value: JSON.stringify(res.data) });
            });
          });
        }
        this.cacheDialog.title = "缓存内容详情（" + row.cacheName + "）";
        this.cacheDialog.visible = true;
      });
    },
    handleClearCache(row) {
      clearCacheName(row.cacheName).then(response => {
        this.$modal.msgSuccess("清理缓存名称[" + row.cacheName + "]成功");
        this.getList();
      });
    },
    handleClearAllCache() {
      clearAll().then(response => {
        this.$modal.msgSuccess("清理全部缓存成功");
        this.getList();
      });
    }
  }
};
</script>
