@echo off
chcp 65001 >nul
echo =====================================================
echo sys_license表字段标准化修复执行脚本
echo =====================================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo [INFO] 开始执行sys_license表字段标准化修复...
echo [INFO] 数据库: %DB_NAME%
echo [INFO] 时间: %date% %time%
echo.

echo [STEP 1] 执行修复脚本...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% %DB_NAME% < "%~dp0..\sql\fix_sys_license_status.sql"

if %errorlevel% neq 0 (
    echo [ERROR] 修复脚本执行失败！
    echo [INFO] 请检查数据库连接和脚本内容
    pause
    exit /b 1
)

echo [SUCCESS] 修复脚本执行成功！
echo.

echo [STEP 2] 执行验证脚本...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% %DB_NAME% < "%~dp0..\sql\verify_sys_license_fix.sql"

if %errorlevel% neq 0 (
    echo [WARNING] 验证脚本执行失败，但修复可能已成功
    echo [INFO] 请手动检查修复结果
) else (
    echo [SUCCESS] 验证脚本执行成功！
)

echo.
echo =====================================================
echo sys_license表字段标准化修复完成
echo =====================================================
echo.
echo [INFO] 修复内容：
echo   - status字段定义：0=启用，1=禁用
echo   - 默认值：'0'（启用状态）
echo   - 数据已安全转换
echo.
echo [INFO] 备份表：sys_license_backup_before_fix
echo [INFO] 如需回滚，请执行：rollback_sys_license_fix.bat
echo.
echo [NEXT] 接下来需要：
echo   1. 更新Java代码中的状态查询逻辑
echo   2. 修复前端页面的状态显示
echo   3. 测试相关功能
echo.

pause