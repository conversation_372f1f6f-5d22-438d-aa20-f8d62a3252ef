package com.wanyu.system.mapper;

import java.util.List;
import java.util.Map;
import com.wanyu.system.domain.WmsOperationLog;
import org.apache.ibatis.annotations.Param;

/**
 * 操作日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface WmsOperationLogMapper 
{
    /**
     * 查询操作日志
     * 
     * @param logId 操作日志主键
     * @return 操作日志
     */
    public WmsOperationLog selectWmsOperationLogByLogId(Long logId);

    /**
     * 查询操作日志列表
     * 
     * @param wmsOperationLog 操作日志
     * @return 操作日志集合
     */
    public List<WmsOperationLog> selectWmsOperationLogList(WmsOperationLog wmsOperationLog);

    /**
     * 查询成功操作日志
     * 
     * @param wmsOperationLog 查询条件
     * @return 成功操作日志集合
     */
    public List<WmsOperationLog> selectSuccessOperations(WmsOperationLog wmsOperationLog);

    /**
     * 查询失败操作日志
     * 
     * @param wmsOperationLog 查询条件
     * @return 失败操作日志集合
     */
    public List<WmsOperationLog> selectFailedOperations(WmsOperationLog wmsOperationLog);

    /**
     * 统计操作状态
     * 
     * @param operationType 操作类型
     * @param operatorId 操作人ID
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public List<Map<String, Object>> countByOperationStatus(@Param("operationType") String operationType, 
                                                           @Param("operatorId") Long operatorId,
                                                           @Param("beginTime") String beginTime, 
                                                           @Param("endTime") String endTime);

    /**
     * 新增操作日志
     * 
     * @param wmsOperationLog 操作日志
     * @return 结果
     */
    public int insertWmsOperationLog(WmsOperationLog wmsOperationLog);

    /**
     * 修改操作日志
     * 
     * @param wmsOperationLog 操作日志
     * @return 结果
     */
    public int updateWmsOperationLog(WmsOperationLog wmsOperationLog);

    /**
     * 删除操作日志
     * 
     * @param logId 操作日志主键
     * @return 结果
     */
    public int deleteWmsOperationLogByLogId(Long logId);

    /**
     * 批量删除操作日志
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWmsOperationLogByLogIds(Long[] logIds);

    /**
     * 清理过期日志
     * 
     * @param days 保留天数
     * @return 结果
     */
    public int cleanExpiredLogs(@Param("days") int days);
}