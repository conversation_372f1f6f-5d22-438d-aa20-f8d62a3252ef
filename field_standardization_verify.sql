-- 字段定义标准化验证脚本
-- 版本: 1.0
-- 描述: 验证字段标准化修复结果

-- 验证sys_license表
SELECT 'sys_license表验证' as test_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as enabled_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as disabled_count,
    SUM(CASE WHEN status NOT IN ('0', '1') THEN 1 ELSE 0 END) as invalid_count
FROM sys_license;

-- 验证sys_license_feature表
SELECT 'sys_license_feature表验证' as test_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as enabled_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as disabled_count,
    SUM(CASE WHEN status NOT IN ('0', '1') THEN 1 ELSE 0 END) as invalid_count
FROM sys_license_feature;

-- 验证数据字典
SELECT 'sys_dict_data验证' as test_name,
    COUNT(*) as total_records
FROM sys_dict_data 
WHERE dict_type = 'sys_normal_disable' 
  AND ((dict_value = '0' AND dict_label = '正常') OR (dict_value = '1' AND dict_label = '停用'));

-- 验证字段注释
SELECT 'sys_license字段注释验证' as test_name,
    COLUMN_COMMENT as field_comment
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME = 'sys_license' 
  AND COLUMN_NAME = 'status';

SELECT 'sys_license_feature字段注释验证' as test_name,
    COLUMN_COMMENT as field_comment
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME = 'sys_license_feature' 
  AND COLUMN_NAME = 'status';