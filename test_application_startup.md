# 应用启动测试指南

## 当前状态
已临时禁用以下可能有问题的控制器：
1. SysJobV2Controller - 定时任务V2控制器
2. ProductAttributeV2Controller - 物品属性V2控制器  
3. SysDictDataController - 字典数据控制器

## 仍然启用的控制器
以下控制器应该能够正常工作：
1. SysUserOnlineV2Controller - 在线用户监控V2
2. ServerV2Controller - 服务监控V2
3. CacheV2Controller - 缓存监控V2
4. SysDeptWarehousePermissionController - 部门仓库权限管理
5. SysLoginStyleController - 登录方式管理
6. SysPermissionListController - 权限字符列表管理
7. SysPermissionListV2Controller - 权限字符列表管理V2
8. WmsPurchaseController - 申购管理
9. QrCodeToolsController - 二维码工具

## 测试步骤

### 1. 编译测试
```bash
cd C:\CKGLXT\warehouse-system\backend
mvn clean compile -q
```

### 2. 启动测试
```bash
mvn spring-boot:run
```

### 3. 检查启动日志
查看控制台输出，确认：
- 应用成功启动
- 没有Bean创建错误
- 端口8080正常监听

### 4. API测试
启动成功后，可以测试以下端点：
- GET http://localhost:8080/monitor/online/v2/list
- GET http://localhost:8080/monitor/server/v2
- GET http://localhost:8080/monitor/cache/v2
- GET http://localhost:8080/system/dept/warehouse/list

## 逐步启用策略

### 阶段1：启用基础控制器
如果应用能够正常启动，逐个启用被禁用的控制器：

1. 先启用 SysDictDataController
2. 再启用 ProductAttributeV2Controller  
3. 最后启用 SysJobV2Controller

### 阶段2：解决依赖问题
对于无法启用的控制器，需要：
1. 检查缺失的依赖
2. 创建必要的Service接口和实现
3. 创建必要的Entity类

## 常见问题及解决方案

### 问题1：Bean创建失败
**原因**: 控制器依赖的Service不存在
**解决**: 创建对应的Service接口和实现类

### 问题2：类找不到
**原因**: 缺少必要的Entity类
**解决**: 创建对应的实体类

### 问题3：循环依赖
**原因**: Bean之间存在循环引用
**解决**: 重构依赖关系或使用@Lazy注解

## 成功标准
- 应用能够正常启动
- 所有启用的控制器都能正常工作
- 菜单权限配置正确
- 前端能够正常访问新增的功能