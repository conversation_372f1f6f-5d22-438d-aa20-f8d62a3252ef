import request from '@/utils/request'

// 查询操作日志列表
export function listOperationLog(query) {
  return request({
    url: '/system/operationLog/list',
    method: 'get',
    params: query
  })
}

// 查询成功操作日志列表
export function listSuccessOperations(query) {
  return request({
    url: '/system/operationLog/success',
    method: 'get',
    params: query
  })
}

// 查询失败操作日志列表
export function listFailedOperations(query) {
  return request({
    url: '/system/operationLog/failed',
    method: 'get',
    params: query
  })
}

// 统计操作状态
export function getOperationStatistics(query) {
  return request({
    url: '/system/operationLog/statistics',
    method: 'get',
    params: query
  })
}

// 查询操作日志详细
export function getOperationLog(logId) {
  return request({
    url: '/system/operationLog/' + logId,
    method: 'get'
  })
}

// 新增操作日志
export function addOperationLog(data) {
  return request({
    url: '/system/operationLog',
    method: 'post',
    data: data
  })
}

// 修改操作日志
export function updateOperationLog(data) {
  return request({
    url: '/system/operationLog',
    method: 'put',
    data: data
  })
}

// 删除操作日志
export function delOperationLog(logId) {
  return request({
    url: '/system/operationLog/' + logId,
    method: 'delete'
  })
}

// 清理过期日志
export function cleanExpiredLogs(days) {
  return request({
    url: '/system/operationLog/clean/' + days,
    method: 'delete'
  })
}