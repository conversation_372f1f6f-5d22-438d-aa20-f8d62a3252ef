package com.wanyu.framework.web.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.wanyu.common.constant.CacheConstants;
import com.wanyu.common.constant.Constants;
import com.wanyu.common.core.domain.entity.SysUser;
import com.wanyu.common.core.domain.model.LoginUser;
import com.wanyu.common.exception.ServiceException;
import com.wanyu.common.exception.user.CaptchaException;
import com.wanyu.common.exception.user.CaptchaExpireException;
import com.wanyu.common.exception.user.UserPasswordNotMatchException;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.MessageUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.common.utils.ip.IpUtils;
import com.wanyu.framework.manager.AsyncManager;
import com.wanyu.framework.manager.factory.AsyncFactory;
import com.wanyu.system.service.ISysConfigService;
import com.wanyu.system.service.ISysUserService;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 多样式登录验证服务
 * 
 * <AUTHOR>
 */
@Component
public class MultiLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    // 内存缓存用于存储验证码
    private static final Map<String, String> CAPTCHA_CACHE = new ConcurrentHashMap<>();

    /**
     * 多样式登录验证
     * 
     * @param loginIdentifier 登录标识符（用户名/手机号/真实姓名）
     * @param password 密码
     * @param loginType 登录类型（username/phone/realname）
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String loginIdentifier, String password, String loginType, String code, String uuid)
    {
        // 验证码校验
        validateCaptcha(loginIdentifier, code, uuid);
        
        // 根据登录类型获取用户信息
        SysUser user = getUserByLoginType(loginIdentifier, loginType);
        if (user == null)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentifier, Constants.LOGIN_FAIL, 
                getLoginFailMessage(loginType)));
            throw new ServiceException(getLoginFailMessage(loginType));
        }

        // 使用用户名进行认证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(user.getUserName(), password);
            AuthenticationManager authManager = this.authenticationManager;
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentifier, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentifier, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            // 更新用户登录类型
            updateUserLoginType(user.getUserId(), loginType);
        }
        
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentifier, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 根据登录类型获取用户
     */
    private SysUser getUserByLoginType(String loginIdentifier, String loginType)
    {
        SysUser user = null;
        switch (loginType)
        {
            case "username":
                user = userService.selectUserByUserName(loginIdentifier);
                break;
            case "phone":
                user = userService.selectUserByPhonenumber(loginIdentifier);
                break;
            case "realname":
                user = userService.selectUserByRealName(loginIdentifier);
                break;
            default:
                throw new ServiceException("不支持的登录类型: " + loginType);
        }
        return user;
    }

    /**
     * 获取登录失败消息
     */
    private String getLoginFailMessage(String loginType)
    {
        switch (loginType)
        {
            case "username":
                return "用户名不存在或密码错误";
            case "phone":
                return "手机号未注册或密码错误";
            case "realname":
                return "真实姓名不存在或密码错误";
            default:
                return "登录失败";
        }
    }

    /**
     * 验证码校验
     */
    public void validateCaptcha(String loginIdentifier, String code, String uuid)
    {
        if (StringUtils.isNotEmpty(code) && StringUtils.isNotEmpty(uuid))
        {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
            String captcha = CAPTCHA_CACHE.get(verifyKey);
            CAPTCHA_CACHE.remove(verifyKey); // 验证后立即删除
            
            if (captcha == null)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentifier, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            
            if (!code.equalsIgnoreCase(captcha))
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentifier, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 设置验证码缓存
     *
     * @param uuid 验证码唯一标识
     * @param code 验证码
     */
    public void setCaptchaCache(String uuid, String code) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        CAPTCHA_CACHE.put(verifyKey, code);
    }
    
    /**
     * 获取验证码缓存
     *
     * @param uuid 验证码唯一标识
     * @return 验证码
     */
    public String getCaptchaFromCache(String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        return CAPTCHA_CACHE.get(verifyKey);
    }
    
    /**
     * 清除验证码缓存
     *
     * @param uuid 验证码唯一标识
     */
    public void clearCaptchaCache(String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        CAPTCHA_CACHE.remove(verifyKey);
    }
    
    /**
     * 定期清理过期验证码（建议在应用启动时开启定时任务）
     */
    public void startCaptchaCleanupTask() {
        // 这里使用一个守护线程来定期清理过期的验证码
        Thread cleanupThread = new Thread(() -> {
            while (true) {
                try {
                    TimeUnit.MINUTES.sleep(5); // 每5分钟清理一次
                    long now = System.currentTimeMillis();
                    
                    // 简单的清理策略，实际应用中可能需要更复杂的过期处理
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
        cleanupThread.setDaemon(true);
        cleanupThread.start();
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    /**
     * 更新用户登录类型
     */
    private void updateUserLoginType(Long userId, String loginType)
    {
        SysUser updateUser = new SysUser();
        updateUser.setUserId(userId);
        updateUser.setLoginType(loginType);
        userService.updateUserProfile(updateUser);
    }
}