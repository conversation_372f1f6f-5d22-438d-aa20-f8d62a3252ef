package com.wanyu.system.mapper;

import java.util.List;
import java.util.Map;
import com.wanyu.system.domain.SysPermissionLog;

/**
 * 权限日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface SysPermissionLogMapper 
{
    /**
     * 查询权限日志
     * 
     * @param logId 权限日志主键
     * @return 权限日志
     */
    public SysPermissionLog selectSysPermissionLogByLogId(Long logId);

    /**
     * 查询权限日志列表
     * 
     * @param sysPermissionLog 权限日志
     * @return 权限日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogList(SysPermissionLog sysPermissionLog);

    /**
     * 新增权限日志
     * 
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    public int insertSysPermissionLog(SysPermissionLog sysPermissionLog);

    /**
     * 修改权限日志
     * 
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    public int updateSysPermissionLog(SysPermissionLog sysPermissionLog);

    /**
     * 删除权限日志
     * 
     * @param logId 权限日志主键
     * @return 结果
     */
    public int deleteSysPermissionLogByLogId(Long logId);

    /**
     * 批量删除权限日志
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysPermissionLogByLogIds(Long[] logIds);

    /**
     * 清空权限日志
     * 
     * @return 结果
     */
    public int cleanSysPermissionLog();

    /**
     * 统计权限操作总数
     * 
     * @param sysPermissionLog 查询条件
     * @return 总数
     */
    public int countTotalPermissions(SysPermissionLog sysPermissionLog);

    /**
     * 统计今日权限操作数
     * 
     * @param sysPermissionLog 查询条件
     * @return 今日操作数
     */
    public int countTodayPermissions(SysPermissionLog sysPermissionLog);

    /**
     * 获取权限操作趋势数据
     * 
     * @param sysPermissionLog 查询条件
     * @return 趋势数据
     */
    public List<Map<String, Object>> getPermissionTrend(SysPermissionLog sysPermissionLog);
}