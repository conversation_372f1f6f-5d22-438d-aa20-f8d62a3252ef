package com.wanyu.web.controller.system;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Anonymous;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;

/**
 * SSL证书管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/ssl")
public class SslCertificateController extends BaseController
{
    /**
     * 下载SSL客户端证书 (CER格式)
     */
    @Anonymous
    @GetMapping("/download/cer")
    public ResponseEntity<Resource> downloadCertificate()
    {
        try 
        {
            // 从classpath加载证书文件
            Resource resource = new ClassPathResource("ssl-certs/warehouse-system.cer");
            
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename=\"warehouse-system.cer\"")
                    .body(resource);
        } 
        catch (Exception e) 
        {
            logger.error("下载SSL证书失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 下载SSL客户端证书 (PEM格式)
     */
    @Anonymous
    @GetMapping("/download/pem")
    public ResponseEntity<Resource> downloadCertificatePem()
    {
        try 
        {
            // 从classpath加载PEM格式证书文件
            Resource resource = new ClassPathResource("ssl-certs/warehouse-system.pem");
            
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename=\"warehouse-system.pem\"")
                    .body(resource);
        } 
        catch (Exception e) 
        {
            logger.error("下载SSL证书(PEM)失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取HTTPS配置信息
     */
    @Anonymous
    @GetMapping("/info")
    public AjaxResult getHttpsInfo(HttpServletRequest request)
    {
        Map<String, Object> info = new HashMap<>();
        
        try 
        {
            // 获取服务器信息
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            String scheme = request.getScheme();
            boolean isHttps = "https".equals(scheme);
            
            // 获取本机IP地址
            String localIp = InetAddress.getLocalHost().getHostAddress();
            
            info.put("isHttps", isHttps);
            info.put("serverName", serverName);
            info.put("serverPort", serverPort);
            info.put("scheme", scheme);
            info.put("localIp", localIp);
            
            // HTTPS访问地址
            if (isHttps) {
                info.put("httpsUrl", "https://" + serverName + ":" + serverPort);
                info.put("localHttpsUrl", "https://" + localIp + ":" + serverPort);
            } else {
                info.put("httpsUrl", "https://" + serverName + ":8443");
                info.put("localHttpsUrl", "https://" + localIp + ":8443");
            }
            
            // 证书下载链接
            String baseUrl = scheme + "://" + serverName + ":" + serverPort;
            info.put("certificateDownloadUrl", baseUrl + "/system/ssl/download/cer");
            info.put("certificatePemDownloadUrl", baseUrl + "/system/ssl/download/pem");
            
            // 检查证书文件是否存在
            Resource cerResource = new ClassPathResource("ssl-certs/warehouse-system.cer");
            Resource pemResource = new ClassPathResource("ssl-certs/warehouse-system.pem");
            
            info.put("certificateExists", cerResource.exists());
            info.put("pemCertificateExists", pemResource.exists());
            
            return AjaxResult.success("获取HTTPS信息成功", info);
        } 
        catch (UnknownHostException e) 
        {
            logger.error("获取本机IP地址失败", e);
            return AjaxResult.error("获取HTTPS信息失败：无法获取本机IP地址");
        }
        catch (Exception e) 
        {
            logger.error("获取HTTPS信息失败", e);
            return AjaxResult.error("获取HTTPS信息失败");
        }
    }
    
    /**
     * 获取证书安装指南
     */
    @Anonymous
    @GetMapping("/guide")
    public AjaxResult getCertificateInstallGuide()
    {
        Map<String, Object> guide = new HashMap<>();
        
        // Windows安装指南
        String[] windowsSteps = {
            "1. 下载证书文件 warehouse-system.cer",
            "2. 双击证书文件",
            "3. 点击安装证书",
            "4. 选择本地计算机，点击下一步",
            "5. 选择将所有证书放入下列存储",
            "6. 点击浏览，选择受信任的根证书颁发机构",
            "7. 点击下一步，然后完成",
            "8. 在安全警告对话框中点击是"
        };
        
        // Linux安装指南
        String[] linuxSteps = {
            "1. 下载证书文件 warehouse-system.pem",
            "2. 复制到系统证书目录: sudo cp warehouse-system.pem /usr/local/share/ca-certificates/warehouse-system.crt",
            "3. 更新证书存储: sudo update-ca-certificates",
            "4. 重启浏览器"
        };
        
        // Mac安装指南
        String[] macSteps = {
            "1. 下载证书文件 warehouse-system.pem",
            "2. 双击证书文件，输入管理员密码",
            "3. 在钥匙串访问中找到证书",
            "4. 双击证书，展开信任部分",
            "5. 将使用此证书时设置为始终信任"
        };
        
        guide.put("windows", windowsSteps);
        guide.put("linux", linuxSteps);
        guide.put("mac", macSteps);
        
        // 通用注意事项
        String[] notes = {
            "安装证书后需要重启浏览器",
            "首次访问HTTPS网站时可能仍会显示警告，点击继续即可",
            "证书有效期为10年",
            "如果证书安装失败，请以管理员权限重试"
        };
        
        guide.put("notes", notes);
        
        return AjaxResult.success("获取证书安装指南成功", guide);
    }
}