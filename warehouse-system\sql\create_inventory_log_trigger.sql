-- ===================================================================
-- 创建出入库日志自动填充触发器
-- 确保新插入的记录自动填充仓库名称和物品信息
-- ===================================================================

-- 删除已存在的触发器
DROP TRIGGER IF EXISTS tr_inventory_log_auto_fill_before_insert;
DROP TRIGGER IF EXISTS tr_inventory_log_auto_fill_before_update;

DELIMITER $$

-- 创建插入前触发器
CREATE TRIGGER tr_inventory_log_auto_fill_before_insert
BEFORE INSERT ON wms_inventory_log
FOR EACH ROW
BEGIN
    -- 自动填充仓库名称
    IF NEW.warehouse_id IS NOT NULL AND (NEW.warehouse_name IS NULL OR NEW.warehouse_name = '') THEN
        SELECT warehouse_name INTO NEW.warehouse_name 
        FROM sys_warehouse 
        WHERE warehouse_id = NEW.warehouse_id 
        LIMIT 1;
        
        -- 如果没有找到仓库名称，使用默认格式
        IF NEW.warehouse_name IS NULL OR NEW.warehouse_name = '' THEN
            SET NEW.warehouse_name = CONCAT('仓库-', NEW.warehouse_id);
        END IF;
    END IF;
    
    -- 自动填充物品信息
    IF NEW.product_id IS NOT NULL THEN
        -- 填充物品名称
        IF NEW.product_name IS NULL OR NEW.product_name = '' THEN
            SELECT product_name INTO NEW.product_name 
            FROM wms_product 
            WHERE product_id = NEW.product_id 
            LIMIT 1;
            
            -- 如果没有找到物品名称，使用默认格式
            IF NEW.product_name IS NULL OR NEW.product_name = '' THEN
                SET NEW.product_name = CONCAT('物品-', NEW.product_id);
            END IF;
        END IF;
        
        -- 填充物品编码
        IF NEW.product_code IS NULL OR NEW.product_code = '' THEN
            SELECT product_code INTO NEW.product_code 
            FROM wms_product 
            WHERE product_id = NEW.product_id 
            LIMIT 1;
            
            -- 如果没有找到物品编码，使用默认格式
            IF NEW.product_code IS NULL OR NEW.product_code = '' THEN
                SET NEW.product_code = CONCAT('CODE-', NEW.product_id);
            END IF;
        END IF;
        
        -- 填充物品规格
        IF NEW.product_spec IS NULL OR NEW.product_spec = '' THEN
            SELECT ps.spec_name INTO NEW.product_spec 
            FROM wms_product p
            LEFT JOIN wms_specification ps ON p.spec_id = ps.spec_id
            WHERE p.product_id = NEW.product_id 
            LIMIT 1;
            
            -- 如果没有找到规格，使用默认值
            IF NEW.product_spec IS NULL OR NEW.product_spec = '' THEN
                SET NEW.product_spec = '标准规格';
            END IF;
        END IF;
        
        -- 填充物品单位
        IF NEW.product_unit IS NULL OR NEW.product_unit = '' THEN
            SELECT COALESCE(pu.unit_name, pu.unit_code) INTO NEW.product_unit 
            FROM wms_product p
            LEFT JOIN wms_unit pu ON p.unit_id = pu.unit_id
            WHERE p.product_id = NEW.product_id 
            LIMIT 1;
            
            -- 如果没有找到单位，使用默认值
            IF NEW.product_unit IS NULL OR NEW.product_unit = '' THEN
                SET NEW.product_unit = '个';
            END IF;
        END IF;
    END IF;
    
    -- 设置默认状态
    IF NEW.status IS NULL OR NEW.status = '' THEN
        SET NEW.status = '0';
    END IF;
    
    -- 设置操作时间
    IF NEW.operation_time IS NULL THEN
        SET NEW.operation_time = NOW();
    END IF;
    
    -- 设置创建时间
    IF NEW.create_time IS NULL THEN
        SET NEW.create_time = NOW();
    END IF;
    
    -- 设置更新时间
    IF NEW.update_time IS NULL THEN
        SET NEW.update_time = NOW();
    END IF;
END$$

-- 创建更新前触发器
CREATE TRIGGER tr_inventory_log_auto_fill_before_update
BEFORE UPDATE ON wms_inventory_log
FOR EACH ROW
BEGIN
    -- 自动填充仓库名称（如果仓库ID发生变化或仓库名称为空）
    IF NEW.warehouse_id != OLD.warehouse_id OR NEW.warehouse_name IS NULL OR NEW.warehouse_name = '' OR NEW.warehouse_name LIKE '仓库-%' THEN
        SELECT warehouse_name INTO NEW.warehouse_name 
        FROM sys_warehouse 
        WHERE warehouse_id = NEW.warehouse_id 
        LIMIT 1;
        
        -- 如果没有找到仓库名称，使用默认格式
        IF NEW.warehouse_name IS NULL OR NEW.warehouse_name = '' THEN
            SET NEW.warehouse_name = CONCAT('仓库-', NEW.warehouse_id);
        END IF;
    END IF;
    
    -- 设置更新时间
    SET NEW.update_time = NOW();
END$$

DELIMITER ;

-- 验证触发器创建
SELECT 
    TRIGGER_NAME as '触发器名称',
    EVENT_MANIPULATION as '触发事件',
    EVENT_OBJECT_TABLE as '目标表',
    TRIGGER_SCHEMA as '数据库',
    ACTION_TIMING as '触发时机'
FROM information_schema.TRIGGERS 
WHERE EVENT_OBJECT_TABLE = 'wms_inventory_log' 
AND TRIGGER_SCHEMA = DATABASE()
ORDER BY TRIGGER_NAME;

-- 测试触发器功能
-- 注意：这只是测试，实际使用时会被应用程序调用
-- INSERT INTO wms_inventory_log (warehouse_id, product_id, operation_type, quantity, before_quantity, after_quantity, operator) 
-- VALUES (4, 201, 'IN', 1.00, 0.00, 1.00, 'trigger_test');

-- 显示最新记录验证触发器效果
-- SELECT log_id, warehouse_id, warehouse_name, product_id, product_name, operation_type, operator, operation_time 
-- FROM wms_inventory_log 
-- ORDER BY log_id DESC 
-- LIMIT 3;

SELECT '出入库日志自动填充触发器创建完成！' as message;
