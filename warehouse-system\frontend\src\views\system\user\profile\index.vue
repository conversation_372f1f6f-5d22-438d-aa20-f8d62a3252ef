<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <div class="text-center">
              <userAvatar />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="user" />用户名称
                <div class="pull-right">{{ user.userName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="user" />用户昵称
                <div class="pull-right">{{ user.nickName || '未设置' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="user" />真实姓名
                <div class="pull-right">{{ user.realName || '未设置' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone" />手机号码
                <div class="pull-right">{{ user.phonenumber || '未设置' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email" />用户邮箱
                <div class="pull-right">{{ user.email || '未设置' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="peoples" />用户性别
                <div class="pull-right">{{ getSexText(user.sex) }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />所属部门
                <div class="pull-right" v-if="user.dept">{{ user.dept.deptName }}</div>
                <div class="pull-right" v-else>未分配部门</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="peoples" />所属角色
                <div class="pull-right">{{ roleGroup || '未分配角色' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="validCode" />用户类型
                <div class="pull-right">{{ getUserTypeText(user.userType) }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="circle-check" />账号状态
                <div class="pull-right">
                  <el-tag :type="user.status === '0' ? 'success' : 'danger'" size="mini">
                    {{ user.status === '0' ? '正常' : '停用' }}
                  </el-tag>
                </div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="monitor" />最后登录IP
                <div class="pull-right">{{ user.loginIp || '未记录' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />最后登录时间
                <div class="pull-right">{{ user.loginDate || '未记录' }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />创建日期
                <div class="pull-right">{{ user.createTime }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />更新时间
                <div class="pull-right">{{ user.updateTime || '未更新' }}</div>
              </li>
              <li class="list-group-item" v-if="user.remark">
                <svg-icon icon-class="documentation" />备注信息
                <div class="pull-right">{{ user.remark }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>基本资料</span>
          </div>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo :user="user" @refresh="getUser" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import { getUserProfile } from "@/api/system/user";

export default {
  name: "Profile",
  components: { userAvatar, userInfo, resetPwd },
  data() {
    return {
      user: {},
      roleGroup: {},
      activeTab: "userinfo"
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
      });
    },
    // 获取性别文本
    getSexText(sex) {
      const sexMap = {
        '0': '男',
        '1': '女',
        '2': '未知'
      };
      return sexMap[sex] || '未设置';
    },
    // 获取用户类型文本
    getUserTypeText(userType) {
      const typeMap = {
        '00': '系统用户',
        '01': '注册用户'
      };
      return typeMap[userType] || '未知类型';
    }
  }
};
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.list-group {
  padding-left: 0;
  margin-bottom: 0;
}

.list-group-striped .list-group-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.list-group-striped .list-group-item:last-child {
  border-bottom: none;
}

.list-group-striped .list-group-item:nth-child(odd) {
  background-color: #fafafa;
  padding-left: 10px;
  padding-right: 10px;
  margin: 0 -10px;
}

.list-group-item svg {
  margin-right: 8px;
  color: #409eff;
}

.pull-right {
  font-weight: 500;
  color: #606266;
}

.text-center {
  text-align: center;
  margin-bottom: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.clearfix span {
  font-weight: 600;
  color: #303133;
}

// 响应式设计
@media (max-width: 768px) {
  .list-group-item {
    flex-direction: column;
    align-items: flex-start;
    
    .pull-right {
      margin-top: 5px;
      align-self: flex-end;
    }
  }
}
</style>
