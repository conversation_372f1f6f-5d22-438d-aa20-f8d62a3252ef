@echo off
chcp 65001 >nul
echo ========================================
echo 删除库区和货架相关组件
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo 🗑️  开始删除库区和货架相关组件...
echo.

echo 📋 1. 删除数据库表和相关数据...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < remove_area_location_components.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 数据库清理完成！
    echo.
    
    echo 📋 2. 删除后端Java文件...
    
    echo 删除控制器文件:
    if exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseAreaController.java" (
        del "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseAreaController.java"
        echo   ✅ WarehouseAreaController.java 已删除
    )
    
    if exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseLocationController.java" (
        del "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseLocationController.java"
        echo   ✅ WarehouseLocationController.java 已删除
    )
    
    echo.
    echo 📋 3. 删除前端Vue文件...
    
    if exist "warehouse-system\frontend\src\views\warehouse\area" (
        rmdir /s /q "warehouse-system\frontend\src\views\warehouse\area"
        echo   ✅ warehouse/area 目录已删除
    )
    
    if exist "warehouse-system\frontend\src\views\warehouse\location" (
        rmdir /s /q "warehouse-system\frontend\src\views\warehouse\location"
        echo   ✅ warehouse/location 目录已删除
    )
    
    echo.
    echo ========================================
    echo ✅ 库区和货架组件删除完成！
    echo ========================================
    echo.
    echo 📝 删除内容总结:
    echo   1. ✅ 数据库表: wms_warehouse_area, wms_warehouse_rack
    echo   2. ✅ 相关菜单和权限数据
    echo   3. ✅ 后端控制器: WarehouseAreaController, WarehouseLocationController
    echo   4. ✅ 前端页面: warehouse/area, warehouse/location
    echo.
    echo 🔄 下一步操作:
    echo   1. 重新编译后端项目
    echo   2. 重启后端服务
    echo   3. 刷新前端页面
    echo   4. 检查是否还有相关的实体类和服务需要删除
    echo.
    echo 💡 提示: 如果还有相关的实体类、服务接口和Mapper文件，
    echo          需要手动删除或运行进一步的清理脚本
    echo.
) else (
    echo.
    echo ❌ 数据库清理失败！
    echo 请检查数据库连接和权限设置
    echo.
)

echo 按任意键退出...
pause >nul