package com.wanyu.framework.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.wanyu.framework.interceptor.PermissionInterceptor;

/**
 * 权限配置
 * 
 * <AUTHOR>
 */
@Configuration
public class PermissionConfig implements WebMvcConfigurer {
    
    @Autowired
    private PermissionInterceptor permissionInterceptor;

    /**
     * 注册权限拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(permissionInterceptor)
                // 拦截所有请求
                .addPathPatterns("/**")
                // 排除不需要拦截的路径
                .excludePathPatterns(
                    "/login",                // 登录
                    "/logout",               // 注销
                    "/register",            // 注册
                    "/dev-api/register",    // 代理注册
                    "/captchaImage",        // 验证码
                    "/dev-api/captchaImage",// 代理验证码
                    "/profile/**",           // 个人信息
                    "/common/**",            // 通用请求
                    "/swagger-resources/**",  // swagger
                    "/webjars/**",           // swagger-ui
                    // 授权管理相关接口
                    "/system/license/**",    // 所有授权接口
                    "/dev-api/system/license/**", // 所有授权代理接口
                    "/error",                // 错误页面
                    "/favicon.ico",          // 图标
                    "/css/**",               // 静态资源
                    "/js/**",                // 静态资源
                    "/img/**",               // 静态资源
                    "/fonts/**",             // 静态资源
                    "/static/**",            // 静态资源
                    "/*/api-docs",           // swagger-ui
                    "/druid/**",             // druid
                    "/actuator/**"           // 监控端点
                );
    }
}