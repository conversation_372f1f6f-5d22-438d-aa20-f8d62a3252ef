import request from '@/utils/request'

// 查询功能权限列表
export function listLicenseFeature(query) {
  return request({
    url: '/system/feature/list',
    method: 'get',
    params: query
  })
}

// 查询功能权限详细
export function getLicenseFeature(featureId) {
  return request({
    url: '/system/feature/' + featureId,
    method: 'get'
  })
}

// 根据功能代码查询功能权限
export function getLicenseFeatureByCode(featureCode) {
  return request({
    url: '/system/feature/code/' + featureCode,
    method: 'get'
  })
}

// 查询启用的功能权限列表
export function listEnabledFeatures() {
  return request({
    url: '/system/feature/enabled',
    method: 'get'
  })
}

// 查询核心功能列表
export function listCoreFeatures() {
  return request({
    url: '/system/feature/core',
    method: 'get'
  })
}

// 根据授权类型查询可用功能
export function listFeaturesByLicenseType(licenseType) {
  return request({
    url: '/system/feature/license/' + licenseType,
    method: 'get'
  })
}

// 检查功能是否可用
export function checkFeatureAvailable(featureCode) {
  return request({
    url: '/system/feature/available/' + featureCode,
    method: 'get'
  })
}

// 新增功能权限
export function addLicenseFeature(data) {
  return request({
    url: '/system/feature',
    method: 'post',
    data: data
  })
}

// 修改功能权限
export function updateLicenseFeature(data) {
  return request({
    url: '/system/feature',
    method: 'put',
    data: data
  })
}

// 删除功能权限
export function delLicenseFeature(featureIds) {
  return request({
    url: '/system/feature/' + featureIds,
    method: 'delete'
  })
}

// 启用功能
export function enableFeature(featureId) {
  return request({
    url: '/system/feature/enable/' + featureId,
    method: 'put'
  })
}

// 禁用功能
export function disableFeature(featureId) {
  return request({
    url: '/system/feature/disable/' + featureId,
    method: 'put'
  })
}

// 批量启用功能
export function enableFeatures(featureIds) {
  return request({
    url: '/system/feature/enable',
    method: 'put',
    data: featureIds
  })
}

// 批量禁用功能
export function disableFeatures(featureIds) {
  return request({
    url: '/system/feature/disable',
    method: 'put',
    data: featureIds
  })
}

// 导出功能权限
export function exportLicenseFeature(query) {
  return request({
    url: '/system/feature/export',
    method: 'post',
    params: query
  })
}