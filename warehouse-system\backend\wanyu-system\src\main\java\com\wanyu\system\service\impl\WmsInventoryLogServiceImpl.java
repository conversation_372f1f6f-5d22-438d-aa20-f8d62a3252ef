package com.wanyu.system.service.impl;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.system.mapper.WmsInventoryLogMapper;
import com.wanyu.system.domain.WmsInventoryLog;
import com.wanyu.system.domain.ProductInfo;
import com.wanyu.system.service.IWmsInventoryLogService;
import com.wanyu.system.service.IProductInfoService;
import com.wanyu.system.domain.WmsInventory;
import com.wanyu.system.service.IWmsInventoryService;
import com.wanyu.common.utils.DateUtils;

/**
 * 出入库日志Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class WmsInventoryLogServiceImpl implements IWmsInventoryLogService 
{
    @Autowired
    private WmsInventoryLogMapper wmsInventoryLogMapper;
    
    @Autowired
    private IWmsInventoryService wmsInventoryService;
    
    @Autowired(required = false)
    private IProductInfoService productInfoService;

    @Autowired
    private com.wanyu.system.service.ISysUserService userService;

    /**
     * 查询出入库日志
     * 
     * @param logId 出入库日志ID
     * @return 出入库日志
     */
    @Override
    public WmsInventoryLog selectWmsInventoryLogByLogId(Long logId)
    {
        return wmsInventoryLogMapper.selectWmsInventoryLogByLogId(logId);
    }

    /**
     * 查询出入库日志列表
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 出入库日志
     */
    @Override
    public List<WmsInventoryLog> selectWmsInventoryLogList(WmsInventoryLog wmsInventoryLog)
    {
        // 仓库权限过滤：非管理员用户只查有权限的仓库
        com.wanyu.common.core.domain.model.LoginUser loginUser = com.wanyu.common.utils.SecurityUtils.getLoginUser();
        if (loginUser != null && loginUser.getUser() != null) {
            boolean isAdmin = loginUser.getUser().getUserId() == 1L
                || loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()));
            if (!isAdmin) {
                // 修复权限过滤逻辑，包含用户直接权限、角色权限和部门权限
                Long userId = loginUser.getUser().getUserId();
                String scopeSql = " AND l.warehouse_id IN (" +
                    "SELECT warehouse_id FROM sys_user_warehouse WHERE user_id = " + userId + " " +
                    "UNION " +
                    "SELECT rw.warehouse_id FROM sys_role_warehouse rw " +
                    "INNER JOIN sys_user_role ur ON rw.role_id = ur.role_id " +
                    "WHERE ur.user_id = " + userId + " " +
                    "UNION " +
                    "SELECT dw.warehouse_id FROM sys_dept_warehouse dw " +
                    "INNER JOIN sys_user u ON dw.dept_id = u.dept_id " +
                    "WHERE u.user_id = " + userId + ") ";
                
                if (wmsInventoryLog.getParams() == null) {
                    wmsInventoryLog.setParams(new java.util.HashMap<>());
                }
                wmsInventoryLog.getParams().put("warehouseScope", scopeSql);
                
                System.err.println("=== 出入库日志权限过滤 ===");
                System.err.println("用户ID: " + userId);
                System.err.println("权限SQL: " + scopeSql);
            } else {
                System.err.println("=== 管理员用户，跳过权限过滤 ===");
            }
        }
        List<WmsInventoryLog> logList = wmsInventoryLogMapper.selectWmsInventoryLogList(wmsInventoryLog);
        
        System.err.println("=== 出入库日志查询结果 ===");
        System.err.println("查询到记录数: " + (logList != null ? logList.size() : 0));
        
        // 转换用户名为真实姓名
        if (logList != null && !logList.isEmpty()) {
            for (WmsInventoryLog log : logList) {
                convertUserNames(log);
            }
        }
        
        return logList;
    }

    /**
     * 根据用户仓库权限查询出入库日志列表
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 出入库日志
     */
    @Override
    public List<WmsInventoryLog> selectWmsInventoryLogListWithAuth(WmsInventoryLog wmsInventoryLog)
    {
        // 添加当前用户ID到查询参数中，用于权限过滤
        if (wmsInventoryLog.getParams() == null) {
            wmsInventoryLog.setParams(new java.util.HashMap<>());
        }
        
        Long userId = SecurityUtils.getUserId();
        String username = SecurityUtils.getUsername();
        wmsInventoryLog.getParams().put("userId", userId);
        
        System.err.println("=== selectWmsInventoryLogListWithAuth 调用 ===");
        System.err.println("用户: " + username + " (ID: " + userId + ")");
        System.err.println("查询参数: " + wmsInventoryLog.getParams());
        
        List<WmsInventoryLog> logList = wmsInventoryLogMapper.selectWmsInventoryLogListWithAuth(wmsInventoryLog);
        
        System.err.println("Mapper查询结果: " + (logList != null ? logList.size() : 0) + " 条记录");
        
        // 转换用户名为真实姓名
        if (logList != null && !logList.isEmpty()) {
            System.err.println("前3条记录详情:");
            for (int i = 0; i < Math.min(3, logList.size()); i++) {
                WmsInventoryLog log = logList.get(i);
                System.err.println("  " + (i+1) + ". 操作: " + log.getOperationType() + 
                    ", 仓库: " + log.getWarehouseName() + " (ID: " + log.getWarehouseId() + ")" +
                    ", 物品: " + log.getProductName() + ", 数量: " + log.getQuantity());
                convertUserNames(log);
            }
        } else {
            System.err.println("没有查询到任何出入库日志记录");
        }
        
        return logList;
    }

    /**
     * 新增出入库日志
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 结果
     */
    @Override
    public int insertWmsInventoryLog(WmsInventoryLog wmsInventoryLog)
    {
        wmsInventoryLog.setCreateTime(DateUtils.getNowDate());
        wmsInventoryLog.setCreateBy(SecurityUtils.getUsername());
        return wmsInventoryLogMapper.insertWmsInventoryLog(wmsInventoryLog);
    }

    /**
     * 修改出入库日志
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 结果
     */
    @Override
    public int updateWmsInventoryLog(WmsInventoryLog wmsInventoryLog)
    {
        wmsInventoryLog.setUpdateTime(DateUtils.getNowDate());
        wmsInventoryLog.setUpdateBy(SecurityUtils.getUsername());
        return wmsInventoryLogMapper.updateWmsInventoryLog(wmsInventoryLog);
    }

    /**
     * 批量删除出入库日志
     * 
     * @param logIds 需要删除的出入库日志ID
     * @return 结果
     */
    @Override
    public int deleteWmsInventoryLogByLogIds(Long[] logIds)
    {
        return wmsInventoryLogMapper.deleteWmsInventoryLogByLogIds(logIds);
    }

    /**
     * 删除出入库日志信息
     * 
     * @param logId 出入库日志ID
     * @return 结果
     */
    @Override
    public int deleteWmsInventoryLogByLogId(Long logId)
    {
        return wmsInventoryLogMapper.deleteWmsInventoryLogByLogId(logId);
    }

    /**
     * 清空出入库日志
     *
     * @return 结果
     */
    @Override
    public int cleanWmsInventoryLog()
    {
        return wmsInventoryLogMapper.cleanWmsInventoryLog();
    }

    /**
     * 获取库存统计信息
     * 
     * @param params 查询参数
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getInventoryStatistics(Map<String, Object> params)
    {
        // 添加当前用户ID到查询参数中，用于权限过滤
        params.put("userId", SecurityUtils.getUserId());
        return wmsInventoryLogMapper.getInventoryStatistics(params);
    }

    /**
     * 获取库存操作趋势数据
     * 
     * @param params 查询参数
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getInventoryTrend(Map<String, Object> params)
    {
        // 添加当前用户ID到查询参数中，用于权限过滤
        params.put("userId", SecurityUtils.getUserId());
        return wmsInventoryLogMapper.getInventoryTrend(params);
    }

    /**
     * 记录库存变动日志
     * 
     * @param warehouseId 仓库ID
     * @param productId 产品ID
     * @param operationType 操作类型（IN-入库，OUT-出库，TRANSFER-调拨，CHECK-盘点）
     * @param quantity 操作数量
     * @param beforeQuantity 操作前数量
     * @param afterQuantity 操作后数量
     * @param relatedOrderId 关联单据号
     * @param relatedOrderType 关联单据类型
     * @param operator 操作人
     * @param remark 备注
     * @return 结果
     */
    @Override
    public int recordInventoryLog(Long warehouseId, Long productId, String operationType, 
            BigDecimal quantity, BigDecimal beforeQuantity, BigDecimal afterQuantity,
            String relatedOrderId, String relatedOrderType, String operator, String remark)
    {
        WmsInventoryLog log = new WmsInventoryLog();
        log.setWarehouseId(warehouseId);
        log.setProductId(productId);
        log.setOperationType(operationType);
        log.setQuantity(quantity);
        log.setBeforeQuantity(beforeQuantity);
        log.setAfterQuantity(afterQuantity);
        log.setRelatedOrderId(relatedOrderId);
        log.setRelatedOrderType(relatedOrderType);
        log.setOperator(operator);
        log.setOperatorId(SecurityUtils.getUserId());
        log.setOperationTime(DateUtils.getNowDate());
        log.setRemark(remark);
        log.setStatus("0");
        log.setCreateTime(DateUtils.getNowDate());
        log.setCreateBy(operator);
        
        // 自动填充仓库和物品信息
        fillWarehouseAndProductInfo(log);

        int result = wmsInventoryLogMapper.insertWmsInventoryLog(log);

        // 插入后立即更新仓库名称（确保数据正确）
        if (result > 0 && log.getLogId() != null) {
            try {
                wmsInventoryLogMapper.updateWarehouseNameByLogId(log.getLogId());
                System.out.println("插入后更新仓库名称成功，日志ID: " + log.getLogId());
            } catch (Exception e) {
                System.err.println("插入后更新仓库名称失败，日志ID: " + log.getLogId() + ", 错误: " + e.getMessage());
            }
        }

        return result;
    }

    /**
     * 记录库存操作日志（增强版）
     * 
     * @param operationType 操作类型
     * @param warehouseId 仓库ID
     * @param productId 物品ID
     * @param quantity 操作数量
     * @param beforeQuantity 操作前数量
     * @param afterQuantity 操作后数量
     * @param relatedOrderId 关联单据号
     * @param reason 操作原因
     * @param remark 备注
     */
    @Override
    public void recordInventoryLogEnhanced(String operationType, Long warehouseId, Long productId, 
                                 BigDecimal quantity, BigDecimal beforeQuantity, 
                                 BigDecimal afterQuantity, String relatedOrderId, 
                                 String reason, String remark)
    {
        WmsInventoryLog log = new WmsInventoryLog();
        log.setOperationType(operationType);
        log.setWarehouseId(warehouseId);
        log.setProductId(productId);
        log.setQuantity(quantity);
        
        // 如果没有提供操作前和操作后数量，则查询当前库存来计算
        if (beforeQuantity == null || afterQuantity == null) {
            // 查询当前库存
            BigDecimal currentQuantity = BigDecimal.ZERO;
            WmsInventory currentInventory = wmsInventoryService.selectWmsInventoryByProductIdAndWarehouseId(productId, warehouseId);
            if (currentInventory != null) {
                currentQuantity = currentInventory.getQuantity();
            }
            
            // 如果操作前数量未提供，则使用当前库存
            if (beforeQuantity == null) {
                log.setBeforeQuantity(currentQuantity);
            } else {
                log.setBeforeQuantity(beforeQuantity);
            }
            
            // 如果操作后数量未提供，则根据操作类型计算
            if (afterQuantity == null) {
                // 对于调拨出库，数量是负数，所以需要相加
                log.setAfterQuantity(currentQuantity.add(quantity));
            } else {
                log.setAfterQuantity(afterQuantity);
            }
        } else {
            log.setBeforeQuantity(beforeQuantity);
            log.setAfterQuantity(afterQuantity);
        }
        
        log.setRelatedOrderId(relatedOrderId);
        log.setReason(reason);
        log.setRemark(remark);
        log.setOperator(SecurityUtils.getUsername());
        log.setOperatorId(SecurityUtils.getUserId());
        log.setOperationTime(new Date());
        log.setStatus("0");
        
        // 自动填充仓库和物品信息
        fillWarehouseAndProductInfo(log);
        
        // 插入日志记录
        insertWmsInventoryLog(log);
        
        // 插入后立即更新仓库名称（确保数据正确）
        if (log.getLogId() != null) {
            try {
                wmsInventoryLogMapper.updateWarehouseNameByLogId(log.getLogId());
                System.out.println("插入后更新仓库名称成功，日志ID: " + log.getLogId());
            } catch (Exception e) {
                System.err.println("插入后更新仓库名称失败，日志ID: " + log.getLogId() + ", 错误: " + e.getMessage());
            }
        }
    }

    @Override
    public List<WmsInventoryLog> selectWmsInventoryLogListByInventoryId(Long inventoryId) {
        return wmsInventoryLogMapper.selectWmsInventoryLogListByInventoryId(inventoryId);
    }
    
    /**
     * 按物品类别统计库存操作
     * 
     * @param params 查询参数
     * @return 统计数据
     */
    @Override
    public List<Map<String, Object>> getInventoryStatsByCategory(Map<String, Object> params) {
        // 添加当前用户ID到查询参数中，用于权限过滤
        params.put("userId", SecurityUtils.getUserId());
        return wmsInventoryLogMapper.getInventoryStatsByCategory(params);
    }


    
    /**
     * 自动填充仓库和物品信息
     *
     * @param log 出入库日志对象
     */
    private void fillWarehouseAndProductInfo(WmsInventoryLog log) {
        try {
            // 填充仓库信息
            if (log.getWarehouseId() != null) {
                try {
                    Map<String, Object> warehouseInfo = wmsInventoryLogMapper.getWarehouseInfo(log.getWarehouseId());
                    if (warehouseInfo != null && warehouseInfo.get("warehouse_name") != null) {
                        String warehouseName = (String) warehouseInfo.get("warehouse_name");
                        log.setWarehouseName(warehouseName);
                        System.out.println("成功填充仓库名称: " + warehouseName + " (仓库ID: " + log.getWarehouseId() + ")");
                    } else {
                        // 尝试从sys_warehouse表中获取仓库名称
                        String warehouseName = getWarehouseNameFromSysWarehouse(log.getWarehouseId());
                        if (warehouseName != null) {
                            log.setWarehouseName(warehouseName);
                            System.out.println("通过sys_warehouse表填充仓库名称: " + warehouseName + " (仓库ID: " + log.getWarehouseId() + ")");
                        } else {
                            String defaultName = "仓库-" + log.getWarehouseId();
                            log.setWarehouseName(defaultName);
                            System.out.println("使用默认仓库名称: " + defaultName + " (仓库ID: " + log.getWarehouseId() + ")");
                        }
                    }
                } catch (Exception e) {
                    // 尝试从sys_warehouse表中获取仓库名称
                    String warehouseName = getWarehouseNameFromSysWarehouse(log.getWarehouseId());
                    if (warehouseName != null) {
                        log.setWarehouseName(warehouseName);
                        System.out.println("通过sys_warehouse表填充仓库名称: " + warehouseName + " (仓库ID: " + log.getWarehouseId() + ")");
                    } else {
                        String defaultName = "仓库-" + log.getWarehouseId();
                        log.setWarehouseName(defaultName);
                        System.err.println("填充仓库信息失败，使用默认名称: " + defaultName + ", 错误: " + e.getMessage());
                    }
                }
            }
            
            // 填充物品信息
            if (log.getProductId() != null) {
                // 优先使用ProductInfoService
                if (productInfoService != null) {
                    try {
                        ProductInfo productInfo = productInfoService.selectProductInfoByProductId(log.getProductId());
                        if (productInfo != null) {
                            log.setProductName(productInfo.getProductName());
                            log.setProductCode(productInfo.getProductCode());
                            log.setProductSpec(productInfo.getSpecName());
                            log.setProductUnit(productInfo.getUnitName());
                        } else {
                            fillProductInfoFromMapper(log);
                        }
                    } catch (Exception e) {
                        // 如果ProductInfoService调用失败，使用Mapper直接查询
                        fillProductInfoFromMapper(log);
                    }
                } else {
                    // 如果ProductInfoService不可用，使用Mapper直接查询
                    fillProductInfoFromMapper(log);
                }
            }
            
            // 填充关联单据信息
            fillRelatedOrderInfo(log);
            
        } catch (Exception e) {
            // 如果填充信息失败，设置默认值，不影响主要的日志记录功能
            System.err.println("填充仓库和物品信息失败: " + e.getMessage());
            setDefaultWarehouseAndProductInfo(log);
        }
    }
    
    /**
     * 通过Mapper直接查询填充物品信息
     * 
     * @param log 出入库日志对象
     */
    private void fillProductInfoFromMapper(WmsInventoryLog log) {
        try {
            Map<String, Object> productInfo = wmsInventoryLogMapper.getProductInfo(log.getProductId());
            if (productInfo != null) {
                log.setProductName((String) productInfo.get("product_name"));
                log.setProductCode((String) productInfo.get("product_code"));
                log.setProductSpec((String) productInfo.get("product_spec"));
                log.setProductUnit((String) productInfo.get("product_unit"));
            } else {
                setDefaultProductInfo(log);
            }
        } catch (Exception e) {
            setDefaultProductInfo(log);
        }
    }
    
    /**
     * 设置默认的物品信息
     * 
     * @param log 出入库日志对象
     */
    private void setDefaultProductInfo(WmsInventoryLog log) {
        log.setProductName("物品-" + log.getProductId());
        log.setProductCode("CODE-" + log.getProductId());
        log.setProductSpec("标准规格");
        log.setProductUnit("个");
    }
    
    /**
     * 填充关联单据信息
     * 
     * @param log 出入库日志对象
     */
    private void fillRelatedOrderInfo(WmsInventoryLog log) {
        try {
            // 如果已经有关联单据信息，则不需要生成
            if (log.getRelatedOrderId() != null && !log.getRelatedOrderId().isEmpty()) {
                // 如果有关联单据ID但没有类型，则根据操作类型推断
                if (log.getRelatedOrderType() == null || log.getRelatedOrderType().isEmpty()) {
                    log.setRelatedOrderType(getOrderTypeByOperationType(log.getOperationType()));
                }
                return;
            }
            
            // 生成关联单据信息
            String operationType = log.getOperationType();
            Date operationTime = log.getOperationTime() != null ? log.getOperationTime() : new Date();
            
            // 生成单据号格式：操作类型前缀 + 日期 + 时间戳后4位
            String dateStr = DateUtils.dateTime(operationTime).substring(0, 8); // YYYYMMDD
            String serialNo = String.format("%04d", (int)(System.currentTimeMillis() % 10000));
            
            String orderPrefix = getOrderPrefixByOperationType(operationType);
            String orderId = orderPrefix + dateStr + serialNo;
            
            log.setRelatedOrderId(orderId);
            log.setRelatedOrderType(getOrderTypeByOperationType(operationType));
            
        } catch (Exception e) {
            System.err.println("填充关联单据信息失败: " + e.getMessage());
            // 设置默认的关联单据信息
            setDefaultRelatedOrderInfo(log);
        }
    }
    
    /**
     * 根据操作类型获取单据前缀
     * 
     * @param operationType 操作类型
     * @return 单据前缀
     */
    private String getOrderPrefixByOperationType(String operationType) {
        if (operationType == null) return "OP";
        
        switch (operationType.toUpperCase()) {
            case "IN":
            case "1":
                return "IN";
            case "OUT":
            case "2":
                return "OUT";
            case "TRANSFER":
            case "3":
                return "TF";
            case "ADJUST":
            case "4":
                return "ADJ";
            case "CHECK":
            case "5":
                return "CHK";
            default:
                return "OP";
        }
    }
    
    /**
     * 根据操作类型获取单据类型
     * 
     * @param operationType 操作类型
     * @return 单据类型
     */
    private String getOrderTypeByOperationType(String operationType) {
        if (operationType == null) return "OTHER";
        
        switch (operationType.toUpperCase()) {
            case "IN":
            case "1":
                return "PURCHASE";
            case "OUT":
            case "2":
                return "SALES";
            case "TRANSFER":
            case "3":
                return "TRANSFER";
            case "ADJUST":
            case "4":
                return "ADJUST";
            case "CHECK":
            case "5":
                return "CHECK";
            default:
                return "OTHER";
        }
    }
    
    /**
     * 设置默认的关联单据信息
     * 
     * @param log 出入库日志对象
     */
    private void setDefaultRelatedOrderInfo(WmsInventoryLog log) {
        if (log.getRelatedOrderId() == null || log.getRelatedOrderId().isEmpty()) {
            String prefix = getOrderPrefixByOperationType(log.getOperationType());
            String timestamp = String.valueOf(System.currentTimeMillis()).substring(6); // 取后7位
            log.setRelatedOrderId(prefix + timestamp);
        }
        
        if (log.getRelatedOrderType() == null || log.getRelatedOrderType().isEmpty()) {
            log.setRelatedOrderType(getOrderTypeByOperationType(log.getOperationType()));
        }
    }
    
    /**
     * 设置默认的仓库和物品信息
     * 
     * @param log 出入库日志对象
     */
    private void setDefaultWarehouseAndProductInfo(WmsInventoryLog log) {
        if (log.getWarehouseId() != null && (log.getWarehouseName() == null || log.getWarehouseName().isEmpty())) {
            log.setWarehouseName("仓库-" + log.getWarehouseId());
        }
        
        if (log.getProductId() != null && (log.getProductName() == null || log.getProductName().isEmpty())) {
            setDefaultProductInfo(log);
        }
        
        // 确保关联单据信息也有默认值
        setDefaultRelatedOrderInfo(log);
    }

    /**
     * 转换用户名为真实姓名
     *
     * @param log 库存日志
     */
    private void convertUserNames(WmsInventoryLog log) {
        if (log == null) {
            return;
        }

        // 转换操作人员
        if (log.getOperator() != null && !log.getOperator().isEmpty()) {
            log.setOperator(getRealName(log.getOperator()));
        }

        // 转换创建人
        if (log.getCreateBy() != null && !log.getCreateBy().isEmpty()) {
            log.setCreateBy(getRealName(log.getCreateBy()));
        }

        // 转换更新人
        if (log.getUpdateBy() != null && !log.getUpdateBy().isEmpty()) {
            log.setUpdateBy(getRealName(log.getUpdateBy()));
        }
    }

    /**
     * 根据用户名获取真实姓名
     */
    private String getRealName(String username) {
        if (username == null || username.isEmpty()) {
            return username;
        }
        try {
            com.wanyu.common.core.domain.entity.SysUser user = userService.selectUserByUserName(username);
            if (user != null) {
                // 优先使用真实姓名，其次使用昵称
                if (user.getRealName() != null && !user.getRealName().isEmpty()) {
                    System.err.println("✅ 用户名转换成功: " + username + " -> " + user.getRealName());
                    return user.getRealName();
                } else if (user.getNickName() != null && !user.getNickName().isEmpty()) {
                    System.err.println("✅ 用户名转换成功: " + username + " -> " + user.getNickName());
                    return user.getNickName();
                }
            }
        } catch (Exception e) {
            System.err.println("❌ 查询用户失败: " + username + ", 错误: " + e.getMessage());
        }
        return username;
    }
    
    /**
     * 从sys_warehouse表获取仓库名称
     * 
     * @param warehouseId 仓库ID
     * @return 仓库名称
     */
    private String getWarehouseNameFromSysWarehouse(Long warehouseId) {
        if (warehouseId == null) {
            return null;
        }
        
        try {
            Map<String, Object> warehouseInfo = wmsInventoryLogMapper.getWarehouseInfo(warehouseId);
            if (warehouseInfo != null && warehouseInfo.get("warehouse_name") != null) {
                return (String) warehouseInfo.get("warehouse_name");
            }
        } catch (Exception e) {
            System.err.println("❌ 从sys_warehouse表查询仓库名称失败: 仓库ID=" + warehouseId + ", 错误: " + e.getMessage());
        }
        
        return null;
    }
}