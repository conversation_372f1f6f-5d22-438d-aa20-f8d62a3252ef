-- 修复物品相关表名规范化脚本
-- 将 wms_product_category -> wms_category
-- 将 wms_product_specification -> wms_specification  
-- 将 wms_product_unit -> wms_unit
-- 执行日期: 2025-08-31
-- 说明: 统一使用简化的表名规范

-- 1. 创建/验证 wms_category 表
CREATE TABLE IF NOT EXISTS `wms_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `category_name` varchar(30) NOT NULL COMMENT '分类名称',
  `category_code` varchar(30) DEFAULT NULL COMMENT '分类编码',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_code` (`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品分类表';

-- 2. 创建/验证 wms_specification 表
CREATE TABLE IF NOT EXISTS `wms_specification` (
  `spec_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `spec_name` varchar(50) NOT NULL COMMENT '规格名称',
  `spec_code` varchar(30) DEFAULT NULL COMMENT '规格编码',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`spec_id`),
  UNIQUE KEY `uk_spec_code` (`spec_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品规格表';

-- 3. 创建/验证 wms_unit 表
CREATE TABLE IF NOT EXISTS `wms_unit` (
  `unit_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '单位ID',
  `unit_name` varchar(50) NOT NULL COMMENT '单位名称',
  `unit_code` varchar(30) DEFAULT NULL COMMENT '单位编码',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`unit_id`),
  UNIQUE KEY `uk_unit_code` (`unit_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品单位表';

-- 4. 数据迁移存储过程
DELIMITER $$

CREATE PROCEDURE MigrateWmsTableData()
BEGIN
    DECLARE category_table_exists INT DEFAULT 0;
    DECLARE spec_table_exists INT DEFAULT 0;
    DECLARE unit_table_exists INT DEFAULT 0;
    
    -- 检查旧表是否存在并迁移数据
    
    -- 检查 wms_product_category 表
    SELECT COUNT(*) INTO category_table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product_category';
    
    IF category_table_exists > 0 THEN
        INSERT IGNORE INTO wms_category (
            category_id, parent_id, ancestors, category_name, category_code, 
            order_num, status, del_flag, create_by, create_time, update_by, update_time, remark
        )
        SELECT 
            category_id, parent_id, ancestors, category_name, category_code,
            order_num, status, del_flag, create_by, create_time, update_by, update_time, remark
        FROM wms_product_category;
        
        SELECT CONCAT('已迁移 ', ROW_COUNT(), ' 条记录从 wms_product_category 到 wms_category') as migration_result;
    END IF;
    
    -- 检查 wms_product_specification 表
    SELECT COUNT(*) INTO spec_table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product_specification';
    
    IF spec_table_exists > 0 THEN
        INSERT IGNORE INTO wms_specification (
            spec_id, spec_name, spec_code, status, remark, 
            create_by, create_time, update_by, update_time
        )
        SELECT 
            spec_id, spec_name, spec_code, status, remark,
            create_by, create_time, update_by, update_time
        FROM wms_product_specification;
        
        SELECT CONCAT('已迁移 ', ROW_COUNT(), ' 条记录从 wms_product_specification 到 wms_specification') as migration_result;
    END IF;
    
    -- 检查 wms_product_unit 表
    SELECT COUNT(*) INTO unit_table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product_unit';
    
    IF unit_table_exists > 0 THEN
        INSERT IGNORE INTO wms_unit (
            unit_id, unit_name, unit_code, status, remark,
            create_by, create_time, update_by, update_time
        )
        SELECT 
            unit_id, unit_name, unit_code, status, remark,
            create_by, create_time, update_by, update_time
        FROM wms_product_unit;
        
        SELECT CONCAT('已迁移 ', ROW_COUNT(), ' 条记录从 wms_product_unit 到 wms_unit') as migration_result;
    END IF;
    
    -- 如果没有旧表，输出提示信息
    IF category_table_exists = 0 AND spec_table_exists = 0 AND unit_table_exists = 0 THEN
        SELECT '未找到需要迁移的旧表，所有表已使用新的命名规范' as migration_result;
    END IF;
    
END$$

DELIMITER ;

-- 执行数据迁移
CALL MigrateWmsTableData();

-- 删除临时存储过程
DROP PROCEDURE IF EXISTS MigrateWmsTableData;

-- 5. 插入默认数据（如果表为空）

-- 插入默认物品分类
INSERT IGNORE INTO `wms_category` (`category_id`, `parent_id`, `ancestors`, `category_name`, `category_code`, `order_num`, `status`, `del_flag`, `create_by`, `create_time`) VALUES
(1, 0, '0', '电子产品', 'ELECTRONICS', 1, '0', '0', 'admin', NOW()),
(2, 0, '0', '办公用品', 'OFFICE', 2, '0', '0', 'admin', NOW()),
(3, 0, '0', '生活用品', 'DAILY', 3, '0', '0', 'admin', NOW()),
(4, 1, '0,1', '手机', 'PHONE', 1, '0', '0', 'admin', NOW()),
(5, 1, '0,1', '电脑', 'COMPUTER', 2, '0', '0', 'admin', NOW()),
(6, 2, '0,2', '文具', 'STATIONERY', 1, '0', '0', 'admin', NOW()),
(7, 2, '0,2', '打印用品', 'PRINTING', 2, '0', '0', 'admin', NOW());

-- 插入默认物品规格
INSERT IGNORE INTO `wms_specification` (`spec_name`, `spec_code`, `status`, `create_by`, `create_time`) VALUES
('标准规格', 'STANDARD', '0', 'admin', NOW()),
('小号', 'SMALL', '0', 'admin', NOW()),
('中号', 'MEDIUM', '0', 'admin', NOW()),
('大号', 'LARGE', '0', 'admin', NOW()),
('特大号', 'XLARGE', '0', 'admin', NOW()),
('迷你', 'MINI', '0', 'admin', NOW()),
('加长', 'EXTENDED', '0', 'admin', NOW()),
('加宽', 'WIDE', '0', 'admin', NOW());

-- 插入默认物品单位
INSERT IGNORE INTO `wms_unit` (`unit_name`, `unit_code`, `status`, `create_by`, `create_time`) VALUES
('个', 'PCS', '0', 'admin', NOW()),
('台', 'SET', '0', 'admin', NOW()),
('箱', 'BOX', '0', 'admin', NOW()),
('包', 'PACK', '0', 'admin', NOW'),
('袋', 'BAG', '0', 'admin', NOW()),
('瓶', 'BOTTLE', '0', 'admin', NOW()),
('支', 'PIECE', '0', 'admin', NOW()),
('张', 'SHEET', '0', 'admin', NOW()),
('本', 'BOOK', '0', 'admin', NOW()),
('套', 'SUITE', '0', 'admin', NOW()),
('米', 'METER', '0', 'admin', NOW()),
('千克', 'KG', '0', 'admin', NOW()),
('升', 'LITER', '0', 'admin', NOW()),
('吨', 'TON', '0', 'admin', NOW());

-- 6. 更新 wms_product 表中的外键引用（如果存在）
-- 这里假设 wms_product 表可能引用了这些表的ID

-- 检查并更新 wms_product 表的分类ID引用
UPDATE wms_product p 
INNER JOIN wms_category c ON p.category_name = c.category_name 
SET p.category_id = c.category_id 
WHERE p.category_id IS NULL OR p.category_id = 0;

-- 检查并更新 wms_product 表的规格ID引用  
UPDATE wms_product p 
INNER JOIN wms_specification s ON p.spec_name = s.spec_name 
SET p.spec_id = s.spec_id 
WHERE p.spec_id IS NULL OR p.spec_id = 0;

-- 检查并更新 wms_product 表的单位ID引用
UPDATE wms_product p 
INNER JOIN wms_unit u ON p.unit_name = u.unit_name 
SET p.unit_id = u.unit_id 
WHERE p.unit_id IS NULL OR p.unit_id = 0;

-- 7. 验证表结构和数据
SELECT '=== 表结构验证 ===' as verification;

SELECT 'wms_category表结构:' as verification;
DESCRIBE wms_category;

SELECT 'wms_specification表结构:' as verification;
DESCRIBE wms_specification;

SELECT 'wms_unit表结构:' as verification;
DESCRIBE wms_unit;

SELECT '=== 数据统计 ===' as verification;

SELECT 'wms_category数据统计:' as verification;
SELECT COUNT(*) as category_count FROM wms_category WHERE del_flag = '0';

SELECT 'wms_specification数据统计:' as verification;
SELECT COUNT(*) as spec_count FROM wms_specification;

SELECT 'wms_unit数据统计:' as verification;
SELECT COUNT(*) as unit_count FROM wms_unit;

-- 8. 显示示例数据
SELECT '=== 示例数据 ===' as verification;

SELECT '物品分类示例:' as verification;
SELECT category_id, category_name, category_code, parent_id FROM wms_category WHERE del_flag = '0' LIMIT 5;

SELECT '物品规格示例:' as verification;
SELECT spec_id, spec_name, spec_code FROM wms_specification LIMIT 5;

SELECT '物品单位示例:' as verification;
SELECT unit_id, unit_name, unit_code FROM wms_unit LIMIT 5;

-- 9. 输出完成信息
SELECT '=== 物品相关表名修复完成 ===' as completion_message;
SELECT '1. wms_category表已创建/验证' as step1;
SELECT '2. wms_specification表已创建/验证' as step2;
SELECT '3. wms_unit表已创建/验证' as step3;
SELECT '4. 数据迁移已完成（如果存在旧表）' as step4;
SELECT '5. 默认数据已插入' as step5;
SELECT '6. 外键引用已更新' as step6;
SELECT '表名规范化完成，请重新编译并启动后端服务' as next_step;