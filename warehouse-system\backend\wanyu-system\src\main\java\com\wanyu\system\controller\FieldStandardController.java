package com.wanyu.system.controller;

import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.FieldStandardValidator;
import com.wanyu.system.task.FieldStandardMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 字段标准管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@RestController
@RequestMapping("/system/fieldStandard")
public class FieldStandardController extends BaseController {
    
    @Autowired
    private FieldStandardMonitor fieldStandardMonitor;
    
    /**
     * 检查指定表的字段标准合规性
     */
    @PreAuthorize("@ss.hasPermi('system:fieldStandard:check')")
    @GetMapping("/check/{tableName}")
    public AjaxResult checkTableStandard(@PathVariable String tableName) {
        try {
            FieldStandardValidator.TableStandardResult result = FieldStandardValidator.checkTableStandard(tableName);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("检查表字段标准失败: {}", e.getMessage(), e);
            return AjaxResult.error("检查表字段标准失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成字段标准合规性报告
     */
    @PreAuthorize("@ss.hasPermi('system:fieldStandard:report')")
    @PostMapping("/report")
    public AjaxResult generateComplianceReport(@RequestBody List<String> tableNames) {
        try {
            if (tableNames == null || tableNames.isEmpty()) {
                // 使用默认监控表列表
                tableNames = Arrays.asList(
                    "sys_license", "sys_license_feature", "wms_operation_log",
                    "sys_user", "sys_role", "sys_menu", "sys_dict_data",
                    "wms_inventory", "wms_inventory_in", "wms_inventory_out"
                );
            }
            
            FieldStandardValidator.ComplianceReport report = FieldStandardValidator.generateComplianceReport(tableNames);
            return AjaxResult.success(report);
        } catch (Exception e) {
            logger.error("生成合规性报告失败: {}", e.getMessage(), e);
            return AjaxResult.error("生成合规性报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发字段标准检查
     */
    @PreAuthorize("@ss.hasPermi('system:fieldStandard:check')")
    @Log(title = "字段标准检查", businessType = BusinessType.OTHER)
    @PostMapping("/manualCheck")
    public AjaxResult manualCheck() {
        try {
            FieldStandardValidator.ComplianceReport report = fieldStandardMonitor.manualCheckStandards();
            return AjaxResult.success("字段标准检查完成", report);
        } catch (Exception e) {
            logger.error("手动字段标准检查失败: {}", e.getMessage(), e);
            return AjaxResult.error("手动字段标准检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取监控统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:fieldStandard:view')")
    @GetMapping("/stats")
    public AjaxResult getMonitoringStats() {
        try {
            FieldStandardMonitor.MonitoringStats stats = fieldStandardMonitor.getMonitoringStats();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取监控统计信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取监控统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证字段值是否符合标准
     */
    @PreAuthorize("@ss.hasPermi('system:fieldStandard:validate')")
    @PostMapping("/validate")
    public AjaxResult validateFieldValue(@RequestParam String fieldName, @RequestParam String fieldValue) {
        try {
            FieldStandardValidator.ValidationResult result = FieldStandardValidator.validateFieldValue(fieldName, fieldValue);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("验证字段值失败: {}", e.getMessage(), e);
            return AjaxResult.error("验证字段值失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取字段定义标准
     */
    @PreAuthorize("@ss.hasPermi('system:fieldStandard:view')")
    @GetMapping("/definition/{fieldType}")
    public AjaxResult getFieldDefinition(@PathVariable String fieldType) {
        try {
            FieldStandardValidator.FieldType type = FieldStandardValidator.FieldType.valueOf(fieldType.toUpperCase());
            
            FieldDefinitionInfo info = new FieldDefinitionInfo();
            info.setFieldType(type);
            info.setDescription(type.getDescription());
            
            switch (type) {
                case STATUS:
                    info.setStandardDefinition("status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）'");
                    info.setValidValues(Arrays.asList("0", "1"));
                    info.setDefaultValue("0");
                    break;
                case OPERATION_STATUS:
                    info.setStandardDefinition("operation_status CHAR(1) DEFAULT '0' COMMENT '操作状态（0成功 1失败）'");
                    info.setValidValues(Arrays.asList("0", "1"));
                    info.setDefaultValue("0");
                    break;
                case BOOLEAN:
                    info.setStandardDefinition("is_xxx CHAR(1) DEFAULT '0' COMMENT '是否xxx（0否 1是）'");
                    info.setValidValues(Arrays.asList("0", "1"));
                    info.setDefaultValue("0");
                    break;
                case DELETE_FLAG:
                    info.setStandardDefinition("del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）'");
                    info.setValidValues(Arrays.asList("0", "2"));
                    info.setDefaultValue("0");
                    break;
                case FLAG:
                    info.setStandardDefinition("xxx_flag CHAR(1) DEFAULT '0' COMMENT 'xxx标记（0关闭 1开启）'");
                    info.setValidValues(Arrays.asList("0", "1"));
                    info.setDefaultValue("0");
                    break;
                default:
                    info.setStandardDefinition("未定义标准");
                    info.setValidValues(Arrays.asList());
                    info.setDefaultValue(null);
            }
            
            return AjaxResult.success(info);
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("不支持的字段类型: " + fieldType);
        } catch (Exception e) {
            logger.error("获取字段定义失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取字段定义失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成字段标准检查SQL脚本
     */
    @PreAuthorize("@ss.hasPermi('system:fieldStandard:export')")
    @GetMapping("/generateScript")
    public AjaxResult generateCheckScript(@RequestParam(defaultValue = "warehouse_system") String databaseName) {
        try {
            String script = FieldStandardValidator.generateFieldStandardCheckScript(databaseName);
            return AjaxResult.success("SQL脚本生成成功", script);
        } catch (Exception e) {
            logger.error("生成检查脚本失败: {}", e.getMessage(), e);
            return AjaxResult.error("生成检查脚本失败: " + e.getMessage());
        }
    }
    
    /**
     * 字段定义信息类
     */
    public static class FieldDefinitionInfo {
        private FieldStandardValidator.FieldType fieldType;
        private String description;
        private String standardDefinition;
        private List<String> validValues;
        private String defaultValue;
        
        // Getters and Setters
        public FieldStandardValidator.FieldType getFieldType() { return fieldType; }
        public void setFieldType(FieldStandardValidator.FieldType fieldType) { this.fieldType = fieldType; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getStandardDefinition() { return standardDefinition; }
        public void setStandardDefinition(String standardDefinition) { this.standardDefinition = standardDefinition; }
        
        public List<String> getValidValues() { return validValues; }
        public void setValidValues(List<String> validValues) { this.validValues = validValues; }
        
        public String getDefaultValue() { return defaultValue; }
        public void setDefaultValue(String defaultValue) { this.defaultValue = defaultValue; }
    }
}