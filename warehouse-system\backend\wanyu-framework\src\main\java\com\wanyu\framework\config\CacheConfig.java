package com.wanyu.framework.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 缓存配置类 (使用内存缓存)
 * 
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 简单内存缓存管理器
     */
    @Bean("cacheManager")
    public CacheManager simpleCacheManager() {
        return new ConcurrentMapCacheManager();
    }
}