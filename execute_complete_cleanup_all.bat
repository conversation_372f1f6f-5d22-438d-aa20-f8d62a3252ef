@echo off
chcp 65001 >nul
echo ========================================
echo 执行完整的库位、货架、岗位组件清理
echo ========================================

echo.
echo 步骤1: 执行数据库清理...
mysql -h localhost -P 3306 -u root -p123456 warehouse_system < complete_cleanup_all_components.sql > cleanup_results.txt
echo 数据库清理结果已保存到 cleanup_results.txt

echo.
echo 步骤2: 执行文件删除...
call complete_cleanup_all_components.bat

echo.
echo 步骤3: 验证删除结果...
echo 正在验证数据库表是否已删除...
mysql -h localhost -P 3306 -u root -p123456 -e "USE warehouse_system; SHOW TABLES LIKE '%area%'; SHOW TABLES LIKE '%rack%'; SHOW TABLES LIKE '%location%'; SHOW TABLES LIKE '%post%';"

echo.
echo 步骤4: 验证菜单是否已删除...
mysql -h localhost -P 3306 -u root -p123456 -e "USE warehouse_system; SELECT COUNT(*) as remaining_menus FROM sys_menu WHERE menu_name LIKE '%库区%' OR menu_name LIKE '%货架%' OR menu_name LIKE '%库位%' OR menu_name LIKE '%岗位%' OR perms LIKE '%area%' OR perms LIKE '%rack%' OR perms LIKE '%location%' OR perms LIKE '%post%';"

echo.
echo 步骤5: 重新编译项目...
cd /d "C:\CKGLXT\warehouse-system\backend"
call mvn clean compile -Dmaven.test.skip=true
if %errorlevel% neq 0 (
    echo 后端编译失败，请检查代码
    pause
    exit /b 1
)

echo.
echo 步骤6: 重新构建前端...
cd /d "C:\CKGLXT\warehouse-system\frontend"
call npm run build:prod
if %errorlevel% neq 0 (
    echo 前端构建失败，请检查代码
    pause
    exit /b 1
)

echo.
echo ========================================
echo 完整清理完成！
echo ========================================
echo.
echo 已彻底删除的内容：
echo - 数据库表: 库区、货架、库位、岗位相关表
echo - 前端页面: 所有相关组件和API文件
echo - 后端代码: 所有相关的Java类和XML文件
echo - 路由配置: 已更新所有路由文件
echo - 菜单权限: 已删除所有相关菜单项
echo - 代码引用: 已清理所有相关引用
echo.
echo 系统现在只保留核心功能：
echo - 仓库信息管理
echo - 物品管理
echo - 库存管理
echo - 用户权限管理（不含岗位）
echo.
pause