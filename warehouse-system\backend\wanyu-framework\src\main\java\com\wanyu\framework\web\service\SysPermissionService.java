package com.wanyu.framework.web.service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.wanyu.common.constant.UserConstants;
import com.wanyu.common.core.domain.entity.SysRole;
import com.wanyu.common.core.domain.entity.SysUser;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.framework.security.service.PermissionCacheService;
import com.wanyu.system.service.ISysMenuService;
import com.wanyu.system.service.ISysPermissionService;
import com.wanyu.system.service.ISysRoleService;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Component
public class SysPermissionService
{
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysPermissionService permissionService;

    @Autowired
    private PermissionCacheService permissionCacheService;

    /**
     * 获取角色数据权限
     *
     * @param user 用户信息
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(SysUser user)
    {
        Set<String> roles = new HashSet<String>();
        // 管理员或超级管理员拥有所有权限
        if (user.isAdmin() || user.isSuperAdmin())
        {
            roles.add("admin");
            roles.add("super_admin");
        }
        else
        {
            roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     *
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(SysUser user)
    {
        Set<String> perms = new HashSet<String>();
        // 管理员或超级管理员拥有所有权限
        if (user.isAdmin() || user.isSuperAdmin())
        {
            perms.add("*:*:*");
        }
        else
        {
            // 尝试从缓存获取权限
            Long userId = user.getUserId();
            Set<String> cachedPerms = permissionCacheService.getUserPermissions(userId);

            if (cachedPerms != null && !cachedPerms.isEmpty())
            {
                // 使用缓存的权限
                perms.addAll(cachedPerms);
            }
            
            // 无论是否使用缓存，都要确保角色的权限属性被正确设置，以便数据权限匹配
            List<SysRole> userRoles = user.getRoles();
            if (!CollectionUtils.isEmpty(userRoles))
            {
                for (SysRole role : userRoles)
                {
                    if (StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL))
                    {
                        // 尝试从缓存获取角色权限
                        Set<String> cachedRolePerms = permissionCacheService.getRolePermissions(role.getRoleId());
                        if (cachedRolePerms != null && !cachedRolePerms.isEmpty())
                        {
                            role.setPermissions(cachedRolePerms);
                        }
                        else
                        {
                            // 从数据库获取角色权限并缓存
                            Set<String> rolePerms = menuService.selectMenuPermsByRoleId(role.getRoleId());
                            role.setPermissions(rolePerms);
                            permissionCacheService.setRolePermissions(role.getRoleId(), rolePerms);
                        }
                    }
                }
            }
            
            // 如果没有缓存权限，从数据库获取
            if (cachedPerms == null || cachedPerms.isEmpty())
            {
                // 首先获取用户直接分配的菜单权限
                Set<String> userDirectPerms = menuService.selectMenuPermsByUserIdDirect(userId);

                // 如果用户有直接分配的菜单权限，则使用用户直接分配的菜单权限
                if (!CollectionUtils.isEmpty(userDirectPerms))
                {
                    perms.addAll(userDirectPerms);
                }
                // 如果用户没有直接分配的菜单权限，则使用用户通过角色获得的菜单权限
                else
                {
                    if (!CollectionUtils.isEmpty(userRoles))
                    {
                        // 多角色设置permissions属性，以便数据权限匹配权限
                        for (SysRole role : userRoles)
                        {
                            if (StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL))
                            {
                                Set<String> rolePerms = menuService.selectMenuPermsByRoleId(role.getRoleId());
                                role.setPermissions(rolePerms);
                                perms.addAll(rolePerms);

                                // 缓存角色权限
                                permissionCacheService.setRolePermissions(role.getRoleId(), rolePerms);
                            }
                        }
                    }
                }

                // 添加用户直接分配的其他权限
                perms.addAll(permissionService.selectPermissionKeysByUserId(userId));

                // 缓存用户权限
                permissionCacheService.setUserPermissions(userId, perms);
            }
        }
        return perms;
    }
}
