package com.wanyu.system.service;

import java.util.List;
import com.wanyu.system.domain.SysDataLog;

/**
 * 数据日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface ISysDataLogService 
{
    /**
     * 查询数据日志
     * 
     * @param logId 数据日志主键
     * @return 数据日志
     */
    public SysDataLog selectDataLogById(Long logId);

    /**
     * 查询数据日志列表
     * 
     * @param dataLog 数据日志
     * @return 数据日志集合
     */
    public List<SysDataLog> selectDataLogList(SysDataLog dataLog);

    /**
     * 新增数据日志
     * 
     * @param dataLog 数据日志
     * @return 结果
     */
    public int insertDataLog(SysDataLog dataLog);

    /**
     * 修改数据日志
     * 
     * @param dataLog 数据日志
     * @return 结果
     */
    public int updateDataLog(SysDataLog dataLog);

    /**
     * 批量删除数据日志
     * 
     * @param logIds 需要删除的数据日志主键集合
     * @return 结果
     */
    public int deleteDataLogByIds(Long[] logIds);

    /**
     * 删除数据日志信息
     * 
     * @param logId 数据日志主键
     * @return 结果
     */
    public int deleteDataLogById(Long logId);

    /**
     * 清空数据日志
     */
    public void cleanDataLog();
}