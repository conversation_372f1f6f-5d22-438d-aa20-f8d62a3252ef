package com.wanyu.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 权限日志对象 sys_permission_log
 * 
 * <AUTHOR>
 */
public class SysPermissionLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String userName;

    /** 权限类型（1菜单 2按钮 3API 4数据） */
    @Excel(name = "权限类型", readConverterExp = "1=菜单,2=按钮,3=API,4=数据")
    private String permissionType;

    /** 权限标识 */
    @Excel(name = "权限标识")
    private String permission;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String method;

    /** 请求URL */
    @Excel(name = "请求URL")
    private String url;

    /** 请求IP */
    @Excel(name = "请求IP")
    private String ip;

    /** 验证结果（0成功 1失败） */
    @Excel(name = "验证结果", readConverterExp = "0=成功,1=失败")
    private String result;

    /** 失败原因 */
    @Excel(name = "失败原因")
    private String failReason;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 操作类型 */
    @Excel(name = "操作类型")
    private String operationType;

    /** 目标类型 */
    @Excel(name = "目标类型")
    private String targetType;

    /** 目标ID */
    @Excel(name = "目标ID")
    private Long targetId;

    /** 旧值 */
    @Excel(name = "旧值")
    private String oldValue;

    /** 新值 */
    @Excel(name = "新值")
    private String newValue;

    /** 变更差异 */
    @Excel(name = "变更差异")
    private String changeDiff;

    public void setLogId(Long logId)
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setPermissionType(String permissionType) 
    {
        this.permissionType = permissionType;
    }

    public String getPermissionType() 
    {
        return permissionType;
    }
    public void setPermission(String permission) 
    {
        this.permission = permission;
    }

    public String getPermission() 
    {
        return permission;
    }
    public void setMethod(String method) 
    {
        this.method = method;
    }

    public String getMethod() 
    {
        return method;
    }
    public void setUrl(String url) 
    {
        this.url = url;
    }

    public String getUrl() 
    {
        return url;
    }
    public void setIp(String ip) 
    {
        this.ip = ip;
    }

    public String getIp() 
    {
        return ip;
    }
    public void setResult(String result) 
    {
        this.result = result;
    }

    public String getResult() 
    {
        return result;
    }
    public void setFailReason(String failReason) 
    {
        this.failReason = failReason;
    }

    public String getFailReason() 
    {
        return failReason;
    }
    public void setOperTime(Date operTime) 
    {
        this.operTime = operTime;
    }

    public Date getOperTime() 
    {
        return operTime;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setOldValue(String oldValue) {
        this.oldValue = oldValue;
    }

    public String getOldValue() {
        return oldValue;
    }

    public void setNewValue(String newValue) {
        this.newValue = newValue;
    }

    public String getNewValue() {
        return newValue;
    }

    public void setChangeDiff(String changeDiff) {
        this.changeDiff = changeDiff;
    }

    public String getChangeDiff() {
        return changeDiff;
    }

    public void setOperatorId(Long operatorId) {
        this.userId = operatorId;
    }

    public Long getOperatorId() {
        return userId;
    }

    public void setOperatorName(String operatorName) {
        this.userName = operatorName;
    }

    public String getOperatorName() {
        return userName;
    }

    public void setRequestMethod(String method) {
        this.method = method;
    }

    public String getRequestMethod() {
        return method;
    }

    public void setRequestUrl(String url) {
        this.url = url;
    }

    public String getRequestUrl() {
        return url;
    }

    public void setStatus(String status) {
        this.result = status;
    }

    public String getStatus() {
        return result;
    }

    @Override
    public String toString() {
        return "SysPermissionLog [logId=" + logId + ", userId=" + userId + ", userName=" + userName
                + ", permissionType=" + permissionType + ", permission=" + permission + ", method=" + method + ", url="
                + url + ", ip=" + ip + ", result=" + result + ", failReason=" + failReason + ", operTime=" + operTime
                + ", operationType=" + operationType + ", targetType=" + targetType + ", targetId=" + targetId
                + ", oldValue=" + oldValue + ", newValue=" + newValue + ", changeDiff=" + changeDiff + "]";
    }
}
