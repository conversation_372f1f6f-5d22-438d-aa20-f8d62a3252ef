-- MySQL dump 10.13  Distrib 8.1.0, for Win64 (x86_64)
--
-- Host: localhost    Database: warehouse_system
-- ------------------------------------------------------
-- Server version	8.1.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `qrtz_blob_triggers`
--

DROP TABLE IF EXISTS `qrtz_blob_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_blob_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_name鐨勫?閿',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_group鐨勫?閿',
  `blob_data` blob COMMENT '存储序列化Trigger对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='Blob类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_blob_triggers`
--

LOCK TABLES `qrtz_blob_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_blob_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_blob_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_calendars`
--

DROP TABLE IF EXISTS `qrtz_calendars`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_calendars` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鏃ュ巻鍚嶇О',
  `calendar` blob NOT NULL COMMENT '瀛樻斁鎸佷箙鍖朿alendar瀵硅薄',
  PRIMARY KEY (`sched_name`,`calendar_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='日历信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_calendars`
--

LOCK TABLES `qrtz_calendars` WRITE;
/*!40000 ALTER TABLE `qrtz_calendars` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_calendars` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_cron_triggers`
--

DROP TABLE IF EXISTS `qrtz_cron_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_cron_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_name鐨勫?閿',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_group鐨勫?閿',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'cron琛ㄨ揪寮',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鏃跺尯',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='Cron类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_cron_triggers`
--

LOCK TABLES `qrtz_cron_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_cron_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_cron_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_fired_triggers`
--

DROP TABLE IF EXISTS `qrtz_fired_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_fired_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '璋冨害鍣ㄥ疄渚媔d',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_name鐨勫?閿',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_group鐨勫?閿',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '璋冨害鍣ㄥ疄渚嬪悕',
  `fired_time` bigint NOT NULL COMMENT '瑙﹀彂鐨勬椂闂',
  `sched_time` bigint NOT NULL COMMENT '瀹氭椂鍣ㄥ埗瀹氱殑鏃堕棿',
  `priority` int NOT NULL COMMENT '浼樺厛绾',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鐘舵?',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浠诲姟鍚嶇О',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浠诲姟缁勫悕',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鏄?惁骞跺彂',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鏄?惁鎺ュ彈鎭㈠?鎵ц?',
  PRIMARY KEY (`sched_name`,`entry_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='已触发的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_fired_triggers`
--

LOCK TABLES `qrtz_fired_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_fired_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_fired_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_job_details`
--

DROP TABLE IF EXISTS `qrtz_job_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_job_details` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '浠诲姟鍚嶇О',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '浠诲姟缁勫悕',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鐩稿叧浠嬬粛',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鎵ц?浠诲姟绫诲悕绉',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鏄?惁鎸佷箙鍖',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鏄?惁骞跺彂',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鏄?惁鏇存柊鏁版嵁',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鏄?惁鎺ュ彈鎭㈠?鎵ц?',
  `job_data` blob COMMENT '瀛樻斁鎸佷箙鍖杍ob瀵硅薄',
  PRIMARY KEY (`sched_name`,`job_name`,`job_group`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='任务详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_job_details`
--

LOCK TABLES `qrtz_job_details` WRITE;
/*!40000 ALTER TABLE `qrtz_job_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_job_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_locks`
--

DROP TABLE IF EXISTS `qrtz_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_locks` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鎮茶?閿佸悕绉',
  PRIMARY KEY (`sched_name`,`lock_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='存储的悲观锁信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_locks`
--

LOCK TABLES `qrtz_locks` WRITE;
/*!40000 ALTER TABLE `qrtz_locks` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_paused_trigger_grps`
--

DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_paused_trigger_grps` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_group鐨勫?閿',
  PRIMARY KEY (`sched_name`,`trigger_group`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='暂停的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_paused_trigger_grps`
--

LOCK TABLES `qrtz_paused_trigger_grps` WRITE;
/*!40000 ALTER TABLE `qrtz_paused_trigger_grps` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_paused_trigger_grps` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_scheduler_state`
--

DROP TABLE IF EXISTS `qrtz_scheduler_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_scheduler_state` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '瀹炰緥鍚嶇О',
  `last_checkin_time` bigint NOT NULL COMMENT '涓婃?妫?煡鏃堕棿',
  `checkin_interval` bigint NOT NULL COMMENT '妫?煡闂撮殧鏃堕棿',
  PRIMARY KEY (`sched_name`,`instance_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='调度器状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_scheduler_state`
--

LOCK TABLES `qrtz_scheduler_state` WRITE;
/*!40000 ALTER TABLE `qrtz_scheduler_state` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_scheduler_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_simple_triggers`
--

DROP TABLE IF EXISTS `qrtz_simple_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simple_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_name鐨勫?閿',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_group鐨勫?閿',
  `repeat_count` bigint NOT NULL COMMENT '閲嶅?鐨勬?鏁扮粺璁',
  `repeat_interval` bigint NOT NULL COMMENT '閲嶅?鐨勯棿闅旀椂闂',
  `times_triggered` bigint NOT NULL COMMENT '宸茬粡瑙﹀彂鐨勬?鏁',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='简单触发器的信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_simple_triggers`
--

LOCK TABLES `qrtz_simple_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_simple_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_simple_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_simprop_triggers`
--

DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simprop_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_name鐨勫?閿',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers琛╰rigger_group鐨勫?閿',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String绫诲瀷鐨則rigger鐨勭?涓?釜鍙傛暟',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String绫诲瀷鐨則rigger鐨勭?浜屼釜鍙傛暟',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String绫诲瀷鐨則rigger鐨勭?涓変釜鍙傛暟',
  `int_prop_1` int DEFAULT NULL COMMENT 'int绫诲瀷鐨則rigger鐨勭?涓?釜鍙傛暟',
  `int_prop_2` int DEFAULT NULL COMMENT 'int绫诲瀷鐨則rigger鐨勭?浜屼釜鍙傛暟',
  `long_prop_1` bigint DEFAULT NULL COMMENT 'long绫诲瀷鐨則rigger鐨勭?涓?釜鍙傛暟',
  `long_prop_2` bigint DEFAULT NULL COMMENT 'long绫诲瀷鐨則rigger鐨勭?浜屼釜鍙傛暟',
  `dec_prop_1` decimal(13,4) DEFAULT NULL COMMENT 'decimal绫诲瀷鐨則rigger鐨勭?涓?釜鍙傛暟',
  `dec_prop_2` decimal(13,4) DEFAULT NULL COMMENT 'decimal绫诲瀷鐨則rigger鐨勭?浜屼釜鍙傛暟',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Boolean绫诲瀷鐨則rigger鐨勭?涓?釜鍙傛暟',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Boolean绫诲瀷鐨則rigger鐨勭?浜屼釜鍙傛暟',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='同步机制的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_simprop_triggers`
--

LOCK TABLES `qrtz_simprop_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_simprop_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_simprop_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_triggers`
--

DROP TABLE IF EXISTS `qrtz_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '璋冨害鍚嶇О',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '瑙﹀彂鍣ㄧ殑鍚嶅瓧',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '瑙﹀彂鍣ㄦ墍灞炵粍鐨勫悕瀛',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details琛╦ob_name鐨勫?閿',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details琛╦ob_group鐨勫?閿',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鐩稿叧浠嬬粛',
  `next_fire_time` bigint DEFAULT NULL COMMENT '涓婁竴娆¤Е鍙戞椂闂达紙姣??锛',
  `prev_fire_time` bigint DEFAULT NULL COMMENT '涓嬩竴娆¤Е鍙戞椂闂达紙榛樿?涓?1琛ㄧず涓嶈Е鍙戯級',
  `priority` int DEFAULT NULL COMMENT '浼樺厛绾',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '瑙﹀彂鍣ㄧ姸鎬',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '瑙﹀彂鍣ㄧ殑绫诲瀷',
  `start_time` bigint NOT NULL COMMENT '寮??鏃堕棿',
  `end_time` bigint DEFAULT NULL COMMENT '缁撴潫鏃堕棿',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鏃ョ▼琛ㄥ悕绉',
  `misfire_instr` smallint DEFAULT NULL COMMENT '琛ュ伩鎵ц?鐨勭瓥鐣',
  `job_data` blob COMMENT '瀛樻斁鎸佷箙鍖杍ob瀵硅薄',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`) USING BTREE,
  KEY `sched_name` (`sched_name`,`job_name`,`job_group`) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='触发器详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_triggers`
--

LOCK TABLES `qrtz_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_api_permission`
--

DROP TABLE IF EXISTS `sys_api_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_api_permission` (
  `api_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'API鏉冮檺ID',
  `api_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API璺?緞',
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '璇锋眰鏂规硶',
  `permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鏉冮檺鏍囪瘑',
  `mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'DEFAULT' COMMENT '楠岃瘉妯″紡锛圓ND-鎵?湁鏉冮檺閮借?鏈夛紝OR-鏈変竴涓?潈闄愬嵆鍙?紝DEFAULT-榛樿?锛',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '鐘舵?锛?姝ｅ父 1鍋滅敤锛',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鎻忚堪',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鍒涘缓鑰',
  `create_time` datetime DEFAULT NULL COMMENT '鍒涘缓鏃堕棿',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鏇存柊鑰',
  `update_time` datetime DEFAULT NULL COMMENT '鏇存柊鏃堕棿',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '澶囨敞',
  PRIMARY KEY (`api_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='API鏉冮檺琛';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_api_permission`
--

LOCK TABLES `sys_api_permission` WRITE;
/*!40000 ALTER TABLE `sys_api_permission` DISABLE KEYS */;
INSERT INTO `sys_api_permission` VALUES (1,'/api/v1/users','GET','system:user:list','DEFAULT','0','获取用户列表','admin','2025-07-13 23:38:21','',NULL,'用户列表API权限'),(2,'/api/v1/users/{id}','GET','system:user:query','DEFAULT','0','获取用户详情','admin','2025-07-13 23:38:21','',NULL,'用户详情API权限'),(3,'/api/v1/users','POST','system:user:add','DEFAULT','0','创建用户','admin','2025-07-13 23:38:21','',NULL,'创建用户API权限'),(4,'/api/v1/users/{id}','PUT','system:user:edit','DEFAULT','0','更新用户','admin','2025-07-13 23:38:21','',NULL,'更新用户API权限'),(5,'/api/v1/users/{id}','DELETE','system:user:remove','DEFAULT','0','删除用户','admin','2025-07-13 23:38:21','',NULL,'删除用户API权限'),(6,'/api/v1/roles','GET','system:role:list','DEFAULT','0','获取角色列表','admin','2025-07-13 23:38:21','',NULL,'角色列表API权限'),(7,'/api/v1/roles/{id}','GET','system:role:query','DEFAULT','0','获取角色详情','admin','2025-07-13 23:38:21','',NULL,'角色详情API权限'),(8,'/api/v1/roles','POST','system:role:add','DEFAULT','0','创建角色','admin','2025-07-13 23:38:21','',NULL,'创建角色API权限'),(9,'/api/v1/roles/{id}','PUT','system:role:edit','DEFAULT','0','更新角色','admin','2025-07-13 23:38:21','',NULL,'更新角色API权限'),(10,'/api/v1/roles/{id}','DELETE','system:role:remove','DEFAULT','0','删除角色','admin','2025-07-13 23:38:21','',NULL,'删除角色API权限');
/*!40000 ALTER TABLE `sys_api_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_config`
--

DROP TABLE IF EXISTS `sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_config` (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_config`
--

LOCK TABLES `sys_config` WRITE;
/*!40000 ALTER TABLE `sys_config` DISABLE KEYS */;
INSERT INTO `sys_config` VALUES (1,'Default Skin','sys.index.skinName','skin-blue','Y','admin','2025-05-15 18:05:51','',NULL,'skin-blue'),(2,'Default Password','sys.user.initPassword','admin123','Y','admin','2025-05-15 18:05:51','',NULL,'admin123'),(3,'Sidebar Theme','sys.index.sideTheme','theme-dark','Y','admin','2025-05-15 18:05:51','',NULL,'theme-dark'),(100,'是否开启注册功能','sys.account.registerUser','true','Y','',NULL,'',NULL,NULL);
/*!40000 ALTER TABLE `sys_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_data_log`
--

DROP TABLE IF EXISTS `sys_data_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_data_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `table_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '表名',
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作类型',
  `record_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '记录ID',
  `old_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '原始数据',
  `new_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '新数据',
  `changed_fields` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '变更字段',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作用户',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `client_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户端IP',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `idx_table_name` (`table_name`) USING BTREE,
  KEY `idx_operation_type` (`operation_type`) USING BTREE,
  KEY `idx_user_name` (`user_name`) USING BTREE,
  KEY `idx_operation_time` (`operation_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='数据变更日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_data_log`
--

LOCK TABLES `sys_data_log` WRITE;
/*!40000 ALTER TABLE `sys_data_log` DISABLE KEYS */;
INSERT INTO `sys_data_log` VALUES (1,'sys_user','UPDATE','1','{\"status\":\"0\"}','{\"status\":\"1\"}','status','admin','2025-07-26 16:16:55','192.168.1.1','system','2025-07-26 16:16:55',NULL,NULL,NULL),(2,'wms_inventory_in','INSERT','264',NULL,'{\"inCode\":\"IN20250726001\",\"status\":\"0\"}','inCode,status','superadmin','2025-07-26 16:16:55','192.168.1.1','system','2025-07-26 16:16:55',NULL,NULL,NULL),(3,'sys_role','DELETE','100','{\"roleName\":\"测试角色\"}',NULL,'roleName','admin','2025-07-26 16:16:55','192.168.1.1','system','2025-07-26 16:16:55',NULL,NULL,NULL);
/*!40000 ALTER TABLE `sys_data_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_data_permission`
--

DROP TABLE IF EXISTS `sys_data_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_data_permission` (
  `perm_id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `perm_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限名称',
  `perm_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限类型（1部门权限 2仓库权限）',
  `perm_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限范围（1全部数据 2自定义数据 3本部门/仓库数据 4本部门/仓库及以下数据 5仅本人数据）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`perm_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='数据权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_data_permission`
--

LOCK TABLES `sys_data_permission` WRITE;
/*!40000 ALTER TABLE `sys_data_permission` DISABLE KEYS */;
INSERT INTO `sys_data_permission` VALUES (1,'部门全部数据权限','1','1','0','admin','2025-07-13 23:42:57','',NULL,'部门全部数据权限'),(2,'本部门数据权限','1','3','0','admin','2025-07-13 23:42:57','',NULL,'仅本部门数据权限'),(3,'本部门及以下数据权限','1','4','0','admin','2025-07-13 23:42:57','',NULL,'本部门及以下数据权限'),(4,'仅本人数据权限','1','5','0','admin','2025-07-13 23:42:57','',NULL,'仅本人数据权限'),(5,'仓库全部数据权限','2','1','0','admin','2025-07-13 23:42:57','',NULL,'仓库全部数据权限'),(6,'本仓库数据权限','2','3','0','admin','2025-07-13 23:42:57','',NULL,'仅本仓库数据权限'),(7,'本仓库及以下数据权限','2','4','0','admin','2025-07-13 23:42:57','',NULL,'本仓库及以下数据权限');
/*!40000 ALTER TABLE `sys_data_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_data_permission_dept`
--

DROP TABLE IF EXISTS `sys_data_permission_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_data_permission_dept` (
  `perm_id` bigint NOT NULL COMMENT '权限ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`perm_id`,`dept_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='数据权限和部门关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_data_permission_dept`
--

LOCK TABLES `sys_data_permission_dept` WRITE;
/*!40000 ALTER TABLE `sys_data_permission_dept` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_data_permission_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_data_permission_warehouse`
--

DROP TABLE IF EXISTS `sys_data_permission_warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_data_permission_warehouse` (
  `perm_id` bigint NOT NULL COMMENT '权限ID',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  PRIMARY KEY (`perm_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='数据权限和仓库关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_data_permission_warehouse`
--

LOCK TABLES `sys_data_permission_warehouse` WRITE;
/*!40000 ALTER TABLE `sys_data_permission_warehouse` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_data_permission_warehouse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dept`
--

DROP TABLE IF EXISTS `sys_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '部门名称',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=232 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='部门表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dept`
--

LOCK TABLES `sys_dept` WRITE;
/*!40000 ALTER TABLE `sys_dept` DISABLE KEYS */;
INSERT INTO `sys_dept` VALUES (100,0,'0','万裕物业',0,'admin','15888888888','<EMAIL>','0','0','admin','2025-05-15 18:05:51','superadmin','2025-05-18 20:54:31'),(205,100,'0,100','万裕物业行政人事部',1,NULL,NULL,NULL,'0','0','admin','2025-05-19 17:39:24','superadmin','2025-06-29 22:06:40'),(206,100,'0,100','东方水岸',1,NULL,NULL,NULL,'0','0','admin','2025-06-11 14:44:02','superadmin','2025-06-25 19:04:39'),(207,100,'0,100','东方湖岸',1,NULL,NULL,NULL,'0','0','admin','2025-06-11 15:31:32','superadmin','2025-06-15 19:36:49'),(208,100,'0,100','东方韵',1,NULL,NULL,NULL,'0','0','admin','2025-06-11 17:19:37','superadmin','2025-06-15 17:39:13'),(209,100,'0,100','东方河畔',1,NULL,NULL,NULL,'0','0','admin','2025-06-13 23:13:30','superadmin','2025-07-24 20:27:56'),(210,100,'0,100','现代东方',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:10:21','superadmin','2025-07-24 20:27:48'),(211,206,'0,100,206','客服部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:10:43','',NULL),(212,206,'0,100,206','工程部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:10:55','',NULL),(213,206,'0,100,206','秩序部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:11:07','superadmin','2025-07-19 15:43:12'),(214,206,'0,100,206','保洁部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:11:19','',NULL),(215,207,'0,100,207','客服部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:11:42','',NULL),(216,207,'0,100,207','工程部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:11:53','',NULL),(217,207,'0,100,207','秩序部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:12:06','',NULL),(218,207,'0,100,207','保洁部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:12:17','',NULL),(219,208,'0,100,208','客服部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:14:04','',NULL),(220,208,'0,100,208','工程部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:14:14','',NULL),(221,208,'0,100,208','秩序部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:14:24','',NULL),(222,208,'0,100,208','保洁部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:14:37','',NULL),(223,209,'0,100,209','客服部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:14:51','',NULL),(224,209,'0,100,209','工程部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:15:01','',NULL),(225,209,'0,100,209','秩序部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:15:16','',NULL),(226,209,'0,100,209','保洁部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:15:27','',NULL),(227,210,'0,100,210','客服部',1,'',NULL,NULL,'0','0','admin','2025-06-16 09:15:38','superadmin','2025-07-24 20:28:26'),(228,210,'0,100,210','工程部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:15:47','',NULL),(229,210,'0,100,210','秩序部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:15:58','',NULL),(230,210,'0,100,210','保洁部',1,NULL,NULL,NULL,'0','0','admin','2025-06-16 09:16:07','',NULL);
/*!40000 ALTER TABLE `sys_dept` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `dept_delete_backup_trigger` BEFORE UPDATE ON `sys_dept` FOR EACH ROW BEGIN
IF NEW.del_flag = '2' AND OLD.del_flag = '0' THEN
INSERT IGNORE INTO sys_dept_backup SELECT * FROM sys_dept WHERE dept_id = OLD.dept_id;
END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `sys_dept_data_permission`
--

DROP TABLE IF EXISTS `sys_dept_data_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dept_data_permission` (
  `perm_id` bigint NOT NULL COMMENT '鏉冮檺ID',
  `dept_id` bigint NOT NULL COMMENT '閮ㄩ棬ID',
  PRIMARY KEY (`perm_id`,`dept_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='部门和数据权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dept_data_permission`
--

LOCK TABLES `sys_dept_data_permission` WRITE;
/*!40000 ALTER TABLE `sys_dept_data_permission` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_dept_data_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_data`
--

DROP TABLE IF EXISTS `sys_dict_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '瀛楀吀缂栫爜',
  `dict_sort` int DEFAULT '0' COMMENT '瀛楀吀鎺掑簭',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '瀛楀吀鏍囩?',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '瀛楀吀閿??',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '瀛楀吀绫诲瀷',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鏍峰紡灞炴?锛堝叾浠栨牱寮忔墿灞曪級',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '琛ㄦ牸鍥炴樉鏍峰紡',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'N' COMMENT '鏄?惁榛樿?锛圷鏄?N鍚︼級',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '鐘舵?锛?姝ｅ父 1鍋滅敤锛',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鍒涘缓鑰',
  `create_time` datetime DEFAULT NULL COMMENT '鍒涘缓鏃堕棿',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鏇存柊鑰',
  `update_time` datetime DEFAULT NULL COMMENT '鏇存柊鏃堕棿',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '澶囨敞',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2068 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='字典数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_data`
--

LOCK TABLES `sys_dict_data` WRITE;
/*!40000 ALTER TABLE `sys_dict_data` DISABLE KEYS */;
INSERT INTO `sys_dict_data` VALUES (100,1,'正常','0','sys_normal_disable','','primary','Y','0','admin','2025-05-16 15:46:42','',NULL,'正常状态'),(101,2,'停用','1','sys_normal_disable','','danger','N','0','admin','2025-05-16 15:46:42','',NULL,'停用状态'),(102,3,'权限拒绝','PERMISSION_DENIED','sys_security_event_type','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'权限拒绝'),(103,4,'账户锁定','ACCOUNT_LOCKED','sys_security_event_type','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'账户锁定'),(104,5,'可疑活动','SUSPICIOUS_ACTIVITY','sys_security_event_type','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'可疑活动'),(105,1,'低','LOW','sys_risk_level','','success','N','0','admin','2025-07-26 21:43:56','',NULL,'低风险'),(106,2,'中','MEDIUM','sys_risk_level','','warning','N','0','admin','2025-07-26 21:43:56','',NULL,'中风险'),(107,3,'高','HIGH','sys_risk_level','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'高风险'),(108,4,'严重','CRITICAL','sys_risk_level','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'严重风险'),(109,1,'未处理','0','sys_handle_status','','info','N','0','admin','2025-07-26 21:43:56','',NULL,'未处理'),(110,2,'已处理','1','sys_handle_status','','success','N','0','admin','2025-07-26 21:43:56','',NULL,'已处理'),(111,3,'已忽略','2','sys_handle_status','','warning','N','0','admin','2025-07-26 21:43:56','',NULL,'已忽略'),(112,1,'系统错误','SYSTEM','sys_error_type','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'系统错误'),(113,2,'业务错误','BUSINESS','sys_error_type','','warning','N','0','admin','2025-07-26 21:43:56','',NULL,'业务错误'),(114,3,'数据库错误','DATABASE','sys_error_type','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'数据库错误'),(115,4,'网络错误','NETWORK','sys_error_type','','warning','N','0','admin','2025-07-26 21:43:56','',NULL,'网络错误'),(116,5,'验证错误','VALIDATION','sys_error_type','','info','N','0','admin','2025-07-26 21:43:56','',NULL,'验证错误'),(117,1,'信息','INFO','sys_error_level','','info','N','0','admin','2025-07-26 21:43:56','',NULL,'信息级别'),(118,2,'警告','WARN','sys_error_level','','warning','N','0','admin','2025-07-26 21:43:56','',NULL,'警告级别'),(119,3,'错误','ERROR','sys_error_level','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'错误级别'),(120,4,'致命','FATAL','sys_error_level','','danger','N','0','admin','2025-07-26 21:43:56','',NULL,'致命级别'),(1001,1,'正常','0','inventory_status','','success','Y','0','admin','2025-05-17 11:24:51','',NULL,'正常'),(1003,3,'缺货','2','inventory_status','','danger','N','0','admin','2025-05-17 11:24:51','',NULL,'缺货'),(1011,1,'采购入库','1','inventory_in_type','','primary','Y','0','admin','2025-05-17 11:24:51','',NULL,'采购入库'),(1012,2,'生产入库','2','inventory_in_type','','success','N','0','admin','2025-05-17 11:24:51','',NULL,'生产入库'),(1013,3,'调拨入库','3','inventory_in_type','','info','N','0','admin','2025-05-17 11:24:51','',NULL,'调拨入库'),(1014,4,'其他入库','4','inventory_in_type','','warning','N','0','admin','2025-05-17 11:24:51','',NULL,'其他入库'),(1032,2,'生产出库','2','inventory_out_type','','success','N','0','admin','2025-05-17 11:24:51','',NULL,'生产出库'),(1033,3,'调拨出库','3','inventory_out_type','','info','N','0','admin','2025-05-17 11:24:51','',NULL,'调拨出库'),(1034,4,'其他出库','4','inventory_out_type','','warning','N','0','admin','2025-05-17 11:24:51','',NULL,'其他出库'),(1101,1,'入库','1','inventory_oper_type','','success','Y','0','admin','2025-05-17 11:47:38','',NULL,'入库'),(1102,2,'出库','2','inventory_oper_type','','danger','N','0','admin','2025-05-17 11:47:38','',NULL,'出库'),(1103,3,'调拨','3','inventory_oper_type','','warning','N','0','admin','2025-05-17 11:47:38','',NULL,'调拨'),(1104,4,'盘点','4','inventory_oper_type','','info','N','0','admin','2025-05-17 11:47:38','',NULL,'盘点'),(1111,1,'菜单','1','sys_perm_type','','primary','Y','0','admin','2025-05-17 11:47:38','',NULL,'菜单'),(1112,2,'按钮','2','sys_perm_type','','success','N','0','admin','2025-05-17 11:47:38','',NULL,'按钮'),(1113,3,'API','3','sys_perm_type','','info','N','0','admin','2025-05-17 11:47:38','',NULL,'API'),(1121,1,'登录日志','1','sys_log_type','','primary','Y','0','admin','2025-05-17 11:47:38','',NULL,'登录日志'),(1123,3,'系统异常','3','sys_log_type','','danger','N','0','admin','2025-05-17 11:47:38','',NULL,'系统异常'),(1124,4,'其他日志','4','sys_log_type','','info','N','0','admin','2025-05-17 11:47:38','',NULL,'其他日志'),(2001,1,'入库','1','inventory_oper_type','','success','N','0','admin','2025-06-17 12:35:40','',NULL,'入库操作'),(2002,2,'出库','2','inventory_oper_type','','warning','N','0','admin','2025-06-17 12:35:40','',NULL,'出库操作'),(2003,3,'调拨','3','inventory_oper_type','','info','N','0','admin','2025-06-17 12:35:40','',NULL,'调拨操作'),(2004,4,'盘点','4','inventory_oper_type','','primary','N','0','admin','2025-06-17 12:35:40','',NULL,'盘点操作'),(2005,5,'调整','5','inventory_oper_type','','danger','N','0','admin','2025-06-17 12:35:40','',NULL,'库存调整'),(2006,1,'男','0','sys_user_sex',NULL,NULL,'N','0','admin','2025-07-14 21:22:03','',NULL,NULL),(2007,2,'女','1','sys_user_sex',NULL,NULL,'N','0','admin','2025-07-14 21:22:03','',NULL,NULL),(2008,3,'未知','2','sys_user_sex',NULL,NULL,'N','0','admin','2025-07-14 21:22:03','',NULL,NULL),(2009,1,'通知','1','sys_notice_type',NULL,NULL,'N','0','admin','2025-07-14 21:47:46','',NULL,NULL),(2010,2,'公告','2','sys_notice_type',NULL,NULL,'N','0','admin','2025-07-14 21:47:46','',NULL,NULL),(2015,1,'采购单','PURCHASE','wms_order_type','','success','N','0','admin','2025-07-23 13:18:10','',NULL,'采购入库单'),(2016,2,'销售单','SALES','wms_order_type','','warning','N','0','admin','2025-07-23 13:18:10','',NULL,'销售出库单'),(2017,3,'调拨单','TRANSFER','wms_order_type','','info','N','0','admin','2025-07-23 13:18:10','',NULL,'调拨单'),(2018,4,'盘点单','CHECK','wms_order_type','','primary','N','0','admin','2025-07-23 13:18:10','',NULL,'盘点单'),(2019,5,'退货单','RETURN','wms_order_type','','danger','N','0','admin','2025-07-23 13:18:10','',NULL,'退货单'),(2020,6,'其他','OTHER','wms_order_type','','default','N','0','admin','2025-07-23 13:18:10','',NULL,'其他单据'),(2027,2,'预警','1','inventory_status','','warning','N','0','admin','2025-07-25 22:59:45','',NULL,'库存预警状态'),(2028,1,'未审核','0','inventory_in_status','','warning','Y','0','admin','2025-07-26 11:41:35','',NULL,'未审核状态'),(2029,2,'已审核','1','inventory_in_status','','success','N','0','admin','2025-07-26 11:41:35','',NULL,'已审核状态'),(2030,3,'已取消','2','inventory_in_status','','danger','N','0','admin','2025-07-26 11:41:35','',NULL,'已取消状态'),(2031,1,'未审核','0','inventory_out_status','','warning','Y','0','admin','2025-07-26 11:41:35','',NULL,'未审核状态'),(2032,2,'已审核','1','inventory_out_status','','success','N','0','admin','2025-07-26 11:41:35','',NULL,'已审核状态'),(2033,3,'已取消','2','inventory_out_status','','danger','N','0','admin','2025-07-26 11:41:35','',NULL,'已取消状态'),(2034,1,'未审核','0','inventory_transfer_status','','warning','Y','0','admin','2025-07-26 11:41:35','',NULL,'未审核状态'),(2035,2,'已审核','1','inventory_transfer_status','','success','N','0','admin','2025-07-26 11:41:35','',NULL,'已审核状态'),(2036,3,'已取消','2','inventory_transfer_status','','danger','N','0','admin','2025-07-26 11:41:35','',NULL,'已取消状态'),(2037,1,'未审核','0','inventory_check_status','','warning','Y','0','admin','2025-07-26 11:41:35','',NULL,'未审核状态'),(2038,2,'已审核','1','inventory_check_status','','success','N','0','admin','2025-07-26 11:41:35','',NULL,'已审核状态'),(2039,3,'已取消','2','inventory_check_status','','danger','N','0','admin','2025-07-26 11:41:35','',NULL,'已取消状态'),(2040,1,'其它','0','sys_oper_type','','info','Y','0','admin','2025-07-26 11:41:35','',NULL,'其它操作'),(2041,2,'新增','1','sys_oper_type','','primary','N','0','admin','2025-07-26 11:41:35','',NULL,'新增操作'),(2042,3,'修改','2','sys_oper_type','','success','N','0','admin','2025-07-26 11:41:35','',NULL,'修改操作'),(2043,4,'删除','3','sys_oper_type','','danger','N','0','admin','2025-07-26 11:41:35','',NULL,'删除操作'),(2044,5,'授权','4','sys_oper_type','','primary','N','0','admin','2025-07-26 11:41:35','',NULL,'授权操作'),(2045,6,'导出','5','sys_oper_type','','warning','N','0','admin','2025-07-26 11:41:35','',NULL,'导出操作'),(2046,7,'导入','6','sys_oper_type','','warning','N','0','admin','2025-07-26 11:41:35','',NULL,'导入操作'),(2047,8,'强退','7','sys_oper_type','','danger','N','0','admin','2025-07-26 11:41:35','',NULL,'强退操作'),(2048,9,'生成代码','8','sys_oper_type','','warning','N','0','admin','2025-07-26 11:41:35','',NULL,'生成代码'),(2049,10,'清空数据','9','sys_oper_type','','danger','N','0','admin','2025-07-26 11:41:35','',NULL,'清空数据'),(2050,1,'成功','0','sys_common_status','','primary','Y','0','admin','2025-07-26 11:41:35','',NULL,'正常状态'),(2051,2,'失败','1','sys_common_status','','danger','N','0','admin','2025-07-26 11:41:35','',NULL,'停用状态'),(2052,1,'登录失败','LOGIN_FAIL','sys_security_event_type','','danger','Y','0','admin','2025-07-26 16:16:55','',NULL,'登录失败事件'),(2053,2,'密码修改','PASSWORD_CHANGE','sys_security_event_type','','warning','N','0','admin','2025-07-26 16:16:55','',NULL,'密码修改事件'),(2054,3,'权限拒绝','PERMISSION_DENIED','sys_security_event_type','','danger','N','0','admin','2025-07-26 16:16:55','',NULL,'权限拒绝事件'),(2055,4,'账户锁定','ACCOUNT_LOCKED','sys_security_event_type','','danger','N','0','admin','2025-07-26 16:16:55','',NULL,'账户锁定事件'),(2056,5,'可疑活动','SUSPICIOUS_ACTIVITY','sys_security_event_type','','danger','N','0','admin','2025-07-26 16:16:55','',NULL,'可疑活动事件'),(2057,1,'低','LOW','sys_risk_level','','success','Y','0','admin','2025-07-26 16:16:55','',NULL,'低风险'),(2058,2,'中','MEDIUM','sys_risk_level','','warning','N','0','admin','2025-07-26 16:16:55','',NULL,'中风险'),(2059,3,'高','HIGH','sys_risk_level','','danger','N','0','admin','2025-07-26 16:16:55','',NULL,'高风险'),(2060,4,'严重','CRITICAL','sys_risk_level','','danger','N','0','admin','2025-07-26 16:16:55','',NULL,'严重风险'),(2061,1,'新增','INSERT','sys_data_operation_type','','primary','Y','0','admin','2025-07-26 16:16:55','',NULL,'新增数据'),(2062,2,'修改','UPDATE','sys_data_operation_type','','success','N','0','admin','2025-07-26 16:16:55','',NULL,'修改数据'),(2063,3,'删除','DELETE','sys_data_operation_type','','danger','N','0','admin','2025-07-26 16:16:55','',NULL,'删除数据'),(2064,4,'查询','SELECT','sys_data_operation_type','','info','N','0','admin','2025-07-26 16:16:55','',NULL,'查询数据'),(2065,1,'未处理','0','sys_handle_status','','warning','Y','0','admin','2025-07-26 16:16:55','',NULL,'未处理状态'),(2066,2,'已处理','1','sys_handle_status','','success','N','0','admin','2025-07-26 16:16:55','',NULL,'已处理状态'),(2067,3,'已忽略','2','sys_handle_status','','info','N','0','admin','2025-07-26 16:16:55','',NULL,'已忽略状态');
/*!40000 ALTER TABLE `sys_dict_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_type`
--

DROP TABLE IF EXISTS `sys_dict_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '瀛楀吀涓婚敭',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '瀛楀吀鍚嶇О',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '瀛楀吀绫诲瀷',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '鐘舵?锛?姝ｅ父 1鍋滅敤锛',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鍒涘缓鑰',
  `create_time` datetime DEFAULT NULL COMMENT '鍒涘缓鏃堕棿',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鏇存柊鑰',
  `update_time` datetime DEFAULT NULL COMMENT '鏇存柊鏃堕棿',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '澶囨敞',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE KEY `dict_type` (`dict_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=135 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='字典类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_type`
--

LOCK TABLES `sys_dict_type` WRITE;
/*!40000 ALTER TABLE `sys_dict_type` DISABLE KEYS */;
INSERT INTO `sys_dict_type` VALUES (100,'操作类型','sys_oper_type','0','admin','2025-07-25 11:54:56','admin','2025-07-26 11:41:35','出入库操作类型列表'),(101,'仓库操作类型','inventory_in_type','0','admin','2025-05-17 11:24:51','admin','2025-07-25 11:54:57','仓库管理系统操作类型'),(103,'出库类型','inventory_out_type','0','admin','2025-05-17 11:24:51','',NULL,'出库类型'),(104,'错误级别','sys_error_level','0','admin','2025-07-26 21:43:56','',NULL,'错误级别列表'),(111,'权限类型','sys_perm_type','0','admin','2025-05-17 11:47:38','',NULL,'权限类型'),(112,'系统日志类型','sys_log_type','0','admin','2025-05-17 11:47:38','',NULL,'系统日志类型'),(120,'出入库操作类型','inventory_oper_type','0','admin','2025-06-17 12:35:40','',NULL,'出入库操作类型列表'),(121,'库存操作类型','wms_operation_type','0','admin','2025-07-23 13:18:10','',NULL,'出入库操作类型字典'),(122,'单据类型','wms_order_type','0','admin','2025-07-23 13:18:10','',NULL,'关联单据类型字典'),(123,'出库状态','inventory_out_status','0','admin','2025-07-25 22:23:37','admin','2025-07-26 11:41:35','出库单状态字典'),(124,'入库状态','inventory_in_status','0','admin','2025-07-25 22:34:44','admin','2025-07-26 11:41:35','入库单状态字典'),(127,'调拨状态','inventory_transfer_status','0','admin','2025-07-26 11:41:35','',NULL,'调拨单状态列表'),(128,'盘点状态','inventory_check_status','0','admin','2025-07-26 11:41:35','',NULL,'盘点单状态列表'),(130,'系统状态','sys_common_status','0','admin','2025-07-26 11:41:35','',NULL,'登录状态列表'),(131,'安全事件类型','sys_security_event_type','0','admin','2025-07-26 16:16:55','',NULL,'安全事件类型列表'),(132,'风险级别','sys_risk_level','0','admin','2025-07-26 16:16:55','',NULL,'风险级别列表'),(133,'数据操作类型','sys_data_operation_type','0','admin','2025-07-26 16:16:55','',NULL,'数据操作类型列表'),(134,'处理状态','sys_handle_status','0','admin','2025-07-26 16:16:55','',NULL,'处理状态列表');
/*!40000 ALTER TABLE `sys_dict_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_error_log`
--

DROP TABLE IF EXISTS `sys_error_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_error_log` (
  `error_id` bigint NOT NULL AUTO_INCREMENT COMMENT '错误编号',
  `error_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '错误标题',
  `error_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '错误类型',
  `error_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '错误级别',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '错误消息',
  `error_stack` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '错误堆栈',
  `request_uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '请求URI',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '请求方法',
  `request_params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '请求参数',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '用户代理',
  `client_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '客户端IP',
  `oper_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '操作用户',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '处理状态（0未处理 1已处理 2已忽略）',
  `handle_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '处理人',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '处理备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`error_id`) USING BTREE,
  KEY `idx_error_type` (`error_type`) USING BTREE,
  KEY `idx_oper_time` (`oper_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='错误日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_error_log`
--

LOCK TABLES `sys_error_log` WRITE;
/*!40000 ALTER TABLE `sys_error_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_error_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_hardware_fingerprint`
--

DROP TABLE IF EXISTS `sys_hardware_fingerprint`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_hardware_fingerprint` (
  `fingerprint_id` bigint NOT NULL AUTO_INCREMENT COMMENT '鎸囩汗ID',
  `machine_id` varchar(100) NOT NULL COMMENT '鏈哄櫒ID',
  `cpu_id` varchar(100) DEFAULT NULL COMMENT 'CPU ID',
  `disk_id` varchar(100) DEFAULT NULL COMMENT '纾佺洏ID',
  `mac_address` varchar(100) DEFAULT NULL COMMENT 'MAC鍦板潃',
  `os_info` varchar(200) DEFAULT NULL COMMENT '鎿嶄綔绯荤粺淇℃伅',
  `fingerprint_hash` varchar(200) NOT NULL COMMENT '鎸囩汗鍝堝笇鍊',
  `first_seen` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '棣栨?妫?祴鏃堕棿',
  `last_seen` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '鏈?悗妫?祴鏃堕棿',
  `status` char(1) DEFAULT '1' COMMENT '鐘舵?(0=绂佺敤,1=鍚?敤)',
  PRIMARY KEY (`fingerprint_id`),
  UNIQUE KEY `uk_fingerprint_hash` (`fingerprint_hash`),
  KEY `idx_machine_id` (`machine_id`),
  KEY `idx_status` (`status`),
  KEY `idx_first_seen` (`first_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='纭?欢鎸囩汗琛';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_hardware_fingerprint`
--

LOCK TABLES `sys_hardware_fingerprint` WRITE;
/*!40000 ALTER TABLE `sys_hardware_fingerprint` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_hardware_fingerprint` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job`
--

DROP TABLE IF EXISTS `sys_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job` (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '浠诲姟ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '浠诲姟鍚嶇О',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '浠诲姟缁勫悕',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '璋冪敤鐩?爣瀛楃?涓',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT 'cron鎵ц?琛ㄨ揪寮',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '3' COMMENT '璁″垝鎵ц?閿欒?绛栫暐锛?绔嬪嵆鎵ц? 2鎵ц?涓?? 3鏀惧純鎵ц?锛',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1' COMMENT '鏄?惁骞跺彂鎵ц?锛?鍏佽? 1绂佹?锛',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '鐘舵?锛?姝ｅ父 1鏆傚仠锛',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '鍒涘缓鑰',
  `create_time` datetime DEFAULT NULL COMMENT '鍒涘缓鏃堕棿',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '鏇存柊鑰',
  `update_time` datetime DEFAULT NULL COMMENT '鏇存柊鏃堕棿',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '澶囨敞淇℃伅',
  PRIMARY KEY (`job_id`,`job_name`,`job_group`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='定时任务调度表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job`
--

LOCK TABLES `sys_job` WRITE;
/*!40000 ALTER TABLE `sys_job` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_job` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job_log`
--

DROP TABLE IF EXISTS `sys_job_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job_log` (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '浠诲姟鏃ュ織ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '浠诲姟鍚嶇О',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '浠诲姟缁勫悕',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '璋冪敤鐩?爣瀛楃?涓',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '鏃ュ織淇℃伅',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '鎵ц?鐘舵?锛?姝ｅ父 1澶辫触锛',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '寮傚父淇℃伅',
  `create_time` datetime DEFAULT NULL COMMENT '鍒涘缓鏃堕棿',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='定时任务调度日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job_log`
--

LOCK TABLES `sys_job_log` WRITE;
/*!40000 ALTER TABLE `sys_job_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_job_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_license_feature`
--

DROP TABLE IF EXISTS `sys_license_feature`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_license_feature` (
  `feature_id` bigint NOT NULL AUTO_INCREMENT,
  `feature_code` varchar(50) NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `feature_desc` varchar(200) DEFAULT NULL,
  `license_types` varchar(100) DEFAULT NULL,
  `is_core` char(1) DEFAULT '0',
  `sort_order` int DEFAULT '0',
  `status` char(1) DEFAULT '1',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`feature_id`),
  UNIQUE KEY `feature_code` (`feature_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_license_feature`
--

LOCK TABLES `sys_license_feature` WRITE;
/*!40000 ALTER TABLE `sys_license_feature` DISABLE KEYS */;
INSERT INTO `sys_license_feature` VALUES (1,'user_management','User Management',NULL,'trial,standard,enterprise','1',1,'1','2025-07-27 23:29:06'),(2,'warehouse_management','Warehouse Management',NULL,'standard,enterprise','1',2,'1','2025-07-27 23:29:06'),(3,'inventory_management','Inventory Management',NULL,'trial,standard,enterprise','1',3,'1','2025-07-27 23:29:06');
/*!40000 ALTER TABLE `sys_license_feature` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_log_audit`
--

DROP TABLE IF EXISTS `sys_log_audit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_log_audit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IP',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='审计日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_log_audit`
--

LOCK TABLES `sys_log_audit` WRITE;
/*!40000 ALTER TABLE `sys_log_audit` DISABLE KEYS */;
INSERT INTO `sys_log_audit` VALUES (1,'admin','登录系统','127.0.0.1','成功','无','2025-07-16 17:34:22'),(2,'user1','修改资料','************','成功','无','2025-07-16 17:34:22'),(3,'user2','删除记录','************','失败','权限不足','2025-07-16 17:34:22'),(4,'admin','登录','127.0.0.1','成功','生产环境测试数据','2025-07-16 21:49:12');
/*!40000 ALTER TABLE `sys_log_audit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_log_data`
--

DROP TABLE IF EXISTS `sys_log_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_log_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IP',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='数据日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_log_data`
--

LOCK TABLES `sys_log_data` WRITE;
/*!40000 ALTER TABLE `sys_log_data` DISABLE KEYS */;
INSERT INTO `sys_log_data` VALUES (1,'admin','导入数据','127.0.0.1','成功','无','2025-07-16 17:34:22'),(2,'user1','导出数据','************','成功','无','2025-07-16 17:34:22');
/*!40000 ALTER TABLE `sys_log_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_log_error`
--

DROP TABLE IF EXISTS `sys_log_error`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_log_error` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IP',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='错误日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_log_error`
--

LOCK TABLES `sys_log_error` WRITE;
/*!40000 ALTER TABLE `sys_log_error` DISABLE KEYS */;
INSERT INTO `sys_log_error` VALUES (1,'admin','系统异常','127.0.0.1','失败','NullPointerException','2025-07-16 17:34:22'),(2,'user2','数据错误','************','失败','数据格式不符','2025-07-16 17:34:22');
/*!40000 ALTER TABLE `sys_log_error` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_log_login`
--

DROP TABLE IF EXISTS `sys_log_login`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_log_login` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IP',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='登录日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_log_login`
--

LOCK TABLES `sys_log_login` WRITE;
/*!40000 ALTER TABLE `sys_log_login` DISABLE KEYS */;
INSERT INTO `sys_log_login` VALUES (1,'admin','登录','127.0.0.1','成功','无','2025-07-16 17:34:22'),(2,'user1','登录','************','失败','密码错误','2025-07-16 17:34:22');
/*!40000 ALTER TABLE `sys_log_login` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_log_permission`
--

DROP TABLE IF EXISTS `sys_log_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_log_permission` (
  `user_id` bigint NOT NULL COMMENT '鐢ㄦ埛ID',
  `log_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鏃ュ織绫诲瀷',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '鐘舵?锛?姝ｅ父 1鍋滅敤锛',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '鍒涘缓鑰',
  `create_time` datetime DEFAULT NULL COMMENT '鍒涘缓鏃堕棿',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '鏇存柊鑰',
  `update_time` datetime DEFAULT NULL COMMENT '鏇存柊鏃堕棿',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '澶囨敞',
  PRIMARY KEY (`user_id`,`log_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鐢ㄦ埛鏃ュ織鏉冮檺琛';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_log_permission`
--

LOCK TABLES `sys_log_permission` WRITE;
/*!40000 ALTER TABLE `sys_log_permission` DISABLE KEYS */;
INSERT INTO `sys_log_permission` VALUES (1,'AUDIT','0','admin','2025-07-16 12:54:49','',NULL,NULL),(1,'DATA','0','admin','2025-07-16 12:54:49','',NULL,NULL),(1,'ERROR','0','admin','2025-07-16 12:54:49','',NULL,NULL),(1,'LOGIN','0','admin','2025-07-16 12:54:49','',NULL,NULL),(1,'OPERATION','0','admin','2025-07-16 12:54:49','',NULL,NULL),(1,'PERMISSION','0','admin','2025-07-16 12:54:49','',NULL,NULL),(1,'SECURITY','0','admin','2025-07-16 12:54:49','',NULL,NULL),(1,'SYSTEM','0','admin','2025-07-16 12:54:49','',NULL,NULL),(2,'AUDIT','0',NULL,NULL,'',NULL,NULL),(2,'DATA','0',NULL,NULL,'',NULL,NULL),(2,'ERROR','0',NULL,NULL,'',NULL,NULL),(2,'LOGIN','0',NULL,NULL,'',NULL,NULL),(2,'OPERATION','0',NULL,NULL,'',NULL,NULL),(2,'PERMISSION','0',NULL,NULL,'',NULL,NULL),(2,'SECURITY','0',NULL,NULL,'',NULL,NULL),(2,'SYSTEM','0',NULL,NULL,'',NULL,NULL),(6,'AUDIT','0',NULL,NULL,'',NULL,NULL),(6,'DATA','0',NULL,NULL,'',NULL,NULL),(6,'ERROR','0',NULL,NULL,'',NULL,NULL),(6,'LOGIN','0',NULL,NULL,'',NULL,NULL),(6,'OPERATION','0',NULL,NULL,'',NULL,NULL),(6,'PERMISSION','0',NULL,NULL,'',NULL,NULL),(6,'SECURITY','0',NULL,NULL,'',NULL,NULL),(6,'SYSTEM','0',NULL,NULL,'',NULL,NULL);
/*!40000 ALTER TABLE `sys_log_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_log_security`
--

DROP TABLE IF EXISTS `sys_log_security`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_log_security` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IP',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='安全日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_log_security`
--

LOCK TABLES `sys_log_security` WRITE;
/*!40000 ALTER TABLE `sys_log_security` DISABLE KEYS */;
INSERT INTO `sys_log_security` VALUES (1,'admin','权限变更','127.0.0.1','成功','无','2025-07-16 17:34:22'),(2,'user2','非法访问','************','失败','拦截','2025-07-16 17:34:22');
/*!40000 ALTER TABLE `sys_log_security` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_log_type`
--

DROP TABLE IF EXISTS `sys_log_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_log_type` (
  `log_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志类型',
  `log_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志名称',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='日志类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_log_type`
--

LOCK TABLES `sys_log_type` WRITE;
/*!40000 ALTER TABLE `sys_log_type` DISABLE KEYS */;
INSERT INTO `sys_log_type` VALUES ('AUDIT','审计日志','0','审计相关日志','admin','2025-07-16 12:54:31','',NULL,NULL),('DATA','数据日志','0','数据操作相关日志','admin','2025-07-16 12:54:31','',NULL,NULL),('ERROR','错误日志','0','系统错误相关日志','admin','2025-07-16 12:54:31','',NULL,NULL),('LOGIN','登录日志','0','用户登录、登出相关日志','admin','2025-07-16 12:46:56','',NULL,'登录日志类型'),('OPERATION','操作日志','0','用户操作相关日志','admin','2025-07-16 12:54:21','',NULL,NULL),('PERMISSION','权限日志','0','权限管理相关日志','admin','2025-07-16 12:54:31','',NULL,NULL),('SECURITY','安全日志','0','安全相关日志','admin','2025-07-16 12:54:31','',NULL,NULL),('SYSTEM','系统日志','0','系统运行相关日志','admin','2025-07-16 12:54:31','',NULL,NULL);
/*!40000 ALTER TABLE `sys_log_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_logininfor`
--

DROP TABLE IF EXISTS `sys_logininfor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_logininfor` (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '璁块棶ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鐢ㄦ埛璐﹀彿',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鐧诲綍IP鍦板潃',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鐧诲綍鍦扮偣',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '娴忚?鍣ㄧ被鍨',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鎿嶄綔绯荤粺',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '鐧诲綍鐘舵?锛?鎴愬姛 1澶辫触锛',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鎻愮ず娑堟伅',
  `login_time` datetime DEFAULT NULL COMMENT '璁块棶鏃堕棿',
  PRIMARY KEY (`info_id`) USING BTREE,
  KEY `idx_login_time` (`login_time`) USING BTREE,
  KEY `idx_login_name` (`user_name`) USING BTREE,
  KEY `idx_login_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统访问记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_logininfor`
--

LOCK TABLES `sys_logininfor` WRITE;
/*!40000 ALTER TABLE `sys_logininfor` DISABLE KEYS */;
INSERT INTO `sys_logininfor` VALUES (1,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-26 21:43:54'),(2,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-26 22:26:00'),(3,'superadmin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-26 22:55:51'),(4,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-26 22:56:00'),(5,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-27 18:50:56'),(6,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-27 22:50:16'),(7,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-27 23:04:57'),(8,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-27 23:37:35'),(9,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-27 23:43:20'),(10,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-28 06:49:54'),(11,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-28 06:50:05'),(12,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-28 10:49:17'),(13,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-28 11:04:36'),(14,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-28 11:48:19'),(15,'莫少雄','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-28 12:31:23');
/*!40000 ALTER TABLE `sys_logininfor` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_menu` (
  `menu_id` bigint NOT NULL DEFAULT '0' COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由参数',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链(0是 1否)',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存(0缓存 1不缓存)',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '菜单类型(M目录 C菜单 F按钮)',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '菜单状态(0显示 1隐藏)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '菜单状态(0正常 1停用)',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `route_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='菜单权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu`
--

LOCK TABLES `sys_menu` WRITE;
/*!40000 ALTER TABLE `sys_menu` DISABLE KEYS */;
INSERT INTO `sys_menu` VALUES (2,'首页',1,1,'index','index',NULL,1,0,'C','0','0','','dashboard','admin','2025-05-16 14:06:28','',NULL,'首页菜单',NULL),(3,'系统管理',0,2,'system','Layout',NULL,1,0,'M','0','0','','system','admin','2025-05-16 14:06:28','',NULL,'系统管理目录',NULL),(4,'用户管理',3,1,'user','system/user/index',NULL,1,0,'C','0','0','system:user:list','user','admin','2025-05-16 14:06:28','',NULL,'用户管理菜单',NULL),(5,'角色管理',3,2,'role','system/role/index',NULL,1,0,'C','0','0','system:role:list','peoples','admin','2025-05-16 14:06:28','',NULL,'角色管理菜单',NULL),(6,'菜单管理',3,3,'menu','system/menu/index',NULL,1,0,'C','0','0','system:menu:list','tree-table','admin','2025-05-16 14:06:28','',NULL,'菜单管理菜单',NULL),(7,'部门管理',3,4,'dept','system/dept/index',NULL,1,0,'C','0','0','system:dept:list','tree','admin','2025-05-16 14:06:28','',NULL,'部门管理菜单',NULL),(8,'通知公告',3,5,'notice','system/notice/index',NULL,1,0,'C','0','0','system:notice:list','message','admin','2025-05-16 14:06:28','',NULL,'通知公告菜单',NULL),(9,'权限管理',0,3,'auth','Layout',NULL,1,0,'M','0','0','','lock','admin','2025-05-16 14:06:28','',NULL,'权限管理目录',NULL),(10,'权限定义',9,1,'permission','system/permission/index',NULL,1,0,'C','0','0','system:permission:list','validCode','admin','2025-05-16 14:06:28','',NULL,'权限定义菜单',NULL),(11,'权限模板',9,2,'permission-template','system/permission/template',NULL,1,0,'C','0','0','system:permission:template','form','admin','2025-05-16 14:06:28','',NULL,'权限模板菜单',NULL),(12,'数据权限',9,3,'data-permission','system/permission/dataPermission',NULL,1,0,'C','0','0','system:permission:data','table','admin','2025-05-16 14:06:28','',NULL,'数据权限菜单',NULL),(13,'API权限',9,4,'api-permission','system/permission/apiPermission',NULL,1,0,'C','0','0','system:permission:api','swagger','admin','2025-05-16 14:06:28','',NULL,'API权限菜单',NULL),(14,'仓库管理',0,4,'warehouse','Layout',NULL,1,0,'M','0','0','','warehouse','admin','2025-05-16 14:06:28','',NULL,'仓库管理目录',NULL),(15,'仓库信息',14,1,'info','warehouse/info/index',NULL,1,0,'C','0','0','warehouse:info:list','warehouse','admin','2025-05-16 14:06:28','superadmin','2025-07-21 19:48:50','仓库信息菜单',''),(17,'物品管理',0,5,'product','Layout',NULL,1,0,'M','0','0','','shopping','admin','2025-05-16 14:06:28','',NULL,'物品管理目录',NULL),(18,'物品分类',17,1,'category','product/category/index',NULL,1,0,'C','0','0','product:category:list','category','admin','2025-05-16 14:06:28','',NULL,'物品分类菜单',NULL),(19,'物品信息',17,2,'info','product/info/index',NULL,1,0,'C','0','0','product:info:list','shopping','admin','2025-05-16 14:06:28','',NULL,'物品信息菜单',NULL),(20,'物品规格',17,3,'spec','product/spec/index',NULL,1,0,'C','0','0','product:spec:list','spec','admin','2025-05-16 14:06:28','',NULL,'物品规格菜单',NULL),(21,'物品单位',17,4,'unit','product/unit/index',NULL,1,0,'C','0','0','product:unit:list','unit','admin','2025-05-16 14:06:28','',NULL,'物品单位菜单',NULL),(22,'二维码管理',0,6,'qrcode','Layout',NULL,1,0,'M','0','0','','code','admin','2025-05-16 14:06:28','',NULL,'二维码管理目录',NULL),(23,'二维码生成',22,1,'generate','qrcode/generate/index',NULL,1,0,'C','0','0','qrcode:generate:list','code','admin','2025-05-16 14:06:28','',NULL,'二维码生成菜单',NULL),(24,'批量生成',22,2,'batch','qrcode/batch/index',NULL,1,0,'C','0','0','qrcode:batch:list','job','admin','2025-05-16 14:06:28','',NULL,'批量生成菜单',NULL),(25,'二维码扫描',22,3,'scan','qrcode/scan/index',NULL,1,0,'C','0','0','qrcode:scan:list','search','admin','2025-05-16 14:06:28','',NULL,'二维码扫描菜单',NULL),(26,'批量扫描',22,4,'batch-scan','qrcode/batchScan/index',NULL,1,0,'C','0','0','qrcode:batchScan:list','eye-open','admin','2025-05-16 14:06:28','',NULL,'批量扫描菜单',NULL),(27,'库存管理',0,7,'inventory','Layout',NULL,1,0,'M','0','0','','documentation','admin','2025-05-16 14:06:28','',NULL,'库存管理目录',NULL),(28,'库存查询',27,1,'stock','inventory/stock/index',NULL,1,0,'C','0','0','inventory:stock:list','search','admin','2025-05-16 14:06:28','',NULL,'库存查询菜单',NULL),(29,'入库管理',27,2,'in','inventory/in/index',NULL,1,0,'C','0','0','inventory:in:list','upload','admin','2025-05-16 14:06:28','',NULL,'入库管理菜单',NULL),(30,'出库管理',27,3,'out','inventory/out/index',NULL,1,0,'C','0','0','inventory:out:list','download','admin','2025-05-16 14:06:28','',NULL,'出库管理菜单',NULL),(31,'库存调拨',27,4,'transfer','inventory/transfer/index',NULL,1,0,'C','0','0','inventory:transfer:list','drag','admin','2025-05-16 14:06:28','',NULL,'库存调拨菜单',NULL),(32,'库存盘点',27,5,'check','inventory/check/index',NULL,1,0,'C','0','0','inventory:check:list','checkbox','admin','2025-05-16 14:06:28','',NULL,'库存盘点菜单',NULL),(33,'库存预警',27,6,'alert','inventory/alert/index',NULL,1,0,'C','0','0','inventory:alert:list','warning','admin','2025-05-16 14:06:28','',NULL,'库存预警菜单',NULL),(34,'日志管理',0,8,'log','Layout',NULL,1,0,'M','0','0','','log','admin','2025-05-16 14:06:28','',NULL,'日志管理目录',NULL),(35,'操作日志',34,1,'operation','log/operation/index',NULL,1,0,'C','0','0','log:operation:list','log','admin','2025-05-16 14:06:28','',NULL,'操作日志菜单',NULL),(36,'出入库日志',34,2,'stock','log/stock/index',NULL,1,0,'C','0','0','log:stock:list','log','admin','2025-05-16 14:06:28','',NULL,'出入库日志菜单',NULL),(37,'系统日志',34,3,'system','log/system/index',NULL,1,0,'C','0','0','log:system:list','log','admin','2025-05-16 14:06:28','',NULL,'系统日志菜单',NULL),(38,'日志权限',34,4,'permission','log/permission/index',NULL,1,0,'C','0','0','log:permission:list','log','admin','2025-05-16 14:06:28','',NULL,'日志权限菜单',NULL),(39,'报表统计',0,9,'report','Layout',NULL,1,0,'M','0','0','','chart','admin','2025-05-16 14:06:28','',NULL,'报表统计目录',NULL),(40,'库存报表',39,1,'stock','report/stock/index',NULL,1,0,'C','0','0','inventory:stock:list','documentation','admin','2025-05-16 14:06:29','',NULL,'库存报表菜单',NULL),(41,'入库报表',39,2,'in','report/in/index',NULL,1,0,'C','0','0','report:in:list','upload','admin','2025-05-16 14:06:29','',NULL,'入库报表菜单',NULL),(42,'出库报表',39,3,'out','report/out/index',NULL,1,0,'C','0','0','report:out:list','download','admin','2025-05-16 14:06:29','',NULL,'出库报表菜单',NULL),(2100,'库存预警规则',33,5,'alertrule','inventory/alertrule/index',NULL,1,0,'C','0','0','warehouse:alertrule:list','warning','admin','2025-06-15 22:26:16','',NULL,'库存预警规则菜单',NULL),(2110,'库存预警记录',33,6,'alertlog','inventory/alertlog/index',NULL,1,0,'C','0','0','inventory:alertlog:list','log','admin','2025-06-15 22:26:16','',NULL,'库存预警记录菜单',NULL),(3038,'仓库新增',15,2,'#','',NULL,1,0,'F','0','0','warehouse:info:add','plus','admin','2025-05-18 21:06:20','superadmin','2025-07-21 19:48:54','',''),(3039,'仓库编辑',15,3,'#','',NULL,1,0,'F','0','0','warehouse:info:edit','edit','admin','2025-05-18 21:06:20','superadmin','2025-07-21 19:43:29','',''),(3040,'仓库删除',15,4,'#','',NULL,1,0,'F','0','0','warehouse:info:remove','delete','admin','2025-05-18 21:06:20','superadmin','2025-07-21 19:49:02','',''),(3041,'仓库导出',15,6,'#','',NULL,1,0,'F','0','0','warehouse:info:export','download','admin','2025-05-18 21:06:20','superadmin','2025-07-21 19:49:11','',''),(3042,'仓库用户管理',14,2,'user','system/warehouse/user/index',NULL,1,0,'C','0','0','warehouse:info:list','box','admin','2025-05-18 21:06:20','',NULL,'Warehouse User Management Menu',NULL),(3784,'审计日志',34,1,'/audit-log','log/operation/index',NULL,1,0,'C','0','0','log:audit','audit','',NULL,'',NULL,'',NULL),(3785,'数据日志',34,2,'/data-log','log/data/index',NULL,1,0,'C','0','0','log:data','data','',NULL,'',NULL,'',NULL),(3786,'错误日志',34,3,'/error-log','log/error/index',NULL,1,0,'C','0','0','log:error','error','',NULL,'',NULL,'',NULL),(3787,'登录日志',34,4,'/login-log','log/system/index',NULL,1,0,'C','0','0','log:login','login','',NULL,'',NULL,'',NULL),(3788,'安全日志',34,5,'/security-log','log/security/index',NULL,1,0,'C','0','0','log:security','security','',NULL,'',NULL,'',NULL),(3792,'仓库导入',15,5,'',NULL,NULL,1,0,'F','0','0','warehouse:info:import','#','system','2025-07-21 11:42:49','superadmin','2025-07-21 19:49:06','',''),(3859,'新增',20,1,'',NULL,NULL,1,0,'F','0','0','product:spec:add','#','system','2025-07-21 13:00:29','',NULL,'',NULL),(3860,'编辑',20,2,'',NULL,NULL,1,0,'F','0','0','product:spec:edit','#','system','2025-07-21 13:00:29','',NULL,'',NULL),(3861,'删除',20,3,'',NULL,NULL,1,0,'F','0','0','product:spec:remove','#','system','2025-07-21 13:00:29','',NULL,'',NULL),(3862,'导出',20,4,'',NULL,NULL,1,0,'F','0','0','product:spec:export','#','system','2025-07-21 13:00:29','',NULL,'',NULL),(3863,'新增',21,1,'',NULL,NULL,1,0,'F','0','0','product:unit:add','#','system','2025-07-21 13:00:38','',NULL,'',NULL),(3864,'编辑',21,2,'',NULL,NULL,1,0,'F','0','0','product:unit:edit','#','system','2025-07-21 13:00:38','',NULL,'',NULL),(3865,'删除',21,3,'',NULL,NULL,1,0,'F','0','0','product:unit:remove','#','system','2025-07-21 13:00:38','',NULL,'',NULL),(3866,'导出',21,4,'',NULL,NULL,1,0,'F','0','0','product:unit:export','#','system','2025-07-21 13:00:38','',NULL,'',NULL),(3893,'新增',28,1,'',NULL,NULL,1,0,'F','0','0','inventory:stock:add','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3894,'修改',28,2,'',NULL,NULL,1,0,'F','0','0','inventory:stock:edit','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3895,'删除',28,3,'',NULL,NULL,1,0,'F','0','0','inventory:stock:remove','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3896,'导出',28,4,'',NULL,NULL,1,0,'F','0','0','inventory:stock:export','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3897,'报表',28,5,'',NULL,NULL,1,0,'F','0','0','inventory:stock:list','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3898,'预警报表',28,6,'',NULL,NULL,1,0,'F','0','0','inventory:stock:alert','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3899,'阈值设置',28,7,'',NULL,NULL,1,0,'F','0','0','inventory:stock:threshold','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3900,'高级分析',28,8,'',NULL,NULL,1,0,'F','0','0','inventory:stock:analysis','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3901,'物品库存',28,9,'',NULL,NULL,1,0,'F','0','0','inventory:stock:product','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3902,'查看',28,10,'',NULL,NULL,1,0,'F','0','0','inventory:stock:list','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3949,'删除',36,1,'',NULL,NULL,1,0,'F','0','0','log:inventory:remove','#','system','2025-07-21 22:38:35','',NULL,'',NULL),(3950,'导出',36,2,'',NULL,NULL,1,0,'F','0','0','log:stock:export','#','system','2025-07-21 22:38:35','',NULL,'',NULL),(3951,'查看',36,3,'',NULL,NULL,1,0,'F','0','0','log:inventory:query','#','system','2025-07-21 22:38:35','',NULL,'',NULL),(3960,'新增',40,1,'',NULL,NULL,1,0,'F','0','0','report:stock:add','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3961,'修改',40,2,'',NULL,NULL,1,0,'F','0','0','report:stock:edit','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3962,'删除',40,3,'',NULL,NULL,1,0,'F','0','0','report:stock:remove','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3963,'导出',40,4,'',NULL,NULL,1,0,'F','0','0','report:stock:export','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3964,'报表',40,5,'',NULL,NULL,1,0,'F','0','0','report:stock:list','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3965,'新增',41,1,'',NULL,NULL,1,0,'F','0','0','report:in:add','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3966,'修改',41,2,'',NULL,NULL,1,0,'F','0','0','report:in:edit','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3967,'删除',41,3,'',NULL,NULL,1,0,'F','0','0','report:in:remove','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3968,'导出',41,4,'',NULL,NULL,1,0,'F','0','0','report:in:export','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3969,'报表',41,5,'',NULL,NULL,1,0,'F','0','0','report:in:list','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3970,'新增',42,1,'',NULL,NULL,1,0,'F','0','0','report:out:add','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3971,'修改',42,2,'',NULL,NULL,1,0,'F','0','0','report:out:edit','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3972,'删除',42,3,'',NULL,NULL,1,0,'F','0','0','report:out:remove','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3973,'导出',42,4,'',NULL,NULL,1,0,'F','0','0','report:out:export','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3974,'报表',42,5,'',NULL,NULL,1,0,'F','0','0','report:out:list','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(4006,'入库查询',29,1,'',NULL,NULL,1,0,'F','0','0','inventory:in:list','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4007,'入库新增',29,2,'',NULL,NULL,1,0,'F','0','0','inventory:in:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4008,'入库修改',29,3,'',NULL,NULL,1,0,'F','0','0','inventory:in:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4009,'入库删除',29,4,'',NULL,NULL,1,0,'F','0','0','inventory:in:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4010,'入库导出',29,5,'',NULL,NULL,1,0,'F','0','0','inventory:in:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4011,'入库审核',29,6,'',NULL,NULL,1,0,'F','0','0','inventory:in:audit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4012,'入库打印',29,7,'',NULL,NULL,1,0,'F','0','0','inventory:in:print','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4013,'扫码入库',29,8,'',NULL,NULL,1,0,'F','0','0','inventory:in:scan','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4014,'批量入库',29,9,'',NULL,NULL,1,0,'F','0','0','inventory:in:batch','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4015,'出库查询',30,1,'',NULL,NULL,1,0,'F','0','0','inventory:out:list','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4016,'出库新增',30,2,'',NULL,NULL,1,0,'F','0','0','inventory:out:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4017,'出库修改',30,3,'',NULL,NULL,1,0,'F','0','0','inventory:out:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4018,'出库删除',30,4,'',NULL,NULL,1,0,'F','0','0','inventory:out:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4019,'出库导出',30,5,'',NULL,NULL,1,0,'F','0','0','inventory:out:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4020,'出库审核',30,6,'',NULL,NULL,1,0,'F','0','0','inventory:out:audit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4021,'出库打印',30,7,'',NULL,NULL,1,0,'F','0','0','inventory:out:print','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4022,'扫码出库',30,8,'',NULL,NULL,1,0,'F','0','0','inventory:out:scan','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4023,'批量出库',30,9,'',NULL,NULL,1,0,'F','0','0','inventory:out:batch','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4024,'调拨查询',31,1,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4025,'调拨新增',31,2,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4026,'调拨修改',31,3,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4027,'调拨删除',31,4,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4028,'调拨导出',31,5,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4029,'调拨审核',31,6,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:audit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4030,'调拨打印',31,7,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:print','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4031,'盘点查询',32,1,'',NULL,NULL,1,0,'F','0','0','inventory:check:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4032,'盘点新增',32,2,'',NULL,NULL,1,0,'F','0','0','inventory:check:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4033,'盘点修改',32,3,'',NULL,NULL,1,0,'F','0','0','inventory:check:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4034,'盘点删除',32,4,'',NULL,NULL,1,0,'F','0','0','inventory:check:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4035,'盘点导出',32,5,'',NULL,NULL,1,0,'F','0','0','inventory:check:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4036,'盘点审核',32,6,'',NULL,NULL,1,0,'F','0','0','inventory:check:audit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4037,'盘点打印',32,7,'',NULL,NULL,1,0,'F','0','0','inventory:check:print','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4038,'加载库存',32,8,'',NULL,NULL,1,0,'F','0','0','inventory:check:load','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4039,'库存查询',0,1,'',NULL,NULL,1,0,'F','0','0','inventory:inventory:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4040,'库存修改',0,2,'',NULL,NULL,1,0,'F','0','0','inventory:inventory:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4041,'库存删除',0,3,'',NULL,NULL,1,0,'F','0','0','inventory:inventory:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4042,'库存导出',0,4,'',NULL,NULL,1,0,'F','0','0','inventory:inventory:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4043,'库存调整',0,5,'',NULL,NULL,1,0,'F','0','0','inventory:inventory:adjust','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4044,'仓库查询',0,1,'',NULL,NULL,1,0,'F','0','0','inventory:warehouse:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4045,'仓库新增',0,2,'',NULL,NULL,1,0,'F','0','0','inventory:warehouse:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4046,'仓库修改',0,3,'',NULL,NULL,1,0,'F','0','0','inventory:warehouse:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4047,'仓库删除',0,4,'',NULL,NULL,1,0,'F','0','0','inventory:warehouse:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4048,'仓库导出',0,5,'',NULL,NULL,1,0,'F','0','0','inventory:warehouse:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4049,'物品查询',19,1,'',NULL,NULL,1,0,'F','0','0','product:info:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4050,'物品新增',19,2,'',NULL,NULL,1,0,'F','0','0','product:info:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4051,'物品修改',19,3,'',NULL,NULL,1,0,'F','0','0','product:info:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4052,'物品删除',19,4,'',NULL,NULL,1,0,'F','0','0','product:info:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4053,'物品导出',19,5,'',NULL,NULL,1,0,'F','0','0','product:info:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4054,'物品导入',19,6,'',NULL,NULL,1,0,'F','0','0','product:info:import','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4055,'分类查询',18,1,'',NULL,NULL,1,0,'F','0','0','product:category:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4056,'分类新增',18,2,'',NULL,NULL,1,0,'F','0','0','product:category:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4057,'分类修改',18,3,'',NULL,NULL,1,0,'F','0','0','product:category:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4058,'分类删除',18,4,'',NULL,NULL,1,0,'F','0','0','product:category:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4059,'分类导出',18,5,'',NULL,NULL,1,0,'F','0','0','product:category:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4060,'报表查询',0,1,'',NULL,NULL,1,0,'F','0','0','system:report:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4061,'报表导出',0,2,'',NULL,NULL,1,0,'F','0','0','system:report:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4062,'日志查询',0,1,'',NULL,NULL,1,0,'F','0','0','log:inventory:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4063,'日志删除',0,2,'',NULL,NULL,1,0,'F','0','0','log:inventory:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4064,'日志导出',0,3,'',NULL,NULL,1,0,'F','0','0','log:stock:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4065,'系统日志查询',37,1,'',NULL,NULL,1,0,'F','0','0','monitor:logininfor:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4066,'系统日志删除',37,2,'',NULL,NULL,1,0,'F','0','0','log:system:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4067,'系统日志导出',37,3,'',NULL,NULL,1,0,'F','0','0','log:system:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4068,'账户解锁',37,4,'',NULL,NULL,1,0,'F','0','0','monitor:logininfor:unlock','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4069,'操作日志查询',35,1,'',NULL,NULL,1,0,'F','0','0','monitor:operlog:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4070,'操作日志删除',35,2,'',NULL,NULL,1,0,'F','0','0','log:operation:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4071,'操作日志导出',35,3,'',NULL,NULL,1,0,'F','0','0','log:operation:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4072,'操作日志详细',35,4,'',NULL,NULL,1,0,'F','0','0','monitor:operlog:detail','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4073,'安全日志查询',0,1,'',NULL,NULL,1,0,'F','0','0','log:security:list','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4074,'安全日志删除',0,2,'',NULL,NULL,1,0,'F','0','0','log:security:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4075,'安全日志导出',0,3,'',NULL,NULL,1,0,'F','0','0','log:security:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4076,'数据日志查询',0,1,'',NULL,NULL,1,0,'F','0','0','log:data:list','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4077,'数据日志删除',0,2,'',NULL,NULL,1,0,'F','0','0','log:data:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4078,'数据日志导出',0,3,'',NULL,NULL,1,0,'F','0','0','log:data:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4079,'权限日志查询',38,1,'',NULL,NULL,1,0,'F','0','0','log:permission:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4080,'权限日志删除',38,2,'',NULL,NULL,1,0,'F','0','0','log:permission:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4081,'权限日志导出',38,3,'',NULL,NULL,1,0,'F','0','0','log:permission:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4082,'审计日志查询',0,1,'',NULL,NULL,1,0,'F','0','0','log:audit:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4083,'审计日志删除',0,2,'',NULL,NULL,1,0,'F','0','0','log:audit:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4084,'审计日志导出',0,3,'',NULL,NULL,1,0,'F','0','0','log:audit:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4085,'业务日志查询',0,1,'',NULL,NULL,1,0,'F','0','0','log:business:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4086,'业务日志删除',0,2,'',NULL,NULL,1,0,'F','0','0','log:business:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4087,'业务日志导出',0,3,'',NULL,NULL,1,0,'F','0','0','log:business:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4088,'异常日志查询',0,1,'',NULL,NULL,1,0,'F','0','0','log:exception:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4089,'异常日志删除',0,2,'',NULL,NULL,1,0,'F','0','0','log:exception:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4090,'异常日志导出',0,3,'',NULL,NULL,1,0,'F','0','0','log:exception:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4091,'用户查询',4,1,'',NULL,NULL,1,0,'F','0','0','system:user:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4092,'用户新增',4,2,'',NULL,NULL,1,0,'F','0','0','system:user:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4093,'用户修改',4,3,'',NULL,NULL,1,0,'F','0','0','system:user:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4094,'用户删除',4,4,'',NULL,NULL,1,0,'F','0','0','system:user:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4095,'用户导出',4,5,'',NULL,NULL,1,0,'F','0','0','system:user:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4096,'用户导入',4,6,'',NULL,NULL,1,0,'F','0','0','system:user:import','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4097,'重置密码',4,7,'',NULL,NULL,1,0,'F','0','0','system:user:resetPwd','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4098,'分配角色',4,8,'',NULL,NULL,1,0,'F','0','0','system:user:role','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4099,'角色查询',5,1,'',NULL,NULL,1,0,'F','0','0','system:role:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4100,'角色新增',5,2,'',NULL,NULL,1,0,'F','0','0','system:role:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4101,'角色修改',5,3,'',NULL,NULL,1,0,'F','0','0','system:role:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4102,'角色删除',5,4,'',NULL,NULL,1,0,'F','0','0','system:role:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4103,'角色导出',5,5,'',NULL,NULL,1,0,'F','0','0','system:role:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4104,'分配权限',5,6,'',NULL,NULL,1,0,'F','0','0','system:role:auth','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4105,'菜单查询',6,1,'',NULL,NULL,1,0,'F','0','0','system:menu:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4106,'菜单新增',6,2,'',NULL,NULL,1,0,'F','0','0','system:menu:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4107,'菜单修改',6,3,'',NULL,NULL,1,0,'F','0','0','system:menu:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4108,'菜单删除',6,4,'',NULL,NULL,1,0,'F','0','0','system:menu:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4109,'部门查询',7,1,'',NULL,NULL,1,0,'F','0','0','system:dept:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4110,'部门新增',7,2,'',NULL,NULL,1,0,'F','0','0','system:dept:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4111,'部门修改',7,3,'',NULL,NULL,1,0,'F','0','0','system:dept:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4112,'部门删除',7,4,'',NULL,NULL,1,0,'F','0','0','system:dept:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4113,'岗位查询',0,1,'',NULL,NULL,1,0,'F','0','0','system:post:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4114,'岗位新增',0,2,'',NULL,NULL,1,0,'F','0','0','system:post:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4115,'岗位修改',0,3,'',NULL,NULL,1,0,'F','0','0','system:post:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4116,'岗位删除',0,4,'',NULL,NULL,1,0,'F','0','0','system:post:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4117,'岗位导出',0,5,'',NULL,NULL,1,0,'F','0','0','system:post:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4118,'字典查询',0,1,'',NULL,NULL,1,0,'F','0','0','system:dict:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4119,'字典新增',0,2,'',NULL,NULL,1,0,'F','0','0','system:dict:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4120,'字典修改',0,3,'',NULL,NULL,1,0,'F','0','0','system:dict:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4121,'字典删除',0,4,'',NULL,NULL,1,0,'F','0','0','system:dict:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4122,'字典导出',0,5,'',NULL,NULL,1,0,'F','0','0','system:dict:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4123,'参数查询',0,1,'',NULL,NULL,1,0,'F','0','0','system:config:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4124,'参数新增',0,2,'',NULL,NULL,1,0,'F','0','0','system:config:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4125,'参数修改',0,3,'',NULL,NULL,1,0,'F','0','0','system:config:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4126,'参数删除',0,4,'',NULL,NULL,1,0,'F','0','0','system:config:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4127,'参数导出',0,5,'',NULL,NULL,1,0,'F','0','0','system:config:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4128,'刷新缓存',0,6,'',NULL,NULL,1,0,'F','0','0','system:config:refresh','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4129,'公告查询',8,1,'',NULL,NULL,1,0,'F','0','0','system:notice:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4130,'公告新增',8,2,'',NULL,NULL,1,0,'F','0','0','system:notice:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4131,'公告修改',8,3,'',NULL,NULL,1,0,'F','0','0','system:notice:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4132,'公告删除',8,4,'',NULL,NULL,1,0,'F','0','0','system:notice:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4139,'字典管理',3,6,'dict','system/dict/index',NULL,1,0,'C','0','0','system:dict:list','dict','admin','2025-07-27 22:23:18','admin','2025-07-27 22:23:18','字典管理菜单',NULL),(4140,'参数设置',3,7,'config','system/config/index',NULL,1,0,'C','0','0','system:config:list','edit','admin','2025-07-27 22:23:29','admin','2025-07-27 22:23:29','参数设置菜单',NULL),(4141,'二维码工具',3,8,'qrcode','system/qrcode/index',NULL,1,0,'C','0','0','system:qrcode:list','qrcode','admin','2025-07-27 22:24:02','admin','2025-07-27 22:24:02','二维码工具菜单',NULL),(4142,'授权管理',3,9,'license','system/license/index',NULL,1,0,'C','0','0','system:license:list','component','admin','2025-07-27 22:24:56','admin','2025-07-27 22:24:56','授权管理菜单',NULL),(4143,'物品条码',17,5,'barcode','product/barcode/index',NULL,1,0,'C','0','0','product:barcode:list','code','admin','2025-07-27 22:25:11','admin','2025-07-27 22:25:11','物品条码菜单',NULL),(4144,'用户权限',9,5,'userPermission','system/permission/userPermission',NULL,1,0,'C','0','0','system:permission:user','peoples','admin','2025-07-27 22:25:21','admin','2025-07-27 22:25:21','用户权限管理菜单',NULL),(4145,'字典查询',4139,1,'#','',NULL,1,0,'F','0','0','system:dict:query','#','admin','2025-07-27 22:26:24','admin','2025-07-27 22:26:24','',NULL),(4146,'字典新增',4139,2,'#','',NULL,1,0,'F','0','0','system:dict:add','#','admin','2025-07-27 22:26:24','admin','2025-07-27 22:26:24','',NULL),(4147,'字典修改',4139,3,'#','',NULL,1,0,'F','0','0','system:dict:edit','#','admin','2025-07-27 22:26:24','admin','2025-07-27 22:26:24','',NULL),(4148,'字典删除',4139,4,'#','',NULL,1,0,'F','0','0','system:dict:remove','#','admin','2025-07-27 22:26:24','admin','2025-07-27 22:26:24','',NULL),(4149,'字典导出',4139,5,'#','',NULL,1,0,'F','0','0','system:dict:export','#','admin','2025-07-27 22:26:24','admin','2025-07-27 22:26:24','',NULL),(4150,'参数查询',4140,1,'#','',NULL,1,0,'F','0','0','system:config:query','#','admin','2025-07-27 22:26:38','admin','2025-07-27 22:26:38','',NULL),(4151,'参数新增',4140,2,'#','',NULL,1,0,'F','0','0','system:config:add','#','admin','2025-07-27 22:26:38','admin','2025-07-27 22:26:38','',NULL),(4152,'参数修改',4140,3,'#','',NULL,1,0,'F','0','0','system:config:edit','#','admin','2025-07-27 22:26:38','admin','2025-07-27 22:26:38','',NULL),(4153,'参数删除',4140,4,'#','',NULL,1,0,'F','0','0','system:config:remove','#','admin','2025-07-27 22:26:38','admin','2025-07-27 22:26:38','',NULL),(4154,'参数导出',4140,5,'#','',NULL,1,0,'F','0','0','system:config:export','#','admin','2025-07-27 22:26:38','admin','2025-07-27 22:26:38','',NULL),(4155,'刷新缓存',4140,6,'#','',NULL,1,0,'F','0','0','system:config:refresh','#','admin','2025-07-27 22:26:38','admin','2025-07-27 22:26:38','',NULL),(4156,'二维码生成',4141,1,'#','',NULL,1,0,'F','0','0','system:qrcode:generate','#','admin','2025-07-27 22:26:48','admin','2025-07-27 22:26:48','',NULL),(4157,'二维码扫描',4141,2,'#','',NULL,1,0,'F','0','0','system:qrcode:scan','#','admin','2025-07-27 22:26:48','admin','2025-07-27 22:26:48','',NULL),(4158,'批量生成',4141,3,'#','',NULL,1,0,'F','0','0','system:qrcode:batch','#','admin','2025-07-27 22:26:48','admin','2025-07-27 22:26:48','',NULL),(4159,'二维码下载',4141,4,'#','',NULL,1,0,'F','0','0','system:qrcode:download','#','admin','2025-07-27 22:26:48','admin','2025-07-27 22:26:48','',NULL),(4160,'授权查询',4142,1,'#','',NULL,1,0,'F','0','0','system:license:query','#','admin','2025-07-27 22:27:03','admin','2025-07-27 22:27:03','',NULL),(4161,'授权更新',4142,2,'#','',NULL,1,0,'F','0','0','system:license:update','#','admin','2025-07-27 22:27:03','admin','2025-07-27 22:27:03','',NULL),(4162,'授权验证',4142,3,'#','',NULL,1,0,'F','0','0','system:license:verify','#','admin','2025-07-27 22:27:03','admin','2025-07-27 22:27:03','',NULL),(4163,'授权导出',4142,4,'#','',NULL,1,0,'F','0','0','system:license:export','#','admin','2025-07-27 22:27:03','admin','2025-07-27 22:27:03','',NULL),(4164,'条码查询',4143,1,'#','',NULL,1,0,'F','0','0','product:barcode:query','#','admin','2025-07-27 22:27:29','admin','2025-07-27 22:27:29','',NULL),(4165,'条码生成',4143,2,'#','',NULL,1,0,'F','0','0','product:barcode:generate','#','admin','2025-07-27 22:27:29','admin','2025-07-27 22:27:29','',NULL),(4166,'条码打印',4143,3,'#','',NULL,1,0,'F','0','0','product:barcode:print','#','admin','2025-07-27 22:27:29','admin','2025-07-27 22:27:29','',NULL),(4167,'条码导出',4143,4,'#','',NULL,1,0,'F','0','0','product:barcode:export','#','admin','2025-07-27 22:27:29','admin','2025-07-27 22:27:29','',NULL),(4168,'批量生成',4143,5,'#','',NULL,1,0,'F','0','0','product:barcode:batch','#','admin','2025-07-27 22:27:29','admin','2025-07-27 22:27:29','',NULL),(4169,'权限查询',4144,1,'#','',NULL,1,0,'F','0','0','system:permission:query','#','admin','2025-07-27 22:27:42','admin','2025-07-27 22:27:42','',NULL),(4170,'权限分配',4144,2,'#','',NULL,1,0,'F','0','0','system:permission:assign','#','admin','2025-07-27 22:27:42','admin','2025-07-27 22:27:42','',NULL),(4171,'权限回收',4144,3,'#','',NULL,1,0,'F','0','0','system:permission:revoke','#','admin','2025-07-27 22:27:42','admin','2025-07-27 22:27:42','',NULL),(4172,'权限导出',4144,4,'#','',NULL,1,0,'F','0','0','log:permission:export','#','admin','2025-07-27 22:27:42','admin','2025-07-27 22:27:42','',NULL),(4173,'权限审计',4144,5,'#','',NULL,1,0,'F','0','0','system:permission:audit','#','admin','2025-07-27 22:27:42','admin','2025-07-27 22:27:42','',NULL);
/*!40000 ALTER TABLE `sys_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_menu_backup`
--

DROP TABLE IF EXISTS `sys_menu_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_menu_backup` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由参数',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链(0是 1否)',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存(0缓存 1不缓存)',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '菜单类型(M目录 C菜单 F按钮)',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '菜单状态(0显示 1隐藏)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '菜单状态(0正常 1停用)',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `route_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由名称',
  PRIMARY KEY (`menu_id`) USING BTREE,
  KEY `idx_menu_parent_id` (`parent_id`) USING BTREE,
  KEY `idx_menu_status` (`status`) USING BTREE,
  KEY `idx_menu_visible` (`visible`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4133 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='菜单权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu_backup`
--

LOCK TABLES `sys_menu_backup` WRITE;
/*!40000 ALTER TABLE `sys_menu_backup` DISABLE KEYS */;
INSERT INTO `sys_menu_backup` VALUES (9,'权限管理',0,3,'auth','Layout',NULL,1,0,'M','0','0','','lock','admin','2025-05-16 14:06:28','',NULL,'权限管理目录',NULL),(10,'权限定义',9,1,'permission','system/permission/index',NULL,1,0,'C','0','0','system:permission:list','validCode','admin','2025-05-16 14:06:28','',NULL,'权限定义菜单',NULL),(11,'权限模板',9,2,'permission-template','system/permission/template',NULL,1,0,'C','0','0','system:permission:template','form','admin','2025-05-16 14:06:28','',NULL,'权限模板菜单',NULL),(12,'数据权限',9,3,'data-permission','system/permission/dataPermission',NULL,1,0,'C','0','0','system:permission:data','table','admin','2025-05-16 14:06:28','',NULL,'数据权限菜单',NULL),(13,'API权限',9,4,'api-permission','system/permission/apiPermission',NULL,1,0,'C','0','0','system:permission:api','swagger','admin','2025-05-16 14:06:28','',NULL,'API权限菜单',NULL),(14,'仓库管理',0,4,'warehouse','Layout',NULL,1,0,'M','0','0','','warehouse','admin','2025-05-16 14:06:28','',NULL,'仓库管理目录',NULL),(15,'仓库信息',14,1,'info','warehouse/info/index',NULL,1,0,'C','0','0','warehouse:info:list','warehouse','admin','2025-05-16 14:06:28','superadmin','2025-07-21 19:48:50','仓库信息菜单',''),(20,'物品规格',17,3,'spec','product/spec/index',NULL,1,0,'C','0','0','product:spec:list','spec','admin','2025-05-16 14:06:28','',NULL,'物品规格菜单',NULL),(21,'物品单位',17,4,'unit','product/unit/index',NULL,1,0,'C','0','0','product:unit:list','unit','admin','2025-05-16 14:06:28','',NULL,'物品单位菜单',NULL),(22,'二维码管理',0,6,'qrcode','Layout',NULL,1,0,'M','0','0','','code','admin','2025-05-16 14:06:28','',NULL,'二维码管理目录',NULL),(23,'二维码生成',22,1,'generate','qrcode/generate/index',NULL,1,0,'C','0','0','qrcode:generate:list','code','admin','2025-05-16 14:06:28','',NULL,'二维码生成菜单',NULL),(24,'批量生成',22,2,'batch','qrcode/batch/index',NULL,1,0,'C','0','0','qrcode:batch:list','job','admin','2025-05-16 14:06:28','',NULL,'批量生成菜单',NULL),(25,'二维码扫描',22,3,'scan','qrcode/scan/index',NULL,1,0,'C','0','0','qrcode:scan:list','search','admin','2025-05-16 14:06:28','',NULL,'二维码扫描菜单',NULL),(26,'批量扫描',22,4,'batch-scan','qrcode/batchScan/index',NULL,1,0,'C','0','0','qrcode:batchScan:list','eye-open','admin','2025-05-16 14:06:28','',NULL,'批量扫描菜单',NULL),(28,'库存查询',27,1,'stock','inventory/stock/index',NULL,1,0,'C','0','0','inventory:stock:list','search','admin','2025-05-16 14:06:28','',NULL,'库存查询菜单',NULL),(33,'库存预警',27,6,'alert','inventory/alert/index',NULL,1,0,'C','0','0','inventory:alert:list','warning','admin','2025-05-16 14:06:28','',NULL,'库存预警菜单',NULL),(36,'出入库日志',34,2,'stock','log/stock/index',NULL,1,0,'C','0','0','log:stock:list','log','admin','2025-05-16 14:06:28','',NULL,'出入库日志菜单',NULL),(39,'报表统计',0,9,'report','Layout',NULL,1,0,'M','0','0','','chart','admin','2025-05-16 14:06:28','',NULL,'报表统计目录',NULL),(40,'库存报表',39,1,'stock','report/stock/index',NULL,1,0,'C','0','0','inventory:stock:list','documentation','admin','2025-05-16 14:06:29','',NULL,'库存报表菜单',NULL),(41,'入库报表',39,2,'in','report/in/index',NULL,1,0,'C','0','0','report:in:list','upload','admin','2025-05-16 14:06:29','',NULL,'入库报表菜单',NULL),(42,'出库报表',39,3,'out','report/out/index',NULL,1,0,'C','0','0','report:out:list','download','admin','2025-05-16 14:06:29','',NULL,'出库报表菜单',NULL),(1000,'系统管理',0,2,'system','Layout',NULL,1,0,'M','0','0','','system','admin','2025-05-16 14:06:28','',NULL,'系统管理目录',NULL),(1100,'库存管理',0,7,'inventory','Layout',NULL,1,0,'M','0','0','','documentation','admin','2025-05-16 14:06:28','',NULL,'库存管理目录',NULL),(1200,'物品管理',0,5,'product','Layout',NULL,1,0,'M','0','0','','shopping','admin','2025-05-16 14:06:28','',NULL,'物品管理目录',NULL),(1400,'日志管理',0,8,'log','Layout',NULL,1,0,'M','0','0','','log','admin','2025-05-16 14:06:28','',NULL,'日志管理目录',NULL),(2100,'库存预警规则',33,5,'alertrule','inventory/alertrule/index',NULL,1,0,'C','0','0','warehouse:alertrule:list','warning','admin','2025-06-15 22:26:16','',NULL,'库存预警规则菜单',NULL),(2110,'库存预警记录',33,6,'alertlog','inventory/alertlog/index',NULL,1,0,'C','0','0','inventory:alertlog:list','log','admin','2025-06-15 22:26:16','',NULL,'库存预警记录菜单',NULL),(3001,'用户管理',1000,1,'user','system/user/index',NULL,1,0,'C','0','0','system:user:list','user','admin','2025-05-16 14:06:28','',NULL,'用户管理菜单',NULL),(3002,'角色管理',1000,2,'role','system/role/index',NULL,1,0,'C','0','0','system:role:list','peoples','admin','2025-05-16 14:06:28','',NULL,'角色管理菜单',NULL),(3003,'菜单管理',1000,3,'menu','system/menu/index',NULL,1,0,'C','0','0','system:menu:list','tree-table','admin','2025-05-16 14:06:28','',NULL,'菜单管理菜单',NULL),(3004,'部门管理',1000,4,'dept','system/dept/index',NULL,1,0,'C','0','0','system:dept:list','tree','admin','2025-05-16 14:06:28','',NULL,'部门管理菜单',NULL),(3008,'通知公告',1000,5,'notice','system/notice/index',NULL,1,0,'C','0','0','system:notice:list','message','admin','2025-05-16 14:06:28','',NULL,'通知公告菜单',NULL),(3038,'仓库新增',15,2,'#','',NULL,1,0,'F','0','0','system:warehouse:add','plus','admin','2025-05-18 21:06:20','superadmin','2025-07-21 19:48:54','',''),(3039,'仓库编辑',15,3,'#','',NULL,1,0,'F','0','0','system:warehouse:edit','edit','admin','2025-05-18 21:06:20','superadmin','2025-07-21 19:43:29','',''),(3040,'仓库删除',15,4,'#','',NULL,1,0,'F','0','0','system:warehouse:remove','delete','admin','2025-05-18 21:06:20','superadmin','2025-07-21 19:49:02','',''),(3041,'仓库导出',15,6,'#','',NULL,1,0,'F','0','0','system:warehouse:export','download','admin','2025-05-18 21:06:20','superadmin','2025-07-21 19:49:11','',''),(3042,'仓库用户管理',14,2,'user','system/warehouse/user/index',NULL,1,0,'C','0','0','system:warehouse:list','box','admin','2025-05-18 21:06:20','',NULL,'Warehouse User Management Menu',NULL),(3101,'入库管理',1100,2,'in','inventory/in/index',NULL,1,0,'C','0','0','inventory:in:list','upload','admin','2025-05-16 14:06:28','',NULL,'入库管理菜单',NULL),(3102,'出库管理',1100,3,'out','inventory/out/index',NULL,1,0,'C','0','0','inventory:out:list','download','admin','2025-05-16 14:06:28','',NULL,'出库管理菜单',NULL),(3103,'库存调拨',1100,4,'transfer','inventory/transfer/index',NULL,1,0,'C','0','0','inventory:transfer:list','drag','admin','2025-05-16 14:06:28','',NULL,'库存调拨菜单',NULL),(3104,'库存盘点',1100,5,'check','inventory/check/index',NULL,1,0,'C','0','0','inventory:check:list','checkbox','admin','2025-05-16 14:06:28','',NULL,'库存盘点菜单',NULL),(3201,'物品信息',1200,2,'info','product/info/index',NULL,1,0,'C','0','0','product:info:list','shopping','admin','2025-05-16 14:06:28','',NULL,'物品信息菜单',NULL),(3202,'物品分类',1200,1,'category','product/category/index',NULL,1,0,'C','0','0','product:category:list','category','admin','2025-05-16 14:06:28','',NULL,'物品分类菜单',NULL),(3402,'系统日志',1400,3,'system','log/system/index',NULL,1,0,'C','0','0','monitor:logininfor:list','log','admin','2025-05-16 14:06:28','',NULL,'系统日志菜单',NULL),(3403,'操作日志',1400,1,'operation','log/operation/index',NULL,1,0,'C','0','0','monitor:operlog:list','log','admin','2025-05-16 14:06:28','',NULL,'操作日志菜单',NULL),(3406,'日志权限',1400,4,'permission','log/permission/index',NULL,1,0,'C','0','0','log:permission:list','log','admin','2025-05-16 14:06:28','',NULL,'日志权限菜单',NULL),(3784,'审计日志',34,1,'/audit-log','log/operation/index',NULL,1,0,'C','0','0','log:audit','audit','',NULL,'',NULL,'',NULL),(3785,'数据日志',34,2,'/data-log','log/data/index',NULL,1,0,'C','0','0','log:data','data','',NULL,'',NULL,'',NULL),(3786,'错误日志',34,3,'/error-log','log/error/index',NULL,1,0,'C','0','0','log:error','error','',NULL,'',NULL,'',NULL),(3787,'登录日志',34,4,'/login-log','log/system/index',NULL,1,0,'C','0','0','log:login','login','',NULL,'',NULL,'',NULL),(3788,'安全日志',34,5,'/security-log','log/security/index',NULL,1,0,'C','0','0','log:security','security','',NULL,'',NULL,'',NULL),(3792,'仓库导入',15,5,'',NULL,NULL,1,0,'F','0','0','system:warehouse:import','#','system','2025-07-21 11:42:49','superadmin','2025-07-21 19:49:06','',''),(3859,'新增',20,1,'',NULL,NULL,1,0,'F','0','0','product:spec:add','#','system','2025-07-21 13:00:29','',NULL,'',NULL),(3860,'编辑',20,2,'',NULL,NULL,1,0,'F','0','0','product:spec:edit','#','system','2025-07-21 13:00:29','',NULL,'',NULL),(3861,'删除',20,3,'',NULL,NULL,1,0,'F','0','0','product:spec:remove','#','system','2025-07-21 13:00:29','',NULL,'',NULL),(3862,'导出',20,4,'',NULL,NULL,1,0,'F','0','0','product:spec:export','#','system','2025-07-21 13:00:29','',NULL,'',NULL),(3863,'新增',21,1,'',NULL,NULL,1,0,'F','0','0','product:unit:add','#','system','2025-07-21 13:00:38','',NULL,'',NULL),(3864,'编辑',21,2,'',NULL,NULL,1,0,'F','0','0','product:unit:edit','#','system','2025-07-21 13:00:38','',NULL,'',NULL),(3865,'删除',21,3,'',NULL,NULL,1,0,'F','0','0','product:unit:remove','#','system','2025-07-21 13:00:38','',NULL,'',NULL),(3866,'导出',21,4,'',NULL,NULL,1,0,'F','0','0','product:unit:export','#','system','2025-07-21 13:00:38','',NULL,'',NULL),(3893,'新增',28,1,'',NULL,NULL,1,0,'F','0','0','inventory:stock:add','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3894,'修改',28,2,'',NULL,NULL,1,0,'F','0','0','inventory:stock:edit','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3895,'删除',28,3,'',NULL,NULL,1,0,'F','0','0','inventory:stock:remove','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3896,'导出',28,4,'',NULL,NULL,1,0,'F','0','0','inventory:stock:export','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3897,'报表',28,5,'',NULL,NULL,1,0,'F','0','0','inventory:stock:list','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3898,'预警报表',28,6,'',NULL,NULL,1,0,'F','0','0','inventory:stock:alert','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3899,'阈值设置',28,7,'',NULL,NULL,1,0,'F','0','0','inventory:stock:threshold','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3900,'高级分析',28,8,'',NULL,NULL,1,0,'F','0','0','inventory:stock:analysis','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3901,'物品库存',28,9,'',NULL,NULL,1,0,'F','0','0','inventory:stock:product','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3902,'查看',28,10,'',NULL,NULL,1,0,'F','0','0','inventory:stock:query','#','system','2025-07-21 20:12:55','',NULL,'',NULL),(3960,'新增',40,1,'',NULL,NULL,1,0,'F','0','0','report:stock:add','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3961,'修改',40,2,'',NULL,NULL,1,0,'F','0','0','report:stock:edit','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3962,'删除',40,3,'',NULL,NULL,1,0,'F','0','0','report:stock:remove','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3963,'导出',40,4,'',NULL,NULL,1,0,'F','0','0','report:stock:export','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3964,'报表',40,5,'',NULL,NULL,1,0,'F','0','0','report:stock:list','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3965,'新增',41,1,'',NULL,NULL,1,0,'F','0','0','report:in:add','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3966,'修改',41,2,'',NULL,NULL,1,0,'F','0','0','report:in:edit','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3967,'删除',41,3,'',NULL,NULL,1,0,'F','0','0','report:in:remove','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3968,'导出',41,4,'',NULL,NULL,1,0,'F','0','0','report:in:export','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3969,'报表',41,5,'',NULL,NULL,1,0,'F','0','0','report:in:list','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3970,'新增',42,1,'',NULL,NULL,1,0,'F','0','0','report:out:add','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3971,'修改',42,2,'',NULL,NULL,1,0,'F','0','0','report:out:edit','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3972,'删除',42,3,'',NULL,NULL,1,0,'F','0','0','report:out:remove','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3973,'导出',42,4,'',NULL,NULL,1,0,'F','0','0','report:out:export','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(3974,'报表',42,5,'',NULL,NULL,1,0,'F','0','0','report:out:list','#','system','2025-07-21 22:47:45','',NULL,'',NULL),(4006,'入库查询',3101,1,'',NULL,NULL,1,0,'F','0','0','inventory:in:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4007,'入库新增',3101,2,'',NULL,NULL,1,0,'F','0','0','inventory:in:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4008,'入库修改',3101,3,'',NULL,NULL,1,0,'F','0','0','inventory:in:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4009,'入库删除',3101,4,'',NULL,NULL,1,0,'F','0','0','inventory:in:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4010,'入库导出',3101,5,'',NULL,NULL,1,0,'F','0','0','inventory:in:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4011,'入库审核',3101,6,'',NULL,NULL,1,0,'F','0','0','inventory:in:audit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4012,'入库打印',3101,7,'',NULL,NULL,1,0,'F','0','0','inventory:in:print','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4013,'扫码入库',3101,8,'',NULL,NULL,1,0,'F','0','0','inventory:in:scan','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4014,'批量入库',3101,9,'',NULL,NULL,1,0,'F','0','0','inventory:in:batch','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4015,'出库查询',3102,1,'',NULL,NULL,1,0,'F','0','0','inventory:out:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4016,'出库新增',3102,2,'',NULL,NULL,1,0,'F','0','0','inventory:out:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4017,'出库修改',3102,3,'',NULL,NULL,1,0,'F','0','0','inventory:out:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4018,'出库删除',3102,4,'',NULL,NULL,1,0,'F','0','0','inventory:out:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4019,'出库导出',3102,5,'',NULL,NULL,1,0,'F','0','0','inventory:out:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4020,'出库审核',3102,6,'',NULL,NULL,1,0,'F','0','0','inventory:out:audit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4021,'出库打印',3102,7,'',NULL,NULL,1,0,'F','0','0','inventory:out:print','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4022,'扫码出库',3102,8,'',NULL,NULL,1,0,'F','0','0','inventory:out:scan','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4023,'批量出库',3102,9,'',NULL,NULL,1,0,'F','0','0','inventory:out:batch','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4024,'调拨查询',3103,1,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4025,'调拨新增',3103,2,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4026,'调拨修改',3103,3,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4027,'调拨删除',3103,4,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4028,'调拨导出',3103,5,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4029,'调拨审核',3103,6,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:audit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4030,'调拨打印',3103,7,'',NULL,NULL,1,0,'F','0','0','inventory:transfer:print','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4031,'盘点查询',3104,1,'',NULL,NULL,1,0,'F','0','0','inventory:check:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4032,'盘点新增',3104,2,'',NULL,NULL,1,0,'F','0','0','inventory:check:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4033,'盘点修改',3104,3,'',NULL,NULL,1,0,'F','0','0','inventory:check:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4034,'盘点删除',3104,4,'',NULL,NULL,1,0,'F','0','0','inventory:check:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4035,'盘点导出',3104,5,'',NULL,NULL,1,0,'F','0','0','inventory:check:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4036,'盘点审核',3104,6,'',NULL,NULL,1,0,'F','0','0','inventory:check:audit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4037,'盘点打印',3104,7,'',NULL,NULL,1,0,'F','0','0','inventory:check:print','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4038,'加载库存',3104,8,'',NULL,NULL,1,0,'F','0','0','inventory:check:load','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4049,'物品查询',3201,1,'',NULL,NULL,1,0,'F','0','0','product:info:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4050,'物品新增',3201,2,'',NULL,NULL,1,0,'F','0','0','product:info:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4051,'物品修改',3201,3,'',NULL,NULL,1,0,'F','0','0','product:info:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4052,'物品删除',3201,4,'',NULL,NULL,1,0,'F','0','0','product:info:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4053,'物品导出',3201,5,'',NULL,NULL,1,0,'F','0','0','product:info:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4054,'物品导入',3201,6,'',NULL,NULL,1,0,'F','0','0','product:info:import','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4055,'分类查询',3202,1,'',NULL,NULL,1,0,'F','0','0','product:category:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4056,'分类新增',3202,2,'',NULL,NULL,1,0,'F','0','0','product:category:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4057,'分类修改',3202,3,'',NULL,NULL,1,0,'F','0','0','product:category:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4058,'分类删除',3202,4,'',NULL,NULL,1,0,'F','0','0','product:category:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4059,'分类导出',3202,5,'',NULL,NULL,1,0,'F','0','0','product:category:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4065,'系统日志查询',3402,1,'',NULL,NULL,1,0,'F','0','0','monitor:logininfor:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4066,'系统日志删除',3402,2,'',NULL,NULL,1,0,'F','0','0','monitor:logininfor:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4067,'系统日志导出',3402,3,'',NULL,NULL,1,0,'F','0','0','monitor:logininfor:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4068,'账户解锁',3402,4,'',NULL,NULL,1,0,'F','0','0','monitor:logininfor:unlock','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4069,'操作日志查询',3403,1,'',NULL,NULL,1,0,'F','0','0','monitor:operlog:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4070,'操作日志删除',3403,2,'',NULL,NULL,1,0,'F','0','0','monitor:operlog:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4071,'操作日志导出',3403,3,'',NULL,NULL,1,0,'F','0','0','monitor:operlog:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4072,'操作日志详细',3403,4,'',NULL,NULL,1,0,'F','0','0','monitor:operlog:detail','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4079,'权限日志查询',3406,1,'',NULL,NULL,1,0,'F','0','0','log:permission:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4080,'权限日志删除',3406,2,'',NULL,NULL,1,0,'F','0','0','log:permission:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4081,'权限日志导出',3406,3,'',NULL,NULL,1,0,'F','0','0','log:permission:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4091,'用户查询',3001,1,'',NULL,NULL,1,0,'F','0','0','system:user:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4092,'用户新增',3001,2,'',NULL,NULL,1,0,'F','0','0','system:user:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4093,'用户修改',3001,3,'',NULL,NULL,1,0,'F','0','0','system:user:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4094,'用户删除',3001,4,'',NULL,NULL,1,0,'F','0','0','system:user:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4095,'用户导出',3001,5,'',NULL,NULL,1,0,'F','0','0','system:user:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4096,'用户导入',3001,6,'',NULL,NULL,1,0,'F','0','0','system:user:import','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4097,'重置密码',3001,7,'',NULL,NULL,1,0,'F','0','0','system:user:resetPwd','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4098,'分配角色',3001,8,'',NULL,NULL,1,0,'F','0','0','system:user:role','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4099,'角色查询',3002,1,'',NULL,NULL,1,0,'F','0','0','system:role:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4100,'角色新增',3002,2,'',NULL,NULL,1,0,'F','0','0','system:role:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4101,'角色修改',3002,3,'',NULL,NULL,1,0,'F','0','0','system:role:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4102,'角色删除',3002,4,'',NULL,NULL,1,0,'F','0','0','system:role:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4103,'角色导出',3002,5,'',NULL,NULL,1,0,'F','0','0','system:role:export','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4104,'分配权限',3002,6,'',NULL,NULL,1,0,'F','0','0','system:role:auth','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4105,'菜单查询',3003,1,'',NULL,NULL,1,0,'F','0','0','system:menu:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4106,'菜单新增',3003,2,'',NULL,NULL,1,0,'F','0','0','system:menu:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4107,'菜单修改',3003,3,'',NULL,NULL,1,0,'F','0','0','system:menu:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4108,'菜单删除',3003,4,'',NULL,NULL,1,0,'F','0','0','system:menu:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4109,'部门查询',3004,1,'',NULL,NULL,1,0,'F','0','0','system:dept:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4110,'部门新增',3004,2,'',NULL,NULL,1,0,'F','0','0','system:dept:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4111,'部门修改',3004,3,'',NULL,NULL,1,0,'F','0','0','system:dept:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4112,'部门删除',3004,4,'',NULL,NULL,1,0,'F','0','0','system:dept:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4129,'公告查询',3008,1,'',NULL,NULL,1,0,'F','0','0','system:notice:query','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4130,'公告新增',3008,2,'',NULL,NULL,1,0,'F','0','0','system:notice:add','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4131,'公告修改',3008,3,'',NULL,NULL,1,0,'F','0','0','system:notice:edit','#','admin','2025-07-26 21:43:56','',NULL,'',NULL),(4132,'公告删除',3008,4,'',NULL,NULL,1,0,'F','0','0','system:notice:remove','#','admin','2025-07-26 21:43:56','',NULL,'',NULL);
/*!40000 ALTER TABLE `sys_menu_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_notice`
--

DROP TABLE IF EXISTS `sys_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_notice` (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT 'Notice ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Notice Title',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Notice Type (1:Notification 2:Announcement)',
  `notice_content` longblob COMMENT 'Notice Content',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT 'Status (0:Normal 1:Closed)',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'Created By',
  `create_time` datetime DEFAULT NULL COMMENT 'Create Time',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'Updated By',
  `update_time` datetime DEFAULT NULL COMMENT 'Update Time',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Remark',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='通知公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_notice`
--

LOCK TABLES `sys_notice` WRITE;
/*!40000 ALTER TABLE `sys_notice` DISABLE KEYS */;
INSERT INTO `sys_notice` VALUES (1,'欢迎使用万裕物业仓库管理系统','2',_binary '<p>高效管理您的仓库和物品信息，于2025年7月14日正式开放使用。</p>','0','admin','2023-07-01 11:33:00','superadmin','2025-07-14 22:41:46','admin'),(2,'欢迎使用万裕物业仓库管理系统','1',_binary '<p>高效管理您的仓库和物品信息，于2025年7月14日正式开放使用。</p>','0','admin','2023-07-01 11:33:00','superadmin','2025-07-14 22:41:42','admin'),(3,'欢迎使用万裕物业仓库管理系统','2',_binary '<p>高效管理您的仓库和物品信息，于2025年7月14日正式开放使用。</p>','0','superadmin','2025-07-14 21:49:25','superadmin','2025-07-14 22:41:38',NULL),(4,'欢迎使用万裕物业仓库管理系统','1',_binary '<p>高效管理您的仓库和物品信息，于2025年7月14日正式开放使用。</p>','0','superadmin','2025-07-14 22:20:04','superadmin','2025-07-14 22:41:32',NULL);
/*!40000 ALTER TABLE `sys_notice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_oper_log`
--

DROP TABLE IF EXISTS `sys_oper_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_oper_log` (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '鏃ュ織涓婚敭',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '妯″潡鏍囬?',
  `business_type` int DEFAULT '0' COMMENT '涓氬姟绫诲瀷锛?鍏跺畠 1鏂板? 2淇?敼 3鍒犻櫎锛',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鏂规硶鍚嶇О',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '璇锋眰鏂瑰紡',
  `operator_type` int DEFAULT '0' COMMENT '鎿嶄綔绫诲埆锛?鍏跺畠 1鍚庡彴鐢ㄦ埛 2鎵嬫満绔?敤鎴凤級',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鎿嶄綔浜哄憳',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '閮ㄩ棬鍚嶇О',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '璇锋眰URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '涓绘満鍦板潃',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '鎿嶄綔鍦扮偣',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '璇锋眰鍙傛暟',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '杩斿洖鍙傛暟',
  `status` int DEFAULT '0' COMMENT '鎿嶄綔鐘舵?锛?姝ｅ父 1寮傚父锛',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '閿欒?娑堟伅',
  `cost_time` bigint DEFAULT '0' COMMENT '娑堣?鏃堕棿',
  `oper_time` datetime DEFAULT NULL COMMENT '鎿嶄綔鏃堕棿',
  PRIMARY KEY (`oper_id`) USING BTREE,
  KEY `idx_oper_log_time` (`oper_time`) USING BTREE,
  KEY `idx_oper_log_name` (`oper_name`) USING BTREE,
  KEY `idx_oper_log_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='操作日志记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_oper_log`
--

LOCK TABLES `sys_oper_log` WRITE;
/*!40000 ALTER TABLE `sys_oper_log` DISABLE KEYS */;
INSERT INTO `sys_oper_log` VALUES (1,'操作日志',9,'com.wanyu.web.controller.monitor.SysOperlogController.clean()','DELETE',1,'superadmin','万裕物业','/monitor/operlog/clean','127.0.0.1','内网IP','','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,21,'2025-07-26 21:20:37'),(2,'安全日志',3,'com.wanyu.web.controller.system.SysSecurityLogController.remove()','DELETE',1,'superadmin','万裕物业','/system/security/1,2,3','127.0.0.1','内网IP','[1,2,3]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,4,'2025-07-26 21:44:33'),(3,'错误日志',9,'com.wanyu.web.controller.log.ErrorLogController.clean()','DELETE',1,'superadmin','万裕物业','/api/v1/logs/error/clean','127.0.0.1','内网IP','','{\"msg\":\"清空成功\",\"code\":200}',0,NULL,0,'2025-07-26 21:47:56'),(4,'库存信息',1,'com.wanyu.web.controller.inventory.WmsInventoryController.add()','POST',1,'superadmin','万裕物业','/inventory/stock','127.0.0.1','内网IP','{\"inventoryId\":268,\"maxQuantity\":6,\"minQuantity\":1,\"params\":{},\"price\":null,\"productId\":201,\"quantity\":2,\"status\":\"0\",\"warehouseId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,41,'2025-07-26 21:52:33'),(5,'库存信息',1,'com.wanyu.web.controller.inventory.WmsInventoryController.add()','POST',1,'superadmin','万裕物业','/inventory/stock','127.0.0.1','内网IP','{\"inventoryId\":269,\"maxQuantity\":11,\"minQuantity\":2,\"params\":{},\"price\":null,\"productId\":201,\"quantity\":3,\"status\":\"0\",\"warehouseId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,5,'2025-07-26 21:52:56'),(6,'入库单',1,'com.wanyu.web.controller.inventory.WmsInventoryInController.add()','POST',1,'superadmin','万裕物业','/inventory/in','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 21:56:19\",\"details\":[{\"amount\":6,\"inId\":268,\"params\":{},\"price\":2,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"inTime\":\"2025-07-26 21:56:19\",\"inType\":\"1\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,41,'2025-07-26 21:56:19'),(7,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:56:31\",\"details\":[{\"amount\":6,\"detailId\":278,\"inId\":268,\"params\":{},\"price\":2,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:56:31\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',7,'2025-07-26 21:56:31'),(8,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:56:33\",\"details\":[{\"amount\":6,\"detailId\":278,\"inId\":268,\"params\":{},\"price\":2,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:56:33\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',9,'2025-07-26 21:56:33'),(9,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:56:34\",\"details\":[{\"amount\":6,\"detailId\":278,\"inId\":268,\"params\":{},\"price\":2,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:56:34\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',8,'2025-07-26 21:56:34'),(10,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:56:42\",\"details\":[{\"amount\":6,\"detailId\":278,\"inId\":268,\"params\":{},\"price\":2,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:56:42\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',8,'2025-07-26 21:56:42'),(11,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:56:54\",\"details\":[{\"amount\":6,\"detailId\":278,\"inId\":268,\"params\":{},\"price\":2,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:56:54\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',6,'2025-07-26 21:56:54'),(12,'入库单',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.edit()','PUT',1,'superadmin','万裕物业','/inventory/in','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 21:56:19\",\"details\":[{\"amount\":3,\"detailId\":278,\"inId\":268,\"params\":{},\"price\":1,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"inTime\":\"2025-07-26 21:56:19\",\"inType\":\"2\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:57:19\",\"warehouseId\":1,\"warehouseName\":\"万裕物业总仓库\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,12,'2025-07-26 21:57:19'),(13,'入库单',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.edit()','PUT',1,'superadmin','万裕物业','/inventory/in','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 21:56:19\",\"details\":[{\"amount\":9,\"detailId\":279,\"inId\":268,\"params\":{},\"price\":3,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"inTime\":\"2025-07-26 21:56:19\",\"inType\":\"2\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:57:33\",\"warehouseId\":1,\"warehouseName\":\"万裕物业总仓库\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,9,'2025-07-26 21:57:33'),(14,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:57:39\",\"details\":[{\"amount\":9,\"detailId\":280,\"inId\":268,\"params\":{},\"price\":3,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:57:39\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',8,'2025-07-26 21:57:39'),(15,'入库单',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.edit()','PUT',1,'superadmin','万裕物业','/inventory/in','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 21:56:19\",\"details\":[{\"amount\":0,\"detailId\":280,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"inTime\":\"2025-07-26 21:56:19\",\"inType\":\"2\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:57:55\",\"warehouseId\":1,\"warehouseName\":\"万裕物业总仓库\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,8,'2025-07-26 21:57:55'),(16,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:57:59\",\"details\":[{\"amount\":0,\"detailId\":281,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":3}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:57:59\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',6,'2025-07-26 21:57:59'),(17,'入库单',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.edit()','PUT',1,'superadmin','万裕物业','/inventory/in','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 21:56:19\",\"details\":[{\"amount\":0,\"detailId\":281,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":2}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"inTime\":\"2025-07-26 21:56:19\",\"inType\":\"2\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:58:09\",\"warehouseId\":1,\"warehouseName\":\"万裕物业总仓库\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,9,'2025-07-26 21:58:09'),(18,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:58:13\",\"details\":[{\"amount\":0,\"detailId\":282,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":2}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:58:13\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',7,'2025-07-26 21:58:13'),(19,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:58:40\",\"details\":[{\"amount\":0,\"detailId\":282,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":2}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:58:40\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',6,'2025-07-26 21:58:40'),(20,'入库单',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.edit()','PUT',1,'superadmin','万裕物业','/inventory/in','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 21:56:19\",\"details\":[{\"amount\":0,\"detailId\":282,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":2}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"inTime\":\"2025-07-26 21:56:19\",\"inType\":\"3\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:58:49\",\"warehouseId\":1,\"warehouseName\":\"万裕物业总仓库\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,9,'2025-07-26 21:58:49'),(21,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:58:53\",\"details\":[{\"amount\":0,\"detailId\":283,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":2}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:58:53\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',11,'2025-07-26 21:58:53'),(22,'入库单',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.edit()','PUT',1,'superadmin','万裕物业','/inventory/in','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 21:56:19\",\"details\":[{\"amount\":0,\"detailId\":283,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":2}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"inTime\":\"2025-07-26 21:56:19\",\"inType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:59:16\",\"warehouseId\":1,\"warehouseName\":\"万裕物业总仓库\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,10,'2025-07-26 21:59:16'),(23,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 21:59:20\",\"details\":[{\"amount\":0,\"detailId\":284,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":2}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 21:59:20\"}',NULL,1,'nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2',6,'2025-07-26 21:59:20'),(24,'库存信息',3,'com.wanyu.web.controller.inventory.WmsInventoryController.remove()','DELETE',1,'superadmin','万裕物业','/inventory/stock/268,269','127.0.0.1','内网IP','[268,269]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,4,'2025-07-26 22:01:06'),(25,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 22:01:13\",\"details\":[{\"amount\":0,\"detailId\":284,\"inId\":268,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":2}],\"inCode\":\"IN20250726215619\",\"inId\":268,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 22:01:13\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,802,'2025-07-26 22:01:14'),(26,'库存调拨',1,'com.wanyu.web.controller.inventory.WmsInventoryTransferController.add()','POST',1,'superadmin','万裕物业','/inventory/transfer','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 22:04:45\",\"details\":[{\"params\":{},\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":1,\"transferId\":215}],\"fromWarehouseId\":1,\"params\":{},\"status\":\"0\",\"toWarehouseId\":2,\"transferCode\":\"TR20250726220445\",\"transferId\":215,\"transferTime\":\"2025-07-26 22:04:45\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,21,'2025-07-26 22:04:45'),(27,'库存盘点',1,'com.wanyu.web.controller.inventory.WmsInventoryCheckController.add()','POST',1,'superadmin','万裕物业','/inventory/check','127.0.0.1','内网IP','{\"checkCode\":\"CK20250726220456\",\"checkId\":207,\"checkTime\":\"2025-07-26 22:04:51\",\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 22:04:56\",\"details\":[{\"bookQuantity\":2,\"checkId\":207,\"diffQuantity\":0,\"params\":{},\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"realQuantity\":2}],\"params\":{},\"status\":\"0\",\"warehouseId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,18,'2025-07-26 22:04:56'),(28,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-26 22:07:41\",\"details\":[{\"amount\":0,\"outId\":238,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":1}],\"outCode\":\"OUT20250726220741\",\"outId\":238,\"outTime\":\"2025-07-26 22:07:41\",\"outType\":\"3\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,20,'2025-07-26 22:07:41'),(29,'出库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryOutController.audit()','PUT',1,'superadmin','万裕物业','/inventory/out/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-26 22:07:50\",\"details\":[{\"amount\":0,\"detailId\":244,\"outId\":238,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":1}],\"outCode\":\"OUT20250726220741\",\"outId\":238,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 22:07:50\",\"warehouseId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,21,'2025-07-26 22:07:50'),(30,'库存调拨审核',2,'com.wanyu.web.controller.inventory.WmsInventoryTransferController.audit()','PUT',1,'superadmin','万裕物业','/inventory/transfer/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditTime\":\"2025-07-26 22:10:00\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"transferCode\":\"TR20250726220445\",\"transferId\":215,\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-26 22:10:00\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,39,'2025-07-26 22:10:00'),(31,'物品周转率分析',0,'com.wanyu.web.controller.api.v1.report.InventoryAnalysisController.getTurnoverRate()','GET',1,'superadmin','万裕物业','/api/v1/reports/analysis/turnover','127.0.0.1','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200,\"data\":[]}',0,NULL,5,'2025-07-26 22:46:06'),(32,'安全日志',3,'com.wanyu.web.controller.system.SysSecurityLogController.remove()','DELETE',1,'superadmin','万裕物业','/system/security/1,2,3','127.0.0.1','内网IP','[1,2,3]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,4,'2025-07-26 22:56:57'),(33,'库存明细报表',5,'com.wanyu.web.controller.api.v1.report.ReportExportController.exportInventoryDetailExcel()','GET',1,'superadmin','万裕物业','/api/v1/reports/export/inventory/detail/excel','127.0.0.1','内网IP','{\"fileName\":\"库存明细报表\",\"columns\":\"productCode,productName,warehouseName,specification,unit,quantity,minQuantity,maxQuantity,status,price,totalValue\"}',NULL,0,NULL,798,'2025-07-27 19:13:55'),(34,'二维码管理',1,'com.wanyu.web.controller.system.SysQrCodeController.batchGenerate()','POST',1,'superadmin','万裕物业','/system/qrcode/batchGenerate','127.0.0.1','内网IP','[\"矿泉水(WATER001)\",\"可乐(COLA001)\",\"笔记本电脑(LAPTOP001)\",\"办公桌(DESK001)\",\"签字笔(PEN001)\"]','{\"msg\":\"操作成功\",\"code\":200,\"data\":[{\"qrcode\":\"iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAIAAAD2HxkiAAAF70lEQVR42u3asYHDMAwEQfXftL+BD6nTAZzNZUskhhGfn6RPeyyBBKEEoSQIJQglQShBKAlCCUJJEEoQSoJQglAShBKEkiCUIJQEoQTh/09O7vgnD9jp+pe/cKIghBBCCCGEEEIIIYQQQgghhNCSQQghhBBCCKElgxBCCCGEEMKrNrX/NaYMkGMCQgghhNCmQgghhBBCCKFNhRBCCCGEEEKbCiGEEEIIIYQ2FUII+xEmB2j0BZfja5jcShMFIYQQQgghhCYKQgghhBBCCE0UhBBCCCGEEJooCCGEEEIIITRREAYQPh31SzNREEIIIYQQQmiiIIQQQgghhNBEQQghhBBCCKGJghBCCCGEEEITBeGpN9xKNxmElgxCCCGEEEIILRmEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgjhS0vWv6mjK5njqhHvnygIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQwiMv1F9yjj1VeBZU3TqC0IhDCCGEnoIQQjAghBBCT0EIIRgQQgihpyCEEAwIIYTQUxCqBHx+FDILZcAghBBCCCGEUBBCCCGEEEIoCCGEEEIIIRSEEEIIIYQQKo8wuXP9nI5/V3K/SgZgAXgIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQwk6EJWPX/xrJRh+1VccfhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCOHbAzRa9dYR7/+u0cc6hBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCOFBhP0DdOGdngtvsZQc6xBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEnyMc7ZO0WYdmycpDCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEGYQjl6XkmEdfX8ouRolLw8hhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBC+DnC0Vc6Lnz5rSfI6DMOQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhLD50snop/pve5SsxuiJghBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCHMIOwfuwt9jr4/dOFRCyGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEL4OaeS/9qKsH/E89NfMtgQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhJumP/mGF97OSa5hv08IIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQwgzCkv3un8jRc9z/yf2HJoQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQghhBmHy40tuYIy+CPIs7TckCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIlfSZ/sP+prasBIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQngQ4da7FKP/q//lkzCmXMGBEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIfzwIkj/3Zf+Adp6SWjrykNoFCCEEEIIIYTQKEAIIYQQQgghhBBCCCGEEEIIIYQQQgghhPs+fvSw9l9wSX7XgmsxEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQ9kvrH/Gtd1/yPiGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIczvQbPq0TdmSo4JCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEMLPEZYMa/8POuMKhw1CCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEsBlhfyWf',0,NULL,298,'2025-07-27 22:51:18'),(35,'用户管理',4,'com.wanyu.web.controller.system.SysUserController.insertAuthRole()','PUT',1,'superadmin','万裕物业','/system/user/authRole','127.0.0.1','内网IP','{\"roleIds\":\"1,2,3,4,5,6,7,8\",\"userId\":\"2\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,91,'2025-07-27 23:06:08'),(36,'用户数据权限',4,'com.wanyu.web.controller.system.SysDataPermissionController.assignUserDataPermission()','PUT',1,'superadmin','万裕物业','/system/data/permission/user/assign','127.0.0.1','内网IP','{\"params\":{},\"permIds\":[],\"userIds\":[2]}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,64,'2025-07-27 23:06:27'),(37,'用户数据权限',4,'com.wanyu.web.controller.system.SysDataPermissionController.assignUserDataPermission()','PUT',1,'superadmin','万裕物业','/system/data/permission/user/assign','127.0.0.1','内网IP','{\"params\":{},\"permIds\":[1,2,3,4,5,6,7],\"userIds\":[2]}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,18,'2025-07-27 23:06:44'),(38,'用户数据权限',4,'com.wanyu.web.controller.system.SysDataPermissionController.assignUserDataPermission()','PUT',1,'superadmin','万裕物业','/system/data/permission/user/assign','127.0.0.1','内网IP','{\"params\":{},\"permIds\":[1,2,3,4,5,6,7],\"userIds\":[2]}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,25,'2025-07-27 23:06:54'),(39,'库存信息',1,'com.wanyu.web.controller.inventory.WmsInventoryController.add()','POST',1,'superadmin','万裕物业','/inventory/stock','127.0.0.1','内网IP','{\"inventoryId\":272,\"maxQuantity\":8,\"minQuantity\":1,\"params\":{},\"price\":null,\"productId\":202,\"quantity\":2,\"status\":\"0\",\"warehouseId\":5}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,339,'2025-07-28 10:07:39'),(40,'库存',5,'com.wanyu.web.controller.api.v1.inventory.StocksApiController.export()','GET',1,'superadmin','万裕物业','/api/v1/inventory/stocks/export','127.0.0.1','内网IP','{\"pageSize\":\"10\",\"pageNum\":\"1\"}',NULL,0,NULL,1163,'2025-07-28 10:07:49'),(41,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":7}',NULL,1,'商品[矿泉水]库存不足，当前库存：0，需要出库：1',75,'2025-07-28 10:08:40'),(42,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"COLA001\",\"productId\":202,\"productName\":\"可乐\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":7}',NULL,1,'商品[可乐]库存不足，当前库存：0，需要出库：1',9,'2025-07-28 10:08:45'),(43,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"COLA001\",\"productId\":202,\"productName\":\"可乐\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":1}',NULL,1,'商品[可乐]库存不足，当前库存：0，需要出库：1',8,'2025-07-28 10:08:59'),(44,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":1}',NULL,1,'商品[矿泉水]库存不足，当前库存：0.00，需要出库：1',32,'2025-07-28 10:09:03'),(45,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"LAPTOP001\",\"productId\":203,\"productName\":\"笔记本电脑\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":1}',NULL,1,'商品[笔记本电脑]库存不足，当前库存：0，需要出库：1',26,'2025-07-28 10:09:06'),(46,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"DESK001\",\"productId\":204,\"productName\":\"办公桌\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":1}',NULL,1,'商品[办公桌]库存不足，当前库存：0，需要出库：1',14,'2025-07-28 10:09:09'),(47,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"PEN001\",\"productId\":205,\"productName\":\"签字笔\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":1}',NULL,1,'商品[签字笔]库存不足，当前库存：0，需要出库：1',16,'2025-07-28 10:09:13'),(48,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"PEN001\",\"productId\":205,\"productName\":\"签字笔\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":3}',NULL,1,'商品[签字笔]库存不足，当前库存：0，需要出库：1',15,'2025-07-28 10:09:20'),(49,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":3}',NULL,1,'商品[矿泉水]库存不足，当前库存：0，需要出库：1',5,'2025-07-28 10:09:23'),(50,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"details\":[{\"amount\":0,\"params\":{},\"price\":0,\"productCode\":\"COLA001\",\"productId\":202,\"productName\":\"可乐\",\"quantity\":1}],\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":3}',NULL,1,'商品[可乐]库存不足，当前库存：0，需要出库：1',7,'2025-07-28 10:09:26'),(51,'出库单',1,'com.wanyu.web.controller.inventory.WmsInventoryOutController.add()','POST',1,'superadmin','万裕物业','/inventory/out','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-28 10:10:00\",\"details\":[{\"amount\":0,\"outId\":239,\"params\":{},\"price\":0,\"productCode\":\"COLA001\",\"productId\":202,\"productName\":\"可乐\",\"quantity\":1}],\"outCode\":\"OUT20250728101000\",\"outId\":239,\"outTime\":\"2025-07-28 10:10:00\",\"outType\":\"4\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":5}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,29,'2025-07-28 10:10:00'),(52,'出库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryOutController.audit()','PUT',1,'superadmin','万裕物业','/inventory/out/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-28 10:10:05\",\"details\":[{\"amount\":0,\"detailId\":245,\"outId\":239,\"params\":{},\"price\":0,\"productCode\":\"COLA001\",\"productId\":202,\"productName\":\"可乐\",\"quantity\":1}],\"outCode\":\"OUT20250728101000\",\"outId\":239,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-28 10:10:05\",\"warehouseId\":5}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,2613,'2025-07-28 10:10:08'),(53,'出库明细报表',5,'com.wanyu.web.controller.api.v1.report.ReportExportController.exportOutDetailExcel()','GET',1,'superadmin','万裕物业','/api/v1/reports/export/out/detail/excel','127.0.0.1','内网IP','{\"warehouseId\":\"\",\"outCode\":\"\",\"columns\":\"outCode,warehouseName,outTypeName,productName,productCode,quantity,price,amount,outTime,operateBy,status,auditBy,auditTime\",\"pageSize\":\"10\",\"pageNum\":\"1\",\"outType\":\"\",\"status\":\"\"}',NULL,0,NULL,108,'2025-07-28 10:10:13'),(54,'入库单',1,'com.wanyu.web.controller.inventory.WmsInventoryInController.add()','POST',1,'superadmin','万裕物业','/inventory/in','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-28 10:10:58\",\"details\":[{\"amount\":56,\"inId\":269,\"params\":{},\"price\":4,\"productCode\":\"PEN001\",\"productId\":205,\"productName\":\"签字笔\",\"quantity\":14}],\"inCode\":\"IN20250728101058\",\"inId\":269,\"inTime\":\"2025-07-28 10:10:58\",\"inType\":\"1\",\"params\":{},\"price\":null,\"quantity\":null,\"status\":\"0\",\"warehouseId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,42,'2025-07-28 10:10:58'),(55,'入库单审核',2,'com.wanyu.web.controller.inventory.WmsInventoryInController.audit()','PUT',1,'superadmin','万裕物业','/inventory/in/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditNote\":\"\",\"auditTime\":\"2025-07-28 10:11:03\",\"details\":[{\"amount\":56,\"detailId\":285,\"inId\":269,\"params\":{},\"price\":4,\"productCode\":\"PEN001\",\"productId\":205,\"productName\":\"签字笔\",\"quantity\":14}],\"inCode\":\"IN20250728101058\",\"inId\":269,\"params\":{},\"price\":null,\"quantity\":null,\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-28 10:11:03\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,2078,'2025-07-28 10:11:05'),(56,'入库明细报表',5,'com.wanyu.web.controller.api.v1.report.ReportExportController.exportInDetailExcel()','GET',1,'superadmin','万裕物业','/api/v1/reports/export/in/detail/excel','127.0.0.1','内网IP','{\"warehouseId\":\"\",\"columns\":\"inCode,warehouseName,inTypeName,productName,productCode,quantity,price,amount,inTime,operateBy,status,auditBy,auditTime\",\"pageSize\":\"10\",\"inCode\":\"\",\"pageNum\":\"1\",\"inType\":\"\",\"status\":\"\"}',NULL,0,NULL,72,'2025-07-28 10:11:13'),(57,'库存调拨',1,'com.wanyu.web.controller.inventory.WmsInventoryTransferController.add()','POST',1,'superadmin','万裕物业','/inventory/transfer','127.0.0.1','内网IP','{\"details\":[{\"params\":{},\"productCode\":\"PEN001\",\"productId\":205,\"productName\":\"签字笔\",\"quantity\":1}],\"fromWarehouseId\":5,\"params\":{},\"status\":\"0\",\"toWarehouseId\":1}',NULL,1,'调出仓库库存不足，物品[签字笔]，当前库存：0，需要调拨：1',30,'2025-07-28 10:12:11'),(58,'库存调拨',1,'com.wanyu.web.controller.inventory.WmsInventoryTransferController.add()','POST',1,'superadmin','万裕物业','/inventory/transfer','127.0.0.1','内网IP','{\"details\":[{\"params\":{},\"productCode\":\"LAPTOP001\",\"productId\":203,\"productName\":\"笔记本电脑\",\"quantity\":1}],\"fromWarehouseId\":5,\"params\":{},\"status\":\"0\",\"toWarehouseId\":1}',NULL,1,'调出仓库库存不足，物品[笔记本电脑]，当前库存：0，需要调拨：1',7,'2025-07-28 10:12:33'),(59,'库存调拨',1,'com.wanyu.web.controller.inventory.WmsInventoryTransferController.add()','POST',1,'superadmin','万裕物业','/inventory/transfer','127.0.0.1','内网IP','{\"details\":[{\"params\":{},\"productCode\":\"WATER001\",\"productId\":201,\"productName\":\"矿泉水\",\"quantity\":1}],\"fromWarehouseId\":5,\"params\":{},\"status\":\"0\",\"toWarehouseId\":1}',NULL,1,'调出仓库库存不足，物品[矿泉水]，当前库存：0，需要调拨：1',5,'2025-07-28 10:12:36'),(60,'库存调拨',1,'com.wanyu.web.controller.inventory.WmsInventoryTransferController.add()','POST',1,'superadmin','万裕物业','/inventory/transfer','127.0.0.1','内网IP','{\"createBy\":\"superadmin\",\"createTime\":\"2025-07-28 10:12:38\",\"details\":[{\"params\":{},\"productCode\":\"COLA001\",\"productId\":202,\"productName\":\"可乐\",\"quantity\":1,\"transferId\":216}],\"fromWarehouseId\":5,\"params\":{},\"status\":\"0\",\"toWarehouseId\":1,\"transferCode\":\"TR20250728101238\",\"transferId\":216,\"transferTime\":\"2025-07-28 10:12:38\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,29,'2025-07-28 10:12:38'),(61,'库存调拨审核',2,'com.wanyu.web.controller.inventory.WmsInventoryTransferController.audit()','PUT',1,'superadmin','万裕物业','/inventory/transfer/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditTime\":\"2025-07-28 10:12:42\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"transferCode\":\"TR20250728101238\",\"transferId\":216,\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-28 10:12:42\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,4155,'2025-07-28 10:12:47'),(62,'调拨明细报表',5,'com.wanyu.web.controller.api.v1.report.ReportExportController.exportTransferDetailExcel()','GET',1,'superadmin','万裕物业','/api/v1/reports/export/transfer/excel','127.0.0.1','内网IP','{\"columns\":\"transferCode,fromWarehouseName,toWarehouseName,productName,productCode,quantity,transferTime,operateBy,status,auditBy,auditTime\",\"pageSize\":\"10\",\"toWarehouseId\":\"\",\"transferCode\":\"\",\"fromWarehouseId\":\"\",\"pageNum\":\"1\",\"status\":\"\"}',NULL,0,NULL,299,'2025-07-28 10:12:50'),(63,'库存盘点',1,'com.wanyu.web.controller.inventory.WmsInventoryCheckController.add()','POST',1,'superadmin','万裕物业','/inventory/check','127.0.0.1','内网IP','{\"checkCode\":\"CK20250728101338\",\"checkId\":208,\"checkTime\":\"2025-07-28 10:13:35\",\"createBy\":\"superadmin\",\"createTime\":\"2025-07-28 10:13:38\",\"details\":[{\"bookQuantity\":0,\"checkId\":208,\"diffQuantity\":0,\"params\":{},\"productId\":201,\"realQuantity\":0},{\"bookQuantity\":14,\"checkId\":208,\"diffQuantity\":0,\"params\":{},\"productId\":205,\"realQuantity\":14},{\"bookQuantity\":1,\"checkId\":208,\"diffQuantity\":0,\"params\":{},\"productId\":202,\"realQuantity\":1}],\"params\":{},\"status\":\"0\",\"warehouseId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,31,'2025-07-28 10:13:38'),(64,'库存盘点审核',2,'com.wanyu.web.controller.inventory.WmsInventoryCheckController.audit()','PUT',1,'superadmin','万裕物业','/inventory/check/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditTime\":\"2025-07-28 10:13:47\",\"checkCode\":\"CK20250728101338\",\"checkId\":208,\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-28 10:13:47\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,15,'2025-07-28 10:13:47'),(65,'库存盘点审核',2,'com.wanyu.web.controller.inventory.WmsInventoryCheckController.audit()','PUT',1,'superadmin','万裕物业','/inventory/check/audit','127.0.0.1','内网IP','{\"auditBy\":\"superadmin\",\"auditTime\":\"2025-07-28 10:13:50\",\"checkCode\":\"CK20250726220456\",\"checkId\":207,\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"superadmin\",\"updateTime\":\"2025-07-28 10:13:50\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,11,'2025-07-28 10:13:50'),(66,'盘点明细报表',5,'com.wanyu.web.controller.api.v1.report.ReportExportController.exportCheckDetailExcel()','GET',1,'superadmin','万裕物业','/api/v1/reports/export/check/excel','127.0.0.1','内网IP','{\"warehouseId\":\"\",\"columns\":\"checkCode,warehouseName,checkTime,status,productName,productCode,bookQuantity,realQuantity,diffQuantity,operateBy,auditBy,auditTime\",\"pageSize\":\"10\",\"pageNum\":\"1\",\"checkCode\":\"\",\"status\":\"\"}',NULL,0,NULL,199,'2025-07-28 10:13:56'),(67,'库存预警报表',5,'com.wanyu.web.controller.api.v1.report.ReportExportController.exportInventoryAlertExcel()','GET',1,'superadmin','万裕物业','/api/v1/reports/export/inventory/alert/excel','127.0.0.1','内网IP','{\"productCode\":\"\",\"warehouseId\":\"\",\"pageSize\":\"10\",\"pageNum\":\"1\",\"productName\":\"\",\"status\":\"\"}',NULL,0,NULL,52,'2025-07-28 10:14:29'),(68,'出入库日志',5,'com.wanyu.web.controller.log.InventoryLogController.export()','POST',1,'superadmin','万裕物业','/api/v1/logs/inventory/export','127.0.0.1','内网IP','{\"params\":{\"userId\":1}}',NULL,0,NULL,90,'2025-07-28 10:14:46'),(69,'操作日志',5,'com.wanyu.web.controller.monitor.SysOperlogController.export()','GET',1,'superadmin','万裕物业','/monitor/operlog/export','127.0.0.1','内网IP','{\"pageSize\":\"10\",\"pageNum\":\"1\"}',NULL,0,NULL,165,'2025-07-28 10:15:17'),(70,'库存明细报表',5,'com.wanyu.web.controller.api.v1.report.ReportExportController.exportInventoryDetailExcel()','GET',1,'superadmin','万裕物业','/api/v1/reports/export/inventory/detail/excel','127.0.0.1','内网IP','{\"fileName\":\"库存明细报表\",\"columns\":\"productCode,productName,warehouseName,specification,unit,quantity,minQuantity,maxQuantity,status,price,totalValue\"}',NULL,0,NULL,60,'2025-07-28 10:16:28'),(71,'角色管理',2,'com.wanyu.web.controller.system.SysRoleController.edit()','PUT',1,'superadmin','万裕物业','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-06-20 09:54:50\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[4039,4044,4060,4062,4073,4076,4082,4085,4088,4113,4118,4123,3,4,4091,4092,4093,4094,4095,4096,4097,4098,5,4099,4100,4101,4102,4103,4104,6,4105,4106,4107,4108,7,4109,4110,4111,4112,8,4129,4130,4131,4132,4139,4145,4146,4147,4148,4149,4140,4150,4151,4152,4153,4154,4155,4141,4156,4157,4158,4159,4142,4160,4161,4162,4163,4040,4045,4061,4063,4074,4077,4083,4086,4089,4114,4119,4124,9,10,11,12,13,4144,4169,4170,4171,4172,4173,4041,4046,4064,4075,4078,4084,4087,4090,4115,4120,4125,14,15,3038,3039,3040,3792,3041,3042,4042,4047,4116,4121,4126,17,18,4055,4056,4057,4058,4059,19,4049,4050,4051,4052,4053,4054,20,3859,3860,3861,3862,21,3863,3864,3865,3866,4143,4164,4165,4166,4167,4168,4043,4048,4117,4122,4127,22,23,24,25,26,4128,27,28,3893,3894,3895,3896,3897,3898,3899,3900,3901,3902,29,4006,4007,4008,4009,4010,4011,4012,4013,4014,30,4015,4016,4017,4018,4019,4020,4021,4022,4023,31,4024,4025,4026,4027,4028,4029,4030,32,4031,4032,4033,4034,4035,4036,4037,4038,33,2100,2110,34,35,4069,4070,4071,4072,3784,36,3949,3950,3951,3785,37,4065,4066,4067,4068,3786,38,4079,4080,4081,3787,3788,39,40,3960,3961,3962,3963,3964,41,3965,3966,3967,3968,3969,42,3970,3971,3972,3973,3974,2],\"params\":{},\"remark\":\"拥有系统管理权限\",\"roleId\":2,\"roleKey\":\"system_admin\",\"roleName\":\"系统管理员\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"superadmin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,73,'2025-07-28 10:17:24'),(72,'角色仓库权限',4,'com.wanyu.web.controller.system.SysRoleWarehouseController.batchSave()','POST',1,'superadmin','万裕物业','/api/v1/system/role-warehouses/batch','127.0.0.1','内网IP','{\"roleId\":2,\"warehouseIds\":[1,2,3,4,5,6,7,8,9,10]}',NULL,1,'Unable to connect to Redis; nested exception is org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',2075,'2025-07-28 10:17:38'),(73,'角色仓库权限',4,'com.wanyu.web.controller.system.SysRoleWarehouseController.batchSave()','POST',1,'superadmin','万裕物业','/api/v1/system/role-warehouses/batch','127.0.0.1','内网IP','{\"roleId\":2,\"warehouseIds\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}',NULL,1,'Unable to connect to Redis; nested exception is org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',2050,'2025-07-28 10:17:57'),(74,'角色仓库权限',4,'com.wanyu.web.controller.system.SysRoleWarehouseController.batchSave()','POST',1,'superadmin','万裕物业','/api/v1/system/role-warehouses/batch','127.0.0.1','内网IP','{\"roleId\":2,\"warehouseIds\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}',NULL,1,'Unable to connect to Redis; nested exception is org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',2054,'2025-07-28 10:18:11'),(75,'角色管理',2,'com.wanyu.web.controller.system.SysRoleController.edit()','PUT',1,'superadmin','万裕物业','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-06-20 09:54:50\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[4039,4044,4060,4062,4073,4076,4082,4085,4088,4113,4118,4123,3,4,4091,4092,4093,4094,4095,4096,4097,4098,5,4099,4100,4101,4102,4103,4104,6,4105,4106,4107,4108,7,4109,4110,4111,4112,8,4129,4130,4131,4132,4139,4145,4146,4147,4148,4149,4140,4150,4151,4152,4153,4154,4155,4141,4156,4157,4158,4159,4142,4160,4161,4162,4163,4040,4045,4061,4063,4074,4077,4083,4086,4089,4114,4119,4124,9,10,11,12,13,4144,4169,4170,4171,4172,4173,4041,4046,4064,4075,4078,4084,4087,4090,4115,4120,4125,14,15,3038,3039,3040,3792,3041,3042,4042,4047,4116,4121,4126,17,18,4055,4056,4057,4058,4059,19,4049,4050,4051,4052,4053,4054,20,3859,3860,3861,3862,21,3863,3864,3865,3866,4143,4164,4165,4166,4167,4168,4043,4048,4117,4122,4127,22,23,24,25,26,4128,27,28,3893,3894,3895,3896,3897,3898,3899,3900,3901,3902,29,4006,4007,4008,4009,4010,4011,4012,4013,4014,30,4015,4016,4017,4018,4019,4020,4021,4022,4023,31,4024,4025,4026,4027,4028,4029,4030,32,4031,4032,4033,4034,4035,4036,4037,4038,33,2100,2110,34,35,4069,4070,4071,4072,3784,36,3949,3950,3951,3785,37,4065,4066,4067,4068,3786,38,4079,4080,4081,3787,3788,39,40,3960,3961,3962,3963,3964,41,3965,3966,3967,3968,3969,42,3970,3971,3972,3973,3974,2],\"params\":{},\"remark\":\"拥有系统管理权限\",\"roleId\":2,\"roleKey\":\"system_admin\",\"roleName\":\"系统管理员\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"superadmin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,504,'2025-07-28 10:49:34'),(76,'角色仓库权限',4,'com.wanyu.web.controller.system.SysRoleWarehouseController.batchSave()','POST',1,'superadmin','万裕物业','/api/v1/system/role-warehouses/batch','127.0.0.1','内网IP','{\"roleId\":2,\"warehouseIds\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}',NULL,1,'Unable to connect to Redis; nested exception is org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',2548,'2025-07-28 10:49:49'),(77,'角色仓库权限',4,'com.wanyu.web.controller.system.SysRoleWarehouseController.batchSave()','POST',1,'superadmin','万裕物业','/api/v1/system/role-warehouses/batch','127.0.0.1','内网IP','{\"roleId\":2,\"warehouseIds\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}',NULL,1,'Unable to connect to Redis; nested exception is org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',2057,'2025-07-28 10:49:59'),(78,'角色仓库权限',4,'com.wanyu.web.controller.system.SysRoleWarehouseController.batchSave()','POST',1,'superadmin','万裕物业','/api/v1/system/role-warehouses/batch','127.0.0.1','内网IP','{\"roleId\":2,\"warehouseIds\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}',NULL,1,'Unable to connect to Redis; nested exception is org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',3560,'2025-07-28 11:16:21'),(79,'角色仓库权限',4,'com.wanyu.web.controller.system.SysRoleWarehouseController.batchSave()','POST',1,'superadmin','万裕物业','/api/v1/system/role-warehouses/batch','127.0.0.1','内网IP','{\"roleId\":2,\"warehouseIds\":[]}',NULL,1,'Unable to connect to Redis; nested exception is org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',2070,'2025-07-28 11:19:28'),(80,'角色仓库权限',4,'com.wanyu.web.controller.system.SysRoleWarehouseController.batchSave()','POST',1,'superadmin','万裕物业','/api/v1/system/role-warehouses/batch','127.0.0.1','内网IP','{\"roleId\":3,\"warehouseIds\":[]}',NULL,1,'Unable to connect to Redis; nested exception is org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',2036,'2025-07-28 11:19:51'),(81,'出入库日志',5,'com.wanyu.web.controller.log.InventoryLogController.export()','POST',1,'superadmin','万裕物业','/api/v1/logs/inventory/export','127.0.0.1','内网IP','{\"params\":{\"userId\":1}}',NULL,0,NULL,941,'2025-07-28 11:49:29');
/*!40000 ALTER TABLE `sys_oper_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_permission`
--

DROP TABLE IF EXISTS `sys_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_permission` (
  `permission_id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `permission_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限标识',
  `permission_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '权限类型（0菜单 1按钮）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`permission_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='权限信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_permission`
--

LOCK TABLES `sys_permission` WRITE;
/*!40000 ALTER TABLE `sys_permission` DISABLE KEYS */;
INSERT INTO `sys_permission` VALUES (1,'查看用户','system:user:view','0','0','0','admin','2025-07-14 21:47:09','',NULL,'查看用户信息权限'),(2,'编辑用户','system:user:edit','1','0','0','admin','2025-07-14 21:47:09','',NULL,'编辑用户信息权限'),(3,'查看仓库','system:warehouse:view','0','0','0','admin','2025-07-14 21:47:09','',NULL,'查看仓库信息权限'),(4,'编辑仓库','system:warehouse:edit','1','0','0','admin','2025-07-14 21:47:09','',NULL,'编辑仓库信息权限'),(5,'查看库存','system:inventory:view','0','0','0','admin','2025-07-14 21:47:09','',NULL,'查看库存信息权限'),(6,'编辑库存','system:inventory:edit','1','0','0','admin','2025-07-14 21:47:09','',NULL,'编辑库存信息权限'),(100,'库存日志查看','wms:inventorylog:list','0','0','0','admin','2025-07-15 00:29:17','',NULL,'查看库存日志权限'),(101,'库存日志导出','wms:inventorylog:export','0','0','0','admin','2025-07-15 00:29:17','',NULL,'导出库存日志权限'),(102,'库存日志删除','wms:inventorylog:remove','0','0','0','admin','2025-07-15 00:29:17','',NULL,'删除库存日志权限');
/*!40000 ALTER TABLE `sys_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_permission_audit`
--

DROP TABLE IF EXISTS `sys_permission_audit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_permission_audit` (
  `audit_id` bigint NOT NULL AUTO_INCREMENT COMMENT '审计ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户名',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `permission_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '权限类型（MENU, BUTTON, API, WAREHOUSE等）',
  `permission_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '权限标识',
  `target_id` bigint DEFAULT NULL COMMENT '目标ID',
  `access_result` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '访问结果（0失败 1成功）',
  `access_time` datetime DEFAULT NULL COMMENT '访问时间',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IP地址',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`audit_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_permission_type` (`permission_type`) USING BTREE,
  KEY `idx_permission_key` (`permission_key`) USING BTREE,
  KEY `idx_access_time` (`access_time`) USING BTREE,
  KEY `idx_access_result` (`access_result`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='权限审计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_permission_audit`
--

LOCK TABLES `sys_permission_audit` WRITE;
/*!40000 ALTER TABLE `sys_permission_audit` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_permission_audit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_permission_log`
--

DROP TABLE IF EXISTS `sys_permission_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_permission_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '鏃ュ織ID',
  `user_id` bigint NOT NULL COMMENT '鐢ㄦ埛ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鐢ㄦ埛鍚嶇О',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鐢ㄦ埛鏄电О',
  `perm_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鏉冮檺绫诲瀷锛?鑿滃崟 2鎸夐挳 3API锛',
  `permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鏉冮檺鏍囪瘑',
  `oper_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鎿嶄綔绫诲瀷锛?鏌ヨ? 2鏂板? 3淇?敼 4鍒犻櫎锛',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '鎿嶄綔鐘舵?锛?姝ｅ父 1寮傚父锛',
  `msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鎿嶄綔娑堟伅',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鎿嶄綔IP',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鎿嶄綔鍦扮偣',
  `oper_time` datetime DEFAULT NULL COMMENT '鎿嶄綔鏃堕棿',
  PRIMARY KEY (`log_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='权限日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_permission_log`
--

LOCK TABLES `sys_permission_log` WRITE;
/*!40000 ALTER TABLE `sys_permission_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_permission_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_permission_template`
--

DROP TABLE IF EXISTS `sys_permission_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_permission_template` (
  `template_id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模板名称',
  `template_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '模板类型',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '模板描述',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1.0.0' COMMENT '版本号',
  `version_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本说明',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='权限模板表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_permission_template`
--

LOCK TABLES `sys_permission_template` WRITE;
/*!40000 ALTER TABLE `sys_permission_template` DISABLE KEYS */;
INSERT INTO `sys_permission_template` VALUES (9,'超级管理员模板','1',1,'拥有所有权限的超级管理员模板','0','1.0.0','初始版本','系统内置','admin','2025-07-13 22:26:49',NULL,NULL),(10,'普通用户模板','1',1,'普通用户基础权限模板','0','1.0.0','初始版本','系统内置','admin','2025-07-13 22:26:49',NULL,NULL),(11,'仓库管理员模板','5',1,'仓库管理员权限模板','0','1.0.0','初始版本','系统内置','admin','2025-07-13 22:26:49',NULL,NULL),(12,'销售人员模板','5',1,'销售人员权限模板','0','1.0.0','初始版本','系统内置','admin','2025-07-13 22:26:49',NULL,NULL);
/*!40000 ALTER TABLE `sys_permission_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_permission_template_category`
--

DROP TABLE IF EXISTS `sys_permission_template_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_permission_template_category` (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID',
  `order_num` int DEFAULT '0' COMMENT '排序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `ancestors` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '祖级列表',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='权限模板分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_permission_template_category`
--

LOCK TABLES `sys_permission_template_category` WRITE;
/*!40000 ALTER TABLE `sys_permission_template_category` DISABLE KEYS */;
INSERT INTO `sys_permission_template_category` VALUES (1,'通用模板',0,1,'0',NULL,NULL,NULL,NULL,NULL,NULL),(2,'仓库管理',0,2,'0',NULL,NULL,NULL,NULL,NULL,NULL),(3,'销售管理',0,3,'0',NULL,NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `sys_permission_template_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_permission_template_log`
--

DROP TABLE IF EXISTS `sys_permission_template_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_permission_template_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `template_id` bigint DEFAULT NULL COMMENT '模板ID',
  `template_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '模板名称',
  `target_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '目标类型',
  `target_id` bigint DEFAULT NULL COMMENT '目标ID',
  `target_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '目标名称',
  `apply_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用类型',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='权限模板应用日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_permission_template_log`
--

LOCK TABLES `sys_permission_template_log` WRITE;
/*!40000 ALTER TABLE `sys_permission_template_log` DISABLE KEYS */;
INSERT INTO `sys_permission_template_log` VALUES (1,8,'645456','TEMPLATE',8,'645456','CREATE','0','superadmin','2025-07-13 22:09:39','创建权限模板'),(11,8,'645456','TEMPLATE',8,'645456','DELETE','0','superadmin','2025-07-13 22:20:11','删除权限模板'),(12,12,'销售人员模板','TEMPLATE',12,'销售人员模板','UPDATE','0','superadmin','2025-07-23 11:34:37','修改权限模板');
/*!40000 ALTER TABLE `sys_permission_template_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_permission_template_menu`
--

DROP TABLE IF EXISTS `sys_permission_template_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_permission_template_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=260 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='权限模板-菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_permission_template_menu`
--

LOCK TABLES `sys_permission_template_menu` WRITE;
/*!40000 ALTER TABLE `sys_permission_template_menu` DISABLE KEYS */;
INSERT INTO `sys_permission_template_menu` VALUES (1,1,2),(2,1,3),(3,1,4),(4,1,5),(5,1,6),(6,1,7),(7,1,8),(8,1,9),(9,1,10),(10,1,11),(11,1,12),(12,1,13),(13,1,14),(14,1,15),(15,1,17),(16,1,18),(17,1,19),(18,1,20),(19,1,21),(20,1,22),(21,1,23),(22,1,24),(23,1,25),(24,1,26),(25,1,27),(26,1,28),(27,1,29),(28,1,30),(29,1,31),(30,1,32),(31,1,33),(32,1,34),(33,1,35),(34,1,36),(35,1,37),(36,1,38),(37,1,39),(38,1,40),(39,1,41),(40,1,42),(41,1,1911),(42,1,1915),(43,1,1916),(44,1,2005),(45,1,2006),(46,1,2050),(47,1,2051),(48,1,2052),(49,1,2053),(50,1,2054),(51,1,2100),(52,1,2101),(53,1,2102),(54,1,2103),(55,1,2104),(56,1,2105),(57,1,2110),(58,1,2111),(59,1,2112),(60,1,2113),(61,1,3001),(62,1,3003),(63,1,3004),(64,1,3020),(65,1,3022),(66,1,3023),(67,1,3030),(68,1,3032),(69,1,3033),(70,1,3037),(71,1,3038),(72,1,3039),(73,1,3040),(74,1,3041),(75,1,3042),(76,1,3043),(77,1,3044),(78,1,3045),(79,1,3046),(80,1,3047),(81,1,3048),(82,1,3049),(83,1,3050),(84,1,3051),(85,1,3059),(86,1,3060),(87,1,3061),(88,1,3062),(89,1,3063),(90,1,3064),(91,1,3065),(92,1,3066),(93,1,3067),(94,1,3068),(95,1,3069),(96,1,3070),(97,1,3071),(98,1,3072),(99,1,3073),(100,1,3074),(101,1,3075),(102,1,3076),(103,1,3077),(104,1,3078),(105,1,3079),(106,1,3080),(107,1,3081),(108,1,3082),(109,1,3083),(110,1,3084),(111,1,3085),(112,1,3086),(113,1,3705),(114,1,3706),(115,1,3707),(116,1,3708),(117,1,3709),(118,1,3710),(119,1,3711),(120,1,3712),(121,1,3713),(122,1,3714),(123,1,3715),(124,1,3716),(125,1,3717),(126,1,3718),(127,1,3719),(128,1,3720),(129,1,3721),(130,1,3722),(131,1,3723),(132,1,3724),(133,1,3728),(134,1,3729),(135,1,3730),(136,1,3731),(137,1,3732),(138,1,3733),(139,1,3734),(140,1,3735),(141,1,3736),(142,1,3737),(256,2,101),(257,2,102),(258,4,103),(259,4,104);
/*!40000 ALTER TABLE `sys_permission_template_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_permission_template_warehouse`
--

DROP TABLE IF EXISTS `sys_permission_template_warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_permission_template_warehouse` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='权限模板-仓库关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_permission_template_warehouse`
--

LOCK TABLES `sys_permission_template_warehouse` WRITE;
/*!40000 ALTER TABLE `sys_permission_template_warehouse` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_permission_template_warehouse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE,
  KEY `idx_role_key` (`role_key`) USING BTREE,
  KEY `idx_role_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
INSERT INTO `sys_role` VALUES (1,'超级管理员','super_admin',0,'1',1,1,'0','0','admin','2025-05-15 18:00:00','superadmin',NULL,'拥有系统所有权限'),(2,'系统管理员','system_admin',2,'1',1,1,'0','0','admin','2025-06-20 09:54:50','superadmin','2025-07-28 10:49:33','拥有系统管理权限'),(3,'仓库管理员','warehouse_admin',3,'1',1,1,'0','0','admin','2025-06-20 09:32:17','superadmin','2025-07-21 23:42:58','管理仓库信息和库存'),(4,'库存管理员','inventory_manager',4,'1',0,0,'1','0','admin','2025-07-14 19:18:52','superadmin','2025-07-14 21:32:33','管理库存信息'),(5,'采购管理员','purchase_manager',5,'1',0,0,'1','0','admin','2025-07-14 19:20:22','superadmin','2025-07-14 22:51:58','管理采购和入库'),(6,'仓库操作员','warehouse_operator',6,'1',1,1,'1','0','admin','2025-07-10 19:21:26','superadmin','2025-07-14 20:15:22','仓库日常操作'),(7,'盘点专员','inventory_checker',7,'1',0,0,'1','0','admin','2025-07-14 19:23:22','superadmin','2025-07-14 21:32:30','负责库存盘点'),(8,'普通用户','common_user',8,'1',1,1,'0','0','admin','2025-07-14 19:23:52','superadmin','2025-07-18 01:13:48','基本操作权限');
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `role_delete_backup_trigger` BEFORE UPDATE ON `sys_role` FOR EACH ROW BEGIN
IF NEW.del_flag = '2' AND OLD.del_flag = '0' THEN
INSERT IGNORE INTO sys_role_backup SELECT * FROM sys_role WHERE role_id = OLD.role_id;
END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `sys_role_api`
--

DROP TABLE IF EXISTS `sys_role_api`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_api` (
  `role_id` bigint NOT NULL COMMENT '瑙掕壊ID',
  `api_id` bigint NOT NULL COMMENT 'API鏉冮檺ID',
  PRIMARY KEY (`role_id`,`api_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='角色和API关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_api`
--

LOCK TABLES `sys_role_api` WRITE;
/*!40000 ALTER TABLE `sys_role_api` DISABLE KEYS */;
INSERT INTO `sys_role_api` VALUES (1,101),(1,102),(1,103),(1,104),(1,105),(1,106),(1,107),(1,108),(1,111),(1,112),(1,113),(1,114),(1,115),(1,116),(1,121),(1,122),(1,123),(1,124),(1,125),(1,131),(1,132),(1,133),(1,134),(1,135),(1,136),(1,141),(1,142),(1,143),(1,144),(1,145),(1,146),(1,147),(1,148),(1,158),(1,159),(1,160),(1,161),(1,162),(1,163),(1,164),(1,165),(1,166),(1,167),(1,168),(1,169),(1,170),(1,171),(1,172),(1,173),(1,174),(1,175),(1,176),(1,177),(1,178),(1,179),(1,180),(1,181),(1,182),(1,183),(1,184),(1,185),(1,186),(1,187),(1,188),(1,189),(1,190),(1,191),(1,192),(1,193),(1,194),(1,195),(1,196),(1,197),(1,198),(1,199),(1,200),(1,201),(1,202),(1,203),(1,204),(1,205),(1,206),(1,207),(1,208),(1,209),(1,210),(1,211),(1,212),(1,213),(1,214),(1,215),(1,221),(1,222),(1,223),(1,224),(1,225),(1,231),(1,232),(1,233),(1,234),(1,235),(1,301),(1,302),(1,303),(1,304),(1,305),(1,306),(1,307),(1,308),(1,311),(1,312),(1,313),(1,314),(1,315),(1,316),(1,317),(1,318),(1,319),(1,321),(1,322),(1,323),(1,324),(1,325),(1,326),(1,327),(1,328),(1,329),(1,331),(1,332),(1,333),(1,334),(1,335),(1,336),(1,337),(1,338),(1,341),(1,342),(1,343),(1,344),(1,345),(1,346),(1,347),(1,348),(1,351),(1,352),(1,353),(1,354),(1,355),(1,356),(1,401),(1,402),(1,403),(1,404),(1,405),(1,406),(1,407),(1,408),(1,409),(1,501),(1,502),(1,503),(1,504),(1,511),(1,512),(1,513),(1,514),(1,521),(1,522),(1,523),(1,524),(1,531),(1,532),(1,533),(1,534),(1,601),(1,602),(1,611),(1,612),(1,621),(1,622),(1,631),(1,632),(1,641),(1,642),(1,651),(1,652),(1,701),(1,702),(1,703),(1,704),(1,705),(1,711),(1,712),(1,713),(1,714),(1,715),(1,721),(1,722),(1,723),(1,724),(1,725),(1,731),(1,732),(1,733),(1,734),(1,735),(1,741),(1,742),(1,743),(1,744),(1,745),(1,746),(1,751),(1,752),(1,753),(1,754),(1,755),(1,761),(1,762),(1,763),(1,764),(1,765),(1,771),(1,772),(1,773),(1,774),(1,781),(1,782),(1,791),(1,792);
/*!40000 ALTER TABLE `sys_role_api` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_data_permission`
--

DROP TABLE IF EXISTS `sys_role_data_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_data_permission` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `perm_id` bigint NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`role_id`,`perm_id`) USING BTREE,
  KEY `idx_role_data_permission_role_id` (`role_id`) USING BTREE,
  KEY `idx_role_data_permission_perm_id` (`perm_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色和数据权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_data_permission`
--

LOCK TABLES `sys_role_data_permission` WRITE;
/*!40000 ALTER TABLE `sys_role_data_permission` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_data_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_dept`
--

DROP TABLE IF EXISTS `sys_role_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_dept` (
  `role_id` bigint NOT NULL COMMENT '瑙掕壊ID',
  `dept_id` bigint NOT NULL COMMENT '閮ㄩ棬ID',
  PRIMARY KEY (`role_id`,`dept_id`) USING BTREE COMMENT '瑙掕壊鍜岄儴闂ㄥ叧鑱斾富閿',
  KEY `idx_role_dept_role_id` (`role_id`) USING BTREE,
  KEY `idx_role_dept_dept_id` (`dept_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='角色和部门关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_dept`
--

LOCK TABLES `sys_role_dept` WRITE;
/*!40000 ALTER TABLE `sys_role_dept` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_menu`
--

DROP TABLE IF EXISTS `sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint NOT NULL COMMENT '瑙掕壊ID',
  `menu_id` bigint NOT NULL COMMENT '鑿滃崟ID',
  PRIMARY KEY (`role_id`,`menu_id`) USING BTREE,
  KEY `idx_role_menu_role_id` (`role_id`) USING BTREE,
  KEY `idx_role_menu_menu_id` (`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='角色和菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_menu`
--

LOCK TABLES `sys_role_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_menu` DISABLE KEYS */;
INSERT INTO `sys_role_menu` VALUES (1,2),(1,4),(1,5),(1,6),(1,7),(1,8),(1,9),(1,10),(1,11),(1,12),(1,13),(1,14),(1,15),(1,18),(1,19),(1,20),(1,21),(1,22),(1,23),(1,24),(1,25),(1,26),(1,28),(1,29),(1,30),(1,31),(1,32),(1,33),(1,35),(1,36),(1,37),(1,38),(1,39),(1,40),(1,41),(1,42),(1,1000),(1,1100),(1,1200),(1,1400),(1,1901),(1,1905),(1,1906),(1,1915),(1,1916),(1,2001),(1,2005),(1,2006),(1,2020),(1,2024),(1,2025),(1,2026),(1,2027),(1,2030),(1,2034),(1,2035),(1,2036),(1,2037),(1,2040),(1,2044),(1,2045),(1,2046),(1,2047),(1,2050),(1,2051),(1,2052),(1,2053),(1,2054),(1,2055),(1,2056),(1,2057),(1,2100),(1,2110),(1,3001),(1,3002),(1,3003),(1,3004),(1,3008),(1,3022),(1,3023),(1,3032),(1,3033),(1,3034),(1,3035),(1,3037),(1,3038),(1,3039),(1,3040),(1,3041),(1,3042),(1,3101),(1,3102),(1,3103),(1,3104),(1,3201),(1,3202),(1,3402),(1,3403),(1,3406),(1,3784),(1,3785),(1,3786),(1,3787),(1,3788),(1,3792),(1,3859),(1,3860),(1,3861),(1,3862),(1,3863),(1,3864),(1,3865),(1,3866),(1,3893),(1,3894),(1,3895),(1,3896),(1,3897),(1,3898),(1,3899),(1,3900),(1,3901),(1,3902),(1,3960),(1,3961),(1,3962),(1,3963),(1,3964),(1,3965),(1,3966),(1,3967),(1,3968),(1,3969),(1,3970),(1,3971),(1,3972),(1,3973),(1,3974),(1,4006),(1,4007),(1,4008),(1,4009),(1,4010),(1,4011),(1,4012),(1,4013),(1,4014),(1,4015),(1,4016),(1,4017),(1,4018),(1,4019),(1,4020),(1,4021),(1,4022),(1,4023),(1,4024),(1,4025),(1,4026),(1,4027),(1,4028),(1,4029),(1,4030),(1,4031),(1,4032),(1,4033),(1,4034),(1,4035),(1,4036),(1,4037),(1,4038),(1,4039),(1,4040),(1,4041),(1,4042),(1,4043),(1,4044),(1,4045),(1,4046),(1,4047),(1,4048),(1,4049),(1,4050),(1,4051),(1,4052),(1,4053),(1,4054),(1,4055),(1,4056),(1,4057),(1,4058),(1,4059),(1,4060),(1,4061),(1,4062),(1,4063),(1,4064),(1,4065),(1,4066),(1,4067),(1,4068),(1,4069),(1,4070),(1,4071),(1,4072),(1,4073),(1,4074),(1,4075),(1,4076),(1,4077),(1,4078),(1,4079),(1,4080),(1,4081),(1,4082),(1,4083),(1,4084),(1,4085),(1,4086),(1,4087),(1,4088),(1,4089),(1,4090),(1,4091),(1,4092),(1,4093),(1,4094),(1,4095),(1,4096),(1,4097),(1,4098),(1,4099),(1,4100),(1,4101),(1,4102),(1,4103),(1,4104),(1,4105),(1,4106),(1,4107),(1,4108),(1,4109),(1,4110),(1,4111),(1,4112),(1,4113),(1,4114),(1,4115),(1,4116),(1,4117),(1,4118),(1,4119),(1,4120),(1,4121),(1,4122),(1,4123),(1,4124),(1,4125),(1,4126),(1,4127),(1,4128),(1,4129),(1,4130),(1,4131),(1,4132),(2,2),(2,3),(2,4),(2,5),(2,6),(2,7),(2,8),(2,9),(2,10),(2,11),(2,12),(2,13),(2,14),(2,15),(2,17),(2,18),(2,19),(2,20),(2,21),(2,22),(2,23),(2,24),(2,25),(2,26),(2,27),(2,28),(2,29),(2,30),(2,31),(2,32),(2,33),(2,34),(2,35),(2,36),(2,37),(2,38),(2,39),(2,40),(2,41),(2,42),(2,2100),(2,2110),(2,3038),(2,3039),(2,3040),(2,3041),(2,3042),(2,3784),(2,3785),(2,3786),(2,3787),(2,3788),(2,3792),(2,3859),(2,3860),(2,3861),(2,3862),(2,3863),(2,3864),(2,3865),(2,3866),(2,3893),(2,3894),(2,3895),(2,3896),(2,3897),(2,3898),(2,3899),(2,3900),(2,3901),(2,3902),(2,3949),(2,3950),(2,3951),(2,3960),(2,3961),(2,3962),(2,3963),(2,3964),(2,3965),(2,3966),(2,3967),(2,3968),(2,3969),(2,3970),(2,3971),(2,3972),(2,3973),(2,3974),(2,4006),(2,4007),(2,4008),(2,4009),(2,4010),(2,4011),(2,4012),(2,4013),(2,4014),(2,4015),(2,4016),(2,4017),(2,4018),(2,4019),(2,4020),(2,4021),(2,4022),(2,4023),(2,4024),(2,4025),(2,4026),(2,4027),(2,4028),(2,4029),(2,4030),(2,4031),(2,4032),(2,4033),(2,4034),(2,4035),(2,4036),(2,4037),(2,4038),(2,4039),(2,4040),(2,4041),(2,4042),(2,4043),(2,4044),(2,4045),(2,4046),(2,4047),(2,4048),(2,4049),(2,4050),(2,4051),(2,4052),(2,4053),(2,4054),(2,4055),(2,4056),(2,4057),(2,4058),(2,4059),(2,4060),(2,4061),(2,4062),(2,4063),(2,4064),(2,4065),(2,4066),(2,4067),(2,4068),(2,4069),(2,4070),(2,4071),(2,4072),(2,4073),(2,4074),(2,4075),(2,4076),(2,4077),(2,4078),(2,4079),(2,4080),(2,4081),(2,4082),(2,4083),(2,4084),(2,4085),(2,4086),(2,4087),(2,4088),(2,4089),(2,4090),(2,4091),(2,4092),(2,4093),(2,4094),(2,4095),(2,4096),(2,4097),(2,4098),(2,4099),(2,4100),(2,4101),(2,4102),(2,4103),(2,4104),(2,4105),(2,4106),(2,4107),(2,4108),(2,4109),(2,4110),(2,4111),(2,4112),(2,4113),(2,4114),(2,4115),(2,4116),(2,4117),(2,4118),(2,4119),(2,4120),(2,4121),(2,4122),(2,4123),(2,4124),(2,4125),(2,4126),(2,4127),(2,4128),(2,4129),(2,4130),(2,4131),(2,4132),(2,4139),(2,4140),(2,4141),(2,4142),(2,4143),(2,4144),(2,4145),(2,4146),(2,4147),(2,4148),(2,4149),(2,4150),(2,4151),(2,4152),(2,4153),(2,4154),(2,4155),(2,4156),(2,4157),(2,4158),(2,4159),(2,4160),(2,4161),(2,4162),(2,4163),(2,4164),(2,4165),(2,4166),(2,4167),(2,4168),(2,4169),(2,4170),(2,4171),(2,4172),(2,4173),(8,28),(8,30),(8,1100),(8,3043),(8,3049),(8,3050),(8,3051);
/*!40000 ALTER TABLE `sys_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_template`
--

DROP TABLE IF EXISTS `sys_role_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_template` (
  `template_id` bigint NOT NULL AUTO_INCREMENT COMMENT '妯℃澘ID',
  `template_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '妯℃澘鍚嶇О',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板描述',
  `template_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '妯℃澘绫诲瀷锛?鑿滃崟鏉冮檺 2鏁版嵁鏉冮檺 3API鏉冮檺锛',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '鐘舵?锛?姝ｅ父 1鍋滅敤锛',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '鍒涘缓鑰',
  `create_time` datetime DEFAULT NULL COMMENT '鍒涘缓鏃堕棿',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '鏇存柊鑰',
  `update_time` datetime DEFAULT NULL COMMENT '鏇存柊鏃堕棿',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '澶囨敞',
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=109 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色模板表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_template`
--

LOCK TABLES `sys_role_template` WRITE;
/*!40000 ALTER TABLE `sys_role_template` DISABLE KEYS */;
INSERT INTO `sys_role_template` VALUES (108,'56+5656456',NULL,'1','0','superadmin',NULL,'',NULL,NULL);
/*!40000 ALTER TABLE `sys_role_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_template_api`
--

DROP TABLE IF EXISTS `sys_role_template_api`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_template_api` (
  `template_id` bigint NOT NULL COMMENT '妯℃澘ID',
  `api_id` bigint NOT NULL COMMENT 'API ID',
  PRIMARY KEY (`template_id`,`api_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色模板和API权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_template_api`
--

LOCK TABLES `sys_role_template_api` WRITE;
/*!40000 ALTER TABLE `sys_role_template_api` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_template_api` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_template_data`
--

DROP TABLE IF EXISTS `sys_role_template_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_template_data` (
  `template_id` bigint NOT NULL COMMENT '妯℃澘ID',
  `perm_id` bigint NOT NULL COMMENT '鏉冮檺ID',
  PRIMARY KEY (`template_id`,`perm_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色模板和数据权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_template_data`
--

LOCK TABLES `sys_role_template_data` WRITE;
/*!40000 ALTER TABLE `sys_role_template_data` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_template_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_template_dept`
--

DROP TABLE IF EXISTS `sys_role_template_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_template_dept` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='角色模板与部门关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_template_dept`
--

LOCK TABLES `sys_role_template_dept` WRITE;
/*!40000 ALTER TABLE `sys_role_template_dept` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_template_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_template_menu`
--

DROP TABLE IF EXISTS `sys_role_template_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_template_menu` (
  `template_id` bigint NOT NULL COMMENT '妯℃澘ID',
  `menu_id` bigint NOT NULL COMMENT '鑿滃崟ID',
  PRIMARY KEY (`template_id`,`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色模板和菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_template_menu`
--

LOCK TABLES `sys_role_template_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_template_menu` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_template_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_template_warehouse`
--

DROP TABLE IF EXISTS `sys_role_template_warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_template_warehouse` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='角色模板与仓库关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_template_warehouse`
--

LOCK TABLES `sys_role_template_warehouse` WRITE;
/*!40000 ALTER TABLE `sys_role_template_warehouse` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_template_warehouse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_warehouse`
--

DROP TABLE IF EXISTS `sys_role_warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_warehouse` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `permission_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '权限类型(read/read_write/admin)',
  PRIMARY KEY (`role_id`,`warehouse_id`) USING BTREE,
  KEY `idx_role_warehouse_role_id` (`role_id`) USING BTREE,
  KEY `idx_role_warehouse_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `idx_role_warehouse_composite` (`role_id`,`warehouse_id`,`permission_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='角色-仓库关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_warehouse`
--

LOCK TABLES `sys_role_warehouse` WRITE;
/*!40000 ALTER TABLE `sys_role_warehouse` DISABLE KEYS */;
INSERT INTO `sys_role_warehouse` VALUES (4,1,NULL),(4,2,NULL),(4,3,NULL),(4,4,NULL),(4,5,NULL),(4,6,NULL),(4,7,NULL),(4,8,NULL),(4,9,NULL),(4,10,NULL),(4,11,NULL),(4,12,NULL),(4,13,NULL),(4,14,NULL),(4,15,NULL),(4,16,NULL),(4,17,NULL),(4,18,NULL),(4,19,NULL),(4,20,NULL),(4,21,NULL);
/*!40000 ALTER TABLE `sys_role_warehouse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_security_log`
--

DROP TABLE IF EXISTS `sys_security_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_security_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户名称',
  `event_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '安全事件类型',
  `event_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '事件描述',
  `risk_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '风险级别',
  `client_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户端IP',
  `client_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户端位置',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户代理',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '处理状态（0未处理 1已处理 2已忽略）',
  `handle_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处理人',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处理备注',
  `event_time` datetime DEFAULT NULL COMMENT '事件时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `idx_user_name` (`user_name`) USING BTREE,
  KEY `idx_event_type` (`event_type`) USING BTREE,
  KEY `idx_event_time` (`event_time`) USING BTREE,
  KEY `idx_risk_level` (`risk_level`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='安全日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_security_log`
--

LOCK TABLES `sys_security_log` WRITE;
/*!40000 ALTER TABLE `sys_security_log` DISABLE KEYS */;
INSERT INTO `sys_security_log` VALUES (1,'admin','管理员','LOGIN_FAIL','用户尝试使用错误密码登录','MEDIUM','************0','内网',NULL,'0',NULL,NULL,NULL,'2025-07-26 16:16:55','system','2025-07-26 16:16:55',NULL,NULL,NULL),(2,'test','测试用户','PERMISSION_DENIED','用户尝试访问未授权的功能模块','HIGH','************1','内网',NULL,'0',NULL,NULL,NULL,'2025-07-26 16:16:55','system','2025-07-26 16:16:55',NULL,NULL,NULL),(3,'superadmin','超级管理员','PASSWORD_CHANGE','用户修改了登录密码','LOW','192.168.1.1','内网',NULL,'1',NULL,NULL,NULL,'2025-07-26 16:16:55','system','2025-07-26 16:16:55',NULL,NULL,NULL);
/*!40000 ALTER TABLE `sys_security_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称',
  `real_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '密码',
  `login_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'username' COMMENT '鐧诲綍鏂瑰紡(username:鐢ㄦ埛鍚?phone:鎵嬫満鍙?realname:鐪熷疄濮撳悕)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE,
  KEY `idx_user_name` (`user_name`) USING BTREE,
  KEY `idx_user_status` (`status`) USING BTREE,
  KEY `idx_user_phonenumber` (`phonenumber`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,100,'superadmin','超级管理员','莫少雄','00','<EMAIL>','15888888888','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','realname','0','0','127.0.0.1','2025-07-28 12:31:24','superadmin','2025-05-15 18:00:00','superadmin','2025-07-28 12:31:23','超级管理员'),(2,205,'systemadmin','系统管理员','管理员','00','','13800138000','0','','$2a$10$TcB9ClPolqV3gRgDRBNdbudruK8mr.HQxKtIFoRxZma7XX8kGyCjW','username','0','0','127.0.0.1','2025-07-21 23:20:41','superadmin','2025-05-15 18:00:00','superadmin','2025-07-21 23:20:41','系统管理员'),(3,205,'cangguan','仓库管理员','仓管','00','','13800138001','0','','$2a$10$fQlGmxJtbPN0AaaOjHBIbeSQOgaY0EWkREo1f3b6nqmvgVyHf12Iu','realname','0','0','127.0.0.1','2025-07-25 14:25:42','superadmin','2025-06-20 09:32:17','superadmin','2025-07-25 14:25:41','仓库系统管理员'),(4,205,'commonuser','仓库操作员','操作员','00','','13036521548','0','','$2a$10$TqN3TQ6SnjInYIqHV3LmIelxUXKlHxfosJShxG.rSbT64QJ9XlAyS','phone','0','0','127.0.0.1','2025-07-25 09:59:47','superadmin','2025-07-13 14:33:13','superadmin','2025-07-25 09:59:47','仓库日常操作'),(5,NULL,'putongyonghu','测试用户','普通用户','00','','','0','','$2a$10$XZmCBkHuUpUCX8Ujm9.MCefhxEbZv55WZGifTcnuBvLoZkblbSuiS','username','0','0','',NULL,'superadmin','2025-07-14 19:48:07','superadmin','2025-07-14 20:51:54','基本操作权限'),(6,216,'zhangsan','zhangsan','张三','00','','13036952158','0','','$2a$10$mn2FTuC7VOkkcO4BNPCx6OlLGI97I4Mer3hHcnYjAlt4SF2rpcwHq','username','0','0','127.0.0.1','2025-07-14 22:16:19','','2025-07-14 20:07:05','superadmin','2025-07-15 09:00:15',NULL);
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `user_delete_backup_trigger` BEFORE UPDATE ON `sys_user` FOR EACH ROW BEGIN
IF NEW.del_flag = '2' AND OLD.del_flag = '0' THEN
INSERT IGNORE INTO sys_user_backup SELECT * FROM sys_user WHERE user_id = OLD.user_id;
END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `sys_user_api`
--

DROP TABLE IF EXISTS `sys_user_api`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_api` (
  `user_id` bigint NOT NULL COMMENT '鐢ㄦ埛ID',
  `api_id` bigint NOT NULL COMMENT 'API鏉冮檺ID',
  PRIMARY KEY (`user_id`,`api_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户和API关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_api`
--

LOCK TABLES `sys_user_api` WRITE;
/*!40000 ALTER TABLE `sys_user_api` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_user_api` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_data_permission`
--

DROP TABLE IF EXISTS `sys_user_data_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_data_permission` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `perm_id` bigint NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`user_id`,`perm_id`) USING BTREE,
  KEY `idx_user_data_permission_user_id` (`user_id`) USING BTREE,
  KEY `idx_user_data_permission_perm_id` (`perm_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户和数据权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_data_permission`
--

LOCK TABLES `sys_user_data_permission` WRITE;
/*!40000 ALTER TABLE `sys_user_data_permission` DISABLE KEYS */;
INSERT INTO `sys_user_data_permission` VALUES (1,1),(1,2),(1,3),(1,4),(1,5),(1,6),(1,7),(2,1),(2,2),(2,3),(2,4),(2,5),(2,6),(2,7),(3,2),(3,6),(6,1),(6,2),(6,3),(6,4),(6,5),(6,6),(6,7);
/*!40000 ALTER TABLE `sys_user_data_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_menu`
--

DROP TABLE IF EXISTS `sys_user_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_menu` (
  `user_id` bigint NOT NULL COMMENT '鐢ㄦ埛ID',
  `menu_id` bigint NOT NULL COMMENT '鑿滃崟ID',
  PRIMARY KEY (`user_id`,`menu_id`) USING BTREE,
  KEY `idx_user_menu_user_id` (`user_id`) USING BTREE,
  KEY `idx_user_menu_menu_id` (`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户和菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_menu`
--

LOCK TABLES `sys_user_menu` WRITE;
/*!40000 ALTER TABLE `sys_user_menu` DISABLE KEYS */;
INSERT INTO `sys_user_menu` VALUES (1,2),(1,3),(1,4),(1,5),(1,6),(1,7),(1,8),(1,9),(1,10),(1,11),(1,12),(1,13),(1,14),(1,15),(1,17),(1,18),(1,19),(1,20),(1,21),(1,22),(1,23),(1,24),(1,25),(1,26),(1,27),(1,28),(1,29),(1,30),(1,31),(1,32),(1,33),(1,34),(1,35),(1,36),(1,37),(1,38),(1,39),(1,40),(1,41),(1,42),(1,1901),(1,1905),(1,1906),(1,1911),(1,1915),(1,1916),(1,2001),(1,2005),(1,2006),(1,2020),(1,2024),(1,2025),(1,2026),(1,2027),(1,2030),(1,2034),(1,2035),(1,2036),(1,2037),(1,2040),(1,2044),(1,2045),(1,2046),(1,2047),(1,2050),(1,2054),(1,2055),(1,2056),(1,2057),(1,3001),(1,3003),(1,3004),(1,3020),(1,3022),(1,3023),(1,3030),(1,3032),(1,3033),(2,2),(2,3),(2,4),(2,7),(2,8),(2,14),(2,15),(2,17),(2,18),(2,19),(2,20),(2,21),(2,22),(2,23),(2,24),(2,25),(2,26),(2,27),(2,28),(2,29),(2,30),(2,31),(2,32),(2,33),(2,34),(2,35),(2,36),(2,37),(2,38),(2,39),(2,40),(2,41),(2,42),(2,2100),(2,2110),(2,3038),(2,3039),(2,3040),(2,3041),(2,3042),(2,3784),(2,3785),(2,3786),(2,3787),(2,3788),(2,3789),(2,3790),(2,3791),(2,3792),(2,3851),(2,3852),(2,3853),(2,3854),(2,3855),(2,3856),(2,3857),(2,3858),(2,3859),(2,3860),(2,3861),(2,3862),(2,3863),(2,3864),(2,3865),(2,3866),(2,3893),(2,3894),(2,3895),(2,3896),(2,3897),(2,3898),(2,3899),(2,3900),(2,3901),(2,3902),(2,3913),(2,3914),(2,3915),(2,3916),(2,3917),(2,3918),(2,3919),(2,3920),(2,3921),(2,3922),(2,3923),(2,3924),(2,3925),(2,3926),(2,3927),(2,3928),(2,3929),(2,3930),(2,3931),(2,3932),(2,3933),(2,3934),(2,3935),(2,3936),(2,3937),(2,3938),(2,3939),(2,3940),(2,3941),(2,3942),(2,3943),(2,3944),(2,3945),(2,3946),(2,3947),(2,3948),(2,3949),(2,3950),(2,3951),(2,3952),(2,3953),(2,3954),(2,3955),(2,3956),(2,3957),(2,3958),(2,3959),(2,3960),(2,3961),(2,3962),(2,3963),(2,3964),(2,3965),(2,3966),(2,3967),(2,3968),(2,3969),(2,3970),(2,3971),(2,3972),(2,3973),(2,3974),(2,3975),(2,3976),(2,3977),(2,3981),(2,3982),(2,3983),(2,3984),(2,3985),(2,3986),(2,3987),(2,3989),(2,3990),(3,14),(3,15),(3,17),(3,18),(3,19),(3,20),(3,21),(3,27),(3,28),(3,29),(3,30),(3,31),(3,32),(3,33),(3,2100),(3,2110),(3,3038),(3,3039),(3,3040),(3,3041),(3,3042),(3,3789),(3,3790),(3,3791),(3,3792),(3,3855),(3,3856),(3,3857),(3,3858),(3,3859),(3,3860),(3,3861),(3,3862),(3,3863),(3,3864),(3,3865),(3,3866),(3,3893),(3,3899),(3,3900),(3,3901),(3,3902),(3,3913),(3,3914),(3,3915),(3,3916),(3,3917),(3,3918),(3,3919),(3,3920),(3,3921),(3,3922),(3,3923),(3,3924),(3,3925),(3,3926),(3,3927),(3,3928),(3,3929),(3,3930),(3,3931),(3,3932),(3,3933),(3,3934),(3,3935),(3,3936),(3,3937),(3,3938),(3,3939),(3,3940),(3,3941),(3,3942),(3,3943),(3,3944),(3,3945),(3,3946),(3,3947),(3,3948),(6,3),(6,27),(6,28),(6,30),(6,3043),(6,3049),(6,3050),(6,3051),(100,2),(100,3),(100,4),(100,5),(100,6),(100,7),(100,8),(100,9),(100,10),(100,11),(100,12),(100,13),(100,14),(100,15),(100,17),(100,18),(100,19),(100,20),(100,21),(100,22),(100,23),(100,24),(100,25),(100,26),(100,27),(100,28),(100,29),(100,30),(100,31),(100,32),(100,33),(100,34),(100,35),(100,36),(100,37),(100,38),(100,39),(100,40),(100,41),(100,42),(100,1901),(100,1905),(100,1906),(100,1911),(100,1915),(100,1916),(100,2005),(100,2006),(100,2100),(100,2101),(100,2102),(100,2103),(100,2104),(100,2105),(100,2110),(100,2111),(100,2112),(100,2113),(100,3001),(100,3003),(100,3004),(100,3020),(100,3022),(100,3023),(100,3030),(100,3032),(100,3033),(100,3036),(100,3037),(100,3038),(100,3039),(100,3040),(100,3041),(100,3042),(100,3043),(100,3044),(100,3045),(100,3046),(100,3047),(100,3048),(100,3049),(100,3050),(100,3051),(100,3059),(100,3060),(100,3061),(100,3062),(100,3063),(100,3064),(100,3065),(100,3066),(100,3067),(100,3068),(100,3069),(100,3070),(100,3071),(100,3072),(100,3073),(100,3074),(100,3075),(100,3076),(100,3077),(100,3078),(100,3079),(100,3080),(114,2),(114,3),(114,4),(114,5),(114,6),(114,7),(114,8),(114,9),(114,10),(114,11),(114,12),(114,13),(114,14),(114,15),(114,17),(114,18),(114,19),(114,20),(114,21),(114,22),(114,23),(114,24),(114,25),(114,26),(114,27),(114,28),(114,29),(114,30),(114,31),(114,32),(114,33),(114,34),(114,35),(114,36),(114,37),(114,38),(114,39),(114,40),(114,41),(114,42),(114,1901),(114,1905),(114,1906),(114,1911),(114,1915),(114,1916),(114,2005),(114,2006),(114,2100),(114,2101),(114,2102),(114,2103),(114,2104),(114,2105),(114,2110),(114,2111),(114,2112),(114,2113),(114,3001),(114,3003),(114,3004),(114,3020),(114,3022),(114,3023),(114,3030),(114,3032),(114,3033),(114,3036),(114,3037),(114,3038),(114,3039),(114,3040),(114,3041),(114,3042),(114,3043),(114,3044),(114,3045),(114,3046),(114,3047),(114,3048),(114,3049),(114,3050),(114,3051),(114,3059),(114,3060),(114,3061),(114,3062),(114,3063),(114,3064),(114,3065),(114,3066),(114,3067),(114,3068),(114,3069),(114,3070),(114,3071),(114,3072),(114,3073),(114,3074),(114,3075),(114,3076),(114,3077),(114,3078),(114,3079),(114,3080),(120,2),(120,14),(120,15),(120,17),(120,18),(120,19),(120,20),(120,21),(120,22),(120,23),(120,24),(120,25),(120,26),(120,27),(120,28),(120,29),(120,30),(120,31),(120,32),(120,33),(120,34),(120,35),(120,36),(120,37),(120,38),(120,39),(120,40),(120,41),(120,42),(120,1905),(120,1906),(120,1911),(120,1915),(120,1916),(120,2005),(120,2006),(120,2100),(120,2101),(120,2102),(120,2103),(120,2104),(120,2105),(120,2110),(120,2111),(120,2112),(120,2113),(120,3001),(120,3003),(120,3004),(120,3036),(120,3037),(120,3038),(120,3039),(120,3040),(120,3041),(120,3042),(120,3043),(120,3044),(120,3045),(120,3046),(120,3047),(120,3048),(120,3049),(120,3050),(120,3051),(120,3059),(120,3060),(120,3061),(120,3062),(120,3063),(120,3064),(120,3065),(120,3066),(120,3067),(120,3068),(120,3069),(120,3070),(120,3071),(120,3072),(120,3073),(120,3074),(120,3075),(120,3076),(120,3077),(120,3078),(120,3079),(120,3080),(120,3725),(120,3726),(120,3727),(120,3738),(120,3739),(120,3740),(120,3741),(120,3742),(120,3743);
/*!40000 ALTER TABLE `sys_user_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_permission`
--

DROP TABLE IF EXISTS `sys_user_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_permission` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`user_id`,`permission_id`) USING BTREE,
  KEY `idx_user_permission_user_id` (`user_id`) USING BTREE,
  KEY `idx_user_permission_permission_id` (`permission_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户和权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_permission`
--

LOCK TABLES `sys_user_permission` WRITE;
/*!40000 ALTER TABLE `sys_user_permission` DISABLE KEYS */;
INSERT INTO `sys_user_permission` VALUES (1,1),(1,2),(1,3),(1,4),(1,5),(1,6),(1,100),(1,101),(1,102),(2,100),(2,101),(2,102),(3,100),(3,101),(3,102),(4,100),(4,101),(4,102),(5,100),(5,101),(5,102),(6,100),(6,101),(6,102);
/*!40000 ALTER TABLE `sys_user_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_role` (
  `user_id` bigint NOT NULL COMMENT '鐢ㄦ埛ID',
  `role_id` bigint NOT NULL COMMENT '瑙掕壊ID',
  PRIMARY KEY (`user_id`,`role_id`) USING BTREE,
  KEY `idx_user_role_user_id` (`user_id`) USING BTREE,
  KEY `idx_user_role_role_id` (`role_id`) USING BTREE,
  KEY `idx_user_role_composite` (`user_id`,`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户和角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
INSERT INTO `sys_user_role` VALUES (1,1),(1,4),(2,1),(2,2),(2,3),(2,4),(2,5),(2,6),(2,7),(2,8),(3,3),(3,4),(3,5),(5,2),(6,8),(120,3),(126,3);
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_warehouse`
--

DROP TABLE IF EXISTS `sys_user_warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_warehouse` (
  `user_id` bigint NOT NULL COMMENT '鐢ㄦ埛ID',
  `warehouse_id` bigint NOT NULL COMMENT '浠撳簱ID',
  `permission_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '权限类型(read/read_write/admin)',
  `source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'direct' COMMENT '权限来源(direct/role)',
  PRIMARY KEY (`user_id`,`warehouse_id`) USING BTREE COMMENT '鐢ㄦ埛鍜屼粨搴撳叧鑱斾富閿',
  KEY `idx_user_warehouse_user_id` (`user_id`) USING BTREE,
  KEY `idx_user_warehouse_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `idx_user_warehouse_composite` (`user_id`,`warehouse_id`,`permission_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户和仓库关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_warehouse`
--

LOCK TABLES `sys_user_warehouse` WRITE;
/*!40000 ALTER TABLE `sys_user_warehouse` DISABLE KEYS */;
INSERT INTO `sys_user_warehouse` VALUES (1,1,'FULL','direct'),(2,1,'read_write','direct'),(2,2,'read_write','direct'),(2,3,'read_write','direct'),(2,4,'read_write','direct'),(2,5,'read_write','direct'),(2,6,'read_write','direct'),(2,7,'read_write','direct'),(2,8,'read_write','direct'),(2,9,'read_write','direct'),(2,10,'read_write','direct'),(2,11,'read_write','direct'),(2,12,'read_write','direct'),(2,13,'read_write','direct'),(2,14,'read_write','direct'),(2,15,'read_write','direct'),(2,16,'read_write','direct'),(2,17,'read_write','direct'),(2,18,'read_write','direct'),(2,19,'read_write','direct'),(2,20,'read_write','direct'),(2,21,'read_write','direct'),(3,1,'read_write','direct'),(3,3,'read_write','direct'),(3,6,'read_write','direct'),(6,1,'read_write','direct'),(6,2,'read_write','direct'),(6,3,'read_write','direct'),(6,4,'read_write','direct'),(6,5,'read_write','direct'),(6,6,'read_write','direct'),(6,7,'read_write','direct'),(6,8,'read_write','direct');
/*!40000 ALTER TABLE `sys_user_warehouse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_warehouse`
--

DROP TABLE IF EXISTS `sys_warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_warehouse` (
  `warehouse_id` bigint NOT NULL AUTO_INCREMENT COMMENT '仓库ID',
  `warehouse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '仓库名称',
  `warehouse_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '仓库编码',
  `warehouse_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '仓库地址',
  `warehouse_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '仓库状态（0正常 1停用）',
  `warehouse_capacity` decimal(10,2) DEFAULT NULL COMMENT '仓库容量',
  `warehouse_used` decimal(10,2) DEFAULT '0.00' COMMENT '已使用容量',
  `manager_id` bigint DEFAULT NULL COMMENT '负责人ID',
  `manager_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '负责人姓名',
  `manager_phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '负责人电话',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`warehouse_id`) USING BTREE,
  KEY `idx_warehouse_status` (`warehouse_status`) USING BTREE,
  KEY `idx_warehouse_code` (`warehouse_code`) USING BTREE,
  KEY `idx_warehouse_name` (`warehouse_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='仓库表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_warehouse`
--

LOCK TABLES `sys_warehouse` WRITE;
/*!40000 ALTER TABLE `sys_warehouse` DISABLE KEYS */;
INSERT INTO `sys_warehouse` VALUES (1,'万裕物业总仓库','WH001','东方湖岸物业服务中心二楼','0',1000.00,0.00,NULL,'admin','13800138000',NULL,'2025-07-14 16:12:59',NULL,'2025-07-25 09:11:49',NULL),(2,'东方水岸客服部仓库','WH002','东方水岸','0',800.00,0.00,NULL,'admin','13800138001',NULL,'2025-07-14 16:12:59',NULL,'2025-07-22 15:46:32',NULL),(3,'东方水岸工程部仓库','WH003','东方水岸','0',800.00,0.00,NULL,'admin','13800138002',NULL,'2025-07-14 16:12:59',NULL,'2025-07-24 20:26:21',NULL),(4,'东方水岸秩序部仓库','WH004','东方水岸','0',800.00,0.00,NULL,'admin','13800138003',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(5,'东方水岸保洁部仓库','WH005','东方水岸','0',800.00,0.00,NULL,'admin','13800138004',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(6,'东方湖岸客服部仓库','WH006','东方湖岸','0',800.00,0.00,NULL,'admin','13800138005',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(7,'东方湖岸工程部仓库','WH007','东方湖岸','0',800.00,0.00,NULL,'admin','13800138006',NULL,'2025-07-14 16:12:59',NULL,'2025-07-14 19:57:47',NULL),(8,'东方湖岸秩序部仓库','WH008','东方湖岸','0',800.00,0.00,NULL,'admin','13800138007',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(9,'东方湖岸保洁部仓库','WH009','东方湖岸','0',800.00,0.00,NULL,'admin','13800138008',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(10,'东方韵客服部仓库','WH010','东方韵','0',800.00,0.00,NULL,'admin','13800138009',NULL,'2025-07-14 16:12:59',NULL,'2025-07-15 09:00:41',NULL),(11,'东方韵工程部仓库','WH011','东方韵','0',800.00,0.00,NULL,'admin','13800138010',NULL,'2025-07-14 16:12:59',NULL,'2025-07-17 19:16:15',NULL),(12,'东方韵秩序部仓库','WH012','东方韵','0',800.00,0.00,NULL,'admin','13800138011',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(13,'东方韵保洁部仓库','WH013','东方韵','0',800.00,0.00,NULL,'admin','13800138012',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(14,'东方河畔客服部仓库','WH014','东方河畔','0',800.00,0.00,NULL,'admin','13800138013',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(15,'东方河畔工程部仓库','WH015','东方河畔','0',800.00,0.00,NULL,'admin','13800138014',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(16,'东方河畔秩序部仓库','WH016','东方河畔','0',800.00,0.00,NULL,'admin','13800138015',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(17,'东方河畔保洁部仓库','WH017','东方河畔','0',800.00,0.00,NULL,'admin','13800138016',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(18,'现代东方客服部仓库','WH018','现代东方','0',800.00,0.00,NULL,'admin','13800138017',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(19,'现代东方工程部仓库','WH019','现代东方','0',800.00,0.00,NULL,'admin','13800138018',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(20,'现代东方秩序部仓库','WH020','现代东方','0',800.00,0.00,NULL,'admin','13800138019',NULL,'2025-07-14 16:12:59',NULL,NULL,NULL),(21,'现代东方保洁部仓库','WH021','现代东方','0',800.00,0.00,NULL,'admin','13800138020',NULL,'2025-07-14 16:12:59',NULL,'2025-07-22 15:53:08',NULL);
/*!40000 ALTER TABLE `sys_warehouse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_warehouse_auth`
--

DROP TABLE IF EXISTS `sys_warehouse_auth`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_warehouse_auth` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `auth_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限类型(read/view/manage/admin)',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_user_warehouse` (`user_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='仓库权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_warehouse_auth`
--

LOCK TABLES `sys_warehouse_auth` WRITE;
/*!40000 ALTER TABLE `sys_warehouse_auth` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_warehouse_auth` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_warehouse_data_permission`
--

DROP TABLE IF EXISTS `sys_warehouse_data_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_warehouse_data_permission` (
  `perm_id` bigint NOT NULL COMMENT '鏉冮檺ID',
  `warehouse_id` bigint NOT NULL COMMENT '浠撳簱ID',
  PRIMARY KEY (`perm_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='仓库和数据权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_warehouse_data_permission`
--

LOCK TABLES `sys_warehouse_data_permission` WRITE;
/*!40000 ALTER TABLE `sys_warehouse_data_permission` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_warehouse_data_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_alert_log`
--

DROP TABLE IF EXISTS `wms_alert_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_alert_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `rule_id` bigint DEFAULT NULL COMMENT '规则ID',
  `warehouse_id` bigint DEFAULT NULL COMMENT '仓库ID',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `current_quantity` decimal(10,2) DEFAULT NULL COMMENT '当前库存量',
  `threshold_quantity` decimal(10,2) DEFAULT NULL COMMENT '阈值库存量',
  `alert_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预警类型（1低于最小值 2超过最大值）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0未处理 1已处理）',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理人',
  `process_result` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理结果',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `idx_warehouse_product` (`warehouse_id`,`product_id`) USING BTREE COMMENT '仓库商品索引',
  KEY `idx_rule_id` (`rule_id`) USING BTREE COMMENT '规则ID索引',
  KEY `idx_status` (`status`) USING BTREE COMMENT '状态索引'
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='库存预警记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_alert_log`
--

LOCK TABLES `wms_alert_log` WRITE;
/*!40000 ALTER TABLE `wms_alert_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `wms_alert_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_alert_rule`
--

DROP TABLE IF EXISTS `wms_alert_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_alert_rule` (
  `rule_id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `enable_alert` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用预警系统',
  `enable_low_alert` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用低库存预警',
  `low_alert_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'fixed' COMMENT '低库存预警类型（fixed固定值 percentage百分比 dynamic动态）',
  `default_min_quantity` decimal(10,2) NOT NULL DEFAULT '10.00' COMMENT '默认最小库存量',
  `low_percentage` int NOT NULL DEFAULT '20' COMMENT '低库存百分比阈值',
  `low_dynamic_days` int NOT NULL DEFAULT '30' COMMENT '低库存动态计算天数',
  `low_safety_factor` decimal(10,1) NOT NULL DEFAULT '1.5' COMMENT '低库存安全系数',
  `enable_high_alert` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用高库存预警',
  `high_alert_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'fixed' COMMENT '高库存预警类型（fixed固定值 percentage百分比 dynamic动态）',
  `default_max_quantity` decimal(10,2) NOT NULL DEFAULT '100.00' COMMENT '默认最大库存量',
  `high_percentage` int NOT NULL DEFAULT '200' COMMENT '高库存百分比阈值',
  `high_dynamic_days` int NOT NULL DEFAULT '30' COMMENT '高库存动态计算天数',
  `high_safety_factor` decimal(10,1) NOT NULL DEFAULT '2.0' COMMENT '高库存安全系数',
  `enable_email_notify` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用邮件通知',
  `notify_frequency` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'daily' COMMENT '通知频率（realtime实时 daily每日 weekly每周）',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `notify_receivers` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '通知接收人（用户ID列表，逗号分隔）',
  `last_apply_time` datetime DEFAULT NULL COMMENT '最后应用时间',
  `last_apply_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最后应用用户',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`rule_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='告警规则表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_alert_rule`
--

LOCK TABLES `wms_alert_rule` WRITE;
/*!40000 ALTER TABLE `wms_alert_rule` DISABLE KEYS */;
INSERT INTO `wms_alert_rule` VALUES (1,1,1,'fixed',10.00,20,30,1.5,1,'fixed',100.00,200,30,2.0,1,'daily','2025-05-25 09:00:00',NULL,NULL,NULL,'admin','2025-05-25 00:00:00','',NULL,'Alert rule configuration');
/*!40000 ALTER TABLE `wms_alert_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory`
--

DROP TABLE IF EXISTS `wms_inventory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory` (
  `inventory_id` bigint NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `product_id` bigint NOT NULL COMMENT '物品ID',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) DEFAULT '0.00' COMMENT '库存数量',
  `min_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '最小库存量',
  `max_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '最大库存量',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '库存状态（0正常 1预警 2缺货）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`inventory_id`) USING BTREE,
  KEY `idx_inventory_product_id` (`product_id`) USING BTREE,
  KEY `idx_inventory_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `idx_inventory_product_warehouse` (`product_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=275 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='库存信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory`
--

LOCK TABLES `wms_inventory` WRITE;
/*!40000 ALTER TABLE `wms_inventory` DISABLE KEYS */;
INSERT INTO `wms_inventory` VALUES (270,201,1,0.00,0.00,0.00,'2',NULL,'',NULL,'','2025-07-26 22:10:00'),(271,201,2,1.00,0.00,0.00,'0',NULL,'',NULL,'',NULL),(272,202,5,0.00,1.00,8.00,'2',NULL,'',NULL,'','2025-07-28 10:12:42'),(273,205,1,14.00,0.00,0.00,'0',NULL,'',NULL,'',NULL),(274,202,1,1.00,0.00,0.00,'0',NULL,'',NULL,'',NULL);
/*!40000 ALTER TABLE `wms_inventory` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory_check`
--

DROP TABLE IF EXISTS `wms_inventory_check`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_check` (
  `check_id` bigint NOT NULL AUTO_INCREMENT COMMENT '盘点ID',
  `check_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '盘点单号',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `check_time` datetime NOT NULL COMMENT '盘点时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0未审核 1已审核 2已取消）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `audit_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核附注（内部记录）',
  `audit_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`check_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=209 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='库存盘点单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_check`
--

LOCK TABLES `wms_inventory_check` WRITE;
/*!40000 ALTER TABLE `wms_inventory_check` DISABLE KEYS */;
INSERT INTO `wms_inventory_check` VALUES (207,'CK20250726220456',1,'2025-07-26 22:04:51','1','','superadmin','2025-07-26 22:04:57','superadmin','2025-07-28 10:13:51',NULL,'superadmin','2025-07-28 10:13:51'),(208,'CK20250728101338',1,'2025-07-28 10:13:35','1','','superadmin','2025-07-28 10:13:39','superadmin','2025-07-28 10:13:47',NULL,'superadmin','2025-07-28 10:13:47');
/*!40000 ALTER TABLE `wms_inventory_check` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory_check_detail`
--

DROP TABLE IF EXISTS `wms_inventory_check_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_check_detail` (
  `detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `check_id` bigint NOT NULL COMMENT '盘点ID',
  `product_id` bigint NOT NULL COMMENT '物品ID',
  `book_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '账面数量',
  `real_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '实际数量',
  `diff_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '差异数量',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`detail_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=229 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='库存盘点单明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_check_detail`
--

LOCK TABLES `wms_inventory_check_detail` WRITE;
/*!40000 ALTER TABLE `wms_inventory_check_detail` DISABLE KEYS */;
INSERT INTO `wms_inventory_check_detail` VALUES (225,207,201,2.00,2.00,0.00,NULL),(226,208,201,0.00,0.00,0.00,NULL),(227,208,205,14.00,14.00,0.00,NULL),(228,208,202,1.00,1.00,0.00,NULL);
/*!40000 ALTER TABLE `wms_inventory_check_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory_in`
--

DROP TABLE IF EXISTS `wms_inventory_in`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_in` (
  `in_id` bigint NOT NULL AUTO_INCREMENT COMMENT '入库ID',
  `in_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '入库单号',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `in_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '入库类型（1采购入库 2生产入库 3调拨入库 4其他入库）',
  `in_time` datetime NOT NULL COMMENT '入库时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0未审核 1已审核 2已取消）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `audit_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核附注（内部记录）',
  `audit_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`in_id`) USING BTREE,
  KEY `idx_in_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `idx_in_status` (`status`) USING BTREE,
  KEY `idx_in_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=270 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='入库单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_in`
--

LOCK TABLES `wms_inventory_in` WRITE;
/*!40000 ALTER TABLE `wms_inventory_in` DISABLE KEYS */;
INSERT INTO `wms_inventory_in` VALUES (268,'IN20250726215619',1,'4','2025-07-26 21:56:19','1','','superadmin','2025-07-26 21:56:19','superadmin','2025-07-26 22:01:14','','superadmin','2025-07-26 22:01:14'),(269,'IN20250728101058',1,'1','2025-07-28 10:10:59','1','','superadmin','2025-07-28 10:10:59','superadmin','2025-07-28 10:11:04','','superadmin','2025-07-28 10:11:04');
/*!40000 ALTER TABLE `wms_inventory_in` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory_in_detail`
--

DROP TABLE IF EXISTS `wms_inventory_in_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_in_detail` (
  `detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `in_id` bigint NOT NULL COMMENT '入库ID',
  `product_id` bigint NOT NULL COMMENT '物品ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '入库数量',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '入库单价',
  `amount` decimal(10,2) DEFAULT '0.00' COMMENT '入库金额',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`detail_id`) USING BTREE,
  KEY `idx_in_detail_in_id` (`in_id`) USING BTREE,
  KEY `idx_in_detail_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=286 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='入库单明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_in_detail`
--

LOCK TABLES `wms_inventory_in_detail` WRITE;
/*!40000 ALTER TABLE `wms_inventory_in_detail` DISABLE KEYS */;
INSERT INTO `wms_inventory_in_detail` VALUES (284,268,201,2.00,0.00,0.00,NULL),(285,269,205,14.00,4.00,56.00,NULL);
/*!40000 ALTER TABLE `wms_inventory_in_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory_log`
--

DROP TABLE IF EXISTS `wms_inventory_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'IN' COMMENT '操作类型(IN-入库,OUT-出库,TRANSFER-调拨,ADJUST-调整)',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `warehouse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '仓库名称',
  `product_id` bigint DEFAULT NULL COMMENT '物品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物品名称',
  `product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物品编码',
  `product_spec` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物品规格',
  `product_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物品单位',
  `quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '鎿嶄綔鏁伴噺',
  `before_quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '鎿嶄綔鍓嶆暟閲',
  `after_quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '鎿嶄綔鍚庢暟閲',
  `unit_price` decimal(10,2) DEFAULT '0.00' COMMENT '单价',
  `total_amount` decimal(12,2) DEFAULT '0.00' COMMENT '总金额',
  `related_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '关联单据号',
  `related_order_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联单据类型',
  `operator` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'system' COMMENT '操作人员',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人员ID',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鎿嶄綔鏃堕棿',
  `location_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '库位编码',
  `location_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '库位名称',
  `batch_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '批次号',
  `expire_date` date DEFAULT NULL COMMENT '过期日期',
  `supplier_id` bigint DEFAULT NULL COMMENT '供应商ID',
  `supplier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商名称',
  `customer_id` bigint DEFAULT NULL COMMENT '客户ID',
  `customer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户名称',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作原因',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '鐘舵?锛?姝ｅ父 1寮傚父锛',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '鍒涘缓鑰',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '鏇存柊鏃堕棿',
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `idx_operation_type` (`operation_type`) USING BTREE,
  KEY `idx_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `idx_product_id` (`product_id`) USING BTREE,
  KEY `idx_operation_time` (`operation_time`) USING BTREE,
  KEY `idx_operator` (`operator`) USING BTREE,
  KEY `idx_related_order` (`related_order_id`) USING BTREE,
  KEY `idx_warehouse_operation_time` (`warehouse_id`,`operation_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=108 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='出入库日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_log`
--

LOCK TABLES `wms_inventory_log` WRITE;
/*!40000 ALTER TABLE `wms_inventory_log` DISABLE KEYS */;
INSERT INTO `wms_inventory_log` VALUES (100,'IN',1,'万裕物业总仓库',201,'矿泉水','WATER001','标准规格','瓶',2.00,0.00,2.00,0.00,0.00,'IN2025-07-4554','PURCHASE','superadmin',1,'2025-07-26 22:01:14',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'系统自动记录-入库操作','0','superadmin','2025-07-26 22:01:14','','2025-07-26 22:01:14'),(101,'OUT',1,'万裕物业总仓库',201,'矿泉水','WATER001','标准规格','瓶',1.00,2.00,1.00,0.00,0.00,'OUT2025-07-0455','SALES','superadmin',1,'2025-07-26 22:07:50',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'系统自动记录-出库操作','0','superadmin','2025-07-26 22:07:50','','2025-07-26 22:07:50'),(102,'OUT',1,'万裕物业总仓库',201,'矿泉水','WATER001','标准规格','瓶',1.00,1.00,0.00,0.00,0.00,'OUT2025-07-0661','SALES','superadmin',1,'2025-07-26 22:10:01',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'系统自动记录-出库操作','0','superadmin','2025-07-26 22:10:01','','2025-07-26 22:10:00'),(103,'IN',2,'东方水岸客服部仓库',201,'矿泉水','WATER001','标准规格','瓶',1.00,0.00,1.00,0.00,0.00,'IN2025-07-0673','PURCHASE','superadmin',1,'2025-07-26 22:10:01',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'系统自动记录-入库操作','0','superadmin','2025-07-26 22:10:01','','2025-07-26 22:10:00'),(104,'OUT',5,'东方水岸保洁部仓库',202,'物品-202','CODE-202','标准规格','个',1.00,2.00,1.00,0.00,0.00,'OUT2025-07-8422','SALES','superadmin',1,'2025-07-28 10:10:06',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'系统自动记录-出库操作','0','superadmin','2025-07-28 10:10:06','','2025-07-28 10:10:08'),(105,'IN',1,'万裕物业总仓库',205,'物品-205','CODE-205','标准规格','个',14.00,0.00,14.00,0.00,0.00,'IN2025-07-5957','PURCHASE','superadmin',1,'2025-07-28 10:11:04',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'系统自动记录-入库操作','0','superadmin','2025-07-28 10:11:04','','2025-07-28 10:11:05'),(106,'OUT',5,'东方水岸保洁部仓库',202,'物品-202','CODE-202','标准规格','个',1.00,1.00,0.00,0.00,0.00,'OUT2025-07-4937','SALES','superadmin',1,'2025-07-28 10:12:43',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'系统自动记录-出库操作','0','superadmin','2025-07-28 10:12:43','','2025-07-28 10:12:44'),(107,'IN',1,'万裕物业总仓库',202,'物品-202','CODE-202','标准规格','个',1.00,0.00,1.00,0.00,0.00,'IN2025-07-6993','PURCHASE','superadmin',1,'2025-07-28 10:12:45',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'系统自动记录-入库操作','0','superadmin','2025-07-28 10:12:45','','2025-07-28 10:12:46');
/*!40000 ALTER TABLE `wms_inventory_log` ENABLE KEYS */;
UNLOCK TABLES;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `tr_auto_fill_warehouse_name` BEFORE INSERT ON `wms_inventory_log` FOR EACH ROW BEGIN
    -- 自动填充仓库名称
    IF NEW.warehouse_id IS NOT NULL THEN
        -- 获取仓库名称
        SELECT warehouse_name INTO @warehouse_name 
        FROM sys_warehouse 
        WHERE warehouse_id = NEW.warehouse_id 
        LIMIT 1;
        
        -- 设置仓库名称
        IF @warehouse_name IS NOT NULL AND @warehouse_name != '' THEN
            SET NEW.warehouse_name = @warehouse_name;
        ELSE
            SET NEW.warehouse_name = CONCAT('仓库-', NEW.warehouse_id);
        END IF;
    END IF;
    
    -- 设置默认操作时间
    IF NEW.operation_time IS NULL THEN
        SET NEW.operation_time = NOW();
    END IF;
    
    -- 设置默认创建时间
    IF NEW.create_time IS NULL THEN
        SET NEW.create_time = NOW();
    END IF;
    
    -- 设置默认更新时间
    IF NEW.update_time IS NULL THEN
        SET NEW.update_time = NOW();
    END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `wms_inventory_out`
--

DROP TABLE IF EXISTS `wms_inventory_out`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_out` (
  `out_id` bigint NOT NULL AUTO_INCREMENT COMMENT '出库ID',
  `out_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '出库单号',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `out_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '出库类型（1销售出库 2生产出库 3调拨出库 4其他出库）',
  `out_time` datetime NOT NULL COMMENT '出库时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0未审核 1已审核 2已取消）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `audit_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核附注（内部记录）',
  `audit_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`out_id`) USING BTREE,
  KEY `idx_out_warehouse_id` (`warehouse_id`) USING BTREE,
  KEY `idx_out_status` (`status`) USING BTREE,
  KEY `idx_out_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=240 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='出库单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_out`
--

LOCK TABLES `wms_inventory_out` WRITE;
/*!40000 ALTER TABLE `wms_inventory_out` DISABLE KEYS */;
INSERT INTO `wms_inventory_out` VALUES (238,'OUT20250726220741',1,'3','2025-07-26 22:07:41','1','','superadmin','2025-07-26 22:07:41','superadmin','2025-07-26 22:07:50','','superadmin','2025-07-26 22:07:50'),(239,'OUT20250728101000',5,'4','2025-07-28 10:10:01','1','','superadmin','2025-07-28 10:10:01','superadmin','2025-07-28 10:10:06','','superadmin','2025-07-28 10:10:06');
/*!40000 ALTER TABLE `wms_inventory_out` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory_out_detail`
--

DROP TABLE IF EXISTS `wms_inventory_out_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_out_detail` (
  `detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `out_id` bigint NOT NULL COMMENT '出库ID',
  `product_id` bigint NOT NULL COMMENT '物品ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '出库数量',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '出库单价',
  `amount` decimal(10,2) DEFAULT '0.00' COMMENT '出库金额',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`detail_id`) USING BTREE,
  KEY `idx_out_detail_out_id` (`out_id`) USING BTREE,
  KEY `idx_out_detail_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=246 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='出库单明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_out_detail`
--

LOCK TABLES `wms_inventory_out_detail` WRITE;
/*!40000 ALTER TABLE `wms_inventory_out_detail` DISABLE KEYS */;
INSERT INTO `wms_inventory_out_detail` VALUES (244,238,201,1.00,0.00,0.00,NULL),(245,239,202,1.00,0.00,0.00,NULL);
/*!40000 ALTER TABLE `wms_inventory_out_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory_transfer`
--

DROP TABLE IF EXISTS `wms_inventory_transfer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_transfer` (
  `transfer_id` bigint NOT NULL AUTO_INCREMENT COMMENT '调拨ID',
  `transfer_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调拨单号',
  `from_warehouse_id` bigint NOT NULL COMMENT '调出仓库ID',
  `to_warehouse_id` bigint NOT NULL COMMENT '调入仓库ID',
  `transfer_time` datetime NOT NULL COMMENT '调拨时间',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态（0未审核 1已审核 2已取消）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `audit_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核附注（内部记录）',
  `audit_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`transfer_id`) USING BTREE,
  KEY `idx_transfer_from_warehouse` (`from_warehouse_id`) USING BTREE,
  KEY `idx_transfer_to_warehouse` (`to_warehouse_id`) USING BTREE,
  KEY `idx_transfer_status` (`status`) USING BTREE,
  KEY `idx_transfer_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=217 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='库存调拨单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_transfer`
--

LOCK TABLES `wms_inventory_transfer` WRITE;
/*!40000 ALTER TABLE `wms_inventory_transfer` DISABLE KEYS */;
INSERT INTO `wms_inventory_transfer` VALUES (215,'TR20250726220445',1,2,'2025-07-26 22:04:45','1','','superadmin','2025-07-26 22:04:45','superadmin','2025-07-26 22:10:01',NULL,'superadmin','2025-07-26 22:10:01'),(216,'TR20250728101238',5,1,'2025-07-28 10:12:39','1','','superadmin','2025-07-28 10:12:39','superadmin','2025-07-28 10:12:43',NULL,'superadmin','2025-07-28 10:12:43');
/*!40000 ALTER TABLE `wms_inventory_transfer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_inventory_transfer_detail`
--

DROP TABLE IF EXISTS `wms_inventory_transfer_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_transfer_detail` (
  `detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `transfer_id` bigint NOT NULL COMMENT '调拨ID',
  `product_id` bigint NOT NULL COMMENT '物品ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '调拨数量',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`detail_id`) USING BTREE,
  KEY `idx_transfer_detail_transfer_id` (`transfer_id`) USING BTREE,
  KEY `idx_transfer_detail_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=224 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='库存调拨单明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_inventory_transfer_detail`
--

LOCK TABLES `wms_inventory_transfer_detail` WRITE;
/*!40000 ALTER TABLE `wms_inventory_transfer_detail` DISABLE KEYS */;
INSERT INTO `wms_inventory_transfer_detail` VALUES (222,215,201,1.00,NULL),(223,216,202,1.00,NULL);
/*!40000 ALTER TABLE `wms_inventory_transfer_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_category`
--

DROP TABLE IF EXISTS `wms_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_category` (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '祖级列表',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类编码',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`category_id`) USING BTREE,
  UNIQUE KEY `category_code` (`category_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='物品分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_category`
--

LOCK TABLES `wms_category` WRITE;
/*!40000 ALTER TABLE `wms_category` DISABLE KEYS */;
INSERT INTO `wms_category` VALUES (1,0,'0','电子产品','ELECTRONIC',1,'0',NULL,'','2025-05-19 00:22:44','','2025-07-03 19:38:26','0'),(2,0,'0','办公用品','OFFICE',2,'0',NULL,'','2025-05-19 00:22:44','',NULL,'0'),(3,0,'0','食品饮料','FOOD',3,'0',NULL,'','2025-05-19 00:22:44','',NULL,'0'),(4,1,'0,1','手机','PHONE',1,'0',NULL,'','2025-05-19 00:22:44','','2025-07-19 15:44:25','0'),(5,1,'0,1','电脑','COMPUTER',2,'0',NULL,'','2025-05-19 00:22:44','',NULL,'0'),(6,2,'0,2','文具','STATIONERY',1,'0',NULL,'','2025-05-19 00:22:44','',NULL,'0'),(7,3,'0,3','饮料','DRINK',1,'0',NULL,'','2025-05-19 00:22:44','',NULL,'0'),(8,3,'0,3','清洁用品','CLEANING',1,'0','清洁用品类别','admin','2025-05-17 21:44:06','',NULL,'0'),(9,3,'0,3','厨房用品','KITCHEN',2,'0','厨房用品类别','admin','2025-05-17 21:44:06','','2025-07-22 08:56:03','0'),(14,1,'','asdfasd','asdfasf',0,'0',NULL,'','2025-06-13 23:14:18','',NULL,'2'),(15,3,'','asdfa','sdfa',0,'0',NULL,'','2025-06-15 21:32:41','',NULL,'2'),(16,0,NULL,'asdf','sadf',0,'0',NULL,NULL,'2025-07-06 19:41:26',NULL,NULL,'2'),(17,0,NULL,'sdfg','fgs',1,'0',NULL,NULL,'2025-07-17 19:33:16',NULL,NULL,'2');
/*!40000 ALTER TABLE `wms_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_product`
--

DROP TABLE IF EXISTS `wms_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_product` (
  `product_id` bigint NOT NULL AUTO_INCREMENT COMMENT '物品ID',
  `product_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物品名称',
  `product_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物品编码',
  `product_spec` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物品规格',
  `spec_id` int DEFAULT NULL COMMENT '规格ID',
  `product_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物品单位',
  `unit_id` int DEFAULT NULL COMMENT '单位ID',
  `product_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物品分类',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '物品单价',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '物品状态（0正常 1停用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物品图片URL',
  PRIMARY KEY (`product_id`) USING BTREE,
  KEY `idx_product_code` (`product_code`) USING BTREE,
  KEY `idx_product_name` (`product_name`) USING BTREE,
  KEY `idx_product_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=210 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='物品信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_product`
--

LOCK TABLES `wms_product` WRITE;
/*!40000 ALTER TABLE `wms_product` DISABLE KEYS */;
INSERT INTO `wms_product` VALUES (201,'矿泉水','WATER001',NULL,1,NULL,5,NULL,7,2.50,'0','瓶装矿泉水','','2025-05-19 00:35:07','','2025-07-22 15:48:35','/profile/upload/2025/07/22/屏幕截图 2025-05-15 171140_20250722154832A001.png'),(202,'可乐','COLA001',NULL,1,NULL,5,NULL,7,3.00,'0','瓶装可乐','','2025-05-19 00:35:07','','2025-07-24 20:13:05',''),(203,'笔记本电脑','LAPTOP001',NULL,4,NULL,1,NULL,5,5999.00,'0','高性能笔记本电脑','','2025-05-19 00:35:07','','2025-07-21 10:41:06','/profile/upload/2025/07/21/屏幕截图 2025-05-15 171140_20250721104103A002.png'),(204,'办公桌','DESK001',NULL,4,NULL,1,NULL,6,10.00,'0','标准办公桌','','2025-05-19 00:35:07','',NULL,NULL),(205,'签字笔','PEN001',NULL,1,NULL,1,NULL,6,10.00,'0','黑色签字笔','','2025-05-19 00:35:07','','2025-07-22 15:48:43','/profile/upload/2025/07/22/屏幕截图 2025-05-15 171140_20250722154841A002.png');
/*!40000 ALTER TABLE `wms_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_specification`
--

DROP TABLE IF EXISTS `wms_specification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_specification` (
  `spec_id` int NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `spec_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格名称',
  `spec_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格编码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`spec_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='产品规格表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_specification`
--

LOCK TABLES `wms_specification` WRITE;
/*!40000 ALTER TABLE `wms_specification` DISABLE KEYS */;
INSERT INTO `wms_specification` VALUES (1,'标准规格','STD','0','admin','2023-07-01 11:33:00','admin','2023-07-01 11:33:00','标准规格产品'),(2,'大型规格','LARGE','0','admin','2023-07-01 11:33:00','admin','2025-07-13 14:21:01','大型规格产品'),(3,'小型规格','SMALL','0','admin','2023-07-01 11:33:00','admin','2023-07-01 11:33:00','小型规格产品'),(4,'中型规格','MEDIUM','0','','2025-05-19 00:34:08','',NULL,'中型规格产品'),(5,'特殊规格','SPECIAL','0','','2025-05-19 00:34:08','',NULL,'特殊规格产品');
/*!40000 ALTER TABLE `wms_specification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_unit`
--

DROP TABLE IF EXISTS `wms_unit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_unit` (
  `unit_id` int NOT NULL AUTO_INCREMENT COMMENT '单位ID',
  `unit_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单位名称',
  `unit_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单位编码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`unit_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='产品单位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_unit`
--

LOCK TABLES `wms_unit` WRITE;
/*!40000 ALTER TABLE `wms_unit` DISABLE KEYS */;
INSERT INTO `wms_unit` VALUES (1,'个','PCS','0','admin','2023-07-01 11:33:00','admin','2023-07-01 11:33:00','计数单位'),(2,'箱','BOX','0','admin','2023-07-01 11:33:00','admin','2023-07-01 11:33:00','包装单位'),(3,'千克','KG','0','admin','2023-07-01 11:33:00','admin','2023-07-01 11:33:00','重量单位'),(4,'袋','BAG','0','','2025-05-19 00:34:52','',NULL,'包装单位'),(5,'瓶','BTL','0','','2025-05-19 00:34:52','',NULL,'包装单位'),(6,'米','M','0','','2025-05-19 00:34:52','',NULL,'长度单位'),(7,'升','L','0','','2025-05-19 00:34:52','',NULL,'容量单位'),(8,'张','U008','0','admin','2025-06-05 23:20:16','',NULL,'平面物品单位'),(9,'本','U009','0','admin','2025-06-05 23:20:16','','2025-07-13 14:21:16','书籍单位'),(10,'支','U010','0','admin','2025-06-05 23:20:16','',NULL,'笔类单位');
/*!40000 ALTER TABLE `wms_unit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_role_warehouse`
--

DROP TABLE IF EXISTS `wms_role_warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_role_warehouse` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `permission_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限类型(read/read_write/admin)',
  PRIMARY KEY (`role_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='角色-仓库关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_role_warehouse`
--

LOCK TABLES `wms_role_warehouse` WRITE;
/*!40000 ALTER TABLE `wms_role_warehouse` DISABLE KEYS */;
/*!40000 ALTER TABLE `wms_role_warehouse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_user_warehouse`
--

DROP TABLE IF EXISTS `wms_user_warehouse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_user_warehouse` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `warehouse_id` bigint NOT NULL COMMENT '仓库ID',
  `permission_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '权限类型(read/read_write/admin)',
  `source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'direct' COMMENT '权限来源(direct/role)',
  PRIMARY KEY (`user_id`,`warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户-仓库关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_user_warehouse`
--

LOCK TABLES `wms_user_warehouse` WRITE;
/*!40000 ALTER TABLE `wms_user_warehouse` DISABLE KEYS */;
/*!40000 ALTER TABLE `wms_user_warehouse` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_warehouse_area`
--

DROP TABLE IF EXISTS `wms_warehouse_area`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_warehouse_area` (
  `area_id` bigint NOT NULL AUTO_INCREMENT COMMENT '区域ID',
  `warehouse_id` bigint NOT NULL COMMENT '所属仓库ID',
  `area_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域名称',
  `area_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域编码',
  `area_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '区域状态（0正常 1停用）',
  `area_capacity` decimal(10,2) DEFAULT NULL COMMENT '区域容量',
  `area_used` decimal(10,2) DEFAULT '0.00' COMMENT '已使用容量',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`area_id`) USING BTREE,
  UNIQUE KEY `area_code` (`area_code`) USING BTREE,
  KEY `idx_warehouse_id` (`warehouse_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='仓库区域表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_warehouse_area`
--

LOCK TABLES `wms_warehouse_area` WRITE;
/*!40000 ALTER TABLE `wms_warehouse_area` DISABLE KEYS */;
/*!40000 ALTER TABLE `wms_warehouse_area` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wms_warehouse_rack`
--

DROP TABLE IF EXISTS `wms_warehouse_rack`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_warehouse_rack` (
  `rack_id` bigint NOT NULL AUTO_INCREMENT COMMENT '货架ID',
  `area_id` bigint NOT NULL COMMENT '所属区域ID',
  `rack_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货架名称',
  `rack_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货架编码',
  `rack_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '货架状态（0正常 1停用）',
  `rack_capacity` decimal(10,2) DEFAULT NULL COMMENT '货架容量',
  `rack_used` decimal(10,2) DEFAULT '0.00' COMMENT '已使用容量',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`rack_id`) USING BTREE,
  UNIQUE KEY `rack_code` (`rack_code`) USING BTREE,
  KEY `idx_area_id` (`area_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='仓库货架表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wms_warehouse_rack`
--

LOCK TABLES `wms_warehouse_rack` WRITE;
/*!40000 ALTER TABLE `wms_warehouse_rack` DISABLE KEYS */;
/*!40000 ALTER TABLE `wms_warehouse_rack` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-28 13:06:22
