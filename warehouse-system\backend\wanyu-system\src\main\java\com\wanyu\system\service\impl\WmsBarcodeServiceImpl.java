package com.wanyu.system.service.impl;

import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.system.domain.WmsBarcode;
import com.wanyu.system.domain.WmsBarcodeTemplate;
import com.wanyu.system.mapper.WmsBarcodeMapper;
import com.wanyu.system.mapper.WmsBarcodeTemplateMapper;
import com.wanyu.system.service.IWmsBarcodeService;

/**
 * 物品条码Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class WmsBarcodeServiceImpl implements IWmsBarcodeService
{
    @Autowired
    private WmsBarcodeMapper wmsBarcodeMapper;

    @Autowired
    private WmsBarcodeTemplateMapper wmsBarcodeTemplateMapper;

    /**
     * 查询物品条码
     *
     * @param barcodeId 物品条码主键
     * @return 物品条码
     */
    @Override
    public WmsBarcode selectWmsBarcodeByBarcodeId(Long barcodeId)
    {
        return wmsBarcodeMapper.selectWmsBarcodeByBarcodeId(barcodeId);
    }

    /**
     * 查询物品条码列表
     *
     * @param wmsBarcode 物品条码
     * @return 物品条码
     */
    @Override
    public List<WmsBarcode> selectWmsBarcodeList(WmsBarcode wmsBarcode)
    {
        return wmsBarcodeMapper.selectWmsBarcodeList(wmsBarcode);
    }

    /**
     * 新增物品条码
     *
     * @param wmsBarcode 物品条码
     * @return 结果
     */
    @Override
    public int insertWmsBarcode(WmsBarcode wmsBarcode)
    {
        wmsBarcode.setCreateBy(SecurityUtils.getUsername());
        wmsBarcode.setCreateTime(DateUtils.getNowDate());
        return wmsBarcodeMapper.insertWmsBarcode(wmsBarcode);
    }

    /**
     * 修改物品条码
     *
     * @param wmsBarcode 物品条码
     * @return 结果
     */
    @Override
    public int updateWmsBarcode(WmsBarcode wmsBarcode)
    {
        wmsBarcode.setUpdateBy(SecurityUtils.getUsername());
        wmsBarcode.setUpdateTime(DateUtils.getNowDate());
        return wmsBarcodeMapper.updateWmsBarcode(wmsBarcode);
    }

    /**
     * 批量删除物品条码
     *
     * @param barcodeIds 需要删除的物品条码主键
     * @return 结果
     */
    @Override
    public int deleteWmsBarcodeByBarcodeIds(Long[] barcodeIds)
    {
        return wmsBarcodeMapper.deleteWmsBarcodeByBarcodeIds(barcodeIds);
    }

    /**
     * 删除物品条码信息
     *
     * @param barcodeId 物品条码主键
     * @return 结果
     */
    @Override
    public int deleteWmsBarcodeByBarcodeId(Long barcodeId)
    {
        return wmsBarcodeMapper.deleteWmsBarcodeByBarcodeId(barcodeId);
    }

    /**
     * 生成物品条码
     *
     * @param wmsBarcode 物品条码
     * @return 结果
     */
    @Override
    public WmsBarcode generateWmsBarcode(WmsBarcode wmsBarcode)
    {
        // 如果没有提供条码内容，则自动生成
        if (wmsBarcode.getBarcodeContent() == null || wmsBarcode.getBarcodeContent().isEmpty()) {
            String barcodeContent = generateBarcodeContent();
            wmsBarcode.setBarcodeContent(barcodeContent);
        }

        // 设置默认值
        if (wmsBarcode.getBarcodeType() == null || wmsBarcode.getBarcodeType().isEmpty()) {
            wmsBarcode.setBarcodeType("CODE128");
        }
        if (wmsBarcode.getIsMain() == null) {
            wmsBarcode.setIsMain("0");
        }
        if (wmsBarcode.getStatus() == null) {
            wmsBarcode.setStatus("0");
        }

        // 保存到数据库
        wmsBarcode.setCreateBy(SecurityUtils.getUsername());
        wmsBarcode.setCreateTime(DateUtils.getNowDate());
        wmsBarcodeMapper.insertWmsBarcode(wmsBarcode);

        return wmsBarcode;
    }

    /**
     * 批量生成物品条码
     *
     * @param wmsBarcodeList 物品条码列表
     * @return 结果
     */
    @Override
    public List<WmsBarcode> batchGenerateWmsBarcode(List<WmsBarcode> wmsBarcodeList)
    {
        String username = SecurityUtils.getUsername();
        for (WmsBarcode barcode : wmsBarcodeList) {
            // 设置默认值
            if (barcode.getBarcodeType() == null || barcode.getBarcodeType().isEmpty()) {
                barcode.setBarcodeType("CODE128");
            }
            if (barcode.getIsMain() == null) {
                barcode.setIsMain("0");
            }
            if (barcode.getStatus() == null) {
                barcode.setStatus("0");
            }
            barcode.setCreateBy(username);
            barcode.setCreateTime(DateUtils.getNowDate());
        }

        // 批量插入
        wmsBarcodeMapper.batchInsertWmsBarcode(wmsBarcodeList);
        return wmsBarcodeList;
    }

    /**
     * 获取物品条码图片
     *
     * @param barcodeId 物品条码主键
     * @return 条码图片信息
     */
    @Override
    public String getWmsBarcodeImage(Long barcodeId)
    {
        WmsBarcode barcode = wmsBarcodeMapper.selectWmsBarcodeByBarcodeId(barcodeId);
        if (barcode != null) {
            return barcode.getBarcodeImage();
        }
        return null;
    }

    /**
     * 根据条码内容查询物品条码
     *
     * @param barcodeContent 条码内容
     * @return 物品条码
     */
    @Override
    public WmsBarcode selectWmsBarcodeByContent(String barcodeContent)
    {
        return wmsBarcodeMapper.selectWmsBarcodeByContent(barcodeContent);
    }

    /**
     * 根据物品ID查询物品条码列表
     *
     * @param productId 物品ID
     * @return 物品条码集合
     */
    @Override
    public List<WmsBarcode> selectWmsBarcodeByProductId(Long productId)
    {
        return wmsBarcodeMapper.selectWmsBarcodeByProductId(productId);
    }

    /**
     * 查询物品条码模板列表
     *
     * @return 物品条码模板集合
     */
    @Override
    public List<WmsBarcodeTemplate> selectWmsBarcodeTemplateList()
    {
        WmsBarcodeTemplate template = new WmsBarcodeTemplate();
        template.setStatus("0"); // 只查询正常状态的模板
        return wmsBarcodeTemplateMapper.selectWmsBarcodeTemplateList(template);
    }

    /**
     * 生成条码内容
     *
     * @return 条码内容
     */
    private String generateBarcodeContent()
    {
        // 使用时间戳 + 随机数生成唯一条码
        long timestamp = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return "WMS" + timestamp + uuid.toUpperCase();
    }
}