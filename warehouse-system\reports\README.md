# 字段标准化报告目录

本目录用于存储字段标准化相关的报告文件。

## 报告类型

### 1. 字段合规性报告
- **文件名格式**: `field_compliance_report_YYYYMMDD_HHMMSS.txt`
- **生成方式**: 执行 `generate_field_report.sql` 脚本
- **内容**: 详细的字段定义标准合规性检查结果

### 2. 备份验证报告
- **文件名格式**: `backup_verification_YYYYMMDD_HHMMSS.txt`
- **生成方式**: 执行 `verify_backup.sql` 脚本
- **内容**: 数据备份完整性和正确性验证结果

### 3. 数据迁移报告
- **文件名格式**: `migration_report_YYYYMMDD_HHMMSS.txt`
- **生成方式**: 数据迁移过程中自动生成
- **内容**: 数据迁移执行过程和结果记录

## 使用说明

1. 报告文件按时间戳命名，便于版本管理
2. 建议定期清理过期的报告文件
3. 重要的报告文件应该备份保存
4. 报告内容可用于问题追踪和审计

## 注意事项

- 报告文件可能包含敏感的数据库信息，请妥善保管
- 定期检查报告内容，及时发现和解决问题
- 报告文件较大时，建议使用压缩存储