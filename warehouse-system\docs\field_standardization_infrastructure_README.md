# 字段标准化基础设施使用指南

## 概述

本基础设施为仓库管理系统的字段定义标准化提供了完整的工具集，包括标准规范文档、数据备份验证脚本和字段标准验证工具类。

## 目录结构

```
warehouse-system/
├── docs/
│   ├── field_definition_standards.md          # 字段定义标准规范文档
│   └── field_standardization_infrastructure_README.md
├── sql/
│   ├── backup_database.sql                    # 数据库备份脚本
│   ├── verify_backup.sql                      # 备份验证脚本
│   ├── check_field_standards.sql              # 字段标准检查脚本
│   └── generate_field_report.sql              # 字段合规报告生成脚本
├── scripts/
│   └── execute_backup_and_verify.bat          # 备份和验证执行脚本
├── reports/
│   └── README.md                              # 报告目录说明
└── backend/wanyu-common/src/main/java/com/wanyu/common/utils/
    ├── FieldStandardValidator.java            # 字段标准验证工具类
    └── ValidationResult.java                  # 验证结果类
```

## 使用步骤

### 1. 执行数据备份和验证

```bash
cd warehouse-system/scripts
execute_backup_and_verify.bat
```

这个脚本会依次执行：
- 创建完整的数据库备份
- 验证备份的完整性
- 检查当前字段标准合规性
- 生成详细的合规性报告

### 2. 查看字段标准规范

参考 `docs/field_definition_standards.md` 文档了解：
- 字段定义标准
- 命名规范
- 数据类型规范
- 业务代码规范
- 表创建模板

### 3. 使用字段验证工具类

```java
import com.wanyu.common.utils.FieldStandardValidator;
import com.wanyu.common.utils.ValidationResult;

// 验证单个字段
ValidationResult result = FieldStandardValidator.validateFieldValue("status", "0");
if (!result.isValid()) {
    System.out.println(result.getErrorMessage());
}

// 批量验证字段
Map<String, String> fields = new HashMap<>();
fields.put("status", "0");
fields.put("is_enabled", "1");
fields.put("operation_status", "0");

List<ValidationResult> results = FieldStandardValidator.validateFields(fields);
List<ValidationResult> invalidFields = FieldStandardValidator.getInvalidFields(fields);
```

### 4. 检查字段标准合规性

```sql
-- 执行字段标准检查
mysql -u root -p123456 warehouse_system < sql/check_field_standards.sql

-- 生成详细报告
mysql -u root -p123456 warehouse_system < sql/generate_field_report.sql
```

## 核心组件说明

### 1. 字段标准验证工具类 (FieldStandardValidator)

**主要功能**:
- 验证状态字段值是否符合标准 (0=正常, 1=停用)
- 验证操作状态字段值 (0=成功, 1=失败)
- 验证布尔字段值 (0=否, 1=是)
- 根据字段名自动识别字段类型
- 批量验证多个字段

**常用方法**:
```java
// 状态值常量
FieldStandardValidator.STATUS_ENABLED    // "0" - 启用
FieldStandardValidator.STATUS_DISABLED   // "1" - 禁用
FieldStandardValidator.OPERATION_SUCCESS // "0" - 成功
FieldStandardValidator.OPERATION_FAILED  // "1" - 失败

// 验证方法
isValidStatusValue(String value)         // 验证状态值
isValidOperationStatus(String value)     // 验证操作状态
isValidBooleanValue(String value)        // 验证布尔值
validateFieldValue(String name, String value) // 验证字段值
```

### 2. 数据备份脚本 (backup_database.sql)

**功能**:
- 创建 `warehouse_system_backup` 备份数据库
- 备份关键表: sys_license, sys_license_feature, wms_operation_log, sys_dict_data
- 记录备份信息和统计数据
- 验证备份完整性

**使用**:
```sql
mysql -u root -p123456 < sql/backup_database.sql
```

### 3. 字段标准检查脚本 (check_field_standards.sql)

**功能**:
- 检查表结构是否符合字段标准
- 检查数据值分布和有效性
- 检查数据字典配置
- 生成问题清单和修复建议

**输出**:
- 字段定义合规性检查结果
- 数据值分布统计
- 需要修复的问题列表
- 具体的修复建议

### 4. 合规报告生成脚本 (generate_field_report.sql)

**功能**:
- 生成详细的字段标准合规性报告
- 按问题级别分类 (HIGH/MEDIUM/INFO)
- 提供具体的修复建议
- 统计合规率和问题分布

**报告内容**:
- 报告摘要和统计信息
- 问题级别分布
- 详细检查结果
- 高优先级问题列表
- 修复建议汇总

## 字段标准规范要点

### 1. 状态字段标准
```sql
status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）'
```
- 0 = 正常/启用/激活
- 1 = 停用/禁用/非激活

### 2. 操作状态字段标准
```sql
operation_status CHAR(1) DEFAULT '0' COMMENT '操作状态（0成功 1失败）'
```
- 0 = 操作成功
- 1 = 操作失败

### 3. 布尔字段标准
```sql
is_xxx CHAR(1) DEFAULT '0' COMMENT '是否xxx（0否 1是）'
```
- 0 = 否/False/关闭
- 1 = 是/True/开启

### 4. 删除标记标准
```sql
del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）'
```
- 0 = 记录存在
- 2 = 记录已删除 (注意：使用2而不是1)

## 常见问题和解决方案

### Q1: 如何处理现有数据与标准不符的情况？
**A**: 
1. 先执行数据备份
2. 使用字段标准检查脚本识别问题
3. 根据报告建议执行数据迁移
4. 验证迁移结果

### Q2: 如何在代码中正确使用状态值？
**A**:
```java
// 正确的做法
entity.setStatus(FieldStandardValidator.STATUS_ENABLED);  // "0"
if (FieldStandardValidator.STATUS_ENABLED.equals(entity.getStatus())) {
    // 处理启用状态
}

// 错误的做法
entity.setStatus("1"); // 应该使用常量
if (entity.getStatus() == "0") { // 应该使用equals比较
```

### Q3: 如何确保新增字段符合标准？
**A**:
1. 参考 `field_definition_standards.md` 中的表创建模板
2. 使用 `FieldStandardValidator` 验证字段值
3. 定期运行字段标准检查脚本
4. 在代码审查中检查字段使用

### Q4: 备份失败怎么办？
**A**:
1. 检查数据库连接参数
2. 确认数据库用户权限
3. 检查磁盘空间是否充足
4. 查看MySQL错误日志

## 维护和更新

### 定期检查
- 每月执行字段标准合规性检查
- 每季度更新字段标准规范文档
- 年度审查和优化验证工具

### 版本管理
- 备份脚本版本化管理
- 标准文档变更记录
- 工具类功能更新日志

### 培训推广
- 新员工字段标准培训
- 定期技术分享
- 最佳实践案例收集

## 联系和支持

如有问题或建议，请联系：
- 技术负责人：[姓名]
- 数据库管理员：[姓名]
- 项目经理：[姓名]

---

**文档版本**: 1.0  
**创建日期**: 2025-01-30  
**最后更新**: 2025-01-30