<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>物品分类管理</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-plus"
          @click="handleAdd"
          v-hasPermi="['product:category:add']"
        >新增</el-button>
      </div>
      
      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="分类名称" prop="categoryName">
          <el-input
            v-model="queryParams.categoryName"
            placeholder="请输入分类名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="分类状态" clearable size="small">
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区域 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['product:category:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['product:category:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['product:category:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['product:category:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="categoryList"
        row-key="categoryId"
        default-expand-all
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        @selection-change="handleSelectionChange"
        border
        :indent="32"
        :row-style="rowStyle"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="分类ID" align="center" prop="categoryId" width="100" />
        <el-table-column label="分类编码" align="center" prop="categoryCode" width="150" />
        <el-table-column label="分类名称" align="left" prop="categoryName">
          <template slot-scope="scope">
            <span :style="{paddingLeft: (scope.row.level * 20) + 'px'}">
              <i v-if="scope.row.children && scope.row.children.length > 0" 
                 :class="scope.row.expanded ? 'el-icon-folder-opened' : 'el-icon-folder'"></i>
              <i v-else class="el-icon-document"></i>
              {{ scope.row.categoryName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="orderNum" width="80" />
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="handleStatusChange(scope.row)"
              v-hasPermi="['product:category:edit']"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="handleAdd(scope.row)"
              v-hasPermi="['product:category:add']"
            >新增</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['product:category:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['product:category:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <DeleteDialog
        :visible.sync="deleteDialogVisible"
        @confirm="confirmDelete"
        showPhysicalDelete
      />
    </el-card>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="分类类型" prop="categoryType">
          <el-radio-group v-model="form.categoryType" @change="handleCategoryTypeChange">
            <el-radio label="main">主分类</el-radio>
            <el-radio label="sub">子分类</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上级分类" prop="parentId" v-if="form.categoryType === 'sub'">
          <treeselect
            v-model="form.parentId"
            :options="categoryOptions"
            :normalizer="normalizer"
            :show-count="true"
            :load-options="loadOptions"
            :auto-load-root-options="false"
            placeholder="选择上级分类"
            :loading="loading"
          />
        </el-form-item>
        <el-form-item label="分类编码" prop="categoryCode">
          <el-input v-model="form.categoryCode" placeholder="请输入分类编码" />
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="form.categoryName" :placeholder="form.categoryType === 'main' ? '请输入主分类名称' : '请输入子分类名称'" />
        </el-form-item>
        <el-form-item label="显示排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listCategory, addCategory, updateCategory, delCategory, treeselect as categoryTreeselect } from "@/api/product/category";
import DeleteDialog from '@/components/DeleteDialog';

export default {
  name: "ProductCategory",
  components: { Treeselect, DeleteDialog },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 分类表格数据
      categoryList: [],
      // 分类树选项
      categoryOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除对话框是否可见
      deleteDialogVisible: false,
      // 当前删除的分类ID，用于传递给删除对话框
      currentDeleteCategoryId: null,
      // 查询参数
      queryParams: {
        categoryName: undefined,
        status: undefined
      },
      // 表单参数
      form: {
        categoryId: null,
        parentId: 0,
        categoryCode: "",
        categoryName: "",
        orderNum: 0,
        status: "0",
        categoryType: "main" // 新增分类类型字段
      },
      // 表单校验
      rules: {
        categoryCode: [
          { required: true, message: "分类编码不能为空", trigger: "blur" }
        ],
        categoryName: [
          { required: true, message: "分类名称不能为空", trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: "显示排序不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询分类列表 */
    getList() {
      this.loading = true;
      listCategory(this.queryParams).then(response => {
        console.log("物品分类列表响应:", response);
        // 确保响应数据存在且为数组
        // 修复：后端返回的是TableDataInfo对象，数据在rows字段中
        const rawData = response.data && response.data.rows ? response.data.rows : [];
        console.log("原始数据:", rawData);
        
        // 确保每个节点都有children属性，且parentId为数字类型
        const processData = (data) => {
          if (!data || !Array.isArray(data)) {
            return [];
          }
          
          return data.map(item => {
            // 确保parentId是数字类型
            if (typeof item.parentId === 'string') {
              item.parentId = parseInt(item.parentId);
            }
            // 确保categoryId是数字类型
            if (typeof item.categoryId === 'string') {
              item.categoryId = parseInt(item.categoryId);
            }
            // 确保每个item都有children属性
            if (!item.children) {
              item.children = [];
            }
            return item;
          });
        };
        
        const processedData = processData(rawData);
        console.log("处理后的数据:", processedData);
        
        // 构建完整的树形结构
        const buildTree = (data, parentId = 0) => {
          // 确保data存在且为数组
          if (!data || !Array.isArray(data)) {
            return [];
          }
          
          const result = [];
          for (const item of data) {
            if (item.parentId === parentId) {
              const children = buildTree(data, item.categoryId);
              // 总是设置children属性，即使为空数组
              item.children = children;
              result.push(item);
            }
          }
          return result;
        };
        
        const treeData = buildTree(processedData);
        console.log("构建的树形数据:", treeData);
        
        // 添加level字段用于层级展示
        const addLevel = (data, level = 0) => {
          // 确保data存在且为数组
          if (!data || !Array.isArray(data)) {
            return [];
          }
          
          return data.map(item => {
            item.level = level;
            if (item.children && item.children.length > 0) {
              item.children = addLevel(item.children, level + 1);
            }
            return item;
          });
        };
        
        this.categoryList = addLevel(treeData);
        console.log("最终分类列表:", this.categoryList);
        this.loading = false;
      }).catch(error => {
        console.error("获取分类列表失败:", error);
        console.error("错误详情:", {
          message: error.message,
          response: error.response,
          status: error.response ? error.response.status : null,
          data: error.response ? error.response.data : null
        });
        this.categoryList = [];
        this.loading = false;
        this.$message.error("获取分类列表失败: " + (error.message || "未知错误"));
      });
    },
    /** 查询分类下拉树结构 */
    getTreeselect() {
      categoryTreeselect().then(response => {
        // 确保响应数据存在且为数组
        const data = response.data || [];
        this.categoryOptions = [];
        const data2 = this.buildCategoryTreeOptions(data);
        this.categoryOptions.push({ id: 0, label: '顶级分类', children: data2 });
      }).catch(error => {
        console.error("获取分类下拉树结构失败:", error);
        this.categoryOptions = [{ id: 0, label: '顶级分类', children: [] }];
      });
    },
    /** 构建分类树选项 */
    buildCategoryTreeOptions(categories) {
      // 确保categories存在且为数组
      if (!categories || !Array.isArray(categories)) {
        return [];
      }
      
      return categories.map(category => {
        const option = {
          id: category.categoryId,
          label: category.categoryName,
          children: category.children ? this.buildCategoryTreeOptions(category.children) : []
        };
        return option;
      });
    },
    /** 异步加载子选项 */
    loadOptions({ action, parentNode, callback }) {
      if (action === 'LOAD_CHILDREN_OPTIONS') {
        categoryTreeselect({ parentId: parentNode.id }).then(response => {
          parentNode.children = this.buildCategoryTreeOptions(response.data);
          callback();
        }).catch(() => {
          callback();
        });
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 处理分类类型变更 */
    handleCategoryTypeChange(value) {
      if (value === 'main') {
        this.form.parentId = 0;
      }
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row && row.categoryId) {
        this.form.parentId = row.categoryId;
        this.form.categoryType = 'sub';
      } else {
        this.form.parentId = 0;
        this.form.categoryType = 'main';
      }
      this.open = true;
      this.title = "添加物品分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      this.form = { ...row };
      this.form.categoryType = row.parentId === 0 ? 'main' : 'sub'; // 根据parentId设置分类类型
      this.open = true;
      this.title = "修改物品分类";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.categoryId != null) {
            updateCategory(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCategory(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const categoryIds = row.categoryId || this.ids;
      this.currentDeleteCategoryId = categoryIds;
      this.deleteDialogVisible = true;
    },
    /** 确认删除 */
    confirmDelete(delType) {
      const categoryIds = this.currentDeleteCategoryId;
      if (!categoryIds) {
        this.msgError("请选择要删除的分类");
        return;
      }

      const idsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds];
      const confirmMsg = delType === '2' ? '是否确认物理删除物品分类编号为"' + idsArray + '"的数据项？' : '是否确认逻辑删除物品分类编号为"' + idsArray + '"的数据项？';

      this.$confirm(confirmMsg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return delCategory(idsArray, delType);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
        this.deleteDialogVisible = false;
        this.currentDeleteCategoryId = null;
      }).catch(() => {
        this.deleteDialogVisible = false;
        this.currentDeleteCategoryId = null;
      });
    },
    /** 表单重置 */
    reset() {
      this.form = {
        categoryId: null,
        parentId: 0,
        categoryCode: "",
        categoryName: "",
        orderNum: 0,
        status: "0",
        categoryType: "main" // 新增分类类型字段
      };
      this.resetForm("form");
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.categoryId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 消息提示
    msgSuccess(msg) {
      this.$message({ showClose: true, message: msg, type: "success" });
    },
    msgError(msg) {
      this.$message({ showClose: true, message: msg, type: "error" });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    rowStyle({ row }) {
      if (row.parentId === 0) {
        return { backgroundColor: '#f5f7fa', fontWeight: 'bold' };
      } else {
        return {};
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有物品分类数据项？').then(() => {
        this.$modal.loading("正在导出数据，请稍候...");
        
        // 构建查询参数
        const params = {...this.queryParams};
        
        // 使用axios发送请求，确保携带认证信息
        const baseUrl = process.env.VUE_APP_BASE_API;
        this.$http({
          method: 'get',
          url: baseUrl + '/product/category/export',
          params: params,
          responseType: 'blob',
          headers: { 'Authorization': 'Bearer ' + this.$store.getters.token }
        }).then(response => {
          // 处理响应
          const blob = new Blob([response.data]);
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = `物品分类_${new Date().getTime()}.xlsx`;
          link.click();
          window.URL.revokeObjectURL(link.href);
          
          // 关闭加载提示
          this.$modal.closeLoading();
          this.$modal.msgSuccess("导出成功");
        }).catch(error => {
          console.error("导出失败:", error);
          this.$modal.closeLoading();
          this.$modal.msgError("导出失败，请重试");
        });
      }).catch(() => {});
    },
    /** 转换树选择器数据结构 */
    normalizer(node) {
      return {
        id: node.id,
        label: node.label,
        children: node.children
      };
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.el-card {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.small-padding {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width {
  min-width: 200px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>