(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-72b2abdd"],{"4c0b":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"条码内容",prop:"barcodeContent"}},[r("el-input",{attrs:{placeholder:"请输入条码内容",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.barcodeContent,callback:function(t){e.$set(e.queryParams,"barcodeContent",t)},expression:"queryParams.barcodeContent"}})],1),r("el-form-item",{attrs:{label:"物品名称",prop:"productName"}},[r("el-input",{attrs:{placeholder:"请输入物品名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.productName,callback:function(t){e.$set(e.queryParams,"productName",t)},expression:"queryParams.productName"}})],1),r("el-form-item",{attrs:{label:"条码类型",prop:"barcodeType"}},[r("el-select",{attrs:{placeholder:"请选择条码类型",clearable:""},model:{value:e.queryParams.barcodeType,callback:function(t){e.$set(e.queryParams,"barcodeType",t)},expression:"queryParams.barcodeType"}},e._l(e.dict.type.wms_barcode_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:add"],expression:"['product:barcode:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:edit"],expression:"['product:barcode:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:remove"],expression:"['product:barcode:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:export"],expression:"['product:barcode:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:print"],expression:"['product:barcode:print']"}],attrs:{type:"info",plain:"",icon:"el-icon-printer",size:"mini",disabled:e.multiple},on:{click:e.handlePrint}},[e._v("打印")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:generate"],expression:"['product:barcode:generate']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleBatchGenerate}},[e._v("批量生成")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.barcodeList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"条码ID",align:"center",prop:"barcodeId"}}),r("el-table-column",{attrs:{label:"条码内容",align:"center",prop:"barcodeContent","show-overflow-tooltip":!0}}),r("el-table-column",{attrs:{label:"条码类型",align:"center",prop:"barcodeType"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.wms_barcode_type,value:t.row.barcodeType}})]}}])}),r("el-table-column",{attrs:{label:"物品名称",align:"center",prop:"productName","show-overflow-tooltip":!0}}),r("el-table-column",{attrs:{label:"条码图片",align:"center",prop:"barcodeImage"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("el-image",{staticStyle:{width:"100px",height:"40px"},attrs:{src:e.row.barcodeImage,"preview-src-list":[e.row.barcodeImage]}})]}}])}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:edit"],expression:"['product:barcode:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:remove"],expression:"['product:barcode:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["product:barcode:print"],expression:"['product:barcode:print']"}],attrs:{size:"mini",type:"text",icon:"el-icon-printer"},on:{click:function(r){return e.handlePrint(t.row)}}},[e._v("打印")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"物品",prop:"productId"}},[r("el-select",{attrs:{placeholder:"请选择物品",filterable:""},on:{change:e.handleProductChange},model:{value:e.form.productId,callback:function(t){e.$set(e.form,"productId",t)},expression:"form.productId"}},e._l(e.productOptions,(function(e){return r("el-option",{key:e.productId,attrs:{label:e.productName,value:e.productId}})})),1)],1),r("el-form-item",{attrs:{label:"条码类型",prop:"barcodeType"}},[r("el-select",{attrs:{placeholder:"请选择条码类型"},model:{value:e.form.barcodeType,callback:function(t){e.$set(e.form,"barcodeType",t)},expression:"form.barcodeType"}},e._l(e.dict.type.wms_barcode_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"条码内容",prop:"barcodeContent"}},[r("el-input",{attrs:{placeholder:"请输入条码内容"},model:{value:e.form.barcodeContent,callback:function(t){e.$set(e.form,"barcodeContent",t)},expression:"form.barcodeContent"}})],1),r("el-form-item",{attrs:{label:"条码模板",prop:"templateId"}},[r("el-select",{attrs:{placeholder:"请选择条码模板"},model:{value:e.form.templateId,callback:function(t){e.$set(e.form,"templateId",t)},expression:"form.templateId"}},e._l(e.templateOptions,(function(e){return r("el-option",{key:e.templateId,attrs:{label:e.templateName,value:e.templateId}})})),1)],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:"批量生成条码",visible:e.batchOpen,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.batchOpen=t}}},[r("el-form",{ref:"batchForm",attrs:{model:e.batchForm,rules:e.batchRules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"物品",prop:"productId"}},[r("el-select",{attrs:{placeholder:"请选择物品",filterable:""},on:{change:e.handleBatchProductChange},model:{value:e.batchForm.productId,callback:function(t){e.$set(e.batchForm,"productId",t)},expression:"batchForm.productId"}},e._l(e.productOptions,(function(e){return r("el-option",{key:e.productId,attrs:{label:e.productName,value:e.productId}})})),1)],1),r("el-form-item",{attrs:{label:"条码类型",prop:"barcodeType"}},[r("el-select",{attrs:{placeholder:"请选择条码类型"},model:{value:e.batchForm.barcodeType,callback:function(t){e.$set(e.batchForm,"barcodeType",t)},expression:"batchForm.barcodeType"}},e._l(e.dict.type.wms_barcode_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"条码前缀",prop:"prefix"}},[r("el-input",{attrs:{placeholder:"请输入条码前缀"},model:{value:e.batchForm.prefix,callback:function(t){e.$set(e.batchForm,"prefix",t)},expression:"batchForm.prefix"}})],1),r("el-form-item",{attrs:{label:"起始编号",prop:"startNum"}},[r("el-input-number",{attrs:{min:1,max:9999},model:{value:e.batchForm.startNum,callback:function(t){e.$set(e.batchForm,"startNum",t)},expression:"batchForm.startNum"}})],1),r("el-form-item",{attrs:{label:"编号位数",prop:"numDigits"}},[r("el-input-number",{attrs:{min:1,max:6},model:{value:e.batchForm.numDigits,callback:function(t){e.$set(e.batchForm,"numDigits",t)},expression:"batchForm.numDigits"}})],1),r("el-form-item",{attrs:{label:"生成数量",prop:"count"}},[r("el-input-number",{attrs:{min:1,max:100},model:{value:e.batchForm.count,callback:function(t){e.$set(e.batchForm,"count",t)},expression:"batchForm.count"}})],1),r("el-form-item",{attrs:{label:"条码模板",prop:"templateId"}},[r("el-select",{attrs:{placeholder:"请选择条码模板"},model:{value:e.batchForm.templateId,callback:function(t){e.$set(e.batchForm,"templateId",t)},expression:"batchForm.templateId"}},e._l(e.templateOptions,(function(e){return r("el-option",{key:e.templateId,attrs:{label:e.templateName,value:e.templateId}})})),1)],1),r("el-form-item",{attrs:{label:"预览"}},[r("div",{staticClass:"preview-box"},e._l(e.previewList,(function(t,a){return r("div",{key:a,staticClass:"preview-item"},[e._v(" "+e._s(t)+" ")])})),0)])],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitBatchForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancelBatch}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:"打印预览",visible:e.printOpen,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.printOpen=t}}},[r("div",{staticClass:"print-container"},e._l(e.printList,(function(t,a){return r("div",{key:a,staticClass:"print-item"},[r("div",{staticClass:"barcode-info"},[r("div",{staticClass:"barcode-title"},[e._v(e._s(t.productName))]),r("div",{staticClass:"barcode-image"},[r("img",{attrs:{src:t.barcodeImage,alt:"条码图片"}})]),r("div",{staticClass:"barcode-content"},[e._v(e._s(t.barcodeContent))])])])})),0),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.doPrint}},[e._v("打 印")]),r("el-button",{on:{click:function(t){e.printOpen=!1}}},[e._v("取 消")])],1)])],1)},o=[],i=r("5530"),n=(r("99af"),r("7db0"),r("d81d"),r("14d9"),r("d3b7"),r("25f0"),r("3ca3"),r("4d90"),r("0643"),r("fffc"),r("4e3e"),r("a573"),r("159b"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),r("88a7"),r("271a"),r("5494"),r("b775"));function c(e){return Object(n["a"])({url:"/product/barcode/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/product/barcode/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/product/barcode",method:"put",data:e})}function d(e){return Object(n["a"])({url:"/product/barcode/"+e,method:"delete"})}function u(e){return Object(n["a"])({url:"/product/barcode/generate",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/product/barcode/print/"+e,method:"get"})}function m(e){return Object(n["a"])({url:"/product/barcode/batch/generate",method:"post",data:e})}function b(){return Object(n["a"])({url:"/product/barcode/template/list",method:"get"})}var h=r("ba28"),f=r("bc3a"),v=r.n(f),g=r("5f87"),y={name:"Barcode",dicts:["wms_barcode_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,barcodeList:[],productOptions:[],templateOptions:[],title:"",open:!1,batchOpen:!1,printOpen:!1,printList:[],previewList:[],queryParams:{pageNum:1,pageSize:10,barcodeContent:void 0,productName:void 0,barcodeType:void 0},form:{},batchForm:{productId:void 0,barcodeType:void 0,prefix:"",startNum:1,numDigits:4,count:10,templateId:void 0},rules:{productId:[{required:!0,message:"物品不能为空",trigger:"change"}],barcodeType:[{required:!0,message:"条码类型不能为空",trigger:"change"}],barcodeContent:[{required:!0,message:"条码内容不能为空",trigger:"blur"}],templateId:[{required:!0,message:"条码模板不能为空",trigger:"change"}]},batchRules:{productId:[{required:!0,message:"物品不能为空",trigger:"change"}],barcodeType:[{required:!0,message:"条码类型不能为空",trigger:"change"}],prefix:[{required:!0,message:"条码前缀不能为空",trigger:"blur"}],startNum:[{required:!0,message:"起始编号不能为空",trigger:"blur"}],numDigits:[{required:!0,message:"编号位数不能为空",trigger:"blur"}],count:[{required:!0,message:"生成数量不能为空",trigger:"blur"}],templateId:[{required:!0,message:"条码模板不能为空",trigger:"change"}]}}},watch:{"batchForm.prefix":function(){this.generatePreview()},"batchForm.startNum":function(){this.generatePreview()},"batchForm.numDigits":function(){this.generatePreview()},"batchForm.count":function(){this.generatePreview()}},created:function(){this.$route.query.productId&&(this.queryParams.productId=this.$route.query.productId),this.getList(),this.getProductOptions(),this.getTemplateOptions()},methods:{getList:function(){var e=this;this.loading=!0,c(this.queryParams).then((function(t){e.barcodeList=t.rows,e.total=t.total,e.loading=!1}))},getProductOptions:function(){var e=this;Object(h["e"])().then((function(t){e.productOptions=t.rows}))},getTemplateOptions:function(){var e=this;b().then((function(t){e.templateOptions=t.data}))},generatePreview:function(){if(this.previewList=[],this.batchForm.prefix&&void 0!==this.batchForm.startNum&&void 0!==this.batchForm.numDigits&&void 0!==this.batchForm.count){for(var e=this.batchForm.prefix,t=this.batchForm.startNum,r=this.batchForm.numDigits,a=this.batchForm.count,o=Math.min(a,10),i=0;i<o;i++){var n=t+i,c=n.toString().padStart(r,"0");this.previewList.push("".concat(e).concat(c))}a>10&&this.previewList.push("...")}},handleProductChange:function(e){var t=this.productOptions.find((function(t){return t.productId===e}));t&&(this.form.productName=t.productName)},handleBatchProductChange:function(e){var t=this.productOptions.find((function(t){return t.productId===e}));t&&(this.batchForm.productName=t.productName)},cancel:function(){this.open=!1,this.reset()},cancelBatch:function(){this.batchOpen=!1,this.resetBatch()},reset:function(){this.form={barcodeId:void 0,productId:void 0,productName:void 0,barcodeType:void 0,barcodeContent:void 0,templateId:void 0,remark:void 0},this.resetForm("form")},resetBatch:function(){this.batchForm={productId:void 0,productName:void 0,barcodeType:void 0,prefix:"",startNum:1,numDigits:4,count:10,templateId:void 0},this.previewList=[],this.resetForm("batchForm")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.barcodeId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加物品条码"},handleBatchGenerate:function(){this.resetBatch(),this.batchOpen=!0,this.generatePreview()},handleUpdate:function(e){var t=this;this.reset();var r=e.barcodeId||this.ids[0];l(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改物品条码"}))},handlePrint:function(e){var t=this,r=e.barcodeId?[e.barcodeId]:this.ids;p(r).then((function(e){t.printList=e.data,t.printOpen=!0}))},doPrint:function(){if(0!==this.printList.length){var e=window.open("","_blank"),t="<!DOCTYPE html><html><head><title>条码打印</title>";t+="<style>",t+="body { font-family: Arial, sans-serif; margin: 0; padding: 0; }",t+=".print-container { display: flex; flex-wrap: wrap; }",t+=".print-item { width: 200px; height: 100px; margin: 10px; border: 1px solid #ddd; padding: 5px; }",t+=".barcode-info { display: flex; flex-direction: column; align-items: center; }",t+=".barcode-title { font-size: 12px; margin-bottom: 5px; }",t+=".barcode-image { margin-bottom: 5px; }",t+=".barcode-content { font-size: 10px; }",t+="</style></head><body>",t+='<div class="print-container">',this.printList.forEach((function(e){t+='<div class="print-item">',t+='<div class="barcode-info">',t+='<div class="barcode-title">'+e.productName+"</div>",t+='<div class="barcode-image">',t+='<img src="'+e.barcodeImage+'" alt="条码图片" />',t+="</div>",t+='<div class="barcode-content">'+e.barcodeContent+"</div>",t+="</div></div>"})),t+="</div>",t+="<script>",t+="window.onload = function() {",t+="  window.print();",t+="  setTimeout(function() { window.close(); }, 500);",t+="};",t+="<\/script></body></html>",e.document.open(),e.document.write(t),e.document.close()}else this.$modal.msgError("没有可打印的条码")},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.barcodeId?s(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("生成成功"),e.open=!1,e.getList()})))}))},submitBatchForm:function(){var e=this;this.$refs["batchForm"].validate((function(t){t&&m(e.batchForm).then((function(t){e.$modal.msgSuccess("批量生成成功"),e.batchOpen=!1,e.getList()}))}))},handleDelete:function(e){var t=this,r=e.barcodeId||this.ids;this.$modal.confirm('是否确认删除物品条码编号为"'+r+'"的数据项？').then((function(){return d(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){var e=this;this.$modal.confirm("是否确认导出所有物品条码数据项？").then((function(){e.$modal.loading("正在导出数据，请稍候...");var t=Object(i["a"])({},e.queryParams),r="/prod-api";v()({method:"get",url:r+"/product/barcode/export",params:t,responseType:"blob",headers:{Authorization:"Bearer "+Object(g["a"])()}}).then((function(t){var r=new Blob([t.data]),a=document.createElement("a");a.href=window.URL.createObjectURL(r),a.download="物品条码_".concat((new Date).getTime(),".xlsx"),a.click(),window.URL.revokeObjectURL(a.href),e.$modal.closeLoading(),e.$modal.msgSuccess("导出成功")})).catch((function(t){console.error("导出失败:",t),e.$modal.closeLoading(),e.$modal.msgError("导出失败，请重试")}))})).catch((function(){}))}}},w=y,x=(r("f864"),r("2877")),k=Object(x["a"])(w,a,o,!1,null,"163ed71f",null);t["default"]=k.exports},"69a9":function(e,t,r){},ba28:function(e,t,r){"use strict";r.d(t,"e",(function(){return o})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return n})),r.d(t,"f",(function(){return c})),r.d(t,"b",(function(){return l})),r.d(t,"d",(function(){return s}));var a=r("b775");function o(e){return Object(a["a"])({url:"/product/info/list",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/product/info/"+e,method:"get"})}function n(e){return Object(a["a"])({url:"/product/info",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/product/info",method:"put",data:e})}function l(e){return Object(a["a"])({url:"/product/info/"+e,method:"delete"})}function s(e){return Object(a["a"])({url:"/product/info/details",method:"get",params:{identifier:e}})}},f864:function(e,t,r){"use strict";r("69a9")}}]);