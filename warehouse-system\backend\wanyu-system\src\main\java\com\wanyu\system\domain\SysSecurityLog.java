package com.wanyu.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 安全日志对象 sys_security_log
 * 
 * <AUTHOR>
 */
public class SysSecurityLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志主键 */
    private Long logId;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String nickName;

    /** 安全事件类型 */
    @Excel(name = "事件类型", readConverterExp = "LOGIN_FAIL=登录失败,PASSWORD_CHANGE=密码修改,PERMISSION_DENIED=权限拒绝,ACCOUNT_LOCKED=账户锁定,SUSPICIOUS_ACTIVITY=可疑活动")
    private String eventType;

    /** 事件描述 */
    @Excel(name = "事件描述")
    private String eventDesc;

    /** 风险级别 */
    @Excel(name = "风险级别", readConverterExp = "LOW=低,MEDIUM=中,HIGH=高,CRITICAL=严重")
    private String riskLevel;

    /** 客户端IP */
    @Excel(name = "客户端IP")
    private String clientIp;

    /** 客户端位置 */
    @Excel(name = "客户端位置")
    private String clientLocation;

    /** 用户代理 */
    @Excel(name = "用户代理")
    private String userAgent;

    /** 处理状态 */
    @Excel(name = "处理状态", readConverterExp = "0=未处理,1=已处理,2=已忽略")
    private String status;

    /** 处理人 */
    @Excel(name = "处理人")
    private String handleBy;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 处理备注 */
    @Excel(name = "处理备注")
    private String handleRemark;

    /** 事件时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date eventTime;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }

    public void setEventType(String eventType) 
    {
        this.eventType = eventType;
    }

    public String getEventType() 
    {
        return eventType;
    }

    public void setEventDesc(String eventDesc) 
    {
        this.eventDesc = eventDesc;
    }

    public String getEventDesc() 
    {
        return eventDesc;
    }

    public void setRiskLevel(String riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() 
    {
        return riskLevel;
    }

    public void setClientIp(String clientIp) 
    {
        this.clientIp = clientIp;
    }

    public String getClientIp() 
    {
        return clientIp;
    }

    public void setClientLocation(String clientLocation) 
    {
        this.clientLocation = clientLocation;
    }

    public String getClientLocation() 
    {
        return clientLocation;
    }

    public void setUserAgent(String userAgent) 
    {
        this.userAgent = userAgent;
    }

    public String getUserAgent() 
    {
        return userAgent;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setHandleBy(String handleBy) 
    {
        this.handleBy = handleBy;
    }

    public String getHandleBy() 
    {
        return handleBy;
    }

    public void setHandleTime(Date handleTime) 
    {
        this.handleTime = handleTime;
    }

    public Date getHandleTime() 
    {
        return handleTime;
    }

    public void setHandleRemark(String handleRemark) 
    {
        this.handleRemark = handleRemark;
    }

    public String getHandleRemark() 
    {
        return handleRemark;
    }

    public void setEventTime(Date eventTime) 
    {
        this.eventTime = eventTime;
    }

    public Date getEventTime() 
    {
        return eventTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("eventType", getEventType())
            .append("eventDesc", getEventDesc())
            .append("riskLevel", getRiskLevel())
            .append("clientIp", getClientIp())
            .append("clientLocation", getClientLocation())
            .append("userAgent", getUserAgent())
            .append("status", getStatus())
            .append("handleBy", getHandleBy())
            .append("handleTime", getHandleTime())
            .append("handleRemark", getHandleRemark())
            .append("eventTime", getEventTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
