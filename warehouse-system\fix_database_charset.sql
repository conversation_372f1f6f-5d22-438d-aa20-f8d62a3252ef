-- 设置连接字符集
SET NAMES utf8mb4;

-- 选择数据库
USE warehouse_system;

-- 修改数据库字符集
ALTER DATABASE warehouse_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改所有表的字符集（只保留存在的表）
ALTER TABLE sys_api_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dept CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dept_permission_template CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dict_data CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dict_type CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_error_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_hardware_fingerprint CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_job CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_job_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_license CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_security CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_type CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_logininfor CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_notice CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_oper_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_product CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_api CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_backup CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_dept CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_security_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_template_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_data_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_dept CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_online CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_post CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_warehouse_user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_inventory CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_inventory_alert CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_inventory_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_inventory_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_wms_barcode CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_wms_barcode_template CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_product_category CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_product_specification CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_product_supplier CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_stock_in CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_stock_in_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_stock_out CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_stock_out_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wh_supplier CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复表注释（只保留存在的表）
ALTER TABLE `sys_api_permission` COMMENT='API权限表';
ALTER TABLE `sys_dept` COMMENT='部门表';
ALTER TABLE `sys_dept_permission_template` COMMENT='部门权限模板表';
ALTER TABLE `sys_dict_data` COMMENT='字典数据表';
ALTER TABLE `sys_dict_type` COMMENT='字典类型表';
ALTER TABLE `sys_error_log` COMMENT='错误日志表';
ALTER TABLE `sys_hardware_fingerprint` COMMENT='硬件指纹表';
ALTER TABLE `sys_job` COMMENT='定时任务调度表';
ALTER TABLE `sys_job_log` COMMENT='定时任务调度日志表';
ALTER TABLE `sys_license` COMMENT='许可证表';
ALTER TABLE `sys_log_security` COMMENT='安全日志表';
ALTER TABLE `sys_log_type` COMMENT='日志类型表';
ALTER TABLE `sys_logininfor` COMMENT='系统访问记录表';
ALTER TABLE `sys_menu` COMMENT='菜单权限表';
ALTER TABLE `sys_notice` COMMENT='通知公告表';
ALTER TABLE `sys_oper_log` COMMENT='操作日志记录表';
ALTER TABLE `sys_permission` COMMENT='权限表';
ALTER TABLE `sys_product` COMMENT='产品表';
ALTER TABLE `sys_role` COMMENT='角色信息表';
ALTER TABLE `sys_role_api` COMMENT='角色API权限关联表';
ALTER TABLE `sys_role_backup` COMMENT='角色备份表';
ALTER TABLE `sys_role_dept` COMMENT='角色和部门关联表';
ALTER TABLE `sys_role_menu` COMMENT='角色和菜单关联表';
ALTER TABLE `sys_security_log` COMMENT='安全日志表';
ALTER TABLE `sys_template_warehouse` COMMENT='模板仓库关联表';
ALTER TABLE `sys_user` COMMENT='用户信息表';
ALTER TABLE `sys_user_data_permission` COMMENT='用户和数据权限关联表';
ALTER TABLE `sys_user_dept` COMMENT='用户和部门关联表';
ALTER TABLE `sys_user_menu` COMMENT='用户和菜单关联表';
ALTER TABLE `sys_user_online` COMMENT='在线用户记录表';
ALTER TABLE `sys_user_post` COMMENT='用户与岗位关联表';
ALTER TABLE `sys_user_role` COMMENT '用户和角色关联表';
ALTER TABLE `sys_warehouse` COMMENT='仓库表';
ALTER TABLE `sys_warehouse_user` COMMENT='仓库用户关联表';
ALTER TABLE `wh_inventory` COMMENT='库存表';
ALTER TABLE `wh_inventory_alert` COMMENT='库存预警表';
ALTER TABLE `wh_inventory_detail` COMMENT='库存明细表';
ALTER TABLE `wh_inventory_log` COMMENT='库存日志表';
ALTER TABLE `wh_wms_barcode` COMMENT='物品条码表';
ALTER TABLE `wh_wms_barcode_template` COMMENT='物品条码模板表';
ALTER TABLE `wh_product_category` COMMENT='产品分类表';
ALTER TABLE `wh_product_specification` COMMENT='产品规格表';
ALTER TABLE `wh_product_supplier` COMMENT='产品供应商关联表';
ALTER TABLE `wh_stock_in` COMMENT='入库单表';
ALTER TABLE `wh_stock_in_detail` COMMENT '入库明细表';
ALTER TABLE `wh_stock_out` COMMENT '出库单表';
ALTER TABLE `wh_stock_out_detail` COMMENT '出库明细表';
ALTER TABLE `wh_supplier` COMMENT '供应商表';

-- 修复历史数据编码问题（只保留存在的表）
UPDATE sys_api_permission SET 
  api_name = CONVERT(CONVERT(api_name USING binary) USING utf8mb4),
  api_path = CONVERT(CONVERT(api_path USING binary) USING utf8mb4),
  permission = CONVERT(CONVERT(permission USING binary) USING utf8mb4),
  group_name = CONVERT(CONVERT(group_name USING binary) USING utf8mb4),
  description = CONVERT(CONVERT(description USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_dept SET 
  dept_name = CONVERT(CONVERT(dept_name USING binary) USING utf8mb4),
  leader = CONVERT(CONVERT(leader USING binary) USING utf8mb4),
  phone = CONVERT(CONVERT(phone USING binary) USING utf8mb4),
  email = CONVERT(CONVERT(email USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_dept_permission_template SET 
  template_name = CONVERT(CONVERT(template_name USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_dict_data SET 
  dict_label = CONVERT(CONVERT(dict_label USING binary) USING utf8mb4),
  dict_value = CONVERT(CONVERT(dict_value USING binary) USING utf8mb4),
  css_class = CONVERT(CONVERT(css_class USING binary) USING utf8mb4),
  list_class = CONVERT(CONVERT(list_class USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_dict_type SET 
  dict_name = CONVERT(CONVERT(dict_name USING binary) USING utf8mb4),
  dict_type = CONVERT(CONVERT(dict_type USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_menu SET 
  menu_name = CONVERT(CONVERT(menu_name USING binary) USING utf8mb4),
  perms = CONVERT(CONVERT(perms USING binary) USING utf8mb4),
  icon = CONVERT(CONVERT(icon USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4),
  route_name = CONVERT(CONVERT(route_name USING binary) USING utf8mb4);

UPDATE sys_notice SET 
  notice_title = CONVERT(CONVERT(notice_title USING binary) USING utf8mb4),
  notice_type = CONVERT(CONVERT(notice_type USING binary) USING utf8mb4),
  notice_content = CONVERT(CONVERT(notice_content USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_permission SET 
  permission_name = CONVERT(CONVERT(permission_name USING binary) USING utf8mb4),
  permission_key = CONVERT(CONVERT(permission_key USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_product SET 
  product_name = CONVERT(CONVERT(product_name USING binary) USING utf8mb4),
  product_code = CONVERT(CONVERT(product_code USING binary) USING utf8mb4),
  brand = CONVERT(CONVERT(brand USING binary) USING utf8mb4),
  unit = CONVERT(CONVERT(unit USING binary) USING utf8mb4),
  specification = CONVERT(CONVERT(specification USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_role SET 
  role_name = CONVERT(CONVERT(role_name USING binary) USING utf8mb4),
  role_key = CONVERT(CONVERT(role_key USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_user SET 
  user_name = CONVERT(CONVERT(user_name USING binary) USING utf8mb4),
  nick_name = CONVERT(CONVERT(nick_name USING binary) USING utf8mb4),
  user_type = CONVERT(CONVERT(user_type USING binary) USING utf8mb4),
  email = CONVERT(CONVERT(email USING binary) USING utf8mb4),
  phonenumber = CONVERT(CONVERT(phonenumber USING binary) USING utf8mb4),
  login_ip = CONVERT(CONVERT(login_ip USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE sys_warehouse SET 
  warehouse_name = CONVERT(CONVERT(warehouse_name USING binary) USING utf8mb4),
  warehouse_code = CONVERT(CONVERT(warehouse_code USING binary) USING utf8mb4),
  address = CONVERT(CONVERT(address USING binary) USING utf8mb4),
  contact_person = CONVERT(CONTRACT(contact_person USING binary) USING utf8mb4),
  contact_phone = CONVERT(CONVERT(contact_phone USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE wh_inventory SET 
  batch_number = CONVERT(CONVERT(batch_number USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE wh_inventory_alert SET 
  alert_name = CONVERT(CONVERT(alert_name USING binary) USING utf8mb4),
  alert_rule = CONVERT(CONVERT(alert_rule USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE wh_inventory_log SET 
  operation_type = CONVERT(CONVERT(operation_type USING binary) USING utf8mb4),
  batch_number = CONVERT(CONVERT(batch_number USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE wh_wms_barcode SET 
  product_name = CONVERT(CONVERT(product_name USING binary) USING utf8mb4),
  barcode_content = CONVERT(CONVERT(barcode_content USING binary) USING utf8mb4),
  barcode_type = CONVERT(CONVERT(barcode_type USING binary) USING utf8mb4),
  barcode_image = CONVERT(CONVERT(barcode_image USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE wh_wms_barcode_template SET 
  template_name = CONVERT(CONVERT(template_name USING binary) USING utf8mb4),
  template_type = CONVERT(CONVERT(template_type USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE wh_product_category SET 
  category_name = CONVERT(CONVERT(category_name USING binary) USING utf8mb4),
  category_code = CONVERT(CONVERT(category_code USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE wh_product_specification SET 
  spec_name = CONVERT(CONVERT(spec_name USING binary) USING utf8mb4),
  spec_value = CONVERT(CONVERT(spec_value USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

UPDATE wh_supplier SET 
  supplier_name = CONVERT(CONVERT(supplier_name USING binary) USING utf8mb4),
  supplier_code = CONVERT(CONVERT(supplier_code USING binary) USING utf8mb4),
  contact_person = CONVERT(CONVERT(contact_person USING binary) USING utf8mb4),
  contact_phone = CONVERT(CONVERT(contact_phone USING binary) USING utf8mb4),
  address = CONVERT(CONVERT(address USING binary) USING utf8mb4),
  remark = CONVERT(CONVERT(remark USING binary) USING utf8mb4);

-- 验证修复结果
SELECT * FROM sys_menu LIMIT 5;
SELECT * FROM sys_dept LIMIT 5;
SELECT * FROM sys_user LIMIT 5;