<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="条码内容" prop="barcodeContent">
        <el-input
          v-model="queryParams.barcodeContent"
          placeholder="请输入条码内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入物品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="条码类型" prop="barcodeType">
        <el-select v-model="queryParams.barcodeType" placeholder="请选择条码类型" clearable>
          <el-option
            v-for="dict in dict.type.wms_barcode_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['product:barcode:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['product:barcode:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['product:barcode:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['product:barcode:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-printer"
          size="mini"
          :disabled="multiple"
          @click="handlePrint"
          v-hasPermi="['product:barcode:print']"
        >打印</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleBatchGenerate"
          v-hasPermi="['product:barcode:generate']"
        >批量生成</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="barcodeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="条码ID" align="center" prop="barcodeId" />
      <el-table-column label="条码内容" align="center" prop="barcodeContent" :show-overflow-tooltip="true" />
      <el-table-column label="条码类型" align="center" prop="barcodeType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wms_barcode_type" :value="scope.row.barcodeType"/>
        </template>
      </el-table-column>
      <el-table-column label="物品名称" align="center" prop="productName" :show-overflow-tooltip="true" />
      <el-table-column label="条码图片" align="center" prop="barcodeImage">
        <template slot-scope="scope">
          <el-image
            style="width: 100px; height: 40px"
            :src="scope.row.barcodeImage"
            :preview-src-list="[scope.row.barcodeImage]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['product:barcode:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['product:barcode:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-printer"
            @click="handlePrint(scope.row)"
            v-hasPermi="['product:barcode:print']"
          >打印</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物品条码对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="物品" prop="productId">
          <el-select v-model="form.productId" placeholder="请选择物品" filterable @change="handleProductChange">
            <el-option
              v-for="item in productOptions"
              :key="item.productId"
              :label="item.productName"
              :value="item.productId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="条码类型" prop="barcodeType">
          <el-select v-model="form.barcodeType" placeholder="请选择条码类型">
            <el-option
              v-for="dict in dict.type.wms_barcode_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="条码内容" prop="barcodeContent">
          <el-input v-model="form.barcodeContent" placeholder="请输入条码内容" />
        </el-form-item>
        <el-form-item label="条码模板" prop="templateId">
          <el-select v-model="form.templateId" placeholder="请选择条码模板">
            <el-option
              v-for="item in templateOptions"
              :key="item.templateId"
              :label="item.templateName"
              :value="item.templateId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量生成条码对话框 -->
    <el-dialog title="批量生成条码" :visible.sync="batchOpen" width="600px" append-to-body>
      <el-form ref="batchForm" :model="batchForm" :rules="batchRules" label-width="100px">
        <el-form-item label="物品" prop="productId">
          <el-select v-model="batchForm.productId" placeholder="请选择物品" filterable @change="handleBatchProductChange">
            <el-option
              v-for="item in productOptions"
              :key="item.productId"
              :label="item.productName"
              :value="item.productId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="条码类型" prop="barcodeType">
          <el-select v-model="batchForm.barcodeType" placeholder="请选择条码类型">
            <el-option
              v-for="dict in dict.type.wms_barcode_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="条码前缀" prop="prefix">
          <el-input v-model="batchForm.prefix" placeholder="请输入条码前缀" />
        </el-form-item>
        <el-form-item label="起始编号" prop="startNum">
          <el-input-number v-model="batchForm.startNum" :min="1" :max="9999" />
        </el-form-item>
        <el-form-item label="编号位数" prop="numDigits">
          <el-input-number v-model="batchForm.numDigits" :min="1" :max="6" />
        </el-form-item>
        <el-form-item label="生成数量" prop="count">
          <el-input-number v-model="batchForm.count" :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="条码模板" prop="templateId">
          <el-select v-model="batchForm.templateId" placeholder="请选择条码模板">
            <el-option
              v-for="item in templateOptions"
              :key="item.templateId"
              :label="item.templateName"
              :value="item.templateId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预览">
          <div class="preview-box">
            <div v-for="(item, index) in previewList" :key="index" class="preview-item">
              {{ item }}
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchForm">确 定</el-button>
        <el-button @click="cancelBatch">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 打印预览对话框 -->
    <el-dialog title="打印预览" :visible.sync="printOpen" width="800px" append-to-body>
      <div class="print-container">
        <div v-for="(item, index) in printList" :key="index" class="print-item">
          <div class="barcode-info">
            <div class="barcode-title">{{ item.productName }}</div>
            <div class="barcode-image">
              <img :src="item.barcodeImage" alt="条码图片" />
            </div>
            <div class="barcode-content">{{ item.barcodeContent }}</div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="doPrint">打 印</el-button>
        <el-button @click="printOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBarcode, getBarcode, addBarcode, updateBarcode, delBarcode, exportBarcode, generateBarcode, printBarcode, batchGenerateBarcode, listBarcodeTemplate } from "@/api/product/barcode";
import { listProduct } from "@/api/product/info";
import axios from "axios";
import { getToken } from "@/utils/auth";

export default {
  name: "Barcode",
  dicts: ['wms_barcode_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物品条码表格数据
      barcodeList: [],
      // 物品选项
      productOptions: [],
      // 条码模板选项
      templateOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示批量生成弹出层
      batchOpen: false,
      // 是否显示打印预览弹出层
      printOpen: false,
      // 打印列表
      printList: [],
      // 预览列表
      previewList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        barcodeContent: undefined,
        productName: undefined,
        barcodeType: undefined
      },
      // 表单参数
      form: {},
      // 批量表单参数
      batchForm: {
        productId: undefined,
        barcodeType: undefined,
        prefix: "BC",
        startNum: 1,
        numDigits: 4,
        count: 10,
        templateId: undefined
      },
      // 表单校验
      rules: {
        productId: [
          { required: true, message: "物品不能为空", trigger: "change" }
        ],
        barcodeType: [
          { required: true, message: "条码类型不能为空", trigger: "change" }
        ],
        barcodeContent: [
          { required: true, message: "条码内容不能为空", trigger: "blur" }
        ],
        templateId: [
          { required: true, message: "条码模板不能为空", trigger: "change" }
        ]
      },
      // 批量表单校验
      batchRules: {
        productId: [
          { required: true, message: "物品不能为空", trigger: "change" }
        ],
        barcodeType: [
          { required: true, message: "条码类型不能为空", trigger: "change" }
        ],
        prefix: [
          { required: true, message: "条码前缀不能为空", trigger: "blur" }
        ],
        startNum: [
          { required: true, message: "起始编号不能为空", trigger: "blur" }
        ],
        numDigits: [
          { required: true, message: "编号位数不能为空", trigger: "blur" }
        ],
        count: [
          { required: true, message: "生成数量不能为空", trigger: "blur" }
        ],
        templateId: [
          { required: true, message: "条码模板不能为空", trigger: "change" }
        ]
      }
    };
  },
  watch: {
    'batchForm.prefix': function() {
      this.generatePreview();
    },
    'batchForm.startNum': function() {
      this.generatePreview();
    },
    'batchForm.numDigits': function() {
      this.generatePreview();
    },
    'batchForm.count': function() {
      this.generatePreview();
    }
  },
  created() {
    // 从路由参数中获取产品ID（如果有）
    if (this.$route.query.productId) {
      this.queryParams.productId = this.$route.query.productId;
    }
    this.getList();
    this.getProductOptions();
    this.getTemplateOptions();
  },
  methods: {
    /** 查询物品条码列表 */
    getList() {
      this.loading = true;
      listBarcode(this.queryParams).then(response => {
        this.barcodeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取物品选项 */
    getProductOptions() {
      listProduct().then(response => {
        this.productOptions = response.rows;
      });
    },
    /** 获取条码模板选项 */
    getTemplateOptions() {
      listBarcodeTemplate().then(response => {
        this.templateOptions = response.data;
      });
    },
    /** 生成预览 */
    generatePreview() {
      this.previewList = [];
      if (!this.batchForm.prefix || this.batchForm.startNum === undefined || this.batchForm.numDigits === undefined || this.batchForm.count === undefined) {
        return;
      }

      const prefix = this.batchForm.prefix;
      const startNum = this.batchForm.startNum;
      const numDigits = this.batchForm.numDigits;
      const count = this.batchForm.count;

      // 限制预览数量，最多显示10个
      const previewCount = Math.min(count, 10);

      for (let i = 0; i < previewCount; i++) {
        const num = startNum + i;
        const numStr = num.toString().padStart(numDigits, '0');
        this.previewList.push(`${prefix}${numStr}`);
      }

      if (count > 10) {
        this.previewList.push('...');
      }
    },
    // 物品选择框变更
    handleProductChange(productId) {
      const product = this.productOptions.find(item => item.productId === productId);
      if (product) {
        this.form.productName = product.productName;
      }
    },
    // 批量表单物品选择框变更
    handleBatchProductChange(productId) {
      const product = this.productOptions.find(item => item.productId === productId);
      if (product) {
        this.batchForm.productName = product.productName;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消批量生成按钮
    cancelBatch() {
      this.batchOpen = false;
      this.resetBatch();
    },
    // 表单重置
    reset() {
      this.form = {
        barcodeId: undefined,
        productId: undefined,
        productName: undefined,
        barcodeType: undefined,
        barcodeContent: undefined,
        templateId: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    // 批量表单重置
    resetBatch() {
      this.batchForm = {
        productId: undefined,
        productName: undefined,
        barcodeType: undefined,
        prefix: "BC",
        startNum: 1,
        numDigits: 4,
        count: 10,
        templateId: undefined
      };
      this.previewList = [];
      this.resetForm("batchForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.barcodeId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物品条码";
    },
    /** 批量生成按钮操作 */
    handleBatchGenerate() {
      this.resetBatch();
      this.batchOpen = true;
      this.generatePreview();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const barcodeId = row.barcodeId || this.ids[0];
      getBarcode(barcodeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物品条码";
      });
    },
    /** 打印按钮操作 */
    handlePrint(row) {
      const barcodeIds = row.barcodeId ? [row.barcodeId] : this.ids;
      printBarcode(barcodeIds).then(response => {
        this.printList = response.data;
        this.printOpen = true;
      });
    },
    /** 执行打印 */
    doPrint() {
      if (this.printList.length === 0) {
        this.$modal.msgError("没有可打印的条码");
        return;
      }

      // 创建一个新窗口
      const printWindow = window.open('', '_blank');

      // 构建HTML内容
      let html = '<!DOCTYPE html><html><head><title>条码打印</title>';
      html += '<style>';
      html += 'body { font-family: Arial, sans-serif; margin: 0; padding: 0; }';
      html += '.print-container { display: flex; flex-wrap: wrap; }';
      html += '.print-item { width: 200px; height: 100px; margin: 10px; border: 1px solid #ddd; padding: 5px; }';
      html += '.barcode-info { display: flex; flex-direction: column; align-items: center; }';
      html += '.barcode-title { font-size: 12px; margin-bottom: 5px; }';
      html += '.barcode-image { margin-bottom: 5px; }';
      html += '.barcode-content { font-size: 10px; }';
      html += '</style></head><body>';

      // 添加打印容器
      html += '<div class="print-container">';

      // 添加每个条码项
      this.printList.forEach(function(item) {
        html += '<div class="print-item">';
        html += '<div class="barcode-info">';
        html += '<div class="barcode-title">' + item.productName + '</div>';
        html += '<div class="barcode-image">';
        html += '<img src="' + item.barcodeImage + '" alt="条码图片" />';
        html += '</div>';
        html += '<div class="barcode-content">' + item.barcodeContent + '</div>';
        html += '</div></div>';
      });

      // 关闭打印容器
      html += '</div>';

      // 添加打印脚本
      html += '<script>';
      html += 'window.onload = function() {';
      html += '  window.print();';
      html += '  setTimeout(function() { window.close(); }, 500);';
      html += '};';
      html += '<\/script></body></html>';

      // 写入HTML内容
      printWindow.document.open();
      printWindow.document.write(html);
      printWindow.document.close();
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.barcodeId != undefined) {
            updateBarcode(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            generateBarcode(this.form).then(response => {
              this.$modal.msgSuccess("生成成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交批量表单 */
    submitBatchForm() {
      this.$refs["batchForm"].validate(valid => {
        if (valid) {
          batchGenerateBarcode(this.batchForm).then(response => {
            this.$modal.msgSuccess("批量生成成功");
            this.batchOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const barcodeIds = row.barcodeId || this.ids;
      this.$modal.confirm('是否确认删除物品条码编号为"' + barcodeIds + '"的数据项？').then(() => {
        return delBarcode(barcodeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有物品条码数据项？').then(() => {
        this.$modal.loading("正在导出数据，请稍候...");

        // 构建查询参数
        const params = {...this.queryParams};

        // 使用axios发送请求，确保携带认证信息
        const baseUrl = process.env.VUE_APP_BASE_API;
        axios({
          method: 'get',
          url: baseUrl + '/product/barcode/export',
          params: params,
          responseType: 'blob',
          headers: { 'Authorization': 'Bearer ' + getToken() }
        }).then(response => {
          // 处理响应
          const blob = new Blob([response.data]);
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = `物品条码_${new Date().getTime()}.xlsx`;
          link.click();
          window.URL.revokeObjectURL(link.href);

          // 关闭加载提示
          this.$modal.closeLoading();
          this.$modal.msgSuccess("导出成功");
        }).catch(error => {
          console.error("导出失败:", error);
          this.$modal.closeLoading();
          this.$modal.msgError("导出失败，请重试");
        });
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.preview-box {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px 10px;
}
.preview-item {
  line-height: 24px;
  color: #606266;
}
.print-container {
  display: flex;
  flex-wrap: wrap;
}
.print-item {
  width: 200px;
  height: 100px;
  margin: 10px;
  border: 1px solid #ddd;
  padding: 5px;
}
.barcode-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.barcode-title {
  font-size: 12px;
  margin-bottom: 5px;
}
.barcode-image {
  margin-bottom: 5px;
}
.barcode-content {
  font-size: 10px;
}
</style>
