package com.wanyu.web.controller.inventory;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.system.domain.WmsInventoryLog;
import com.wanyu.system.service.IWmsInventoryLogService;

/**
 * 库存日志Controller
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@RestController
@RequestMapping("/inventory/log")
public class WmsInventoryLogController extends BaseController
{
    @Autowired
    private IWmsInventoryLogService wmsInventoryLogService;

    /**
     * 查询库存日志列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmsInventoryLog wmsInventoryLog)
    {
        try {
            System.err.println("=== WmsInventoryLogController.list() 被调用 ===");
            System.err.println("当前用户: " + com.wanyu.common.utils.SecurityUtils.getUsername());
            System.err.println("用户ID: " + com.wanyu.common.utils.SecurityUtils.getUserId());
            System.err.println("查询参数: " + wmsInventoryLog);
            
            startPage();
            List<WmsInventoryLog> list = wmsInventoryLogService.selectWmsInventoryLogListWithAuth(wmsInventoryLog);
            
            System.err.println("Controller返回记录数: " + (list != null ? list.size() : 0));
            
            TableDataInfo result = getDataTable(list);
            System.err.println("最终返回结果: total=" + result.getTotal() + ", rows=" + (result.getRows() != null ? result.getRows().size() : 0));
            
            return result;
        } catch (Exception e) {
            System.err.println("Controller异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 导出库存日志列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:log:export')")
    @Log(title = "库存日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, WmsInventoryLog wmsInventoryLog)
    {
        List<WmsInventoryLog> list = wmsInventoryLogService.selectWmsInventoryLogListWithAuth(wmsInventoryLog);
        ExcelUtil<WmsInventoryLog> util = new ExcelUtil<WmsInventoryLog>(WmsInventoryLog.class);
        util.exportExcel(response, list, "库存日志数据");
    }

    /**
     * 获取库存日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:log:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return AjaxResult.success(wmsInventoryLogService.selectWmsInventoryLogByLogId(logId));
    }

    /**
     * 删除库存日志
     */
    @PreAuthorize("@ss.hasPermi('inventory:log:remove')")
    @Log(title = "库存日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(wmsInventoryLogService.deleteWmsInventoryLogByLogIds(logIds));
    }

    /**
     * 获取库存日志统计信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:log:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Map<String, Object> params = new java.util.HashMap<>();
        Map<String, Object> statistics = wmsInventoryLogService.getInventoryStatistics(params);
        return AjaxResult.success(statistics);
    }

    /**
     * 获取库存日志趋势数据
     */
    @PreAuthorize("@ss.hasPermi('inventory:log:query')")
    @GetMapping("/trend")
    public AjaxResult getTrend(@RequestParam(defaultValue = "7") Integer days)
    {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("days", days);
        List<Map<String, Object>> trendData = wmsInventoryLogService.getInventoryTrend(params);
        return AjaxResult.success(trendData);
    }

    /**
     * 测试方法：不需要权限的出入库日志查询（临时调试用）
     */
    @GetMapping("/test")
    public AjaxResult testList(WmsInventoryLog wmsInventoryLog)
    {
        try {
            System.err.println("=== 测试方法被调用 ===");
            System.err.println("当前用户: " + com.wanyu.common.utils.SecurityUtils.getUsername());
            System.err.println("用户ID: " + com.wanyu.common.utils.SecurityUtils.getUserId());
            
            List<WmsInventoryLog> list = wmsInventoryLogService.selectWmsInventoryLogListWithAuth(wmsInventoryLog);
            
            System.err.println("测试方法返回记录数: " + (list != null ? list.size() : 0));
            
            if (list != null && !list.isEmpty()) {
                System.err.println("前3条记录:");
                for (int i = 0; i < Math.min(3, list.size()); i++) {
                    WmsInventoryLog log = list.get(i);
                    System.err.println("  " + (i+1) + ". ID=" + log.getLogId() + 
                        ", 操作=" + log.getOperationType() + 
                        ", 仓库=" + log.getWarehouseName() + 
                        ", 物品=" + log.getProductName());
                }
            }
            
            return AjaxResult.success(list);
        } catch (Exception e) {
            System.err.println("测试方法异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("测试失败: " + e.getMessage());
        }
    }


}