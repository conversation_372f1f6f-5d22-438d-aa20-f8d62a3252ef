package com.wanyu.web.controller.log;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.CrossOrigin;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.WmsInventoryLog;
import com.wanyu.system.service.IWmsInventoryLogService;

/**
 * 出入库日志Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/api/v1/logs/inventory")
@CrossOrigin(origins = "*")
public class InventoryLogController extends BaseController
{
    @Autowired
    private IWmsInventoryLogService wmsInventoryLogService;

    /**
     * 查询出入库日志列表
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmsInventoryLog wmsInventoryLog)
    {
        startPage();
        List<WmsInventoryLog> list = wmsInventoryLogService.selectWmsInventoryLogListWithAuth(wmsInventoryLog);
        
        // 调试信息：检查用户名转换是否生效
        if (list != null && !list.isEmpty()) {
            WmsInventoryLog firstLog = list.get(0);
            System.err.println("=== 出入库日志调试信息 ===");
            System.err.println("第一条记录的操作人员: " + firstLog.getOperator());
            System.err.println("第一条记录的创建人: " + firstLog.getCreateBy());
            System.err.println("========================");
        }
        
        return getDataTable(list);
    }

    /**
     * 导出出入库日志列表
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:export')")
    @Log(title = "出入库日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmsInventoryLog wmsInventoryLog)
    {
        List<WmsInventoryLog> list = wmsInventoryLogService.selectWmsInventoryLogListWithAuth(wmsInventoryLog);
        ExcelUtil<WmsInventoryLog> util = new ExcelUtil<WmsInventoryLog>(WmsInventoryLog.class);
        util.exportExcel(response, list, "出入库日志数据");
    }

    /**
     * 获取出入库日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(wmsInventoryLogService.selectWmsInventoryLogByLogId(logId));
    }

    /**
     * 新增出入库日志
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:add')")
    @Log(title = "出入库日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmsInventoryLog wmsInventoryLog)
    {
        return toAjax(wmsInventoryLogService.insertWmsInventoryLog(wmsInventoryLog));
    }

    /**
     * 修改出入库日志
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:edit')")
    @Log(title = "出入库日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmsInventoryLog wmsInventoryLog)
    {
        return toAjax(wmsInventoryLogService.updateWmsInventoryLog(wmsInventoryLog));
    }

    /**
     * 删除出入库日志
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:remove')")
    @Log(title = "出入库日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(wmsInventoryLogService.deleteWmsInventoryLogByLogIds(logIds));
    }

    /**
     * 批量删除出入库日志
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:remove')")
    @Log(title = "出入库日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult batchRemove(@RequestBody Map<String, Object> params)
    {
        @SuppressWarnings("unchecked")
        List<Long> logIds = (List<Long>) params.get("logIds");
        if (logIds == null || logIds.isEmpty()) {
            return error("请选择要删除的数据");
        }
        Long[] ids = logIds.toArray(new Long[0]);
        return toAjax(wmsInventoryLogService.deleteWmsInventoryLogByLogIds(ids));
    }

    /**
     * 清空出入库日志
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:remove')")
    @Log(title = "出入库日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        return toAjax(wmsInventoryLogService.cleanWmsInventoryLog());
    }

    /**
     * 获取库存统计信息
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam Map<String, Object> params)
    {
        Map<String, Object> statistics = wmsInventoryLogService.getInventoryStatistics(params);
        return success(statistics);
    }

    /**
     * 获取库存操作趋势数据
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:list')")
    @GetMapping("/trend")
    public AjaxResult getTrend(@RequestParam Map<String, Object> params)
    {
        List<Map<String, Object>> trendData = wmsInventoryLogService.getInventoryTrend(params);
        return success(trendData);
    }

    /**
     * 按物品类别统计库存操作
     */
    @PreAuthorize("@ss.hasPermi('log:inventory:list')")
    @GetMapping("/stats/category")
    public AjaxResult getStatsByCategory(@RequestParam Map<String, Object> params)
    {
        List<Map<String, Object>> statsData = wmsInventoryLogService.getInventoryStatsByCategory(params);
        return success(statsData);
    }

}