@echo off
chcp 65001 >nul
echo ========================================
echo 创建条码相关表
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo 🔧 创建条码表和模板表...
echo.

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < create_barcode_tables_only.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 条码表创建完成！
    echo.
    echo 📊 验证结果:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT template_id, template_name, template_type FROM wms_barcode_template;"
    echo.
    echo ========================================
    echo ✅ 创建完成！
    echo ========================================
    echo.
    echo 🔄 下一步:
    echo   1. 重新编译后端项目
    echo   2. 重启后端服务
    echo   3. 刷新前端页面测试
    echo.
) else (
    echo.
    echo ❌ 创建失败！
    echo.
)

echo 按任意键退出...
pause >nul