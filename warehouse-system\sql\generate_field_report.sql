-- 字段标准合规报告生成脚本
-- 生成详细的字段定义标准合规性报告

USE warehouse_system;

-- 创建临时报告表
DROP TEMPORARY TABLE IF EXISTS field_report_temp;
CREATE TEMPORARY TABLE field_report_temp (
    report_id INT AUTO_INCREMENT PRIMARY KEY,
    report_section VARCHAR(100),
    table_name VARCHAR(100),
    field_name VARCHAR(100),
    current_value VARCHAR(500),
    standard_value VARCHAR(500),
    compliance_status VARCHAR(50),
    issue_level VARCHAR(20),
    recommendation TEXT,
    check_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 1. 检查表结构合规性
INSERT INTO field_report_temp (report_section, table_name, field_name, current_value, standard_value, compliance_status, issue_level, recommendation)
SELECT 
    '表结构检查' as report_section,
    TABLE_NAME as table_name,
    COLUMN_NAME as field_name,
    CONCAT(DATA_TYPE, '(', IFNULL(CHARACTER_MAXIMUM_LENGTH, ''), ') DEFAULT ', IFNULL(COLUMN_DEFAULT, 'NULL')) as current_value,
    'CHAR(1) DEFAULT ''0''' as standard_value,
    CASE 
        WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 AND COLUMN_DEFAULT = '0' 
        THEN '符合标准'
        ELSE '不符合标准'
    END as compliance_status,
    CASE 
        WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 AND COLUMN_DEFAULT = '0' 
        THEN 'INFO'
        ELSE 'HIGH'
    END as issue_level,
    CASE 
        WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 AND COLUMN_DEFAULT = '0' 
        THEN '字段定义符合标准'
        ELSE '需要修改字段类型为CHAR(1)，默认值为''0'''
    END as recommendation
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME IN ('sys_license', 'sys_license_feature')
  AND COLUMN_NAME = 'status';

-- 2. 检查字段注释合规性
INSERT INTO field_report_temp (report_section, table_name, field_name, current_value, standard_value, compliance_status, issue_level, recommendation)
SELECT 
    '字段注释检查' as report_section,
    TABLE_NAME as table_name,
    COLUMN_NAME as field_name,
    IFNULL(COLUMN_COMMENT, '无注释') as current_value,
    '状态（0正常 1停用）' as standard_value,
    CASE 
        WHEN COLUMN_COMMENT LIKE '%0%正常%1%停用%' OR COLUMN_COMMENT LIKE '%0%启用%1%禁用%'
        THEN '符合标准'
        ELSE '不符合标准'
    END as compliance_status,
    CASE 
        WHEN COLUMN_COMMENT LIKE '%0%正常%1%停用%' OR COLUMN_COMMENT LIKE '%0%启用%1%禁用%'
        THEN 'INFO'
        ELSE 'MEDIUM'
    END as issue_level,
    CASE 
        WHEN COLUMN_COMMENT LIKE '%0%正常%1%停用%' OR COLUMN_COMMENT LIKE '%0%启用%1%禁用%'
        THEN '字段注释符合标准'
        ELSE '需要更新字段注释为标准格式'
    END as recommendation
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME IN ('sys_license', 'sys_license_feature')
  AND COLUMN_NAME = 'status';

-- 3. 检查数据值合规性
INSERT INTO field_report_temp (report_section, table_name, field_name, current_value, standard_value, compliance_status, issue_level, recommendation)
SELECT 
    '数据值检查' as report_section,
    'sys_license' as table_name,
    'status' as field_name,
    CONCAT('有效值: ', valid_count, ', 无效值: ', invalid_count, ', 总计: ', total_count) as current_value,
    '所有值都应该是0或1' as standard_value,
    CASE WHEN invalid_count = 0 THEN '符合标准' ELSE '不符合标准' END as compliance_status,
    CASE WHEN invalid_count = 0 THEN 'INFO' ELSE 'HIGH' END as issue_level,
    CASE 
        WHEN invalid_count = 0 THEN '数据值符合标准'
        ELSE CONCAT('需要清理', invalid_count, '条无效数据')
    END as recommendation
FROM (
    SELECT 
        (SELECT COUNT(*) FROM sys_license WHERE status IN ('0', '1')) as valid_count,
        (SELECT COUNT(*) FROM sys_license WHERE status NOT IN ('0', '1')) as invalid_count,
        (SELECT COUNT(*) FROM sys_license) as total_count
) as data_stats;

INSERT INTO field_report_temp (report_section, table_name, field_name, current_value, standard_value, compliance_status, issue_level, recommendation)
SELECT 
    '数据值检查' as report_section,
    'sys_license_feature' as table_name,
    'status' as field_name,
    CONCAT('有效值: ', valid_count, ', 无效值: ', invalid_count, ', 总计: ', total_count) as current_value,
    '所有值都应该是0或1' as standard_value,
    CASE WHEN invalid_count = 0 THEN '符合标准' ELSE '不符合标准' END as compliance_status,
    CASE WHEN invalid_count = 0 THEN 'INFO' ELSE 'HIGH' END as issue_level,
    CASE 
        WHEN invalid_count = 0 THEN '数据值符合标准'
        ELSE CONCAT('需要清理', invalid_count, '条无效数据')
    END as recommendation
FROM (
    SELECT 
        (SELECT COUNT(*) FROM sys_license_feature WHERE status IN ('0', '1')) as valid_count,
        (SELECT COUNT(*) FROM sys_license_feature WHERE status NOT IN ('0', '1')) as invalid_count,
        (SELECT COUNT(*) FROM sys_license_feature) as total_count
) as data_stats;

-- 4. 检查数据字典配置
INSERT INTO field_report_temp (report_section, table_name, field_name, current_value, standard_value, compliance_status, issue_level, recommendation)
SELECT 
    '数据字典检查' as report_section,
    'sys_dict_data' as table_name,
    dict_type as field_name,
    CONCAT(dict_label, '=', dict_value) as current_value,
    expected_config as standard_value,
    CASE WHEN is_correct THEN '符合标准' ELSE '不符合标准' END as compliance_status,
    CASE WHEN is_correct THEN 'INFO' ELSE 'MEDIUM' END as issue_level,
    CASE 
        WHEN is_correct THEN '字典配置正确'
        ELSE '需要更新字典配置'
    END as recommendation
FROM (
    SELECT 
        dict_type,
        dict_label,
        dict_value,
        CASE 
            WHEN dict_type = 'sys_normal_disable' THEN '正常=0, 停用=1'
            WHEN dict_type = 'operation_status' THEN '成功=0, 失败=1'
            ELSE '未知配置'
        END as expected_config,
        CASE 
            WHEN dict_type = 'sys_normal_disable' AND dict_value = '0' AND dict_label = '正常' THEN TRUE
            WHEN dict_type = 'sys_normal_disable' AND dict_value = '1' AND dict_label = '停用' THEN TRUE
            WHEN dict_type = 'operation_status' AND dict_value = '0' AND dict_label = '成功' THEN TRUE
            WHEN dict_type = 'operation_status' AND dict_value = '1' AND dict_label = '失败' THEN TRUE
            ELSE FALSE
        END as is_correct
    FROM sys_dict_data 
    WHERE dict_type IN ('sys_normal_disable', 'operation_status')
) as dict_check;

-- 生成完整报告
SELECT '========== 字段标准合规性报告 ==========' as report_header;
SELECT CONCAT('报告生成时间: ', NOW()) as report_info;
SELECT CONCAT('数据库: warehouse_system') as report_info;

-- 报告摘要
SELECT 
    '报告摘要' as section,
    COUNT(*) as total_checks,
    SUM(CASE WHEN compliance_status = '符合标准' THEN 1 ELSE 0 END) as compliant_items,
    SUM(CASE WHEN compliance_status = '不符合标准' THEN 1 ELSE 0 END) as non_compliant_items,
    CONCAT(ROUND(SUM(CASE WHEN compliance_status = '符合标准' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2), '%') as compliance_rate
FROM field_report_temp;

-- 按问题级别统计
SELECT 
    '问题级别统计' as section,
    issue_level,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM field_report_temp), 2), '%') as percentage
FROM field_report_temp 
GROUP BY issue_level
ORDER BY 
    CASE issue_level 
        WHEN 'HIGH' THEN 1 
        WHEN 'MEDIUM' THEN 2 
        WHEN 'INFO' THEN 3 
        ELSE 4 
    END;

-- 详细检查结果
SELECT 
    '详细检查结果' as section,
    report_section,
    table_name,
    field_name,
    current_value,
    standard_value,
    compliance_status,
    issue_level,
    recommendation
FROM field_report_temp 
ORDER BY 
    CASE issue_level 
        WHEN 'HIGH' THEN 1 
        WHEN 'MEDIUM' THEN 2 
        WHEN 'INFO' THEN 3 
        ELSE 4 
    END,
    report_section,
    table_name;

-- 需要立即修复的高优先级问题
SELECT 
    '高优先级问题' as section,
    table_name,
    field_name,
    current_value,
    recommendation
FROM field_report_temp 
WHERE issue_level = 'HIGH' AND compliance_status = '不符合标准'
ORDER BY table_name, field_name;

-- 修复建议汇总
SELECT 
    '修复建议汇总' as section,
    recommendation,
    COUNT(*) as affected_items,
    GROUP_CONCAT(CONCAT(table_name, '.', field_name) SEPARATOR ', ') as affected_fields
FROM field_report_temp 
WHERE compliance_status = '不符合标准'
GROUP BY recommendation
ORDER BY COUNT(*) DESC;

-- 清理临时表
DROP TEMPORARY TABLE field_report_temp;