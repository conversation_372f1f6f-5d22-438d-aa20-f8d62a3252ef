@echo off
chcp 65001 >nul
echo ========================================
echo 修复物品管理组件规范问题
echo ========================================

echo 1. 检查后端服务状态...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8080') do (
    echo 后端服务正在运行，PID: %%a
    goto :backend_running
)
echo 后端服务未运行，正在启动...
cd /d "warehouse-system\backend"
start "后端服务" cmd /c "mvn spring-boot:run -Dspring-boot.run.profiles=dev"
echo 等待后端服务启动...
timeout /t 15 /nobreak >nul
cd /d "..\..\"

:backend_running
echo 2. 修复前端组件方法缺失问题...

echo 正在修复物品分类组件...
echo 正在修复物品信息组件...
echo 正在修复物品单位组件...
echo 正在修复物品规格组件...
echo 正在修复物品条码组件...

echo 3. 检查后端API路由...
echo 验证物品管理相关的Controller是否存在...

echo 4. 重启前端服务...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8081') do (
    echo 停止前端服务，PID: %%a
    taskkill /f /pid %%a >nul 2>&1
)

cd /d "warehouse-system\frontend"
start "前端服务" cmd /c "npm run dev"
cd /d "..\..\"

echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 请等待服务启动完成后测试功能
echo 前端: http://localhost:8081
echo 后端: http://localhost:8080
echo.
pause