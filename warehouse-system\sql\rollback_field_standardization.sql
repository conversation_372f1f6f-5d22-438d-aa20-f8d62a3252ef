-- =====================================================
-- 字段定义标准化回滚脚本
-- 
-- 功能描述：
-- 安全地回滚字段定义标准化迁移，恢复到迁移前的状态
-- 
-- 回滚范围：
-- 1. 恢复sys_license表的原始数据和结构
-- 2. 恢复sys_license_feature表的原始数据和结构
-- 3. 恢复相关数据字典配置
-- 4. 清理迁移过程中创建的临时数据
-- 
-- 安全机制：
-- - 多重确认检查
-- - 分阶段回滚
-- - 完整的操作日志
-- - 回滚验证
-- =====================================================

USE warehouse_system;

-- =====================================================
-- 回滚前安全检查
-- =====================================================

-- 获取要回滚的迁移ID
SET @rollback_migration_id = COALESCE(
    @migration_id,
    (SELECT migration_id FROM field_standardization_log 
     WHERE phase = 'COMPLETED' AND step_name = '迁移完成' 
     ORDER BY log_id DESC LIMIT 1)
);

-- 创建回滚日志表
DROP TABLE IF EXISTS field_standardization_rollback_log;
CREATE TABLE field_standardization_rollback_log (
    rollback_id INT AUTO_INCREMENT PRIMARY KEY,
    original_migration_id VARCHAR(50) NOT NULL,
    rollback_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    rollback_phase VARCHAR(50) NOT NULL,
    rollback_step VARCHAR(100) NOT NULL,
    rollback_status ENUM('started', 'completed', 'failed', 'skipped') NOT NULL,
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    end_time DATETIME NULL,
    affected_records INT DEFAULT 0,
    error_message TEXT NULL,
    rollback_details JSON NULL,
    INDEX idx_migration_id (original_migration_id),
    INDEX idx_phase (rollback_phase),
    INDEX idx_status (rollback_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段标准化回滚日志表';

-- 记录回滚开始
INSERT INTO field_standardization_rollback_log 
(original_migration_id, rollback_phase, rollback_step, rollback_status, rollback_details) 
VALUES (@rollback_migration_id, 'INIT', '回滚初始化', 'started', 
        JSON_OBJECT('rollback_start_time', NOW(), 'target_migration_id', @rollback_migration_id));

SELECT 
    '=== 字段定义标准化回滚开始 ===' as message,
    @rollback_migration_id as target_migration_id,
    NOW() as rollback_start_time,
    USER() as executor;

-- =====================================================
-- 安全检查1：验证迁移记录存在
-- =====================================================

SET @migration_exists = (
    SELECT COUNT(*) 
    FROM field_standardization_log 
    WHERE migration_id = @rollback_migration_id
);

IF @migration_exists = 0 THEN
    INSERT INTO field_standardization_rollback_log 
    (original_migration_id, rollback_phase, rollback_step, rollback_status, error_message) 
    VALUES (@rollback_migration_id, 'SAFETY_CHECK', '迁移记录验证', 'failed', 
            '未找到指定的迁移记录，无法执行回滚');
    
    SELECT 
        '❌ 回滚失败' as status,
        '未找到指定的迁移记录' as error_message,
        @rollback_migration_id as migration_id;
    -- 应该终止执行
END IF;

-- =====================================================
-- 安全检查2：验证备份数据存在
-- =====================================================

SET @backup_db_exists = (
    SELECT COUNT(*) 
    FROM information_schema.schemata 
    WHERE schema_name = 'warehouse_system_field_std_backup'
);

SET @backup_tables_count = (
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = 'warehouse_system_field_std_backup'
      AND table_name LIKE '%backup%'
);

IF @backup_db_exists = 0 OR @backup_tables_count < 2 THEN
    INSERT INTO field_standardization_rollback_log 
    (original_migration_id, rollback_phase, rollback_step, rollback_status, error_message) 
    VALUES (@rollback_migration_id, 'SAFETY_CHECK', '备份数据验证', 'failed', 
            CONCAT('备份数据不完整，备份数据库存在:', @backup_db_exists, ', 备份表数量:', @backup_tables_count));
    
    SELECT 
        '❌ 回滚失败' as status,
        '备份数据不完整，无法安全回滚' as error_message,
        CONCAT('备份数据库存在:', @backup_db_exists, ', 备份表数量:', @backup_tables_count) as details;
    -- 应该终止执行
END IF;

-- =====================================================
-- 安全检查3：验证当前数据状态
-- =====================================================

-- 检查当前表结构是否为修复后的状态
SET @current_license_status_default = (
    SELECT COLUMN_DEFAULT 
    FROM information_schema.columns 
    WHERE table_schema = 'warehouse_system' 
      AND table_name = 'sys_license' 
      AND column_name = 'status'
);

SET @current_feature_status_default = (
    SELECT COLUMN_DEFAULT 
    FROM information_schema.columns 
    WHERE table_schema = 'warehouse_system' 
      AND table_name = 'sys_license_feature' 
      AND column_name = 'status'
);

-- 如果当前状态不是修复后的状态，警告用户
IF @current_license_status_default != '0' OR @current_feature_status_default != '0' THEN
    INSERT INTO field_standardization_rollback_log 
    (original_migration_id, rollback_phase, rollback_step, rollback_status, error_message) 
    VALUES (@rollback_migration_id, 'SAFETY_CHECK', '当前状态验证', 'failed', 
            '当前表结构不是预期的修复后状态，可能已经被其他操作修改');
    
    SELECT 
        '⚠️ 回滚警告' as status,
        '当前表结构不是预期的修复后状态' as warning_message,
        CONCAT('license status默认值:', COALESCE(@current_license_status_default, 'NULL'), 
               ', feature status默认值:', COALESCE(@current_feature_status_default, 'NULL')) as current_state;
    -- 可以继续，但需要用户确认
END IF;

-- 更新安全检查状态
UPDATE field_standardization_rollback_log 
SET rollback_status = 'completed', end_time = NOW() 
WHERE original_migration_id = @rollback_migration_id 
  AND rollback_phase = 'INIT' 
  AND rollback_step = '回滚初始化';

SELECT 
    '✓ 安全检查通过，开始执行回滚' as message,
    NOW() as check_completion_time;

-- =====================================================
-- 阶段1：创建当前状态备份（回滚前备份）
-- =====================================================

INSERT INTO field_standardization_rollback_log 
(original_migration_id, rollback_phase, rollback_step, rollback_status) 
VALUES (@rollback_migration_id, 'PRE_ROLLBACK_BACKUP', '回滚前状态备份', 'started');

-- 开始回滚前备份事务
START TRANSACTION;

-- 创建回滚前备份数据库
CREATE DATABASE IF NOT EXISTS warehouse_system_pre_rollback_backup;

-- 备份当前的sys_license表（修复后状态）
DROP TABLE IF EXISTS warehouse_system_pre_rollback_backup.sys_license_current;
CREATE TABLE warehouse_system_pre_rollback_backup.sys_license_current AS 
SELECT * FROM sys_license;

-- 备份当前的sys_license_feature表（修复后状态）
DROP TABLE IF EXISTS warehouse_system_pre_rollback_backup.sys_license_feature_current;
CREATE TABLE warehouse_system_pre_rollback_backup.sys_license_feature_current AS 
SELECT * FROM sys_license_feature;

-- 备份当前的数据字典
DROP TABLE IF EXISTS warehouse_system_pre_rollback_backup.sys_dict_data_current;
CREATE TABLE warehouse_system_pre_rollback_backup.sys_dict_data_current AS 
SELECT * FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status');

-- 创建回滚前备份元信息
DROP TABLE IF EXISTS warehouse_system_pre_rollback_backup.pre_rollback_metadata;
CREATE TABLE warehouse_system_pre_rollback_backup.pre_rollback_metadata (
    backup_id INT AUTO_INCREMENT PRIMARY KEY,
    original_migration_id VARCHAR(50) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_count INT NOT NULL,
    backup_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    backup_purpose VARCHAR(100) DEFAULT '回滚前状态备份'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 记录回滚前备份信息
INSERT INTO warehouse_system_pre_rollback_backup.pre_rollback_metadata 
(original_migration_id, table_name, record_count) VALUES
(@rollback_migration_id, 'sys_license_current', (SELECT COUNT(*) FROM sys_license)),
(@rollback_migration_id, 'sys_license_feature_current', (SELECT COUNT(*) FROM sys_license_feature)),
(@rollback_migration_id, 'sys_dict_data_current', 
 (SELECT COUNT(*) FROM sys_dict_data WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status')));

-- 提交回滚前备份
COMMIT;

-- 更新回滚前备份状态
UPDATE field_standardization_rollback_log 
SET rollback_status = 'completed', end_time = NOW(), 
    affected_records = (SELECT SUM(record_count) FROM warehouse_system_pre_rollback_backup.pre_rollback_metadata WHERE original_migration_id = @rollback_migration_id)
WHERE original_migration_id = @rollback_migration_id 
  AND rollback_phase = 'PRE_ROLLBACK_BACKUP' 
  AND rollback_step = '回滚前状态备份';

SELECT 
    '✓ 回滚前状态备份完成' as message,
    (SELECT SUM(record_count) FROM warehouse_system_pre_rollback_backup.pre_rollback_metadata WHERE original_migration_id = @rollback_migration_id) as backup_records,
    NOW() as completion_time;

-- =====================================================
-- 阶段2：回滚sys_license表
-- =====================================================

INSERT INTO field_standardization_rollback_log 
(original_migration_id, rollback_phase, rollback_step, rollback_status) 
VALUES (@rollback_migration_id, 'ROLLBACK_LICENSE', 'sys_license表回滚', 'started');

-- 开始sys_license回滚事务
START TRANSACTION;

-- 记录回滚前状态
SELECT 
    'sys_license回滚前状态' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '启用(修复后定义)'
        WHEN status = '1' THEN '禁用(修复后定义)'
        ELSE '未知状态'
    END as current_meaning,
    COUNT(*) as count
FROM sys_license 
GROUP BY status
ORDER BY status;

-- 验证备份数据可用性
SET @license_backup_count = (
    SELECT COUNT(*) 
    FROM warehouse_system_field_std_backup.sys_license_backup
);

IF @license_backup_count = 0 THEN
    ROLLBACK;
    INSERT INTO field_standardization_rollback_log 
    (original_migration_id, rollback_phase, rollback_step, rollback_status, error_message) 
    VALUES (@rollback_migration_id, 'ROLLBACK_LICENSE', 'sys_license表回滚', 'failed', 
            'sys_license备份数据为空，无法回滚');
    
    SELECT '❌ sys_license表回滚失败：备份数据为空' as error_message;
    -- 应该终止执行
END IF;

-- 删除当前表
DROP TABLE IF EXISTS sys_license;

-- 从备份恢复表结构和数据
CREATE TABLE sys_license AS 
SELECT * FROM warehouse_system_field_std_backup.sys_license_backup;

-- 重建主键和索引
ALTER TABLE sys_license ADD PRIMARY KEY (license_id);
ALTER TABLE sys_license ADD UNIQUE INDEX uk_license_key (license_key);
ALTER TABLE sys_license ADD INDEX idx_company_name (company_name);
ALTER TABLE sys_license ADD INDEX idx_status (status);
ALTER TABLE sys_license ADD INDEX idx_current (current);

-- 验证回滚结果
SELECT 
    'sys_license回滚后状态' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '禁用(原始定义)'
        WHEN status = '1' THEN '启用(原始定义)'
        ELSE '未知状态'
    END as restored_meaning,
    COUNT(*) as count
FROM sys_license 
GROUP BY status
ORDER BY status;

-- 验证数据完整性
SET @restored_license_count = (SELECT COUNT(*) FROM sys_license);

IF @restored_license_count != @license_backup_count THEN
    ROLLBACK;
    INSERT INTO field_standardization_rollback_log 
    (original_migration_id, rollback_phase, rollback_step, rollback_status, error_message) 
    VALUES (@rollback_migration_id, 'ROLLBACK_LICENSE', 'sys_license表回滚', 'failed', 
            CONCAT('数据完整性验证失败，备份:', @license_backup_count, ', 恢复:', @restored_license_count));
    
    SELECT '❌ sys_license表回滚失败：数据完整性验证失败' as error_message;
    -- 应该终止执行
END IF;

-- 提交sys_license回滚
COMMIT;

-- 更新sys_license回滚状态
UPDATE field_standardization_rollback_log 
SET rollback_status = 'completed', end_time = NOW(), 
    affected_records = @restored_license_count
WHERE original_migration_id = @rollback_migration_id 
  AND rollback_phase = 'ROLLBACK_LICENSE' 
  AND rollback_step = 'sys_license表回滚';

SELECT 
    '✓ sys_license表回滚完成' as message,
    @restored_license_count as restored_records,
    NOW() as completion_time;

-- =====================================================
-- 阶段3：回滚sys_license_feature表
-- =====================================================

INSERT INTO field_standardization_rollback_log 
(original_migration_id, rollback_phase, rollback_step, rollback_status) 
VALUES (@rollback_migration_id, 'ROLLBACK_FEATURE', 'sys_license_feature表回滚', 'started');

-- 开始sys_license_feature回滚事务
START TRANSACTION;

-- 记录回滚前状态
SELECT 
    'sys_license_feature回滚前状态' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '启用(修复后定义)'
        WHEN status = '1' THEN '禁用(修复后定义)'
        ELSE '未知状态'
    END as current_meaning,
    COUNT(*) as count
FROM sys_license_feature 
GROUP BY status
ORDER BY status;

-- 验证备份数据可用性
SET @feature_backup_count = (
    SELECT COUNT(*) 
    FROM warehouse_system_field_std_backup.sys_license_feature_backup
);

IF @feature_backup_count = 0 THEN
    ROLLBACK;
    INSERT INTO field_standardization_rollback_log 
    (original_migration_id, rollback_phase, rollback_step, rollback_status, error_message) 
    VALUES (@rollback_migration_id, 'ROLLBACK_FEATURE', 'sys_license_feature表回滚', 'failed', 
            'sys_license_feature备份数据为空，无法回滚');
    
    SELECT '❌ sys_license_feature表回滚失败：备份数据为空' as error_message;
    -- 应该终止执行
END IF;

-- 删除当前表
DROP TABLE IF EXISTS sys_license_feature;

-- 从备份恢复表结构和数据
CREATE TABLE sys_license_feature AS 
SELECT * FROM warehouse_system_field_std_backup.sys_license_feature_backup;

-- 重建主键和索引
ALTER TABLE sys_license_feature ADD PRIMARY KEY (feature_id);
ALTER TABLE sys_license_feature ADD UNIQUE INDEX uk_feature_code (feature_code);
ALTER TABLE sys_license_feature ADD INDEX idx_license_id (license_id);
ALTER TABLE sys_license_feature ADD INDEX idx_status (status);
ALTER TABLE sys_license_feature ADD INDEX idx_is_core (is_core);

-- 验证回滚结果
SELECT 
    'sys_license_feature回滚后状态' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '禁用(原始定义)'
        WHEN status = '1' THEN '启用(原始定义)'
        ELSE '未知状态'
    END as restored_meaning,
    COUNT(*) as count
FROM sys_license_feature 
GROUP BY status
ORDER BY status;

-- 验证数据完整性
SET @restored_feature_count = (SELECT COUNT(*) FROM sys_license_feature);

IF @restored_feature_count != @feature_backup_count THEN
    ROLLBACK;
    INSERT INTO field_standardization_rollback_log 
    (original_migration_id, rollback_phase, rollback_step, rollback_status, error_message) 
    VALUES (@rollback_migration_id, 'ROLLBACK_FEATURE', 'sys_license_feature表回滚', 'failed', 
            CONCAT('数据完整性验证失败，备份:', @feature_backup_count, ', 恢复:', @restored_feature_count));
    
    SELECT '❌ sys_license_feature表回滚失败：数据完整性验证失败' as error_message;
    -- 应该终止执行
END IF;

-- 提交sys_license_feature回滚
COMMIT;

-- 更新sys_license_feature回滚状态
UPDATE field_standardization_rollback_log 
SET rollback_status = 'completed', end_time = NOW(), 
    affected_records = @restored_feature_count
WHERE original_migration_id = @rollback_migration_id 
  AND rollback_phase = 'ROLLBACK_FEATURE' 
  AND rollback_step = 'sys_license_feature表回滚';

SELECT 
    '✓ sys_license_feature表回滚完成' as message,
    @restored_feature_count as restored_records,
    NOW() as completion_time;

-- =====================================================
-- 阶段4：回滚数据字典配置
-- =====================================================

INSERT INTO field_standardization_rollback_log 
(original_migration_id, rollback_phase, rollback_step, rollback_status) 
VALUES (@rollback_migration_id, 'ROLLBACK_DICT', '数据字典回滚', 'started');

-- 开始数据字典回滚事务
START TRANSACTION;

-- 删除当前的相关字典数据
DELETE FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status');

-- 从备份恢复字典数据
INSERT INTO sys_dict_data 
SELECT * FROM warehouse_system_field_std_backup.sys_dict_data_backup;

-- 验证字典数据恢复
SELECT 
    '数据字典回滚验证' as check_type,
    dict_type,
    dict_value,
    dict_label,
    COUNT(*) as count
FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status')
GROUP BY dict_type, dict_value, dict_label
ORDER BY dict_type, dict_value;

-- 提交数据字典回滚
COMMIT;

-- 更新数据字典回滚状态
UPDATE field_standardization_rollback_log 
SET rollback_status = 'completed', end_time = NOW(), 
    affected_records = (SELECT COUNT(*) FROM sys_dict_data WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status'))
WHERE original_migration_id = @rollback_migration_id 
  AND rollback_phase = 'ROLLBACK_DICT' 
  AND rollback_step = '数据字典回滚';

SELECT 
    '✓ 数据字典回滚完成' as message,
    (SELECT COUNT(*) FROM sys_dict_data WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status')) as restored_dict_records,
    NOW() as completion_time;

-- =====================================================
-- 阶段5：清理迁移相关数据
-- =====================================================

INSERT INTO field_standardization_rollback_log 
(original_migration_id, rollback_phase, rollback_step, rollback_status) 
VALUES (@rollback_migration_id, 'CLEANUP', '迁移数据清理', 'started');

-- 标记原迁移为已回滚
UPDATE field_standardization_log 
SET status = 'rollback', 
    details = JSON_SET(COALESCE(details, '{}'), '$.rollback_time', NOW(), '$.rollback_reason', '用户执行回滚操作')
WHERE migration_id = @rollback_migration_id;

-- 可选：清理迁移过程中的临时表（如果存在）
DROP TABLE IF EXISTS sys_license_backup_before_fix;
DROP TABLE IF EXISTS sys_license_feature_backup;

-- 更新清理状态
UPDATE field_standardization_rollback_log 
SET rollback_status = 'completed', end_time = NOW()
WHERE original_migration_id = @rollback_migration_id 
  AND rollback_phase = 'CLEANUP' 
  AND rollback_step = '迁移数据清理';

SELECT 
    '✓ 迁移数据清理完成' as message,
    NOW() as completion_time;

-- =====================================================
-- 阶段6：回滚验证
-- =====================================================

INSERT INTO field_standardization_rollback_log 
(original_migration_id, rollback_phase, rollback_step, rollback_status) 
VALUES (@rollback_migration_id, 'VALIDATION', '回滚结果验证', 'started');

-- 验证表结构是否恢复到原始状态
SELECT 
    '表结构验证' as check_type,
    table_name,
    column_name,
    column_default,
    column_comment,
    CASE 
        WHEN table_name = 'sys_license' AND column_name = 'status' AND column_comment NOT LIKE '%0%正常%1%停用%' THEN '✓ 已恢复原始定义'
        WHEN table_name = 'sys_license_feature' AND column_name = 'status' AND column_comment NOT LIKE '%0%正常%1%停用%' THEN '✓ 已恢复原始定义'
        ELSE '⚠️ 需要检查'
    END as validation_result
FROM information_schema.columns 
WHERE table_schema = 'warehouse_system' 
  AND table_name IN ('sys_license', 'sys_license_feature') 
  AND column_name = 'status';

-- 验证数据完整性
SELECT 
    '数据完整性验证' as check_type,
    'sys_license' as table_name,
    (SELECT COUNT(*) FROM sys_license) as current_count,
    (SELECT backup_count FROM warehouse_system_field_std_backup.backup_metadata 
     WHERE migration_id = @rollback_migration_id AND table_name = 'sys_license') as original_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM sys_license) = 
             (SELECT backup_count FROM warehouse_system_field_std_backup.backup_metadata 
              WHERE migration_id = @rollback_migration_id AND table_name = 'sys_license') 
        THEN '✓ 数据完整'
        ELSE '✗ 数据不一致'
    END as integrity_status
UNION ALL
SELECT 
    '数据完整性验证',
    'sys_license_feature',
    (SELECT COUNT(*) FROM sys_license_feature),
    (SELECT backup_count FROM warehouse_system_field_std_backup.backup_metadata 
     WHERE migration_id = @rollback_migration_id AND table_name = 'sys_license_feature'),
    CASE 
        WHEN (SELECT COUNT(*) FROM sys_license_feature) = 
             (SELECT backup_count FROM warehouse_system_field_std_backup.backup_metadata 
              WHERE migration_id = @rollback_migration_id AND table_name = 'sys_license_feature') 
        THEN '✓ 数据完整'
        ELSE '✗ 数据不一致'
    END;

-- 验证业务逻辑是否恢复正常
SELECT 
    '业务逻辑验证' as check_type,
    'sys_license当前许可证状态' as check_item,
    COUNT(*) as total_current_licenses,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as enabled_current_licenses,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as disabled_current_licenses,
    CASE 
        WHEN SUM(CASE WHEN current = 1 AND status = '0' THEN 1 ELSE 0 END) > 0 THEN '⚠️ 存在被禁用的当前许可证(原始逻辑)'
        WHEN SUM(CASE WHEN current = 1 AND status = '1' THEN 1 ELSE 0 END) > 0 THEN '✓ 当前许可证状态正常(原始逻辑)'
        ELSE '- 无当前许可证'
    END as logic_validation
FROM sys_license 
WHERE current = 1;

-- 更新回滚验证状态
UPDATE field_standardization_rollback_log 
SET rollback_status = 'completed', end_time = NOW()
WHERE original_migration_id = @rollback_migration_id 
  AND rollback_phase = 'VALIDATION' 
  AND rollback_step = '回滚结果验证';

-- =====================================================
-- 生成回滚报告
-- =====================================================

SELECT 
    '=== 字段定义标准化回滚完成报告 ===' as report_title,
    @rollback_migration_id as original_migration_id,
    NOW() as rollback_completion_time;

-- 回滚步骤统计
SELECT 
    '回滚步骤统计' as section,
    rollback_phase,
    rollback_step,
    rollback_status,
    affected_records,
    TIMESTAMPDIFF(SECOND, start_time, COALESCE(end_time, NOW())) as duration_seconds,
    CASE 
        WHEN rollback_status = 'completed' THEN '✓'
        WHEN rollback_status = 'failed' THEN '✗'
        WHEN rollback_status = 'skipped' THEN '-'
        ELSE '?'
    END as status_icon
FROM field_standardization_rollback_log 
WHERE original_migration_id = @rollback_migration_id
ORDER BY rollback_id;

-- 检查是否有失败的回滚步骤
SET @failed_rollback_steps = (
    SELECT COUNT(*) 
    FROM field_standardization_rollback_log 
    WHERE original_migration_id = @rollback_migration_id AND rollback_status = 'failed'
);

-- 生成回滚结论
SELECT 
    '回滚结论' as conclusion_type,
    CASE 
        WHEN @failed_rollback_steps = 0 THEN '🎉 回滚成功完成！系统已恢复到迁移前状态'
        ELSE '❌ 回滚部分失败，请检查失败步骤并手动处理'
    END as conclusion,
    @rollback_migration_id as migration_id,
    @failed_rollback_steps as failed_steps,
    (SELECT COUNT(DISTINCT rollback_phase) FROM field_standardization_rollback_log WHERE original_migration_id = @rollback_migration_id) as completed_phases,
    (SELECT SUM(affected_records) FROM field_standardization_rollback_log WHERE original_migration_id = @rollback_migration_id AND affected_records > 0) as total_restored_records;

-- 显示回滚后的使用说明
SELECT 
    '=== 回滚后使用说明 ===' as section_title;

SELECT 
    '回滚后的用法' as usage_type,
    '查询启用的许可证' as operation,
    'SELECT * FROM sys_license WHERE status = ''1''' as sql_example
UNION ALL
SELECT 
    '回滚后的用法',
    '查询禁用的许可证',
    'SELECT * FROM sys_license WHERE status = ''0'''
UNION ALL
SELECT 
    '回滚后的用法',
    '启用许可证',
    'UPDATE sys_license SET status = ''1'' WHERE license_id = ?'
UNION ALL
SELECT 
    '回滚后的用法',
    '禁用许可证',
    'UPDATE sys_license SET status = ''0'' WHERE license_id = ?'
UNION ALL
SELECT 
    '回滚后的用法',
    '查询可用功能',
    'SELECT * FROM sys_license_feature WHERE status = ''1''';

SELECT 
    '重要提醒' as reminder_type,
    '1. 系统已恢复到字段标准化之前的状态' as reminder_content
UNION ALL
SELECT 
    '重要提醒',
    '2. 请确保相关的Java代码和前端代码也恢复到对应版本'
UNION ALL
SELECT 
    '重要提醒',
    '3. 如需重新执行字段标准化，请先解决导致回滚的问题'
UNION ALL
SELECT 
    '重要提醒',
    '4. 回滚前的修复后状态已备份到warehouse_system_pre_rollback_backup数据库';

-- 记录回滚完成
INSERT INTO field_standardization_rollback_log 
(original_migration_id, rollback_phase, rollback_step, rollback_status, rollback_details) 
VALUES (@rollback_migration_id, 'COMPLETED', '回滚完成', 'completed', 
        JSON_OBJECT(
            'rollback_completion_time', NOW(),
            'total_duration_minutes', TIMESTAMPDIFF(MINUTE, 
                                                   (SELECT MIN(start_time) FROM field_standardization_rollback_log WHERE original_migration_id = @rollback_migration_id), 
                                                   NOW()),
            'failed_steps', @failed_rollback_steps,
            'rollback_success', CASE WHEN @failed_rollback_steps = 0 THEN true ELSE false END
        ));

SELECT 
    '回滚操作完成' as status,
    @rollback_migration_id as migration_id,
    CASE WHEN @failed_rollback_steps = 0 THEN '成功' ELSE '部分失败' END as result,
    NOW() as completion_time;