package com.wanyu.common.annotation;

import java.lang.annotation.*;

/**
 * 字段标准验证注解
 * 用于标记需要进行字段标准验证的方法
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ValidateFieldStandard {
    
    /**
     * 是否启用验证
     */
    boolean enabled() default true;
    
    /**
     * 验证失败时是否抛出异常
     */
    boolean throwException() default false;
    
    /**
     * 验证失败时的错误消息
     */
    String message() default "字段标准验证失败";
    
    /**
     * 需要验证的字段名称列表，为空时验证所有状态相关字段
     */
    String[] fields() default {};
}