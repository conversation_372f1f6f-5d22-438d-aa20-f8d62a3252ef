package com.wanyu.system.service.impl;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.system.domain.SysOperLog;
import com.wanyu.system.mapper.SysOperLogMapper;
import com.wanyu.system.service.ISysOperLogService;

/**
 * 操作日志 服务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysOperLogServiceImpl implements ISysOperLogService
{
    @Autowired
    private SysOperLogMapper operLogMapper;

    @Autowired
    private com.wanyu.system.service.ISysUserService userService;

    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     */
    @Override
    public void insertOperlog(SysOperLog operLog)
    {
        operLogMapper.insertOperlog(operLog);
    }

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public List<SysOperLog> selectOperLogList(SysOperLog operLog)
    {
        List<SysOperLog> operLogList = operLogMapper.selectOperLogList(operLog);
        
        // 转换用户名为真实姓名
        if (operLogList != null && !operLogList.isEmpty()) {
            for (SysOperLog log : operLogList) {
                convertUserNames(log);
            }
        }
        
        return operLogList;
    }

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(Long[] operIds)
    {
        return operLogMapper.deleteOperLogByIds(operIds);
    }

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public SysOperLog selectOperLogById(Long operId)
    {
        return operLogMapper.selectOperLogById(operId);
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperLog()
    {
        operLogMapper.cleanOperLog();
    }

    /**
     * 转换用户名为真实姓名
     *
     * @param operLog 操作日志
     */
    private void convertUserNames(SysOperLog operLog) {
        if (operLog == null) {
            return;
        }

        // 转换操作人员
        if (operLog.getOperName() != null && !operLog.getOperName().isEmpty()) {
            operLog.setOperName(getRealName(operLog.getOperName()));
        }
    }

    /**
     * 根据用户名获取真实姓名
     */
    private String getRealName(String username) {
        if (username == null || username.isEmpty()) {
            return username;
        }
        try {
            com.wanyu.common.core.domain.entity.SysUser user = userService.selectUserByUserName(username);
            if (user != null) {
                // 优先使用真实姓名，其次使用昵称
                if (user.getRealName() != null && !user.getRealName().isEmpty()) {
                    return user.getRealName();
                } else if (user.getNickName() != null && !user.getNickName().isEmpty()) {
                    return user.getNickName();
                }
            }
        } catch (Exception e) {
            // 查询失败时返回原用户名
            System.err.println("查询用户失败: " + username + ", 错误: " + e.getMessage());
        }
        return username;
    }

    /**
     * 获取操作日志统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getOperLogStatistics()
    {
        return operLogMapper.getOperLogStatistics();
    }

    /**
     * 获取操作日志趋势数据
     * 
     * @param days 天数
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getOperLogTrend(int days)
    {
        return operLogMapper.getOperLogTrend(days);
    }
}
