package com.wanyu.system.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.wanyu.common.core.domain.entity.SysRole;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.system.domain.SysRoleWarehouse;
import com.wanyu.system.mapper.SysRoleWarehouseMapper;
import com.wanyu.system.service.ISysRoleWarehouseService;

/**
 * 角色仓库权限Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysRoleWarehouseServiceImpl implements ISysRoleWarehouseService {
    
    @Autowired
    private SysRoleWarehouseMapper roleWarehouseMapper;
    
    
    /**
     * 查询角色关联的仓库列表
     *
     * @param roleId 角色ID
     * @return 仓库ID列表
     */
    public List<Long> selectRoleWarehouseListByRoleId(Long roleId) {
        return roleWarehouseMapper.selectWarehouseIdsByRoleId(roleId);
    }

    /**
     * 查询角色关联的仓库列表（包含子仓库）
     *
     * @param roleId 角色ID
     * @return 仓库ID列表（包含子仓库）
     */
    @Override
    public List<Long> selectRoleWarehouseAndChildrenByRoleId(Long roleId) {
        List<Long> warehouseIds = selectWarehouseIdsByRoleId(roleId);
        if (StringUtils.isEmpty(warehouseIds)) {
            return new ArrayList<>();
        }
        List<Long> childWarehouseIds = selectChildWarehouseIds(warehouseIds);
        if (StringUtils.isNotEmpty(childWarehouseIds)) {
            warehouseIds.addAll(childWarehouseIds);
        }
        return removeDuplicateElements(warehouseIds);
    }

    /**
     * 查询子仓库ID列表
     *
     * @param warehouseIds 仓库ID列表
     * @return 子仓库ID列表
     */
    private List<Long> selectChildWarehouseIds(List<Long> warehouseIds) {
        if (StringUtils.isEmpty(warehouseIds)) {
            return new ArrayList<>();
        }
        return roleWarehouseMapper.selectChildWarehouseIds(warehouseIds);
    }

    /**
     * 批量新增角色仓库关联
     *
     * @param roleId 角色ID
     * @param warehouseIds 仓库ID数组
     * @return 结果
     */
    @Override
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int batchInsertRoleWarehouse(Long roleId, List<Long> warehouseIds) {
        // 先删除原有关联
        roleWarehouseMapper.deleteRoleWarehouseByRoleId(roleId);
        
        // 如果没有新的关联，直接返回
        if (warehouseIds == null || warehouseIds.isEmpty()) {
            return 0;
        }
        
        // 构建批量插入的数据
        List<SysRoleWarehouse> list = warehouseIds.stream()
                .map(warehouseId -> {
                    SysRoleWarehouse rw = new SysRoleWarehouse();
                    rw.setRoleId(roleId);
                    rw.setWarehouseId(warehouseId);
                    rw.setPermissionType("FULL"); // 设置默认权限类型
                    return rw;
                })
                .collect(Collectors.toList());
        
        // 批量插入
        return roleWarehouseMapper.batchInsertRoleWarehouse(list);
    }

    /**
     * 更新角色仓库关联
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int updateRoleWarehouses(Long roleId, List<Long> warehouseIds) {
        // 先删除原有关联
        roleWarehouseMapper.deleteRoleWarehouseByRoleId(roleId);
        
        // 如果没有新的关联，直接返回
        if (warehouseIds == null || warehouseIds.isEmpty()) {
            return 0;
        }
        
        // 构建批量插入的数据
        List<SysRoleWarehouse> list = warehouseIds.stream()
                .map(warehouseId -> {
                    SysRoleWarehouse rw = new SysRoleWarehouse();
                    rw.setRoleId(roleId);
                    rw.setWarehouseId(warehouseId);
                    rw.setPermissionType("FULL"); // 设置默认权限类型
                    return rw;
                })
                .collect(Collectors.toList());
        
        // 批量插入
        return roleWarehouseMapper.batchInsertRoleWarehouse(list);
    }

    /**
     * 通过角色ID删除角色仓库关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int deleteRoleWarehouseByRoleId(Long roleId) {
        return roleWarehouseMapper.deleteRoleWarehouseByRoleId(roleId);
    }

    /**
     * 批量删除角色仓库关联
     *
     * @param roleIds 需要删除的角色ID数组
     * @return 结果
     */
    @Override
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int deleteRoleWarehouse(Long[] roleIds) {
        return roleWarehouseMapper.deleteRoleWarehouse(roleIds);
    }

    /**
     * 检查角色是否有指定仓库的权限
     *
     * @param roleId 角色ID
     * @param warehouseId 仓库ID
     * @return 是否有权限
     */
    @Override
    public boolean checkRoleWarehousePermission(Long roleId, Long warehouseId) {
        // 获取角色的所有仓库权限（包含子仓库）
        List<Long> warehouseIds = selectRoleWarehouseAndChildrenByRoleId(roleId);
        return warehouseIds.contains(warehouseId);
    }

    /**
     * 批量授权仓库给角色
     *
     * @param roleId 角色ID
     * @param warehouseIds 仓库ID数组
     * @return 结果
     */
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int batchAuthWarehouse(Long roleId, Long[] warehouseIds) {
        // 获取角色已有的仓库权限
        List<Long> existingWarehouseIds = selectRoleWarehouseListByRoleId(roleId);
        
        // 过滤出需要新增的仓库ID
        List<Long> newWarehouseIds = Arrays.stream(warehouseIds)
                .filter(id -> !existingWarehouseIds.contains(id))
                .collect(Collectors.toList());
        
        if (newWarehouseIds.isEmpty()) {
            return 0;
        }
        
        // 构建批量插入的数据
        List<SysRoleWarehouse> list = newWarehouseIds.stream()
                .map(warehouseId -> {
                    SysRoleWarehouse rw = new SysRoleWarehouse();
                    rw.setRoleId(roleId);
                    rw.setWarehouseId(warehouseId);
                    rw.setPermissionType("FULL"); // 设置默认权限类型
                    return rw;
                })
                .collect(Collectors.toList());
        
        // 批量插入
        return roleWarehouseMapper.batchInsertRoleWarehouse(list);
    }

    /**
     * 取消角色仓库授权
     *
     * @param roleId 角色ID
     * @param warehouseId 仓库ID
     * @return 结果
     */
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int deleteRoleWarehouseById(Long roleId, Long warehouseId) {
        return roleWarehouseMapper.deleteRoleWarehouseById(roleId, warehouseId);
    }

    /**
     * 批量取消角色仓库授权
     *
     * @param roleId 角色ID
     * @param warehouseIds 仓库ID数组
     * @return 结果
     */
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int deleteRoleWarehouseByIds(Long roleId, Long[] warehouseIds) {
        return roleWarehouseMapper.deleteRoleWarehouseByIds(roleId, warehouseIds);
    }

    /**
     * 移除重复元素
     *
     * @param list 列表
     * @return 去重后的列表
     */
    private List<Long> removeDuplicateElements(List<Long> list) {
        if (StringUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<SysRoleWarehouse> selectRoleWarehouseList(SysRoleWarehouse roleWarehouse) {
        return roleWarehouseMapper.selectRoleWarehouseList(roleWarehouse);
    }

    @Override
    public List<Long> selectWarehouseIdsByRoleId(Long roleId) {
        return roleWarehouseMapper.selectWarehouseIdsByRoleId(roleId);
    }

    @Override
    public List<Long> selectWarehousesByRoleId(Long roleId) {
        return roleWarehouseMapper.selectWarehouseIdsByRoleId(roleId);
    }

    @Override
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int insertRoleWarehouse(SysRoleWarehouse roleWarehouse) {
        Long roleId = roleWarehouse.getRoleId();
        Long[] warehouseIds = roleWarehouse.getWarehouseIds();

        // 先删除原有的角色仓库关联
        roleWarehouseMapper.deleteRoleWarehouseByRoleId(roleId);

        // 如果没有仓库ID数组，但有单个仓库ID，则处理单个
        if ((warehouseIds == null || warehouseIds.length == 0) && roleWarehouse.getWarehouseId() != null) {
            warehouseIds = new Long[]{roleWarehouse.getWarehouseId()};
        }

        // 如果没有新的关联，直接返回
        if (warehouseIds == null || warehouseIds.length == 0) {
            return 0;
        }

        // 构建批量插入的数据
        List<SysRoleWarehouse> list = Arrays.stream(warehouseIds)
                .map(warehouseId -> {
                    SysRoleWarehouse rw = new SysRoleWarehouse();
                    rw.setRoleId(roleId);
                    rw.setWarehouseId(warehouseId);
                    rw.setPermissionType("FULL"); // 设置默认权限类型
                    return rw;
                })
                .collect(Collectors.toList());

        // 批量插入
        return roleWarehouseMapper.batchInsertRoleWarehouse(list);
    }

    @Override
    @Transactional
    @CacheEvict(value = "roleWarehouse", allEntries = true)
    public int deleteRoleWarehouseByRoleIdAndWarehouseIds(Long roleId, Long[] warehouseIds) {
        if (roleId == null || warehouseIds == null || warehouseIds.length == 0) {
            return 0;
        }
        return roleWarehouseMapper.deleteRoleWarehouseByRoleIdAndWarehouseIds(roleId, warehouseIds);
    }

    @Override
    public int deleteRoleWarehouse(Long roleId, Long warehouseId) {
        SysRoleWarehouse roleWarehouse = new SysRoleWarehouse();
        roleWarehouse.setRoleId(roleId);
        roleWarehouse.setWarehouseId(warehouseId);
        return roleWarehouseMapper.deleteRoleWarehouseInfo(roleWarehouse);
    }

    @Override
    public int deleteRoleWarehouses(Long roleId, List<Long> warehouseIds) {
        return roleWarehouseMapper.deleteRoleWarehouseByRoleIdAndWarehouseIds(roleId, warehouseIds.toArray(new Long[0]));
    }

    @Override
    public int deleteRoleWarehouseByRoleIds(Long[] roleIds) {
        return roleWarehouseMapper.deleteRoleWarehouse(roleIds);
    }
}