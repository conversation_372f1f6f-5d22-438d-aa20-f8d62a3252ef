@echo off
chcp 65001 >nul
echo ========================================
echo 完整清理库位、货架、岗位相关组件
echo ========================================

echo.
echo 1. 删除前端相关页面和组件...
if exist "warehouse-system\frontend\src\views\warehouse\area" (
    rmdir /s /q "warehouse-system\frontend\src\views\warehouse\area"
    echo    已删除: warehouse-system\frontend\src\views\warehouse\area
)

if exist "warehouse-system\frontend\src\views\warehouse\location" (
    rmdir /s /q "warehouse-system\frontend\src\views\warehouse\location"
    echo    已删除: warehouse-system\frontend\src\views\warehouse\location
)

if exist "warehouse-system\frontend\src\views\warehouse\rack" (
    rmdir /s /q "warehouse-system\frontend\src\views\warehouse\rack"
    echo    已删除: warehouse-system\frontend\src\views\warehouse\rack
)

if exist "warehouse-system\frontend\src\views\system\post" (
    rmdir /s /q "warehouse-system\frontend\src\views\system\post"
    echo    已删除: warehouse-system\frontend\src\views\system\post
)

echo.
echo 2. 删除前端API文件...
if exist "warehouse-system\frontend\src\api\warehouse\area.js" (
    del "warehouse-system\frontend\src\api\warehouse\area.js"
    echo    已删除: area.js API文件
)

if exist "warehouse-system\frontend\src\api\warehouse\location.js" (
    del "warehouse-system\frontend\src\api\warehouse\location.js"
    echo    已删除: location.js API文件
)

if exist "warehouse-system\frontend\src\api\warehouse\rack.js" (
    del "warehouse-system\frontend\src\api\warehouse\rack.js"
    echo    已删除: rack.js API文件
)

if exist "warehouse-system\frontend\src\api\system\post.js" (
    del "warehouse-system\frontend\src\api\system\post.js"
    echo    已删除: post.js API文件
)

echo.
echo 3. 删除后端warehouse包中的Area和Location相关文件...
if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\domain\WarehouseArea.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\domain\WarehouseArea.java"
    echo    已删除: WarehouseArea.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\domain\WarehouseLocation.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\domain\WarehouseLocation.java"
    echo    已删除: WarehouseLocation.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\mapper\WarehouseAreaMapper.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\mapper\WarehouseAreaMapper.java"
    echo    已删除: WarehouseAreaMapper.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\mapper\WarehouseLocationMapper.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\mapper\WarehouseLocationMapper.java"
    echo    已删除: WarehouseLocationMapper.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\service\IWarehouseAreaService.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\service\IWarehouseAreaService.java"
    echo    已删除: IWarehouseAreaService.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\service\IWarehouseLocationService.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\service\IWarehouseLocationService.java"
    echo    已删除: IWarehouseLocationService.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\service\impl\WarehouseAreaServiceImpl.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\service\impl\WarehouseAreaServiceImpl.java"
    echo    已删除: WarehouseAreaServiceImpl.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\service\impl\WarehouseLocationServiceImpl.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\warehouse\service\impl\WarehouseLocationServiceImpl.java"
    echo    已删除: WarehouseLocationServiceImpl.java
)

echo.
echo 4. 删除后端system包中的岗位相关文件...
if exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\system\SysPostController.java" (
    del "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\system\SysPostController.java"
    echo    已删除: SysPostController.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\ISysPostService.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\ISysPostService.java"
    echo    已删除: ISysPostService.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\impl\SysPostServiceImpl.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\impl\SysPostServiceImpl.java"
    echo    已删除: SysPostServiceImpl.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\mapper\SysPostMapper.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\mapper\SysPostMapper.java"
    echo    已删除: SysPostMapper.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\domain\SysPost.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\domain\SysPost.java"
    echo    已删除: SysPost.java
)

echo.
echo 5. 删除Mapper XML文件...
if exist "warehouse-system\backend\wanyu-system\src\main\resources\mapper\warehouse\WarehouseAreaMapper.xml" (
    del "warehouse-system\backend\wanyu-system\src\main\resources\mapper\warehouse\WarehouseAreaMapper.xml"
    echo    已删除: WarehouseAreaMapper.xml
)

if exist "warehouse-system\backend\wanyu-system\src\main\resources\mapper\warehouse\WarehouseLocationMapper.xml" (
    del "warehouse-system\backend\wanyu-system\src\main\resources\mapper\warehouse\WarehouseLocationMapper.xml"
    echo    已删除: WarehouseLocationMapper.xml
)

if exist "warehouse-system\backend\wanyu-system\src\main\resources\mapper\system\SysPostMapper.xml" (
    del "warehouse-system\backend\wanyu-system\src\main\resources\mapper\system\SysPostMapper.xml"
    echo    已删除: SysPostMapper.xml
)

echo.
echo 6. 执行数据库清理...
mysql -h localhost -P 3306 -u root -p123456 warehouse_system < complete_cleanup_all_components.sql
if %errorlevel% equ 0 (
    echo    数据库清理完成
) else (
    echo    数据库清理失败，请检查连接
)

echo.
echo 7. 清理编译缓存...
if exist "warehouse-system\backend\target" (
    rmdir /s /q "warehouse-system\backend\target"
    echo    已清理后端编译缓存
)

if exist "warehouse-system\frontend\node_modules\.cache" (
    rmdir /s /q "warehouse-system\frontend\node_modules\.cache"
    echo    已清理前端缓存
)

if exist "warehouse-system\frontend\dist" (
    rmdir /s /q "warehouse-system\frontend\dist"
    echo    已清理前端构建文件
)

echo.
echo ========================================
echo 所有组件清理完成！
echo ========================================
echo.
echo 已删除的内容：
echo - 数据库表: 库区、货架、库位、岗位相关表
echo - 前端页面: area, location, rack, post 相关组件
echo - 后端代码: 所有相关的Controller, Service, Mapper, Domain
echo - 路由配置: 已更新相关路由文件
echo - 菜单权限: 已删除所有相关菜单项
echo.
pause