```sql
-- 修复字典数据中的乱码问题
-- 修复时间：2025-08-21

-- 修复wms_barcode_type类型的乱码
UPDATE sys_dict_data 
SET dict_label = 'QR码' 
WHERE dict_type = 'wms_barcode_type' AND dict_value = 'QR_CODE';

-- 修复inventory_in_type类型的乱码
UPDATE sys_dict_data 
SET dict_label = '申购入库' 
WHERE dict_type = 'inventory_in_type' AND dict_value = '5';

-- 查询验证修复结果
SELECT dict_code, dict_label, dict_value, dict_type 
FROM sys_dict_data 
WHERE dict_type IN ('wms_barcode_type', 'inventory_in_type');
```