@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: Maven 自动安装脚本
:: ========================================

echo.
echo ========================================
echo Maven 自动安装脚本
echo ========================================
echo.

set "MAVEN_VERSION=3.9.5"
set "MAVEN_URL=https://archive.apache.org/dist/maven/maven-3/%MAVEN_VERSION%/binaries/apache-maven-%MAVEN_VERSION%-bin.zip"
set "INSTALL_DIR=C:\apache-maven-%MAVEN_VERSION%"
set "TEMP_ZIP=%TEMP%\apache-maven-%MAVEN_VERSION%-bin.zip"

echo 即将安装 Apache Maven %MAVEN_VERSION%
echo 安装目录: %INSTALL_DIR%
echo.

:: 检查是否已安装
if exist "%INSTALL_DIR%" (
    echo Maven 已安装在: %INSTALL_DIR%
    goto :configure_env
)

:: 下载Maven
echo 正在下载 Maven...
echo 下载地址: %MAVEN_URL%
echo.

:: 使用PowerShell下载
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%MAVEN_URL%' -OutFile '%TEMP_ZIP%'}"

if not exist "%TEMP_ZIP%" (
    echo 下载失败！请手动下载Maven并安装
    echo 下载地址: https://maven.apache.org/download.cgi
    pause
    exit /b 1
)

echo 下载完成！

:: 解压Maven
echo 正在解压Maven到 %INSTALL_DIR%...
powershell -Command "Expand-Archive -Path '%TEMP_ZIP%' -DestinationPath 'C:\' -Force"

if not exist "%INSTALL_DIR%" (
    echo 解压失败！请手动解压并安装Maven
    pause
    exit /b 1
)

echo 解压完成！

:: 清理临时文件
del "%TEMP_ZIP%" >nul 2>&1

:configure_env
echo.
echo ========================================
echo 配置环境变量
echo ========================================
echo.

:: 设置MAVEN_HOME
echo 设置 MAVEN_HOME = %INSTALL_DIR%
setx MAVEN_HOME "%INSTALL_DIR%" >nul

:: 添加到PATH
echo 添加Maven到系统PATH...
for /f "tokens=2*" %%A in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "current_path=%%B"
if not defined current_path set "current_path="

:: 检查PATH中是否已包含Maven
echo !current_path! | findstr /i "maven" >nul
if !errorlevel! equ 0 (
    echo Maven已在PATH中
) else (
    echo 添加Maven到PATH...
    setx PATH "!current_path!;%INSTALL_DIR%\bin" >nul
)

echo.
echo ========================================
echo Maven 安装完成！
echo ========================================
echo.
echo 安装位置: %INSTALL_DIR%
echo.
echo 重要提示：
echo 1. 请关闭当前命令提示符窗口
echo 2. 重新打开新的命令提示符窗口
echo 3. 运行 "mvn --version" 验证安装
echo 4. 然后可以运行部署脚本
echo.

pause