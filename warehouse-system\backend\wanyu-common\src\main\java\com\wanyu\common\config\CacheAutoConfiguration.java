package com.wanyu.common.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Arrays;

/**
 * 缓存自动配置类
 * 只使用内存缓存实现
 * 
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class CacheAutoConfiguration {

    /**
     * 内存缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        // 预创建常用缓存，包括deptTemplate
        cacheManager.setCacheNames(Arrays.asList(
            "userWarehouse", "roleWarehouse", "product", "inventory", 
            "menu", "permission", "dict", "config", "deptTemplate"
        ));
        return cacheManager;
    }

    /**
     * 内存缓存管理器 - 用于明确指定使用内存缓存
     */
    @Bean("memoryCacheManager")
    public CacheManager memoryCacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        // 预创建常用缓存，包括deptTemplate
        cacheManager.setCacheNames(Arrays.asList(
            "userWarehouse", "roleWarehouse", "product", "inventory", 
            "menu", "permission", "dict", "config", "deptTemplate"
        ));
        return cacheManager;
    }
}