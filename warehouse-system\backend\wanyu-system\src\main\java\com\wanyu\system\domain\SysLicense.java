package com.wanyu.system.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 授权管理对象 sys_license
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class SysLicense extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 授权ID */
    private Long licenseId;

    /** 授权密钥 */
    @Excel(name = "授权密钥")
    private String licenseKey;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 联系信息 */
    @Excel(name = "联系信息")
    private String contactInfo;

    /** 授权类型 */
    @Excel(name = "授权类型", readConverterExp = "trial=试用版,standard=标准版,enterprise=企业版")
    private String licenseType;

    /** 最大用户数 */
    @Excel(name = "最大用户数")
    private Integer maxUsers;

    /** 最大仓库数 */
    @Excel(name = "最大仓库数")
    private Integer maxWarehouses;

    /** 授权开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "授权开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 授权结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "授权结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /** 硬件指纹 */
    @Excel(name = "硬件指纹")
    private String hardwareFingerprint;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 授权功能列表 */
    @Excel(name = "授权功能列表")
    private String features;

    /** 功能列表（解析后） */
    private List<String> featureList;

    /** 剩余天数 */
    private Integer remainingDays;

    /** 是否过期 */
    private Boolean expired;

    /** 是否当前使用的授权 */
    private Boolean current;

    public void setLicenseId(Long licenseId) 
    {
        this.licenseId = licenseId;
    }

    public Long getLicenseId() 
    {
        return licenseId;
    }
    public void setLicenseKey(String licenseKey) 
    {
        this.licenseKey = licenseKey;
    }

    public String getLicenseKey() 
    {
        return licenseKey;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setContactInfo(String contactInfo) 
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo() 
    {
        return contactInfo;
    }
    public void setLicenseType(String licenseType) 
    {
        this.licenseType = licenseType;
    }

    public String getLicenseType() 
    {
        return licenseType;
    }
    public void setMaxUsers(Integer maxUsers) 
    {
        this.maxUsers = maxUsers;
    }

    public Integer getMaxUsers() 
    {
        return maxUsers;
    }
    public void setMaxWarehouses(Integer maxWarehouses) 
    {
        this.maxWarehouses = maxWarehouses;
    }

    public Integer getMaxWarehouses() 
    {
        return maxWarehouses;
    }
    public void setStartDate(Date startDate) 
    {
        this.startDate = startDate;
    }

    public Date getStartDate() 
    {
        return startDate;
    }
    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    public Date getEndDate() 
    {
        return endDate;
    }
    public void setHardwareFingerprint(String hardwareFingerprint) 
    {
        this.hardwareFingerprint = hardwareFingerprint;
    }

    public String getHardwareFingerprint() 
    {
        return hardwareFingerprint;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setFeatures(String features) 
    {
        this.features = features;
    }

    public String getFeatures() 
    {
        return features;
    }

    public List<String> getFeatureList() {
        return featureList;
    }

    public void setFeatureList(List<String> featureList) {
        this.featureList = featureList;
    }

    public Integer getRemainingDays() {
        return remainingDays;
    }

    public void setRemainingDays(Integer remainingDays) {
        this.remainingDays = remainingDays;
    }

    public Boolean getExpired() {
        return expired;
    }

    public void setExpired(Boolean expired) {
        this.expired = expired;
    }

    public Boolean getCurrent() {
        return current;
    }

    public void setCurrent(Boolean current) {
        this.current = current;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("licenseId", getLicenseId())
            .append("licenseKey", getLicenseKey())
            .append("companyName", getCompanyName())
            .append("contactInfo", getContactInfo())
            .append("licenseType", getLicenseType())
            .append("maxUsers", getMaxUsers())
            .append("maxWarehouses", getMaxWarehouses())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("hardwareFingerprint", getHardwareFingerprint())
            .append("status", getStatus())
            .append("features", getFeatures())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
}