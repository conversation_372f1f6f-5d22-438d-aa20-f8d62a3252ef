# 字段定义标准化部署快速指南

## 快速开始

### 第一步：环境检查
运行环境诊断脚本检查是否满足部署要求：
```cmd
check_environment.bat
```

### 第二步：选择部署方式

根据环境检查结果，选择合适的部署方式：

#### 选项A：仅数据库部署（推荐，如果缺少Maven）
如果只需要修复数据库字段定义，不需要重新编译代码：
```cmd
deploy_database_only.bat
```

#### 选项B：完整部署（需要Maven）
如果需要完整的部署包括代码编译，请先安装Maven：
```cmd
install_maven.bat
```
然后运行完整部署：
```cmd
deploy_field_standardization.bat
```

### 第三步：解决环境问题（如果需要）
根据诊断结果安装缺失的工具：

#### 如果缺少MySQL客户端：
1. 下载MySQL安装包：https://dev.mysql.com/downloads/mysql/
2. 安装时选择包含客户端工具
3. 将MySQL的bin目录添加到系统PATH

#### 如果缺少Java：
1. 下载JDK：https://www.oracle.com/java/technologies/downloads/
2. 安装JDK 8或11
3. 配置环境变量：
   - JAVA_HOME = JDK安装目录
   - PATH 添加 %JAVA_HOME%\bin

#### 如果缺少Maven：
1. 下载Maven：https://maven.apache.org/download.cgi
2. 解压到目录（如：C:\apache-maven-3.8.6）
3. 配置环境变量：
   - MAVEN_HOME = Maven解压目录
   - PATH 添加 %MAVEN_HOME%\bin

### 第三步：执行部署
环境检查通过后，运行部署脚本：
```cmd
deploy_field_standardization.bat
```

### 第四步：如果需要回滚
如果部署出现问题，运行回滚脚本：
```cmd
rollback_deployment.bat
```

## 常见错误解决

### 错误：程序在环境检查阶段停止
**原因**：缺少必需的工具（MySQL、Java、Maven）
**解决**：
1. 运行 `check_environment.bat` 查看具体缺少什么
2. 按照上面的指南安装缺失的工具
3. 重新打开命令提示符
4. 再次运行部署脚本

### 错误：项目目录不存在
**原因**：脚本中配置的项目路径不正确
**解决**：
1. 确认实际的项目路径
2. 修改脚本中的 PROJECT_ROOT 变量
3. 或者将项目移动到脚本期望的路径

### 错误：数据库连接失败
**原因**：MySQL服务未启动或连接参数错误
**解决**：
1. 启动MySQL服务
2. 确认数据库名、用户名、密码正确
3. 确认数据库 warehouse_system 存在

## 联系支持

如果遇到其他问题，请：
1. 查看日志文件：deployment.log 或 rollback.log
2. 运行环境诊断脚本获取详细信息
3. 联系技术支持团队