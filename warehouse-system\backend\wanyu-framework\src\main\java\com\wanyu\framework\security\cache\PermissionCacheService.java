package com.wanyu.framework.security.cache;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CompletableFuture;
import org.springframework.stereotype.Component;
import com.wanyu.common.constant.CacheConstants;
import com.wanyu.common.utils.StringUtils;

/**
 * 权限缓存服务
 * 
 * <AUTHOR>
 */
@Component
public class PermissionCacheService
{
    /**
     * 内存缓存用于存储权限数据
     */
    private static final Map<String, Object> PERMISSION_CACHE = new ConcurrentHashMap<>();

    /**
     * 缓存有效期（默认30分钟）
     */
    private final long EXPIRATION = 30;

    /**
     * 缓存时间单位
     */
    private final TimeUnit TIMEUNIT = TimeUnit.MINUTES;

    /**
     * 权限缓存前缀
     */
    private final String CACHE_PREFIX = CacheConstants.SYS_AUTH_CACHE;

    /**
     * 获取用户权限列表缓存
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> getUserPermissions(Long userId)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "user_perms:", userId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存用户权限列表
     * 
     * @param userId 用户ID
     * @param permissions 权限列表
     */
    public void setUserPermissions(Long userId, Set<String> permissions)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "user_perms:", userId);
        PERMISSION_CACHE.put(cacheKey, permissions);

        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    /**
     * 删除用户权限列表缓存
     * 
     * @param userId 用户ID
     */
    public void removeUserPermissions(Long userId)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "user_perms:", userId);
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取角色权限列表缓存
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    public Set<String> getRolePermissions(Long roleId)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "role_perms:", roleId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存角色权限列表
     * 
     * @param roleId 角色ID
     * @param permissions 权限列表
     */
    public void setRolePermissions(Long roleId, Set<String> permissions)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "role_perms:", roleId);
        PERMISSION_CACHE.put(cacheKey, permissions);

        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    /**
     * 删除角色权限列表缓存
     * 
     * @param roleId 角色ID
     */
    public void removeRolePermissions(Long roleId)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "role_perms:", roleId);
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取用户角色列表缓存
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    public Set<String> getUserRoles(Long userId)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "user_roles:", userId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 设置用户角色列表缓存
     * 
     * @param userId 用户ID
     * @param roles 角色列表
     */
    public void setUserRoles(Long userId, Set<String> roles)
    {
        if (StringUtils.isNotEmpty(roles))
        {
            String cacheKey = getCacheKey(CACHE_PREFIX + "user_roles:", userId);
            PERMISSION_CACHE.put(cacheKey, roles);
            
            // 启动一个定时任务在指定时间后删除缓存
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                    PERMISSION_CACHE.remove(cacheKey);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
    }

    /**
     * 删除用户角色列表缓存
     * 
     * @param userId 用户ID
     */
    public void deleteUserRoles(Long userId)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "user_roles:", userId);
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取用户菜单列表缓存
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<Map<String, Object>> getUserMenus(Long userId)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "user_menus:", userId);
        return (List<Map<String, Object>>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 设置用户菜单列表缓存
     * 
     * @param userId 用户ID
     * @param menus 菜单列表
     */
    public void setUserMenus(Long userId, List<Map<String, Object>> menus)
    {
        if (StringUtils.isNotEmpty(menus))
        {
            String cacheKey = getCacheKey(CACHE_PREFIX + "user_menus:", userId);
            PERMISSION_CACHE.put(cacheKey, menus);
            
            // 启动一个定时任务在指定时间后删除缓存
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                    PERMISSION_CACHE.remove(cacheKey);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
    }

    /**
     * 删除用户菜单列表缓存
     * 
     * @param userId 用户ID
     */
    public void deleteUserMenus(Long userId)
    {
        String cacheKey = getCacheKey(CACHE_PREFIX + "user_menus:", userId);
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 清除用户所有权限缓存
     * 
     * @param userId 用户ID
     */
    public void clearUserCache(Long userId)
    {
        removeUserPermissions(userId);
        deleteUserRoles(userId);
        deleteUserMenus(userId);
    }

    /**
     * 清除所有用户权限缓存
     */
    public void clearAllPermissionCache()
    {
        PERMISSION_CACHE.clear();
    }

    /**
     * 获取缓存键值
     * 
     * @param prefix 缓存前缀
     * @param id ID
     * @return 缓存键值
     */
    private String getCacheKey(String prefix, Long id)
    {
        return prefix + id;
    }
}