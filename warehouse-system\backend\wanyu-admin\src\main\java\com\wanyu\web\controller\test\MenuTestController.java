package com.wanyu.web.controller.test;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;

/**
 * 菜单测试控制器
 * 用于验证新增菜单功能的基础框架
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@RestController
@RequestMapping("/test/menu")
public class MenuTestController extends BaseController
{
    /**
     * 测试基础功能
     */
    @GetMapping("/hello")
    public AjaxResult hello()
    {
        return success("新增菜单控制器测试成功！");
    }

    /**
     * 测试权限功能
     */
    @GetMapping("/auth")
    public AjaxResult testAuth()
    {
        return success("权限测试成功，用户：" + getUsername());
    }

    /**
     * 获取菜单状态
     */
    @GetMapping("/status")
    public AjaxResult getMenuStatus()
    {
        return success()
            .put("message", "菜单系统运行正常")
            .put("timestamp", System.currentTimeMillis())
            .put("user", getUsername());
    }
}