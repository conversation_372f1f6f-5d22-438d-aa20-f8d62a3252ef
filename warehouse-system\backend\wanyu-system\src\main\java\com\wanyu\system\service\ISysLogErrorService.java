package com.wanyu.system.service;

import java.util.List;
import com.wanyu.system.domain.SysLogError;

/**
 * 错误日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface ISysLogErrorService 
{
    /**
     * 查询错误日志
     * 
     * @param id 错误日志主键
     * @return 错误日志
     */
    public SysLogError selectLogErrorById(Long id);

    /**
     * 查询错误日志列表
     * 
     * @param logError 错误日志
     * @return 错误日志集合
     */
    public List<SysLogError> selectLogErrorList(SysLogError logError);

    /**
     * 新增错误日志
     * 
     * @param logError 错误日志
     * @return 结果
     */
    public int insertLogError(SysLogError logError);

    /**
     * 修改错误日志
     * 
     * @param logError 错误日志
     * @return 结果
     */
    public int updateLogError(SysLogError logError);

    /**
     * 批量删除错误日志
     * 
     * @param ids 需要删除的错误日志主键集合
     * @return 结果
     */
    public int deleteLogErrorByIds(Long[] ids);

    /**
     * 删除错误日志信息
     * 
     * @param id 错误日志主键
     * @return 结果
     */
    public int deleteLogErrorById(Long id);

    /**
     * 清空错误日志
     */
    public void cleanLogError();
}