package com.wanyu.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.system.domain.SysSecurityLog;
import com.wanyu.system.service.ISysSecurityLogService;

/**
 * 安全日志Controller
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@RestController
@RequestMapping("/system/security")
public class SysSecurityLogController extends BaseController
{
    @Autowired
    private ISysSecurityLogService securityLogService;

    /**
     * 查询安全日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:security:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysSecurityLog securityLog)
    {
        startPage();
        List<SysSecurityLog> list = securityLogService.selectSysSecurityLogList(securityLog);
        return getDataTable(list);
    }

    /**
     * 导出安全日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:security:export')")
    @Log(title = "安全日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSecurityLog securityLog)
    {
        List<SysSecurityLog> list = securityLogService.selectSysSecurityLogList(securityLog);
        ExcelUtil<SysSecurityLog> util = new ExcelUtil<SysSecurityLog>(SysSecurityLog.class);
        util.exportExcel(response, list, "安全日志数据");
    }

    /**
     * 获取安全日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:security:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(securityLogService.selectSysSecurityLogByLogId(logId));
    }

    /**
     * 新增安全日志
     */
    @PreAuthorize("@ss.hasPermi('system:security:add')")
    @Log(title = "安全日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysSecurityLog securityLog)
    {
        return toAjax(securityLogService.insertSysSecurityLog(securityLog));
    }

    /**
     * 修改安全日志
     */
    @PreAuthorize("@ss.hasPermi('system:security:edit')")
    @Log(title = "安全日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysSecurityLog securityLog)
    {
        return toAjax(securityLogService.updateSysSecurityLog(securityLog));
    }

    /**
     * 删除安全日志
     */
    @PreAuthorize("@ss.hasPermi('system:security:remove')")
    @Log(title = "安全日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(securityLogService.deleteSysSecurityLogByLogIds(logIds));
    }

    /**
     * 处理安全事件
     */
    @PreAuthorize("@ss.hasPermi('system:security:handle')")
    @Log(title = "安全日志处理", businessType = BusinessType.UPDATE)
    @PutMapping("/handle")
    public AjaxResult handle(@RequestBody SysSecurityLog securityLog)
    {
        return toAjax(securityLogService.handleSecurityEvent(securityLog));
    }
}
