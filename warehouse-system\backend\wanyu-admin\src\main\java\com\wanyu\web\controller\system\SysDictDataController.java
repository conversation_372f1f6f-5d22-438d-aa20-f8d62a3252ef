package com.wanyu.web.controller.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;

/**
 * 字典数据控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController
{
    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/type/{dictType}")
    public AjaxResult dictType(@PathVariable String dictType)
    {
        List<Map<String, Object>> data = new ArrayList<>();
        
        // 根据不同的字典类型返回相应的数据
        switch (dictType) {
            case "inventory_in_type":
                data.add(createDictData("1", "采购入库", "inventory_in_type"));
                data.add(createDictData("2", "生产入库", "inventory_in_type"));
                data.add(createDictData("3", "调拨入库", "inventory_in_type"));
                data.add(createDictData("4", "退货入库", "inventory_in_type"));
                break;
                
            case "inventory_in_status":
                data.add(createDictData("0", "待审核", "inventory_in_status"));
                data.add(createDictData("1", "已审核", "inventory_in_status"));
                data.add(createDictData("2", "已完成", "inventory_in_status"));
                data.add(createDictData("3", "已取消", "inventory_in_status"));
                break;
                
            case "inventory_out_type":
                data.add(createDictData("1", "销售出库", "inventory_out_type"));
                data.add(createDictData("2", "生产出库", "inventory_out_type"));
                data.add(createDictData("3", "调拨出库", "inventory_out_type"));
                data.add(createDictData("4", "报损出库", "inventory_out_type"));
                break;
                
            case "inventory_out_status":
                data.add(createDictData("0", "待审核", "inventory_out_status"));
                data.add(createDictData("1", "已审核", "inventory_out_status"));
                data.add(createDictData("2", "已完成", "inventory_out_status"));
                data.add(createDictData("3", "已取消", "inventory_out_status"));
                break;
                
            case "inventory_transfer_status":
                data.add(createDictData("0", "待审核", "inventory_transfer_status"));
                data.add(createDictData("1", "已审核", "inventory_transfer_status"));
                data.add(createDictData("2", "已完成", "inventory_transfer_status"));
                data.add(createDictData("3", "已取消", "inventory_transfer_status"));
                break;
                
            default:
                // 返回空数据
                break;
        }
        
        return AjaxResult.success(data);
    }
    
    /**
     * 创建字典数据项
     */
    private Map<String, Object> createDictData(String dictValue, String dictLabel, String dictType) {
        Map<String, Object> dict = new HashMap<>();
        dict.put("dictCode", null);
        dict.put("dictSort", 0);
        dict.put("dictLabel", dictLabel);
        dict.put("dictValue", dictValue);
        dict.put("dictType", dictType);
        dict.put("cssClass", "");
        dict.put("listClass", "default");
        dict.put("isDefault", "N");
        dict.put("status", "0");
        dict.put("createBy", "admin");
        dict.put("createTime", "2024-01-01 00:00:00");
        dict.put("updateBy", "");
        dict.put("updateTime", null);
        dict.put("remark", "");
        return dict;
    }
}