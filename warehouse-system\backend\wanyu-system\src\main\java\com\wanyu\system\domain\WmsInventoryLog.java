package com.wanyu.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 出入库日志对象 wms_inventory_log
 *
 * <AUTHOR>
 */
public class WmsInventoryLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 操作类型（IN-入库，OUT-出库，TRANSFER-调拨，CHECK-盘点） */
    @Excel(name = "操作类型", readConverterExp = "IN=入库,OUT=出库,TRANSFER=调拨,ADJUST=调整,CHECK=盘点")
    private String operationType;

    /** 仓库ID */
    @Excel(name = "仓库ID")
    private Long warehouseId;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String warehouseName;

    /** 物品ID */
    @Excel(name = "物品ID")
    private Long productId;

    /** 物品名称 */
    @Excel(name = "物品名称")
    private String productName;

    /** 物品编码 */
    @Excel(name = "物品编码")
    private String productCode;

    /** 物品规格 */
    @Excel(name = "物品规格")
    private String productSpec;

    /** 物品单位 */
    @Excel(name = "物品单位")
    private String productUnit;

    /** 操作数量 */
    @Excel(name = "操作数量")
    private BigDecimal quantity;

    /** 操作前数量 */
    @Excel(name = "操作前数量")
    private BigDecimal beforeQuantity;

    /** 操作后数量 */
    @Excel(name = "操作后数量")
    private BigDecimal afterQuantity;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalAmount;

    /** 关联单据号 */
    @Excel(name = "关联单据号")
    private String relatedOrderId;

    /** 关联单据类型 */
    @Excel(name = "关联单据类型", readConverterExp = "PURCHASE=采购单,SALES=销售单,TRANSFER=调拨单,CHECK=盘点单,RETURN=退货单,OTHER=其他")
    private String relatedOrderType;

    /** 操作人员 */
    @Excel(name = "操作人员")
    private String operator;

    /** 操作人员ID */
    private Long operatorId;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    /** 库位编码 */
    @Excel(name = "库位编码")
    private String locationCode;

    /** 库位名称 */
    @Excel(name = "库位名称")
    private String locationName;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 过期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireDate;

    /** 供应商ID */
    private Long supplierId;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String supplierName;

    /** 客户ID */
    private Long customerId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 操作原因 */
    @Excel(name = "操作原因")
    private String reason;

    /** 状态（0正常 1异常） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    private String status;

    /** 规格型号 (兼容旧字段) */
    @Excel(name = "规格型号")
    private String specification;

    /** 单位 (兼容旧字段) */
    @Excel(name = "单位")
    private String unit;

    public void setLogId(Long logId)
    {
        this.logId = logId;
    }

    public Long getLogId()
    {
        return logId;
    }

    public void setWarehouseId(Long warehouseId)
    {
        this.warehouseId = warehouseId;
    }

    public Long getWarehouseId()
    {
        return warehouseId;
    }

    public String getWarehouseName()
    {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName)
    {
        this.warehouseName = warehouseName;
    }

    public void setProductId(Long productId)
    {
        this.productId = productId;
    }

    public Long getProductId()
    {
        return productId;
    }

    public String getProductName()
    {
        return productName;
    }

    public void setProductName(String productName)
    {
        this.productName = productName;
    }

    public String getProductCode()
    {
        return productCode;
    }

    public void setProductCode(String productCode)
    {
        this.productCode = productCode;
    }

    public void setOperationType(String operationType)
    {
        this.operationType = operationType;
    }

    public String getOperationType()
    {
        return operationType;
    }

    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity()
    {
        return quantity;
    }

    public void setBeforeQuantity(BigDecimal beforeQuantity)
    {
        this.beforeQuantity = beforeQuantity;
    }

    public BigDecimal getBeforeQuantity()
    {
        return beforeQuantity;
    }

    public void setAfterQuantity(BigDecimal afterQuantity)
    {
        this.afterQuantity = afterQuantity;
    }

    public BigDecimal getAfterQuantity()
    {
        return afterQuantity;
    }

    /**
     * 获取数量变化（计算字段）
     * 根据操作类型和实际数量变化计算显示值
     */
    @com.fasterxml.jackson.annotation.JsonProperty("quantityChange")
    public BigDecimal getQuantityChange()
    {
        if (afterQuantity == null || beforeQuantity == null) {
            // 如果没有前后数量，则根据操作类型和数量计算
            if (quantity == null) {
                return BigDecimal.ZERO;
            }
            
            if ("OUT".equals(operationType)) {
                return quantity.negate(); // 出库为负数
            } else {
                return quantity; // 入库、调拨入库、盘点增加等为正数
            }
        }
        
        // 使用实际的前后数量差值
        return afterQuantity.subtract(beforeQuantity);
    }

    public String getProductSpec() {
        return productSpec;
    }

    public void setProductSpec(String productSpec) {
        this.productSpec = productSpec;
    }

    public String getProductUnit() {
        return productUnit;
    }

    public void setProductUnit(String productUnit) {
        this.productUnit = productUnit;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public void setRelatedOrderId(String relatedOrderId)
    {
        this.relatedOrderId = relatedOrderId;
    }

    public String getRelatedOrderId()
    {
        return relatedOrderId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setRelatedOrderType(String relatedOrderType)
    {
        this.relatedOrderType = relatedOrderType;
    }

    public String getRelatedOrderType()
    {
        return relatedOrderType;
    }

    public void setOperator(String operator)
    {
        this.operator = operator;
    }

    public String getOperator()
    {
        return operator;
    }

    public void setOperationTime(Date operationTime)
    {
        this.operationTime = operationTime;
    }

    public Date getOperationTime()
    {
        return operationTime;
    }

    public String getSpecification()
    {
        return specification;
    }

    public void setSpecification(String specification)
    {
        this.specification = specification;
    }

    public String getUnit()
    {
        return unit;
    }

    public void setUnit(String unit)
    {
        this.unit = unit;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("warehouseId", getWarehouseId())
            .append("warehouseName", getWarehouseName())
            .append("productId", getProductId())
            .append("productName", getProductName())
            .append("productCode", getProductCode())
            .append("operationType", getOperationType())
            .append("quantity", getQuantity())
            .append("beforeQuantity", getBeforeQuantity())
            .append("afterQuantity", getAfterQuantity())
            .append("relatedOrderId", getRelatedOrderId())
            .append("relatedOrderType", getRelatedOrderType())
            .append("operator", getOperator())
            .append("operationTime", getOperationTime())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}