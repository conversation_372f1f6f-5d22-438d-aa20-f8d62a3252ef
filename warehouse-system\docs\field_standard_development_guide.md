# 字段标准化开发指南

## 概述

本指南为开发人员提供字段标准化的实践指导，包括具体的代码示例、最佳实践和常见问题解决方案。

## 1. 新建表时的字段标准

### 1.1 标准表结构模板

```sql
-- 业务表创建模板
CREATE TABLE wms_example (
    -- 主键
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    
    -- 业务字段
    name VARCHAR(100) NOT NULL DEFAULT '' COMMENT '名称',
    code VARCHAR(50) NOT NULL DEFAULT '' COMMENT '编码',
    description TEXT COMMENT '描述',
    
    -- 状态字段（必须）
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    
    -- 布尔字段（按需添加）
    is_default CHAR(1) DEFAULT '0' COMMENT '是否默认（0否 1是）',
    is_enabled CHAR(1) DEFAULT '0' COMMENT '是否启用（0否 1是）',
    
    -- 业务状态字段（按需添加）
    audit_status CHAR(1) DEFAULT '0' COMMENT '审核状态（0待审核 1已审核 2已拒绝）',
    
    -- 删除标记（必须）
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    
    -- 审计字段（必须）
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_code (code),
    KEY idx_status (status),
    KEY idx_del_flag (del_flag),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例业务表';
```

### 1.2 字段选择指南

#### 必须包含的字段
- `id`: 主键
- `status`: 通用状态
- `del_flag`: 删除标记
- `create_by`, `create_time`: 创建信息
- `update_by`, `update_time`: 更新信息

#### 可选字段
- `is_xxx`: 布尔类型字段
- `xxx_status`: 特定业务状态
- `enable_xxx`: 开关类型字段

## 2. Java实体类开发

### 2.1 实体类模板

```java
package com.wanyu.system.domain;

import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 示例业务对象 wms_example
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class WmsExample extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 名称 */
    @Excel(name = "名称")
    @NotBlank(message = "名称不能为空")
    @Size(max = 100, message = "名称长度不能超过100个字符")
    private String name;

    /** 编码 */
    @Excel(name = "编码")
    @NotBlank(message = "编码不能为空")
    @Size(max = 50, message = "编码长度不能超过50个字符")
    private String code;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    private String status = "0";

    /** 是否默认（0否 1是） */
    @Excel(name = "是否默认", readConverterExp = "0=否,1=是")
    @Pattern(regexp = "^[01]$", message = "是否默认值必须为0或1")
    private String isDefault = "0";

    /** 审核状态（0待审核 1已审核 2已拒绝） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=已审核,2=已拒绝")
    @Pattern(regexp = "^[012]$", message = "审核状态值必须为0、1或2")
    private String auditStatus = "0";

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("code", getCode())
            .append("description", getDescription())
            .append("status", getStatus())
            .append("isDefault", getIsDefault())
            .append("auditStatus", getAuditStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
```

### 2.2 常量定义

```java
package com.wanyu.common.constant;

/**
 * 字段状态常量
 * 
 * <AUTHOR>
 */
public class FieldStatusConstants {
    
    /** 通用状态 - 正常 */
    public static final String STATUS_NORMAL = "0";
    
    /** 通用状态 - 停用 */
    public static final String STATUS_DISABLE = "1";
    
    /** 操作状态 - 成功 */
    public static final String OPERATION_SUCCESS = "0";
    
    /** 操作状态 - 失败 */
    public static final String OPERATION_FAILED = "1";
    
    /** 审核状态 - 待审核 */
    public static final String AUDIT_PENDING = "0";
    
    /** 审核状态 - 已审核 */
    public static final String AUDIT_APPROVED = "1";
    
    /** 审核状态 - 已拒绝 */
    public static final String AUDIT_REJECTED = "2";
    
    /** 布尔值 - 否 */
    public static final String BOOLEAN_NO = "0";
    
    /** 布尔值 - 是 */
    public static final String BOOLEAN_YES = "1";
    
    /** 删除标志 - 存在 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志 - 删除 */
    public static final String DEL_FLAG_DELETE = "2";
}
```

## 3. Service层开发

### 3.1 Service接口

```java
package com.wanyu.system.service;

import com.wanyu.system.domain.WmsExample;
import java.util.List;

/**
 * 示例业务Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface IWmsExampleService {
    
    /**
     * 查询示例业务列表
     */
    List<WmsExample> selectWmsExampleList(WmsExample wmsExample);
    
    /**
     * 查询启用状态的示例业务列表
     */
    List<WmsExample> selectEnabledWmsExampleList();
    
    /**
     * 新增示例业务
     */
    int insertWmsExample(WmsExample wmsExample);
    
    /**
     * 修改示例业务
     */
    int updateWmsExample(WmsExample wmsExample);
    
    /**
     * 启用示例业务
     */
    int enableWmsExample(Long id);
    
    /**
     * 禁用示例业务
     */
    int disableWmsExample(Long id);
    
    /**
     * 删除示例业务
     */
    int deleteWmsExampleByIds(Long[] ids);
}
```

### 3.2 Service实现类

```java
package com.wanyu.system.service.impl;

import com.wanyu.common.constant.FieldStatusConstants;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.system.domain.WmsExample;
import com.wanyu.system.mapper.WmsExampleMapper;
import com.wanyu.system.service.IWmsExampleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 示例业务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Service
public class WmsExampleServiceImpl implements IWmsExampleService {
    
    @Autowired
    private WmsExampleMapper wmsExampleMapper;

    /**
     * 查询示例业务列表
     */
    @Override
    public List<WmsExample> selectWmsExampleList(WmsExample wmsExample) {
        return wmsExampleMapper.selectWmsExampleList(wmsExample);
    }

    /**
     * 查询启用状态的示例业务列表
     */
    @Override
    public List<WmsExample> selectEnabledWmsExampleList() {
        WmsExample query = new WmsExample();
        query.setStatus(FieldStatusConstants.STATUS_NORMAL); // 0=正常
        return wmsExampleMapper.selectWmsExampleList(query);
    }

    /**
     * 新增示例业务
     */
    @Override
    public int insertWmsExample(WmsExample wmsExample) {
        // 设置默认状态
        if (wmsExample.getStatus() == null) {
            wmsExample.setStatus(FieldStatusConstants.STATUS_NORMAL);
        }
        if (wmsExample.getIsDefault() == null) {
            wmsExample.setIsDefault(FieldStatusConstants.BOOLEAN_NO);
        }
        if (wmsExample.getAuditStatus() == null) {
            wmsExample.setAuditStatus(FieldStatusConstants.AUDIT_PENDING);
        }
        
        wmsExample.setCreateBy(SecurityUtils.getUsername());
        wmsExample.setCreateTime(DateUtils.getNowDate());
        return wmsExampleMapper.insertWmsExample(wmsExample);
    }

    /**
     * 修改示例业务
     */
    @Override
    public int updateWmsExample(WmsExample wmsExample) {
        wmsExample.setUpdateBy(SecurityUtils.getUsername());
        wmsExample.setUpdateTime(DateUtils.getNowDate());
        return wmsExampleMapper.updateWmsExample(wmsExample);
    }

    /**
     * 启用示例业务
     */
    @Override
    public int enableWmsExample(Long id) {
        WmsExample wmsExample = new WmsExample();
        wmsExample.setId(id);
        wmsExample.setStatus(FieldStatusConstants.STATUS_NORMAL); // 0=正常
        wmsExample.setUpdateBy(SecurityUtils.getUsername());
        wmsExample.setUpdateTime(DateUtils.getNowDate());
        return wmsExampleMapper.updateWmsExample(wmsExample);
    }

    /**
     * 禁用示例业务
     */
    @Override
    public int disableWmsExample(Long id) {
        WmsExample wmsExample = new WmsExample();
        wmsExample.setId(id);
        wmsExample.setStatus(FieldStatusConstants.STATUS_DISABLE); // 1=停用
        wmsExample.setUpdateBy(SecurityUtils.getUsername());
        wmsExample.setUpdateTime(DateUtils.getNowDate());
        return wmsExampleMapper.updateWmsExample(wmsExample);
    }

    /**
     * 批量删除示例业务
     */
    @Override
    public int deleteWmsExampleByIds(Long[] ids) {
        return wmsExampleMapper.deleteWmsExampleByIds(ids);
    }
}
```

## 4. Mapper层开发

### 4.1 Mapper接口

```java
package com.wanyu.system.mapper;

import com.wanyu.system.domain.WmsExample;
import java.util.List;

/**
 * 示例业务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface WmsExampleMapper {
    
    /**
     * 查询示例业务列表
     */
    List<WmsExample> selectWmsExampleList(WmsExample wmsExample);
    
    /**
     * 根据ID查询示例业务
     */
    WmsExample selectWmsExampleById(Long id);
    
    /**
     * 新增示例业务
     */
    int insertWmsExample(WmsExample wmsExample);
    
    /**
     * 修改示例业务
     */
    int updateWmsExample(WmsExample wmsExample);
    
    /**
     * 删除示例业务
     */
    int deleteWmsExampleById(Long id);
    
    /**
     * 批量删除示例业务
     */
    int deleteWmsExampleByIds(Long[] ids);
}
```

### 4.2 Mapper XML

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsExampleMapper">
    
    <resultMap type="WmsExample" id="WmsExampleResult">
        <result property="id"           column="id" />
        <result property="name"         column="name" />
        <result property="code"         column="code" />
        <result property="description"  column="description" />
        <result property="status"       column="status" />
        <result property="isDefault"    column="is_default" />
        <result property="auditStatus"  column="audit_status" />
        <result property="delFlag"      column="del_flag" />
        <result property="createBy"     column="create_by" />
        <result property="createTime"   column="create_time" />
        <result property="updateBy"     column="update_by" />
        <result property="updateTime"   column="update_time" />
    </resultMap>

    <sql id="selectWmsExampleVo">
        SELECT id, name, code, description, status, is_default, audit_status, 
               del_flag, create_by, create_time, update_by, update_time 
        FROM wms_example
    </sql>

    <select id="selectWmsExampleList" parameterType="WmsExample" resultMap="WmsExampleResult">
        <include refid="selectWmsExampleVo"/>
        <where>
            del_flag = '0'
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="isDefault != null and isDefault != ''">
                AND is_default = #{isDefault}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                AND audit_status = #{auditStatus}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <select id="selectWmsExampleById" parameterType="Long" resultMap="WmsExampleResult">
        <include refid="selectWmsExampleVo"/>
        WHERE id = #{id} AND del_flag = '0'
    </select>
        
    <insert id="insertWmsExample" parameterType="WmsExample" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO wms_example
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateWmsExample" parameterType="WmsExample">
        UPDATE wms_example
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteWmsExampleById" parameterType="Long">
        UPDATE wms_example SET del_flag = '2' WHERE id = #{id}
    </delete>

    <delete id="deleteWmsExampleByIds" parameterType="String">
        UPDATE wms_example SET del_flag = '2' WHERE id IN 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
```

## 5. Controller层开发

```java
package com.wanyu.system.controller;

import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.WmsExample;
import com.wanyu.system.service.IWmsExampleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 示例业务Controller
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@RestController
@RequestMapping("/system/example")
public class WmsExampleController extends BaseController {
    
    @Autowired
    private IWmsExampleService wmsExampleService;

    /**
     * 查询示例业务列表
     */
    @PreAuthorize("@ss.hasPermi('system:example:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmsExample wmsExample) {
        startPage();
        List<WmsExample> list = wmsExampleService.selectWmsExampleList(wmsExample);
        return getDataTable(list);
    }

    /**
     * 查询启用状态的示例业务列表
     */
    @PreAuthorize("@ss.hasPermi('system:example:list')")
    @GetMapping("/enabled")
    public AjaxResult getEnabledList() {
        List<WmsExample> list = wmsExampleService.selectEnabledWmsExampleList();
        return AjaxResult.success(list);
    }

    /**
     * 导出示例业务列表
     */
    @PreAuthorize("@ss.hasPermi('system:example:export')")
    @Log(title = "示例业务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmsExample wmsExample) {
        List<WmsExample> list = wmsExampleService.selectWmsExampleList(wmsExample);
        ExcelUtil<WmsExample> util = new ExcelUtil<WmsExample>(WmsExample.class);
        util.exportExcel(response, list, "示例业务数据");
    }

    /**
     * 获取示例业务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:example:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(wmsExampleService.selectWmsExampleById(id));
    }

    /**
     * 新增示例业务
     */
    @PreAuthorize("@ss.hasPermi('system:example:add')")
    @Log(title = "示例业务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmsExample wmsExample) {
        return toAjax(wmsExampleService.insertWmsExample(wmsExample));
    }

    /**
     * 修改示例业务
     */
    @PreAuthorize("@ss.hasPermi('system:example:edit')")
    @Log(title = "示例业务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmsExample wmsExample) {
        return toAjax(wmsExampleService.updateWmsExample(wmsExample));
    }

    /**
     * 启用示例业务
     */
    @PreAuthorize("@ss.hasPermi('system:example:edit')")
    @Log(title = "示例业务", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{id}")
    public AjaxResult enable(@PathVariable Long id) {
        return toAjax(wmsExampleService.enableWmsExample(id));
    }

    /**
     * 禁用示例业务
     */
    @PreAuthorize("@ss.hasPermi('system:example:edit')")
    @Log(title = "示例业务", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{id}")
    public AjaxResult disable(@PathVariable Long id) {
        return toAjax(wmsExampleService.disableWmsExample(id));
    }

    /**
     * 删除示例业务
     */
    @PreAuthorize("@ss.hasPermi('system:example:remove')")
    @Log(title = "示例业务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wmsExampleService.deleteWmsExampleByIds(ids));
    }
}
```

## 6. 前端开发

### 6.1 Vue页面模板

```vue
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:example:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:example:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:example:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="exampleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="编码" align="center" prop="code" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="是否默认" align="center" prop="isDefault">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isDefault"/>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.audit_status" :value="scope.row.auditStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:example:edit']"
          >修改</el-button>
          <el-button
            v-if="scope.row.status === '1'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleStatusChange(scope.row, '0')"
            v-hasPermi="['system:example:edit']"
          >启用</el-button>
          <el-button
            v-if="scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleStatusChange(scope.row, '1')"
            v-hasPermi="['system:example:edit']"
          >禁用</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:example:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改示例业务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入编码" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否默认" prop="isDefault">
          <el-radio-group v-model="form.isDefault">
            <el-radio
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExample, getExample, delExample, addExample, updateExample, enableExample, disableExample } from "@/api/system/example";

export default {
  name: "Example",
  dicts: ['sys_normal_disable', 'sys_yes_no', 'audit_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 示例业务表格数据
      exampleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "编码不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询示例业务列表 */
    getList() {
      this.loading = true;
      listExample(this.queryParams).then(response => {
        this.exampleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        code: null,
        description: null,
        status: "0", // 默认正常状态
        isDefault: "0", // 默认否
        auditStatus: "0" // 默认待审核
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加示例业务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getExample(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改示例业务";
      });
    },
    /** 状态修改 */
    handleStatusChange(row, status) {
      let text = status === "0" ? "启用" : "禁用";
      this.$modal.confirm('确认要"' + text + '""' + row.name + '"吗？').then(function() {
        if (status === "0") {
          return enableExample(row.id);
        } else {
          return disableExample(row.id);
        }
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {});
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateExample(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExample(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除示例业务编号为"' + ids + '"的数据项？').then(function() {
        return delExample(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
```

### 6.2 API接口文件

```javascript
// api/system/example.js
import request from '@/utils/request'

// 查询示例业务列表
export function listExample(query) {
  return request({
    url: '/system/example/list',
    method: 'get',
    params: query
  })
}

// 查询示例业务详细
export function getExample(id) {
  return request({
    url: '/system/example/' + id,
    method: 'get'
  })
}

// 新增示例业务
export function addExample(data) {
  return request({
    url: '/system/example',
    method: 'post',
    data: data
  })
}

// 修改示例业务
export function updateExample(data) {
  return request({
    url: '/system/example',
    method: 'put',
    data: data
  })
}

// 启用示例业务
export function enableExample(id) {
  return request({
    url: '/system/example/enable/' + id,
    method: 'put'
  })
}

// 禁用示例业务
export function disableExample(id) {
  return request({
    url: '/system/example/disable/' + id,
    method: 'put'
  })
}

// 删除示例业务
export function delExample(id) {
  return request({
    url: '/system/example/' + id,
    method: 'delete'
  })
}
```

## 7. 数据字典配置

### 7.1 字典类型配置

```sql
-- 审核状态字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('审核状态', 'audit_status', '0', 'admin', NOW(), '通用审核状态字典');

-- 审核状态字典数据
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(1, '待审核', '0', 'audit_status', '', 'info', 'Y', '0', 'admin', NOW(), '待审核状态'),
(2, '已审核', '1', 'audit_status', '', 'success', 'N', '0', 'admin', NOW(), '已审核状态'),
(3, '已拒绝', '2', 'audit_status', '', 'danger', 'N', '0', 'admin', NOW(), '已拒绝状态');
```

## 8. 单元测试

```java
package com.wanyu.system.service;

import com.wanyu.common.constant.FieldStatusConstants;
import com.wanyu.system.domain.WmsExample;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Transactional
public class WmsExampleServiceTest {

    @Autowired
    private IWmsExampleService wmsExampleService;

    @Test
    public void testInsertWmsExample() {
        WmsExample example = new WmsExample();
        example.setName("测试示例");
        example.setCode("TEST001");
        example.setDescription("测试描述");
        
        int result = wmsExampleService.insertWmsExample(example);
        assertEquals(1, result);
        
        // 验证默认状态
        assertNotNull(example.getId());
        assertEquals(FieldStatusConstants.STATUS_NORMAL, example.getStatus());
        assertEquals(FieldStatusConstants.BOOLEAN_NO, example.getIsDefault());
        assertEquals(FieldStatusConstants.AUDIT_PENDING, example.getAuditStatus());
    }

    @Test
    public void testEnableDisableWmsExample() {
        // 创建测试数据
        WmsExample example = new WmsExample();
        example.setName("测试示例");
        example.setCode("TEST002");
        wmsExampleService.insertWmsExample(example);
        
        Long id = example.getId();
        
        // 测试禁用
        int result = wmsExampleService.disableWmsExample(id);
        assertEquals(1, result);
        
        // 验证状态
        WmsExample updated = wmsExampleService.selectWmsExampleById(id);
        assertEquals(FieldStatusConstants.STATUS_DISABLE, updated.getStatus());
        
        // 测试启用
        result = wmsExampleService.enableWmsExample(id);
        assertEquals(1, result);
        
        // 验证状态
        updated = wmsExampleService.selectWmsExampleById(id);
        assertEquals(FieldStatusConstants.STATUS_NORMAL, updated.getStatus());
    }

    @Test
    public void testSelectEnabledWmsExampleList() {
        // 创建测试数据
        WmsExample example1 = new WmsExample();
        example1.setName("启用示例");
        example1.setCode("ENABLED001");
        example1.setStatus(FieldStatusConstants.STATUS_NORMAL);
        wmsExampleService.insertWmsExample(example1);
        
        WmsExample example2 = new WmsExample();
        example2.setName("禁用示例");
        example2.setCode("DISABLED001");
        example2.setStatus(FieldStatusConstants.STATUS_DISABLE);
        wmsExampleService.insertWmsExample(example2);
        
        // 查询启用状态的记录
        List<WmsExample> enabledList = wmsExampleService.selectEnabledWmsExampleList();
        
        // 验证结果
        assertFalse(enabledList.isEmpty());
        for (WmsExample example : enabledList) {
            assertEquals(FieldStatusConstants.STATUS_NORMAL, example.getStatus());
        }
    }
}
```

## 9. 常见问题解决

### 问题1: 状态值判断错误
```java
// 错误写法
if ("1".equals(entity.getStatus())) {
    // 处理启用状态 - 错误！1是禁用状态
}

// 正确写法
if (FieldStatusConstants.STATUS_NORMAL.equals(entity.getStatus())) {
    // 处理启用状态 - 正确！
}
```

### 问题2: 前端状态显示错误
```vue
<!-- 错误写法 -->
<el-button v-if="scope.row.status === '0'" @click="handleEnable">启用</el-button>

<!-- 正确写法 -->
<el-button v-if="scope.row.status === '1'" @click="handleEnable">启用</el-button>
```

### 问题3: 数据库查询条件错误
```sql
-- 错误写法
SELECT * FROM table_name WHERE status = '1'; -- 查询启用状态

-- 正确写法
SELECT * FROM table_name WHERE status = '0'; -- 查询启用状态
```

## 10. 总结

遵循本开发指南，可以确保：

1. **数据一致性**: 所有状态字段使用统一标准
2. **代码规范**: 使用常量定义，避免硬编码
3. **前端正确**: 状态显示和操作逻辑正确
4. **测试完整**: 包含完整的单元测试
5. **文档清晰**: 代码注释和文档完整

在开发过程中，请严格按照本指南执行，确保字段定义标准化的一致性和正确性。