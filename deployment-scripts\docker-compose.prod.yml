version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: warehouse-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: warehouse_prod
      MYSQL_USER: warehouse_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
      - ./backups/mysql:/backups
    ports:
      - "127.0.0.1:3306:3306"
    networks:
      - warehouse-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["C<PERSON>", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      timeout: 20s
      retries: 10
      interval: 30s
      start_period: 60s

  # 后端应用
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile.prod
      args:
        VERSION: ${APP_VERSION:-1.0.0}
    image: warehouse-backend:${APP_VERSION:-1.0.0}
    container_name: warehouse-backend
    restart: unless-stopped
    environment:
      # Spring配置
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: warehouse_user
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_PASSWORD}
      
      # JWT配置
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: 86400000
      
      # 文件上传配置
      WAREHOUSE_UPLOAD_PATH: /app/uploads
      WAREHOUSE_BACKUP_PATH: /app/backups
      
      # JVM配置
      JAVA_OPTS: >
        -Xms1g -Xmx2g
        -XX:+UseG1GC
        -XX:MaxGCPauseMillis=200
        -XX:+HeapDumpOnOutOfMemoryError
        -XX:HeapDumpPath=/app/logs/
        -Djava.awt.headless=true
        -Dfile.encoding=UTF-8
        -Duser.timezone=Asia/Shanghai
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
      - app_backups:/app/backups
      - ./config/application-prod.yml:/app/config/application-prod.yml:ro
    ports:
      - "127.0.0.1:8080:8080"
    networks:
      - warehouse-network
    depends_on:
      mysql:
        condition: service_healthy

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: warehouse-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../frontend/dist:/usr/share/nginx/html:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - warehouse-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: warehouse-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "127.0.0.1:9090:9090"
    networks:
      - warehouse-network
    depends_on:
      - backend

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: warehouse-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_ADMIN_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "127.0.0.1:3000:3000"
    networks:
      - warehouse-network
    depends_on:
      - prometheus

  # 日志收集器
  filebeat:
    image: docker.elastic.co/beats/filebeat:7.15.0
    container_name: warehouse-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./logging/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - app_logs:/var/log/app:ro
      - nginx_logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - warehouse-network
    depends_on:
      - backend
      - nginx

  # 备份服务
  backup:
    image: alpine:latest
    container_name: warehouse-backup
    restart: "no"
    environment:
      MYSQL_HOST: mysql
      MYSQL_USER: warehouse_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      MYSQL_DATABASE: warehouse_prod
      BACKUP_RETENTION_DAYS: 30
    volumes:
      - app_backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    networks:
      - warehouse-network
    depends_on:
      - mysql
    command: /bin/sh -c "chmod +x /backup.sh && crond -f"

volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/warehouse/data/mysql
  

  
  app_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/warehouse/logs
  
  app_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/warehouse/uploads
  
  app_backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/warehouse/backups
  
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/warehouse/monitoring/prometheus
  
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/warehouse/monitoring/grafana
  
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/nginx

networks:
  warehouse-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16