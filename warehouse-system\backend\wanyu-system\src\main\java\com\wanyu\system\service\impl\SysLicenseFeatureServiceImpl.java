package com.wanyu.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.system.mapper.SysLicenseFeatureMapper;
import com.wanyu.system.domain.SysLicenseFeature;
import com.wanyu.system.service.ISysLicenseFeatureService;

/**
 * 功能权限Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
@Service
public class SysLicenseFeatureServiceImpl implements ISysLicenseFeatureService 
{
    private static final Logger log = LoggerFactory.getLogger(SysLicenseFeatureServiceImpl.class);

    @Autowired
    private SysLicenseFeatureMapper sysLicenseFeatureMapper;

    /**
     * 查询功能权限
     * 
     * @param featureId 功能权限主键
     * @return 功能权限
     */
    @Override
    public SysLicenseFeature selectSysLicenseFeatureByFeatureId(Long featureId)
    {
        SysLicenseFeature feature = sysLicenseFeatureMapper.selectSysLicenseFeatureByFeatureId(featureId);
        if (feature != null) {
            // 设置计算字段：enabled = (status == "0")
            feature.setEnabled("0".equals(feature.getStatus()));
        }
        return feature;
    }

    /**
     * 查询功能权限列表
     * 
     * @param sysLicenseFeature 功能权限
     * @return 功能权限
     */
    @Override
    public List<SysLicenseFeature> selectSysLicenseFeatureList(SysLicenseFeature sysLicenseFeature)
    {
        List<SysLicenseFeature> features = sysLicenseFeatureMapper.selectSysLicenseFeatureList(sysLicenseFeature);
        // 为每个功能设置计算字段
        for (SysLicenseFeature feature : features) {
            feature.setEnabled("0".equals(feature.getStatus()));
        }
        return features;
    }

    /**
     * 查询启用的功能权限列表
     * 
     * @return 启用的功能权限集合
     */
    @Override
    public List<SysLicenseFeature> selectEnabledFeatures()
    {
        // 新标准：status='0'表示启用
        List<SysLicenseFeature> features = sysLicenseFeatureMapper.selectSysLicenseFeatureByStatus("0");
        for (SysLicenseFeature feature : features) {
            feature.setEnabled(true);
        }
        log.debug("查询到 {} 个启用的功能", features.size());
        return features;
    }

    /**
     * 查询禁用的功能权限列表
     * 
     * @return 禁用的功能权限集合
     */
    @Override
    public List<SysLicenseFeature> selectDisabledFeatures()
    {
        // 新标准：status='1'表示禁用
        List<SysLicenseFeature> features = sysLicenseFeatureMapper.selectSysLicenseFeatureByStatus("1");
        for (SysLicenseFeature feature : features) {
            feature.setEnabled(false);
        }
        log.debug("查询到 {} 个禁用的功能", features.size());
        return features;
    }

    /**
     * 根据功能代码查询功能权限
     * 
     * @param featureCode 功能代码
     * @return 功能权限
     */
    @Override
    public SysLicenseFeature selectSysLicenseFeatureByCode(String featureCode)
    {
        SysLicenseFeature feature = sysLicenseFeatureMapper.selectSysLicenseFeatureByCode(featureCode);
        if (feature != null) {
            feature.setEnabled("0".equals(feature.getStatus()));
        }
        return feature;
    }

    /**
     * 查询核心功能列表
     * 
     * @return 核心功能集合
     */
    @Override
    public List<SysLicenseFeature> selectCoreFeatures()
    {
        List<SysLicenseFeature> features = sysLicenseFeatureMapper.selectSysLicenseFeatureByCore("1");
        for (SysLicenseFeature feature : features) {
            feature.setEnabled("0".equals(feature.getStatus()));
        }
        log.debug("查询到 {} 个核心功能", features.size());
        return features;
    }

    /**
     * 新增功能权限
     * 
     * @param sysLicenseFeature 功能权限
     * @return 结果
     */
    @Override
    public int insertSysLicenseFeature(SysLicenseFeature sysLicenseFeature)
    {
        // 设置默认状态为启用（新标准：0=启用）
        if (sysLicenseFeature.getStatus() == null) {
            sysLicenseFeature.setStatus("0");
        }
        
        sysLicenseFeature.setCreateTime(DateUtils.getNowDate());
        try {
            sysLicenseFeature.setCreateBy(SecurityUtils.getUsername());
        } catch (Exception e) {
            sysLicenseFeature.setCreateBy("system");
        }
        
        log.info("新增功能权限: code={}, name={}, status={}", 
            sysLicenseFeature.getFeatureCode(), 
            sysLicenseFeature.getFeatureName(), 
            sysLicenseFeature.getStatus());
        
        return sysLicenseFeatureMapper.insertSysLicenseFeature(sysLicenseFeature);
    }

    /**
     * 修改功能权限
     * 
     * @param sysLicenseFeature 功能权限
     * @return 结果
     */
    @Override
    public int updateSysLicenseFeature(SysLicenseFeature sysLicenseFeature)
    {
        sysLicenseFeature.setUpdateTime(DateUtils.getNowDate());
        try {
            sysLicenseFeature.setUpdateBy(SecurityUtils.getUsername());
        } catch (Exception e) {
            sysLicenseFeature.setUpdateBy("system");
        }
        
        log.info("修改功能权限: featureId={}, status={}", 
            sysLicenseFeature.getFeatureId(), 
            sysLicenseFeature.getStatus());
        
        return sysLicenseFeatureMapper.updateSysLicenseFeature(sysLicenseFeature);
    }

    /**
     * 批量删除功能权限
     * 
     * @param featureIds 需要删除的功能权限主键集合
     * @return 结果
     */
    @Override
    public int deleteSysLicenseFeatureByFeatureIds(Long[] featureIds)
    {
        // 检查是否包含核心功能
        for (Long featureId : featureIds) {
            SysLicenseFeature feature = sysLicenseFeatureMapper.selectSysLicenseFeatureByFeatureId(featureId);
            if (feature != null && "1".equals(feature.getIsCore())) {
                throw new RuntimeException("不能删除核心功能: " + feature.getFeatureName());
            }
        }
        
        log.info("批量删除功能权限: featureIds={}", java.util.Arrays.toString(featureIds));
        return sysLicenseFeatureMapper.deleteSysLicenseFeatureByFeatureIds(featureIds);
    }

    /**
     * 删除功能权限信息
     * 
     * @param featureId 功能权限主键
     * @return 结果
     */
    @Override
    public int deleteSysLicenseFeatureByFeatureId(Long featureId)
    {
        // 检查是否为核心功能
        SysLicenseFeature feature = sysLicenseFeatureMapper.selectSysLicenseFeatureByFeatureId(featureId);
        if (feature != null && "1".equals(feature.getIsCore())) {
            throw new RuntimeException("不能删除核心功能: " + feature.getFeatureName());
        }
        
        log.info("删除功能权限: featureId={}", featureId);
        return sysLicenseFeatureMapper.deleteSysLicenseFeatureByFeatureId(featureId);
    }

    /**
     * 启用功能
     * 
     * @param featureId 功能权限主键
     * @return 结果
     */
    @Override
    public int enableFeature(Long featureId)
    {
        // 新标准：status='0'表示启用
        log.info("启用功能: featureId={}", featureId);
        return sysLicenseFeatureMapper.updateFeatureStatus(featureId, "0");
    }

    /**
     * 禁用功能
     * 
     * @param featureId 功能权限主键
     * @return 结果
     */
    @Override
    public int disableFeature(Long featureId)
    {
        // 检查是否为核心功能
        SysLicenseFeature feature = sysLicenseFeatureMapper.selectSysLicenseFeatureByFeatureId(featureId);
        if (feature != null && "1".equals(feature.getIsCore())) {
            throw new RuntimeException("不能禁用核心功能: " + feature.getFeatureName());
        }
        
        // 新标准：status='1'表示禁用
        log.info("禁用功能: featureId={}", featureId);
        return sysLicenseFeatureMapper.updateFeatureStatus(featureId, "1");
    }

    /**
     * 批量启用功能
     * 
     * @param featureIds 功能权限主键集合
     * @return 结果
     */
    @Override
    public int enableFeatures(Long[] featureIds)
    {
        // 新标准：status='0'表示启用
        log.info("批量启用功能: featureIds={}", java.util.Arrays.toString(featureIds));
        return sysLicenseFeatureMapper.updateFeaturesStatus(featureIds, "0");
    }

    /**
     * 批量禁用功能
     * 
     * @param featureIds 功能权限主键集合
     * @return 结果
     */
    @Override
    public int disableFeatures(Long[] featureIds)
    {
        // 检查是否包含核心功能
        for (Long featureId : featureIds) {
            SysLicenseFeature feature = sysLicenseFeatureMapper.selectSysLicenseFeatureByFeatureId(featureId);
            if (feature != null && "1".equals(feature.getIsCore())) {
                throw new RuntimeException("不能禁用核心功能: " + feature.getFeatureName());
            }
        }
        
        // 新标准：status='1'表示禁用
        log.info("批量禁用功能: featureIds={}", java.util.Arrays.toString(featureIds));
        return sysLicenseFeatureMapper.updateFeaturesStatus(featureIds, "1");
    }

    /**
     * 检查功能是否可用
     * 
     * @param featureCode 功能代码
     * @return 是否可用
     */
    @Override
    public boolean isFeatureAvailable(String featureCode)
    {
        SysLicenseFeature feature = sysLicenseFeatureMapper.selectSysLicenseFeatureByCode(featureCode);
        if (feature == null) {
            log.warn("功能不存在: {}", featureCode);
            return false;
        }
        
        // 新标准：status='0'表示启用/可用
        boolean available = "0".equals(feature.getStatus());
        log.debug("功能可用性检查: code={}, available={}", featureCode, available);
        return available;
    }

    /**
     * 根据授权类型查询可用功能
     * 
     * @param licenseType 授权类型
     * @return 可用功能集合
     */
    @Override
    public List<SysLicenseFeature> selectFeaturesByLicenseType(String licenseType)
    {
        List<SysLicenseFeature> features = sysLicenseFeatureMapper.selectFeaturesByLicenseType(licenseType);
        
        // 只返回启用的功能（新标准：status='0'）
        features.removeIf(feature -> !"0".equals(feature.getStatus()));
        
        // 设置计算字段
        for (SysLicenseFeature feature : features) {
            feature.setEnabled(true); // 已经过滤了禁用的功能
        }
        
        log.debug("根据授权类型 {} 查询到 {} 个可用功能", licenseType, features.size());
        return features;
    }
}