-- 字段定义标准化修复脚本
-- 版本: 1.0
-- 描述: 修复sys_license和sys_license_feature表的status字段定义

-- 开始事务
START TRANSACTION;

-- 1. 备份关键表到临时表
CREATE TABLE IF NOT EXISTS sys_license_temp_backup AS SELECT * FROM sys_license;
CREATE TABLE IF NOT EXISTS sys_license_feature_temp_backup AS SELECT * FROM sys_license_feature;

-- 2. 修复sys_license表的status字段
-- 添加临时字段
ALTER TABLE sys_license ADD COLUMN status_new CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- 数据转换（颠倒原有值）
UPDATE sys_license SET status_new = CASE 
    WHEN status = '0' THEN '1'  -- 原来禁用(0) -> 新标准禁用(1)
    WHEN status = '1' THEN '0'  -- 原来启用(1) -> 新标准启用(0)
    ELSE '0' 
END;

-- 删除原字段，重命名新字段
ALTER TABLE sys_license DROP COLUMN status;
ALTER TABLE sys_license CHANGE COLUMN status_new status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- 3. 修复sys_license_feature表的status字段
-- 添加临时字段
ALTER TABLE sys_license_feature ADD COLUMN status_new CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- 数据转换（颠倒原有值）
UPDATE sys_license_feature SET status_new = CASE 
    WHEN status = '0' THEN '1'  -- 原来禁用(0) -> 新标准禁用(1)
    WHEN status = '1' THEN '0'  -- 原来启用(1) -> 新标准启用(0)
    ELSE '0' 
END;

-- 删除原字段，重命名新字段
ALTER TABLE sys_license_feature DROP COLUMN status;
ALTER TABLE sys_license_feature CHANGE COLUMN status_new status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- 4. 更新数据字典
UPDATE sys_dict_data SET dict_label = '正常', dict_value = '0' 
WHERE dict_type = 'sys_normal_disable' AND dict_value = '0';

UPDATE sys_dict_data SET dict_label = '停用', dict_value = '1' 
WHERE dict_type = 'sys_normal_disable' AND dict_value = '1';

-- 5. 确保操作状态字典正确
INSERT IGNORE INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(1, '成功', '0', 'operation_status', '', 'success', 'Y', '0', 'admin', NOW(), '操作成功'),
(2, '失败', '1', 'operation_status', '', 'danger', 'N', '0', 'admin', NOW(), '操作失败');

-- 提交事务
COMMIT;

-- 记录修复完成
INSERT INTO sys_operation_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time)
VALUES ('字段定义标准化', 0, 'field_standardization_fix.sql', 'SQL', 1, 'system', '系统', '/deploy/field-standardization', '127.0.0.1', '本地', '字段定义标准化部署', '部署成功', 0, NULL, NOW());