package com.wanyu.framework.web.service;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.wanyu.common.constant.CacheConstants;
import com.wanyu.common.constant.Constants;
import com.wanyu.common.constant.UserConstants;
import com.wanyu.common.core.domain.entity.SysUser;
import com.wanyu.common.core.domain.model.LoginBody;
import com.wanyu.common.core.domain.model.LoginUser;
import com.wanyu.common.core.cache.CaptchaCacheService;
import com.wanyu.common.exception.ServiceException;
import com.wanyu.common.exception.user.BlackListException;
import com.wanyu.common.exception.user.CaptchaException;
import com.wanyu.common.exception.user.CaptchaExpireException;
import com.wanyu.common.exception.user.UserNotExistsException;
import com.wanyu.common.exception.user.UserPasswordNotMatchException;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.MessageUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.common.utils.LoginTypeUtils;
import com.wanyu.common.utils.ip.IpUtils;
import com.wanyu.framework.manager.AsyncManager;
import com.wanyu.framework.manager.factory.AsyncFactory;
import com.wanyu.framework.security.context.AuthenticationContextHolder;
import com.wanyu.system.service.ISysConfigService;
import com.wanyu.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 登录校验方法
 * 支持多样式登录：用户名、真实姓名、手机号
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    private static final Logger log = LoggerFactory.getLogger(SysLoginService.class);

    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;


    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;
    
    @Autowired
    private CaptchaCacheService captchaCacheService;

    /**
     * 登录验证
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    public String login(LoginBody loginBody)
    {
        // 验证登录类型和标识格式
        validateLoginTypeAndIdentity(loginBody);
        
        // 获取登录参数
        String loginType = loginBody.getLoginType();
        String loginIdentity = getLoginIdentity(loginBody, loginType);
        String password = loginBody.getPassword();
        String code = loginBody.getCode();
        String uuid = loginBody.getUuid();

        log.info("用户登录尝试 - 登录类型: {}, 登录标识: {}", loginType, 
            LoginTypeUtils.getMaskedLoginIdentity(loginIdentity, loginType));

        // 验证码校验
        validateCaptcha(loginIdentity, code, uuid);
        
        // 登录前置校验
        loginPreCheck(loginIdentity, password);
        
        // 用户验证
        Authentication authentication = authenticateUser(loginIdentity, password);
        
        // 记录登录成功信息
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        
        // 更新用户登录类型
        updateUserLoginType(loginUser.getUserId(), loginType);
        
        // 生成token
        String token = tokenService.createToken(loginUser);
        
        log.info("用户登录成功 - 用户ID: {}, 登录类型: {}, 登录标识: {}", 
            loginUser.getUserId(), loginType, LoginTypeUtils.getMaskedLoginIdentity(loginIdentity, loginType));
        
        return token;
    }

    /**
     * 获取登录标识
     *
     * @param loginBody 登录信息
     * @param loginType 登录类型
     * @return 登录标识
     */
    private String getLoginIdentity(LoginBody loginBody, String loginType)
    {
        String loginIdentity = loginBody.getUsername();
        
        if ("realname".equals(loginType)) {
            loginIdentity = loginBody.getRealName();
        } else if ("phone".equals(loginType)) {
            loginIdentity = loginBody.getPhone();
        }
        
        return loginIdentity;
    }

    /**
     * 用户认证
     *
     * @param loginIdentity 登录标识
     * @param password 密码
     * @return 认证结果
     */
    private Authentication authenticateUser(String loginIdentity, String password)
    {
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = 
                new UsernamePasswordAuthenticationToken(loginIdentity, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            handleAuthenticationException(loginIdentity, e);
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        
        return authentication;
    }

    /**
     * 处理认证异常
     *
     * @param loginIdentity 登录标识
     * @param e 异常
     */
    private void handleAuthenticationException(String loginIdentity, Exception e)
    {
        if (e instanceof BadCredentialsException)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, 
                MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        else
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, e.getMessage()));
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 校验验证码
     *
     * @param loginIdentity 登录标识
     * @param code 验证码
     * @param uuid 唯一标识
     */
    public void validateCaptcha(String loginIdentity, String code, String uuid)
    {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (!captchaEnabled)
        {
            return;
        }

        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        
        // 使用统一的验证码缓存服务
        String captcha = captchaCacheService.getCaptcha(verifyKey);

        log.debug("验证码校验 - 登录标识: {}, 验证码Key: {}, 输入验证码: {}, 实际验证码: {}", 
            loginIdentity, verifyKey, code, captcha);

        if (captcha == null)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, 
                MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        
        if (!code.equalsIgnoreCase(captcha))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, 
                MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
        
        // 验证成功后删除验证码
        captchaCacheService.deleteCaptcha(verifyKey);
    }

    /**
     * 登录前置校验
     * 
     * @param loginIdentity 登录标识
     * @param password 用户密码
     */
    public void loginPreCheck(String loginIdentity, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(loginIdentity) || StringUtils.isEmpty(password))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, 
                MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        
        // 密码长度校验
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, 
                MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        
        // 登录标识长度校验
        if (loginIdentity.length() < UserConstants.USERNAME_MIN_LENGTH
                || loginIdentity.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, 
                MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, 
                MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 验证登录类型和标识格式
     * 
     * @param loginBody 登录信息
     */
    private void validateLoginTypeAndIdentity(LoginBody loginBody)
    {
        String loginType = loginBody.getLoginType();
        String loginIdentity = getLoginIdentity(loginBody, loginType);

        // 如果没有指定登录类型，自动检测
        if (StringUtils.isEmpty(loginType))
        {
            loginType = LoginTypeUtils.detectLoginType(loginIdentity);
            loginBody.setLoginType(loginType);
        }

        // 验证登录标识格式
        if (!LoginTypeUtils.validateLoginIdentity(loginIdentity, loginType))
        {
            String errorMsg = String.format("登录标识格式不正确，请输入有效的%s", 
                LoginTypeUtils.getLoginTypeDescription(loginType));
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginIdentity, Constants.LOGIN_FAIL, errorMsg));
            throw new ServiceException(errorMsg);
        }

        log.debug("登录类型验证通过 - 类型: {}, 标识: {}", loginType, 
            LoginTypeUtils.getMaskedLoginIdentity(loginIdentity, loginType));
    }

    /**
     * 更新用户登录类型
     * 
     * @param userId 用户ID
     * @param loginType 登录类型
     */
    private void updateUserLoginType(Long userId, String loginType)
    {
        try
        {
            SysUser updateUser = new SysUser();
            updateUser.setUserId(userId);
            updateUser.setLoginType(loginType);
            userService.updateUserProfile(updateUser);
            
            log.debug("更新用户登录类型成功 - 用户ID: {}, 登录类型: {}", userId, loginType);
        }
        catch (Exception e)
        {
            log.warn("更新用户登录类型失败 - 用户ID: {}, 登录类型: {}, 错误: {}", 
                userId, loginType, e.getMessage());
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }
}