package com.wanyu.system.mapper;

import java.util.List;
import com.wanyu.system.domain.SysLogError;

/**
 * 错误日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface SysLogErrorMapper 
{
    /**
     * 查询错误日志
     * 
     * @param id 错误日志主键
     * @return 错误日志
     */
    public SysLogError selectLogErrorById(Long id);

    /**
     * 查询错误日志列表
     * 
     * @param logError 错误日志
     * @return 错误日志集合
     */
    public List<SysLogError> selectLogErrorList(SysLogError logError);

    /**
     * 新增错误日志
     * 
     * @param logError 错误日志
     * @return 结果
     */
    public int insertLogError(SysLogError logError);

    /**
     * 修改错误日志
     * 
     * @param logError 错误日志
     * @return 结果
     */
    public int updateLogError(SysLogError logError);

    /**
     * 删除错误日志
     * 
     * @param id 错误日志主键
     * @return 结果
     */
    public int deleteLogErrorById(Long id);

    /**
     * 批量删除错误日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLogErrorByIds(Long[] ids);

    /**
     * 清空错误日志
     */
    public void cleanLogError();
}