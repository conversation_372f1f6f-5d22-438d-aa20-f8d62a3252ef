package com.wanyu.web.controller.product;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.WmsBarcode;
import com.wanyu.system.domain.WmsBarcodeTemplate;
import com.wanyu.system.service.IWmsBarcodeService;

/**
 * 物品条码Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/product/barcode")
public class ProductBarcodeController extends BaseController {
    @Autowired
    private IWmsBarcodeService wmsBarcodeService;

    /**
     * 查询物品条码列表
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmsBarcode wmsBarcode) {
        startPage();
        List<WmsBarcode> list = wmsBarcodeService.selectWmsBarcodeList(wmsBarcode);
        return getDataTable(list);
    }

    /**
     * 导出物品条码列表
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:export')")
    @Log(title = "物品条码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmsBarcode wmsBarcode) {
        List<WmsBarcode> list = wmsBarcodeService.selectWmsBarcodeList(wmsBarcode);
        ExcelUtil<WmsBarcode> util = new ExcelUtil<WmsBarcode>(WmsBarcode.class);
        util.exportExcel(response, list, "物品条码数据");
    }

    /**
     * 获取物品条码详细信息
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:query')")
    @GetMapping(value = "/{barcodeId}")
    public AjaxResult getInfo(@PathVariable("barcodeId") Long barcodeId) {
        return success(wmsBarcodeService.selectWmsBarcodeByBarcodeId(barcodeId));
    }

    /**
     * 新增物品条码
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:add')")
    @Log(title = "物品条码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmsBarcode wmsBarcode) {
        return toAjax(wmsBarcodeService.insertWmsBarcode(wmsBarcode));
    }

    /**
     * 修改物品条码
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:edit')")
    @Log(title = "物品条码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmsBarcode wmsBarcode) {
        return toAjax(wmsBarcodeService.updateWmsBarcode(wmsBarcode));
    }

    /**
     * 删除物品条码
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:remove')")
    @Log(title = "物品条码", businessType = BusinessType.DELETE)
    @DeleteMapping("/{barcodeIds}")
    public AjaxResult remove(@PathVariable Long[] barcodeIds) {
        return toAjax(wmsBarcodeService.deleteWmsBarcodeByBarcodeIds(barcodeIds));
    }

    /**
     * 生成物品条码
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:generate')")
    @Log(title = "生成条码", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult generateBarcode(@RequestBody WmsBarcode wmsBarcode) {
        try {
            WmsBarcode generated = wmsBarcodeService.generateWmsBarcode(wmsBarcode);
            return AjaxResult.success("生成成功", generated.getBarcodeContent());
        } catch (Exception e) {
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }

    /**
     * 批量生成物品条码
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:generate')")
    @Log(title = "批量生成条码", businessType = BusinessType.INSERT)
    @PostMapping("/batch/generate")
    public AjaxResult batchGenerateBarcode(@RequestBody List<WmsBarcode> wmsBarcodeList) {
        try {
            List<WmsBarcode> result = wmsBarcodeService.batchGenerateWmsBarcode(wmsBarcodeList);
            return AjaxResult.success("批量生成成功", result);
        } catch (Exception e) {
            return AjaxResult.error("批量生成失败：" + e.getMessage());
        }
    }

    /**
     * 打印物品条码
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:print')")
    @GetMapping("/print/{barcodeIds}")
    public AjaxResult printBarcode(@PathVariable Long[] barcodeIds) {
        // 实际项目中这里应该返回条码打印需要的数据
        return AjaxResult.success("打印成功");
    }

    /**
     * 获取条码图片
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:list')")
    @GetMapping("/image/{barcodeId}")
    public AjaxResult getBarcodeImage(@PathVariable("barcodeId") Long barcodeId) {
        try {
            String image = wmsBarcodeService.getWmsBarcodeImage(barcodeId);
            return AjaxResult.success(image);
        } catch (Exception e) {
            return AjaxResult.error("获取条码图片失败：" + e.getMessage());
        }
    }

    /**
     * 查询物品条码模板列表
     */
    @PreAuthorize("@ss.hasPermi('product:barcode:list')")
    @GetMapping("/template/list")
    public AjaxResult templateList() {
        List<WmsBarcodeTemplate> list = wmsBarcodeService.selectWmsBarcodeTemplateList();
        return AjaxResult.success(list);
    }
}