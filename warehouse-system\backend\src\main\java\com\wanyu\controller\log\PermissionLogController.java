package com.wanyu.controller.log;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.system.domain.SysPermissionLog;
import com.wanyu.system.service.ISysPermissionLogService;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;

/**
 * 权限日志Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/api/v1/logs/permission")
public class PermissionLogController extends BaseController
{
    @Autowired
    private ISysPermissionLogService sysPermissionLogService;

    /**
     * 查询权限日志列表
     */
    @PreAuthorize("@ss.hasPermi('log:permission:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysPermissionLog sysPermissionLog)
    {
        startPage();
        List<SysPermissionLog> list = sysPermissionLogService.selectSysPermissionLogList(sysPermissionLog);
        return getDataTable(list);
    }

    /**
     * 导出权限日志列表
     */
    @PreAuthorize("@ss.hasPermi('log:permission:export')")
    @Log(title = "权限日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPermissionLog sysPermissionLog)
    {
        List<SysPermissionLog> list = sysPermissionLogService.selectSysPermissionLogList(sysPermissionLog);
        ExcelUtil<SysPermissionLog> util = new ExcelUtil<SysPermissionLog>(SysPermissionLog.class);
        util.exportExcel(response, list, "权限日志数据");
    }

    /**
     * 获取权限日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:permission:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return AjaxResult.success(sysPermissionLogService.selectSysPermissionLogByLogId(logId));
    }

    /**
     * 新增权限日志
     */
    @PreAuthorize("@ss.hasPermi('log:permission:add')")
    @Log(title = "权限日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysPermissionLog sysPermissionLog)
    {
        return toAjax(sysPermissionLogService.insertSysPermissionLog(sysPermissionLog));
    }

    /**
     * 修改权限日志
     */
    @PreAuthorize("@ss.hasPermi('log:permission:edit')")
    @Log(title = "权限日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysPermissionLog sysPermissionLog)
    {
        return toAjax(sysPermissionLogService.updateSysPermissionLog(sysPermissionLog));
    }

    /**
     * 删除权限日志
     */
    @PreAuthorize("@ss.hasPermi('log:permission:remove')")
    @Log(title = "权限日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(sysPermissionLogService.deleteSysPermissionLogByLogIds(logIds));
    }

    /**
     * 批量删除权限日志
     */
    @PreAuthorize("@ss.hasPermi('log:permission:remove')")
    @Log(title = "权限日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult batchRemove(@RequestBody Long[] logIds)
    {
        return toAjax(sysPermissionLogService.deleteSysPermissionLogByLogIds(logIds));
    }

    /**
     * 清空权限日志
     */
    @PreAuthorize("@ss.hasPermi('log:permission:remove')")
    @Log(title = "权限日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        sysPermissionLogService.cleanSysPermissionLog();
        return AjaxResult.success();
    }

    /**
     * 获取权限统计信息
     */
    @PreAuthorize("@ss.hasPermi('log:permission:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(SysPermissionLog sysPermissionLog)
    {
        return AjaxResult.success(sysPermissionLogService.getPermissionStatistics(sysPermissionLog));
    }

    /**
     * 获取权限操作趋势数据
     */
    @PreAuthorize("@ss.hasPermi('log:permission:list')")
    @GetMapping("/trend")
    public AjaxResult getTrend(SysPermissionLog sysPermissionLog)
    {
        return AjaxResult.success(sysPermissionLogService.getPermissionTrend(sysPermissionLog));
    }
}