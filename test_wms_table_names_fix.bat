@echo off
chcp 65001 >nul
echo ========================================
echo 物品相关表名修复测试脚本
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456
set BACKEND_URL=http://localhost:8080

echo 🧪 开始测试物品相关表名修复...
echo.

echo 📋 1. 测试数据库表结构...
echo.
echo 检查 wms_category 表:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SHOW CREATE TABLE wms_category\G" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ wms_category 表不存在或无法访问
    goto :error
)

echo.
echo 检查 wms_specification 表:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SHOW CREATE TABLE wms_specification\G" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ wms_specification 表不存在或无法访问
    goto :error
)

echo.
echo 检查 wms_unit 表:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SHOW CREATE TABLE wms_unit\G" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ wms_unit 表不存在或无法访问
    goto :error
)

echo.
echo ✅ 数据库表结构正常

echo.
echo 📊 2. 测试数据完整性...
echo.
echo 物品分类数据:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT category_id, category_name, category_code, parent_id FROM wms_category WHERE del_flag = '0' ORDER BY order_num LIMIT 5;"

echo.
echo 物品规格数据:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT spec_id, spec_name, spec_code FROM wms_specification ORDER BY spec_id LIMIT 5;"

echo.
echo 物品单位数据:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT unit_id, unit_name, unit_code FROM wms_unit ORDER BY unit_id LIMIT 5;"

echo.
echo ✅ 数据完整性检查通过

echo.
echo 🔧 3. 测试后端编译...
echo.
cd /d "C:\CKGLXT\warehouse-system\backend"
if not exist "pom.xml" (
    echo ❌ 未找到后端项目目录
    goto :error
)

echo 执行 Maven 编译...
call mvn clean compile -q -DskipTests
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 后端编译失败
    echo 请检查代码是否有语法错误
    goto :error
)

echo ✅ 后端编译成功

echo.
echo 🌐 4. 测试后端服务连接...
echo.
echo 检查后端服务是否运行在端口 8080...
netstat -an | findstr ":8080" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 后端服务正在运行
    
    echo.
    echo 测试相关API端点...
    
    echo 测试物品分类API:
    curl -s -o nul -w "HTTP状态码: %%{http_code}\n" "%BACKEND_URL%/product/category/list" 2>nul
    
    echo 测试物品规格API:
    curl -s -o nul -w "HTTP状态码: %%{http_code}\n" "%BACKEND_URL%/product/specification/list" 2>nul
    
    echo 测试物品单位API:
    curl -s -o nul -w "HTTP状态码: %%{http_code}\n" "%BACKEND_URL%/product/unit/list" 2>nul
    
) else (
    echo ⚠️  后端服务未运行
    echo 请先启动后端服务进行完整测试
)

echo.
echo 🔍 5. 测试数据关联性...
echo.
echo 检查 wms_product 表与分类的关联:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as products_with_category FROM wms_product p INNER JOIN wms_category c ON p.category_id = c.category_id WHERE c.del_flag = '0';" 2>nul

echo.
echo 检查 wms_product 表与规格的关联:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as products_with_spec FROM wms_product p INNER JOIN wms_specification s ON p.spec_id = s.spec_id;" 2>nul

echo.
echo 检查 wms_product 表与单位的关联:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as products_with_unit FROM wms_product p INNER JOIN wms_unit u ON p.unit_id = u.unit_id;" 2>nul

echo.
echo ========================================
echo ✅ 物品相关表名修复测试完成！
echo ========================================
echo.
echo 📝 测试结果总结:
echo   1. ✅ 数据库表结构正常
echo   2. ✅ 数据完整性检查通过
echo   3. ✅ 后端代码编译成功
echo   4. ⚠️  API测试需要后端服务运行
echo   5. ✅ 数据关联性检查完成
echo.
echo 🔄 建议下一步操作:
echo   1. 启动后端服务: start-system.bat
echo   2. 访问前端页面测试相关功能
echo   3. 测试物品分类、规格、单位的增删改查
echo   4. 测试物品信息与分类、规格、单位的关联
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ 测试过程中发现问题！
echo ========================================
echo.
echo 🔧 建议修复步骤:
echo   1. 重新运行修复脚本: fix_wms_table_names_complete.bat
echo   2. 检查数据库连接配置
echo   3. 检查后端代码语法错误
echo   4. 查看详细错误日志
echo.

:end
echo 按任意键退出...
pause >nul