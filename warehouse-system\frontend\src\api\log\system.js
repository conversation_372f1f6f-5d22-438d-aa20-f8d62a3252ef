import request from '@/utils/request'

// 查询系统日志列表
export function listSystemLog(query) {
  return request({
    url: '/api/v1/monitor/logininfor/list',
    method: 'get',
    params: query
  }).then(res => {
    // 确保返回的数据格式正确
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        rows: res.rows || [],
        total: res.total || 0
      };
    }
    return res;
  }).catch(err => {
    console.error("查询系统日志列表出错:", err);
    return {
      code: 500,
      msg: "查询系统日志列表失败",
      rows: [],
      total: 0
    };
  });
}

// 查询系统日志详细
export function getSystemLog(infoId) {
  return request({
    url: `/api/v1/monitor/logininfor/${infoId}`,
    method: 'get'
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        data: res.data || res
      };
    }
    return res;
  }).catch(err => {
    console.error("查询系统日志详细出错:", err);
    return {
      code: 500,
      msg: "查询系统日志详细失败"
    };
  });
}

// 删除系统日志
export function delSystemLog(infoIds) {
  return request({
    url: `/api/v1/monitor/logininfor/${infoIds}`,
    method: 'delete'
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "删除成功"
      };
    }
    return res;
  }).catch(err => {
    console.error("删除系统日志出错:", err);
    return {
      code: 500,
      msg: "删除系统日志失败"
    };
  });
}

// 批量删除系统日志
export function batchDelSystemLog(infoIds) {
  return request({
    url: '/api/v1/monitor/logininfor/batch',
    method: 'delete',
    data: { infoIds: infoIds }
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "批量删除成功"
      };
    }
    return res;
  }).catch(err => {
    console.error("批量删除系统日志出错:", err);
    return {
      code: 500,
      msg: "批量删除系统日志失败"
    };
  });
}

// 清空系统日志
export function cleanSystemLog() {
  return request({
    url: '/api/v1/monitor/logininfor/clean',
    method: 'delete'
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "清空成功"
      };
    }
    return res;
  }).catch(err => {
    console.error("清空系统日志出错:", err);
    return {
      code: 500,
      msg: "清空系统日志失败"
    };
  });
}

// 导出系统日志
export function exportSystemLog(query) {
  return request({
    url: '/api/v1/monitor/logininfor/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  }).then(res => {
    return res;
  }).catch(err => {
    console.error("导出系统日志出错:", err);
    throw err;
  });
}

// 获取登录统计信息
export function getLoginStatistics(query) {
  return request({
    url: '/api/v1/monitor/logininfor/statistics',
    method: 'get',
    params: query
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        data: res.data || res
      };
    }
    return res;
  }).catch(err => {
    console.error("获取登录统计信息出错:", err);
    return {
      code: 500,
      msg: "获取登录统计信息失败",
      data: {
        totalLogins: 0,
        todayLogins: 0,
        successLogins: 0,
        failedLogins: 0
      }
    };
  });
}

// 获取登录趋势数据
export function getLoginTrend(query) {
  return request({
    url: '/api/v1/monitor/logininfor/trend',
    method: 'get',
    params: query
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        data: res.data || res
      };
    }
    return res;
  }).catch(err => {
    console.error("获取登录趋势数据出错:", err);
    return {
      code: 500,
      msg: "获取登录趋势数据失败",
      data: []
    };
  });
}

// 获取登录地区统计
export function getLoginLocationStats(query) {
  return request({
    url: '/api/v1/monitor/logininfor/location-stats',
    method: 'get',
    params: query
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        data: res.data || res
      };
    }
    return res;
  }).catch(err => {
    console.error("获取登录地区统计出错:", err);
    return {
      code: 500,
      msg: "获取登录地区统计失败",
      data: []
    };
  });
}