package com.wanyu.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 数据日志对象 sys_data_log
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public class SysDataLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志编号 */
    private Long logId;

    /** 表名 */
    @Excel(name = "表名")
    private String tableName;

    /** 操作类型 */
    @Excel(name = "操作类型", readConverterExp = "INSERT=新增,UPDATE=修改,DELETE=删除,SELECT=查询")
    private String operType;

    /** 主键值 */
    @Excel(name = "主键值")
    private String primaryKey;

    /** 操作前数据 */
    private String oldData;

    /** 操作后数据 */
    private String newData;

    /** 变更字段 */
    @Excel(name = "变更字段")
    private String changedFields;

    /** 操作用户 */
    @Excel(name = "操作用户")
    private String operUser;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 客户端IP */
    @Excel(name = "客户端IP")
    private String clientIp;

    /** 操作模块 */
    @Excel(name = "操作模块")
    private String operModule;

    /** 操作方法 */
    @Excel(name = "操作方法")
    private String operMethod;

    /** 请求URI */
    @Excel(name = "请求URI")
    private String requestUri;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String requestMethod;

    /** 操作状态 */
    @Excel(name = "操作状态", readConverterExp = "0=失败,1=成功")
    private String status;

    /** 错误消息 */
    @Excel(name = "错误消息")
    private String errorMsg;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setTableName(String tableName) 
    {
        this.tableName = tableName;
    }

    public String getTableName() 
    {
        return tableName;
    }
    public void setOperType(String operType) 
    {
        this.operType = operType;
    }

    public String getOperType() 
    {
        return operType;
    }
    public void setPrimaryKey(String primaryKey) 
    {
        this.primaryKey = primaryKey;
    }

    public String getPrimaryKey() 
    {
        return primaryKey;
    }
    public void setOldData(String oldData) 
    {
        this.oldData = oldData;
    }

    public String getOldData() 
    {
        return oldData;
    }
    public void setNewData(String newData) 
    {
        this.newData = newData;
    }

    public String getNewData() 
    {
        return newData;
    }
    public void setChangedFields(String changedFields) 
    {
        this.changedFields = changedFields;
    }

    public String getChangedFields() 
    {
        return changedFields;
    }
    public void setOperUser(String operUser) 
    {
        this.operUser = operUser;
    }

    public String getOperUser() 
    {
        return operUser;
    }
    public void setOperTime(Date operTime) 
    {
        this.operTime = operTime;
    }

    public Date getOperTime() 
    {
        return operTime;
    }
    public void setClientIp(String clientIp) 
    {
        this.clientIp = clientIp;
    }

    public String getClientIp() 
    {
        return clientIp;
    }
    public void setOperModule(String operModule) 
    {
        this.operModule = operModule;
    }

    public String getOperModule() 
    {
        return operModule;
    }
    public void setOperMethod(String operMethod) 
    {
        this.operMethod = operMethod;
    }

    public String getOperMethod() 
    {
        return operMethod;
    }
    public void setRequestUri(String requestUri) 
    {
        this.requestUri = requestUri;
    }

    public String getRequestUri() 
    {
        return requestUri;
    }
    public void setRequestMethod(String requestMethod) 
    {
        this.requestMethod = requestMethod;
    }

    public String getRequestMethod() 
    {
        return requestMethod;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setErrorMsg(String errorMsg) 
    {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() 
    {
        return errorMsg;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("tableName", getTableName())
            .append("operType", getOperType())
            .append("primaryKey", getPrimaryKey())
            .append("oldData", getOldData())
            .append("newData", getNewData())
            .append("changedFields", getChangedFields())
            .append("operUser", getOperUser())
            .append("operTime", getOperTime())
            .append("clientIp", getClientIp())
            .append("operModule", getOperModule())
            .append("operMethod", getOperMethod())
            .append("requestUri", getRequestUri())
            .append("requestMethod", getRequestMethod())
            .append("status", getStatus())
            .append("errorMsg", getErrorMsg())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
