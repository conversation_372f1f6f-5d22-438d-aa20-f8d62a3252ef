@echo off
echo 测试编译修复...
echo.

echo 1. 检查文件编码是否正确...
echo 检查 ProductAttribute.java...
findstr "属性名称" "C:\CKGLXT\warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\product\domain\ProductAttribute.java"
if %errorlevel% neq 0 (
    echo 文件编码可能有问题！
) else (
    echo 文件编码正常
)

echo 检查 ProductAttributeOption.java...
findstr "选项值" "C:\CKGLXT\warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\product\domain\ProductAttributeOption.java"
if %errorlevel% neq 0 (
    echo 文件编码可能有问题！
) else (
    echo 文件编码正常
)

echo.
echo 2. 尝试编译...
cd /d "C:\CKGLXT\warehouse-system\backend"
mvn clean compile -q
if %errorlevel% neq 0 (
    echo 编译失败！请检查错误信息
    pause
    exit /b 1
) else (
    echo 编译成功！
)

echo.
echo 3. 启动后端服务...
start "后端服务" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"

echo 4. 等待服务启动...
timeout /t 15 >nul

echo 5. 测试物品属性API...
cd /d "C:\CKGLXT"
echo 测试获取物品属性列表...
curl -X GET "http://localhost:8080/product/attribute/list" -H "Content-Type: application/json"
echo.

echo.
echo 编译修复测试完成！
pause