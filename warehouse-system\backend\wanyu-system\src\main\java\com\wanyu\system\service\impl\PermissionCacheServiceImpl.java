package com.wanyu.system.service.impl;

import java.util.Set;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CompletableFuture;

import org.springframework.stereotype.Service;

import com.wanyu.common.constant.CacheConstants;
import com.wanyu.system.service.IPermissionCacheService;

/**
 * 权限缓存服务实现
 * 
 * <AUTHOR>
 */
@Service
public class PermissionCacheServiceImpl implements IPermissionCacheService {
    
    // 内存缓存用于存储权限数据
    private static final Map<String, Object> PERMISSION_CACHE = new ConcurrentHashMap<>();
    
    // 缓存过期时间（24小时）
    private final long EXPIRE_TIME = 24;
    
    /**
     * 获取用户权限缓存
     */
    @Override
    public Set<String> getUserPermissions(Long userId) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_PERMISSIONS, userId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }
    
    /**
     * 设置用户权限缓存
     */
    @Override
    public void setUserPermissions(Long userId, Set<String> permissions) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_PERMISSIONS, userId);
        PERMISSION_CACHE.put(cacheKey, permissions);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRE_TIME * 60 * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }
    
    /**
     * 移除用户权限缓存
     */
    @Override
    public void removeUserPermissions(Long userId) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_PERMISSIONS, userId);
        PERMISSION_CACHE.remove(cacheKey);
    }
    
    /**
     * 获取用户数据权限缓存
     */
    @Override
    public Set<String> getUserDataPermissions(Long userId) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_DATA_PERMISSIONS, userId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }
    
    /**
     * 设置用户数据权限缓存
     */
    @Override
    public void setUserDataPermissions(Long userId, Set<String> dataPermissions) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_DATA_PERMISSIONS, userId);
        PERMISSION_CACHE.put(cacheKey, dataPermissions);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRE_TIME * 60 * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }
    
    /**
     * 移除用户数据权限缓存
     */
    @Override
    public void removeUserDataPermissions(Long userId) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_DATA_PERMISSIONS, userId);
        PERMISSION_CACHE.remove(cacheKey);
    }
    
    /**
     * 获取用户API权限缓存
     */
    @Override
    public Set<String> getUserApiPermissions(Long userId) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_API_PERMISSIONS, userId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }
    
    /**
     * 设置用户API权限缓存
     */
    @Override
    public void setUserApiPermissions(Long userId, Set<String> apiPermissions) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_API_PERMISSIONS, userId);
        PERMISSION_CACHE.put(cacheKey, apiPermissions);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRE_TIME * 60 * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }
    
    /**
     * 移除用户API权限缓存
     */
    @Override
    public void removeUserApiPermissions(Long userId) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_API_PERMISSIONS, userId);
        PERMISSION_CACHE.remove(cacheKey);
    }
    
    /**
     * 获取用户仓库权限缓存
     */
    @Override
    public Set<Long> getUserWarehousePermissions(Long userId) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_WAREHOUSE_PERMISSIONS, userId);
        return (Set<Long>) PERMISSION_CACHE.get(cacheKey);
    }
    
    /**
     * 设置用户仓库权限缓存
     */
    @Override
    public void setUserWarehousePermissions(Long userId, Set<Long> warehousePermissions) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_WAREHOUSE_PERMISSIONS, userId);
        PERMISSION_CACHE.put(cacheKey, warehousePermissions);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRE_TIME * 60 * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }
    
    /**
     * 移除用户仓库权限缓存
     */
    @Override
    public void removeUserWarehousePermissions(Long userId) {
        String cacheKey = getCacheKey(CacheConstants.SYS_USER_WAREHOUSE_PERMISSIONS, userId);
        PERMISSION_CACHE.remove(cacheKey);
    }
    
    /**
     * 清除所有权限缓存
     */
    @Override
    public void clearAllPermissions() {
        // 清空所有权限缓存
        PERMISSION_CACHE.clear();
    }
    
    /**
     * 获取缓存键值
     * 
     * @param prefix 前缀
     * @param userId 用户ID
     * @return 缓存键值
     */
    private String getCacheKey(String prefix, Long id) {
        return prefix + ":" + id;
    }
}