<template>
  <div class="app-container">
    <!-- 统计信息卡片 -->
    <LogManagement
      :statistics-data="statisticsData"
      :show-trend="true"
      trend-title="出入库操作趋势"
      :trend-data="trendData"
      :trend-period="trendPeriod"
      :chart-config="chartConfig"
      :quick-filters="quickFilters"
      :main-actions="mainActions"
      :batch-actions="batchActions"
      :extra-actions="extraActions"
      :show-search.sync="showSearch"
      @period-change="handlePeriodChange"
      @quick-filter="handleQuickFilter"
      @main-action="handleMainAction"
      @batch-action="handleBatchAction"
      @refresh="handleRefresh"
    />

    <!-- 高级搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="操作类型" prop="operationType">
        <el-select v-model="queryParams.operationType" placeholder="请选择操作类型" clearable>
          <el-option label="入库" value="IN" />
          <el-option label="出库" value="OUT" />
          <el-option label="调拨" value="TRANSFER" />
          <el-option label="调整" value="ADJUST" />
          <el-option label="盘点" value="CHECK" />
        </el-select>
      </el-form-item>
      <el-form-item label="仓库" prop="warehouseId">
        <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable>
          <el-option
            v-for="warehouse in warehouseOptions"
            :key="warehouse.warehouseId"
            :label="warehouse.warehouseName"
            :value="warehouse.warehouseId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作人员" prop="operator">
        <el-input
          v-model="queryParams.operator"
          placeholder="请输入操作人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['log:stock:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="inventoryLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="日志编号" align="center" prop="logId" width="80" />
      <el-table-column label="操作类型" align="center" prop="operationType" width="100">
        <template slot-scope="scope">
          <el-tag :type="getOperationTypeTagType(scope.row.operationType)">
            {{ getOperationTypeName(scope.row.operationType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="仓库名称" align="center" prop="warehouseName" :show-overflow-tooltip="true" />
      <el-table-column label="商品名称" align="center" prop="productName" :show-overflow-tooltip="true" />
      <el-table-column label="商品编码" align="center" prop="productCode" width="120" />
      <el-table-column label="数量变化" align="center" prop="quantityChange" width="100">
        <template slot-scope="scope">
          <span :class="scope.row.quantityChange > 0 ? 'text-success' : 'text-danger'">
            {{ scope.row.quantityChange > 0 ? '+' : '' }}{{ scope.row.quantityChange }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作前库存" align="center" prop="beforeQuantity" width="100" />
      <el-table-column label="操作后库存" align="center" prop="afterQuantity" width="100" />
      <el-table-column label="操作人员" align="center" prop="operator" width="100" />
      <el-table-column label="操作时间" align="center" prop="operationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.operationTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['log:stock:query']"
          >详细</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 出入库日志详细 -->
    <el-dialog title="出入库日志详细" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="120px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作类型：">
              <el-tag :type="getOperationTypeTagType(form.operationType)">
                {{ getOperationTypeName(form.operationType) }}
              </el-tag>
            </el-form-item>
            <el-form-item label="仓库名称：">{{ form.warehouseName }}</el-form-item>
            <el-form-item label="商品名称：">{{ form.productName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品编码：">{{ form.productCode }}</el-form-item>
            <el-form-item label="操作人员：">{{ form.operator }}</el-form-item>
            <el-form-item label="操作时间：">{{ parseTime(form.operationTime) }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="数量变化：">
              <span :class="form.quantityChange > 0 ? 'text-success' : 'text-danger'">
                {{ form.quantityChange > 0 ? '+' : '' }}{{ form.quantityChange }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作前库存：">{{ form.beforeQuantity }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作后库存：">{{ form.afterQuantity }}</el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.remark">
            <el-form-item label="备注信息：">
              <el-input type="textarea" :rows="3" v-model="form.remark" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInventoryLog, delInventoryLog } from "@/api/inventory/log";
import { optionselect as listWarehouse } from "@/api/system/warehouse";
import LogManagement from '@/components/LogManagement/index.vue';

export default {
  name: "StockLog",
  components: {
    LogManagement
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      inventoryLogList: [],
      // 仓库选项
      warehouseOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        operationType: undefined,
        warehouseId: undefined,
        productName: undefined,
        operator: undefined
      },
      // 统计数据
      statisticsData: [],
      // 趋势数据
      trendData: [],
      trendPeriod: '7d',
      // 图表配置
      chartConfig: {
        series: [
          {
            name: '入库操作',
            dataKey: 'inOperations',
            type: 'line',
            color: '#67C23A',
            areaStyle: {
              startColor: 'rgba(103, 194, 58, 0.3)',
              endColor: 'rgba(103, 194, 58, 0.1)'
            }
          },
          {
            name: '出库操作',
            dataKey: 'outOperations',
            type: 'line',
            color: '#F56C6C',
            areaStyle: {
              startColor: 'rgba(245, 108, 108, 0.3)',
              endColor: 'rgba(245, 108, 108, 0.1)'
            }
          },
          {
            name: '调拨操作',
            dataKey: 'transferOperations',
            type: 'line',
            color: '#409EFF',
            areaStyle: {
              startColor: 'rgba(64, 158, 255, 0.3)',
              endColor: 'rgba(64, 158, 255, 0.1)'
            }
          }
        ],
        yAxisName: '操作次数'
      },
      // 快速筛选
      quickFilters: [
        { key: 'today', label: '今日', icon: 'el-icon-date' },
        { key: 'week', label: '本周', icon: 'el-icon-date' },
        { key: 'month', label: '本月', icon: 'el-icon-date' },
        { key: 'in', label: '入库操作', icon: 'el-icon-upload' },
        { key: 'out', label: '出库操作', icon: 'el-icon-download' },
        { key: 'transfer', label: '调拨操作', icon: 'el-icon-sort' }
      ],
      // 主要操作
      mainActions: [
        {
          key: 'export',
          label: '导出Excel',
          type: 'warning',
          icon: 'el-icon-download',
          permission: 'log:stock:export'
        }
      ],
      // 批量操作
      batchActions: [
        {
          key: 'batchDelete',
          label: '批量删除',
          icon: 'el-icon-delete',
          permission: 'log:stock:remove'
        }
      ],
      // 额外操作
      extraActions: [],
      // 日期选择器配置
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    };
  },
  created() {
    this.initData();
  },
  methods: {
    /** 初始化数据 */
    async initData() {
      try {
        await Promise.all([
          this.getWarehouseOptions(),
          this.getStatistics(),
          this.getTrendData()
        ]);
        this.getList();
      } catch (error) {
        console.error('初始化数据失败:', error);
      }
    },

    /** 获取仓库选项 */
    getWarehouseOptions() {
      return listWarehouse().then(response => {
        this.warehouseOptions = response.rows || [];
      }).catch(error => {
        console.error('获取仓库列表失败:', error);
        this.warehouseOptions = [];
      });
    },

    /** 查询出入库日志 */
    getList() {
      this.loading = true;
      const params = this.addDateRange({...this.queryParams}, this.dateRange);

      listInventoryLog(params).then(response => {
        this.inventoryLogList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.$message.error('查询出入库日志失败，请稍后重试');
        // 使用模拟数据
        this.inventoryLogList = this.generateMockData();
        this.total = this.inventoryLogList.length;
      });
    },

    /** 生成模拟数据 */
    generateMockData() {
      const mockData = [];
      const operations = ['IN', 'OUT', 'TRANSFER', 'CHECK'];
      const warehouses = ['主仓库', '分仓库A', '分仓库B'];
      const products = ['商品A', '商品B', '商品C', '商品D'];
      
      for (let i = 1; i <= 20; i++) {
        const operationType = operations[Math.floor(Math.random() * operations.length)];
        const quantityChange = operationType === 'IN' ? 
          Math.floor(Math.random() * 100) + 1 : 
          -(Math.floor(Math.random() * 50) + 1);
        
        mockData.push({
          logId: i,
          operationType: operationType,
          warehouseName: warehouses[Math.floor(Math.random() * warehouses.length)],
          productName: products[Math.floor(Math.random() * products.length)],
          productCode: `P${String(i).padStart(4, '0')}`,
          quantityChange: quantityChange,
          beforeQuantity: Math.floor(Math.random() * 500),
          afterQuantity: Math.floor(Math.random() * 500) + quantityChange,
          operator: '管理员',
          operationTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
          remark: `${operationType === 'IN' ? '入库' : operationType === 'OUT' ? '出库' : '调拨'}操作`
        });
      }
      
      return mockData;
    },

    /** 获取统计数据 */
    getStatistics() {
      // 模拟统计数据
      this.statisticsData = [
        { title: '今日入库', value: 45, icon: 'el-icon-upload', color: '#67C23A' },
        { title: '今日出库', value: 32, icon: 'el-icon-download', color: '#F56C6C' },
        { title: '调拨操作', value: 8, icon: 'el-icon-sort', color: '#409EFF' },
        { title: '盘点操作', value: 3, icon: 'el-icon-view', color: '#E6A23C' }
      ];
      return Promise.resolve();
    },

    /** 获取趋势数据 */
    getTrendData() {
      // 生成出入库趋势数据
      const days = this.trendPeriod === '7d' ? 7 : (this.trendPeriod === '30d' ? 30 : 90);
      const dates = [];
      
      // 生成日期范围
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(this.parseTime(date, '{y}-{m}-{d}'));
      }
      
      // 基于统计数据生成趋势
      const baseIn = this.statisticsData.find(item => item.title === '今日入库')?.value || 45;
      const baseOut = this.statisticsData.find(item => item.title === '今日出库')?.value || 32;
      const baseTransfer = this.statisticsData.find(item => item.title === '调拨操作')?.value || 8;
      
      this.trendData = dates.map((date, index) => {
        const factor = 0.6 + Math.random() * 0.8;
        const dayFactor = index < days - 1 ? 0.7 + (index / days) * 0.6 : 1;
        
        return {
          date: date,
          inOperations: Math.floor(baseIn * factor * dayFactor),
          outOperations: Math.floor(baseOut * factor * dayFactor),
          transferOperations: Math.floor(baseTransfer * factor * dayFactor)
        };
      });
      
      return Promise.resolve();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 刷新数据 */
    handleRefresh() {
      this.getList();
      this.getStatistics();
      this.getTrendData();
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.logId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = { ...row };
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const logIds = row ? [row.logId] : this.ids;
      this.$modal.confirm('是否确认删除选中的出入库日志数据项？').then(() => {
        return delInventoryLog(logIds.join(','));
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
        this.$modal.msgError("删除失败，请稍后重试");
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出当前筛选条件下的出入库日志数据？').then(() => {
        const params = this.addDateRange({...this.queryParams}, this.dateRange);
        delete params.pageNum;
        delete params.pageSize;

        this.download('inventory/log/export', params, `stock_log_${new Date().getTime()}.xlsx`);
      }).catch(() => {});
    },

    /** 快速筛选处理 */
    handleQuickFilter(filterKey) {
      const today = new Date();
      const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      
      switch (filterKey) {
        case 'today':
          this.dateRange = [
            this.parseTime(today, '{y}-{m}-{d}'),
            this.parseTime(today, '{y}-{m}-{d}')
          ];
          break;
        case 'week':
          this.dateRange = [
            this.parseTime(startOfWeek, '{y}-{m}-{d}'),
            this.parseTime(today, '{y}-{m}-{d}')
          ];
          break;
        case 'month':
          this.dateRange = [
            this.parseTime(startOfMonth, '{y}-{m}-{d}'),
            this.parseTime(today, '{y}-{m}-{d}')
          ];
          break;
        case 'in':
          this.queryParams.operationType = 'IN';
          break;
        case 'out':
          this.queryParams.operationType = 'OUT';
          break;
        case 'transfer':
          this.queryParams.operationType = 'TRANSFER';
          break;
        default:
          break;
      }
      this.handleQuery();
    },

    /** 主要操作处理 */
    handleMainAction(actionKey) {
      switch (actionKey) {
        case 'export':
          this.handleExport();
          break;
        default:
          break;
      }
    },

    /** 批量操作处理 */
    handleBatchAction(actionKey) {
      switch (actionKey) {
        case 'batchDelete':
          this.handleDelete();
          break;
        default:
          break;
      }
    },

    /** 周期变化处理 */
    handlePeriodChange(period) {
      this.trendPeriod = period;
      this.getTrendData();
    },

    /** 获取操作类型名称 */
    getOperationTypeName(type) {
      // 处理数字类型的操作类型（兼容旧数据）
      const numberMap = {
        '1': '入库',
        '2': '出库',
        '3': '调拨',
        '4': '调整',
        '5': '盘点'
      };
      
      // 处理字符串类型的操作类型
      const stringMap = {
        'IN': '入库',
        'OUT': '出库',
        'TRANSFER': '调拨',
        'ADJUST': '调整',
        'CHECK': '盘点'
      };
      
      // 先尝试数字映射，再尝试字符串映射
      return numberMap[type] || stringMap[type] || type;
    },

    /** 获取操作类型标签类型 */
    getOperationTypeTagType(type) {
      const typeMap = {
        'IN': 'success',
        'OUT': 'danger',
        'TRANSFER': 'primary',
        'CHECK': 'warning'
      };
      return typeMap[type] || '';
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.text-success {
  color: #67C23A;
  font-weight: bold;
}

.text-danger {
  color: #F56C6C;
  font-weight: bold;
}
</style>