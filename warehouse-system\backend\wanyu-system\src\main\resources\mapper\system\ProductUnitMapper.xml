<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.ProductUnitMapper">

    <resultMap type="ProductUnit" id="ProductUnitResult">
        <result property="unitId"    column="unit_id"    />
        <result property="unitName"    column="unit_name"    />
        <result property="unitCode"    column="unit_code"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProductUnitVo">
        select unit_id, unit_name, unit_code, status, remark, create_by, create_time, update_by, update_time from wms_unit
    </sql>

    <select id="selectProductUnitList" parameterType="ProductUnit" resultMap="ProductUnitResult">
        <include refid="selectProductUnitVo"/>
        <where>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="unitCode != null  and unitCode != ''"> and unit_code like concat('%', #{unitCode}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectProductUnitByUnitId" parameterType="Long" resultMap="ProductUnitResult">
        <include refid="selectProductUnitVo"/>
        where unit_id = #{unitId}
    </select>

    <insert id="insertProductUnit" parameterType="ProductUnit" useGeneratedKeys="true" keyProperty="unitId">
        insert into wms_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitName != null">unit_name,</if>
            <if test="unitCode != null">unit_code,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitName != null">#{unitName},</if>
            <if test="unitCode != null">#{unitCode},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProductUnit" parameterType="ProductUnit">
        update wms_unit
        <trim prefix="SET" suffixOverrides=",">
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="unitCode != null">unit_code = #{unitCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where unit_id = #{unitId}
    </update>

    <delete id="deleteProductUnitByUnitId" parameterType="Long">
        delete from wms_unit where unit_id = #{unitId}
    </delete>

    <delete id="deleteProductUnitByUnitIds" parameterType="String">
        delete from wms_unit where unit_id in
        <foreach item="unitId" collection="array" open="(" separator="," close=")">
            #{unitId}
        </foreach>
    </delete>
</mapper>
