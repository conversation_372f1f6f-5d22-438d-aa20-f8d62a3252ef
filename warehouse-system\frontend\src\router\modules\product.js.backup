import Layout from '@/layout'

// 物品管理路由
export default {
  path: '/product',
  component: Layout,
  redirect: '/product/info',
  name: 'Product',
  meta: {
    title: '物品管理',
    icon: 'shopping'
  },
  children: [
    {
      path: 'info',
      component: () => import('@/views/product/info/index'),
      name: 'ProductInfo',
      meta: { title: '物品信息', icon: 'list' }
    },
    {
      path: 'category',
      component: () => import('@/views/product/category/index'),
      name: 'ProductCategory',
      meta: { title: '物品分类', icon: 'tree' }
    },
    {
      path: 'unit',
      component: () => import('@/views/product/unit/index'),
      name: 'ProductUnit',
      meta: { title: '物品单位', icon: 'component' }
    },
    {
      path: 'spec',
      component: () => import('@/views/product/spec/index'),
      name: 'ProductSpec',
      meta: { title: '物品规格', icon: 'example' }
    },

    {
      path: 'barcode',
      component: () => import('@/views/product/barcode/index'),
      name: 'ProductBarcode',
      meta: { title: '物品条码', icon: 'barcode' }
    },

  ]
}