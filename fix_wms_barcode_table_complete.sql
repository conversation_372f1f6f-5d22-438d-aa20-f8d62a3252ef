-- 修复物品条码表名从product_barcode更改为wms_barcode的完整脚本
-- 执行日期: 2025-08-31
-- 说明: 统一使用wms_前缀的表名规范

-- 1. 检查并创建wms_barcode表
CREATE TABLE IF NOT EXISTS `wms_barcode` (
  `barcode_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '条码ID',
  `product_id` bigint(20) NOT NULL COMMENT '物品ID',
  `product_name` varchar(100) DEFAULT NULL COMMENT '物品名称',
  `barcode_content` varchar(200) NOT NULL COMMENT '条码内容',
  `barcode_type` varchar(20) NOT NULL DEFAULT 'CODE128' COMMENT '条码类型(CODE128,EAN13,QR_CODE等)',
  `barcode_image` varchar(500) DEFAULT NULL COMMENT '条码图片路径',
  `template_id` bigint(20) DEFAULT NULL COMMENT '条码模板ID',
  `is_main` char(1) DEFAULT '0' COMMENT '是否主条码(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`barcode_id`),
  UNIQUE KEY `uk_barcode_content` (`barcode_content`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_barcode_type` (`barcode_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品条码表';

-- 2. 检查并创建wms_barcode_template表
CREATE TABLE IF NOT EXISTS `wms_barcode_template` (
  `template_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_type` varchar(20) NOT NULL COMMENT '模板类型',
  `template_width` int(11) DEFAULT 100 COMMENT '模板宽度(mm)',
  `template_height` int(11) DEFAULT 50 COMMENT '模板高度(mm)',
  `barcode_width` int(11) DEFAULT 80 COMMENT '条码宽度(mm)',
  `barcode_height` int(11) DEFAULT 30 COMMENT '条码高度(mm)',
  `font_size` int(11) DEFAULT 12 COMMENT '字体大小',
  `show_text` char(1) DEFAULT '1' COMMENT '是否显示文字(0否 1是)',
  `show_product_name` char(1) DEFAULT '1' COMMENT '是否显示物品名称(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品条码模板表';

-- 3. 如果存在product_barcode表，迁移数据到wms_barcode表
-- 注意：这里使用存储过程来安全地处理数据迁移
DELIMITER $$

CREATE PROCEDURE MigrateProductBarcodeData()
BEGIN
    DECLARE table_exists INT DEFAULT 0;
    
    -- 检查product_barcode表是否存在
    SELECT COUNT(*) INTO table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'product_barcode';
    
    IF table_exists > 0 THEN
        -- 迁移数据（假设字段结构相似）
        INSERT IGNORE INTO wms_barcode (
            product_id, product_name, barcode_content, barcode_type, 
            barcode_image, template_id, is_main, status, remark, 
            create_by, create_time, update_by, update_time
        )
        SELECT 
            product_id, product_name, barcode_content, 
            IFNULL(barcode_type, 'CODE128') as barcode_type,
            barcode_image, template_id, 
            IFNULL(is_main, '0') as is_main,
            IFNULL(status, '0') as status,
            remark, create_by, create_time, update_by, update_time
        FROM product_barcode;
        
        -- 输出迁移结果
        SELECT CONCAT('已迁移 ', ROW_COUNT(), ' 条记录从 product_barcode 到 wms_barcode') as migration_result;
    ELSE
        SELECT '未找到 product_barcode 表，无需迁移数据' as migration_result;
    END IF;
END$$

DELIMITER ;

-- 执行数据迁移
CALL MigrateProductBarcodeData();

-- 删除临时存储过程
DROP PROCEDURE IF EXISTS MigrateProductBarcodeData;

-- 4. 插入默认条码模板（如果不存在）
INSERT IGNORE INTO `wms_barcode_template` (`template_name`, `template_type`, `template_width`, `template_height`, `barcode_width`, `barcode_height`, `font_size`, `show_text`, `show_product_name`, `status`, `create_by`, `create_time`) VALUES
('标准模板', 'CODE128', 100, 50, 80, 30, 12, '1', '1', '0', 'admin', NOW()),
('小型模板', 'CODE128', 60, 30, 50, 20, 10, '1', '0', '0', 'admin', NOW()),
('二维码模板', 'QR_CODE', 50, 50, 40, 40, 10, '1', '1', '0', 'admin', NOW());

-- 5. 检查并插入条码类型字典数据
-- 检查字典类型是否存在
INSERT IGNORE INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('物品条码类型', 'wms_barcode_type', '0', 'admin', NOW(), '物品条码类型列表');

-- 插入条码类型字典数据项
INSERT IGNORE INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, 'CODE128', 'CODE128', 'wms_barcode_type', '', 'primary', 'Y', '0', 'admin', NOW(), 'CODE128条码'),
(2, 'EAN13', 'EAN13', 'wms_barcode_type', '', 'success', 'N', '0', 'admin', NOW(), 'EAN13条码'),
(3, 'EAN8', 'EAN8', 'wms_barcode_type', '', 'info', 'N', '0', 'admin', NOW(), 'EAN8条码'),
(4, 'UPC_A', 'UPC_A', 'wms_barcode_type', '', 'warning', 'N', '0', 'admin', NOW(), 'UPC-A条码'),
(5, '二维码', 'QR_CODE', 'wms_barcode_type', '', 'danger', 'N', '0', 'admin', NOW(), 'QR二维码');

-- 6. 验证表结构和数据
SELECT 'wms_barcode表结构验证:' as verification;
DESCRIBE wms_barcode;

SELECT 'wms_barcode_template表结构验证:' as verification;
DESCRIBE wms_barcode_template;

SELECT 'wms_barcode数据统计:' as verification;
SELECT COUNT(*) as barcode_count FROM wms_barcode;

SELECT 'wms_barcode_template数据统计:' as verification;
SELECT COUNT(*) as template_count FROM wms_barcode_template;

SELECT '条码类型字典数据统计:' as verification;
SELECT COUNT(*) as dict_count FROM sys_dict_data WHERE dict_type = 'wms_barcode_type';

-- 7. 输出完成信息
SELECT '=== 物品条码表修复完成 ===' as completion_message;
SELECT '1. wms_barcode表已创建/验证' as step1;
SELECT '2. wms_barcode_template表已创建/验证' as step2;
SELECT '3. 数据迁移已完成（如果存在旧表）' as step3;
SELECT '4. 默认模板已插入' as step4;
SELECT '5. 字典数据已插入' as step5;
SELECT '请重新编译并启动后端服务以使更改生效' as next_step;