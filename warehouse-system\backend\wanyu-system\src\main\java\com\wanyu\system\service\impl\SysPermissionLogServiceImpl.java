package com.wanyu.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.wanyu.common.core.domain.model.LoginUser;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.system.domain.SysPermissionLog;
import com.wanyu.system.domain.SysPermissionTemplateLog;
import com.wanyu.system.mapper.SysPermissionLogMapper;
import com.wanyu.system.service.ISysPermissionLogService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 权限变更日志Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysPermissionLogServiceImpl implements ISysPermissionLogService {
    
    @Autowired
    private SysPermissionLogMapper permissionLogMapper;
    
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private com.wanyu.system.service.ISysUserService userService;
    
    /**
     * 查询权限变更日志
     *
     * @param logId 权限变更日志ID
     * @return 权限变更日志
     */
    @Override
    public SysPermissionLog selectSysPermissionLogByLogId(Long logId) {
        return permissionLogMapper.selectSysPermissionLogByLogId(logId);
    }

    /**
     * 查询权限变更日志列表
     *
     * @param permissionLog 权限变更日志
     * @return 权限变更日志集合
     */
    @Override
    public List<SysPermissionLog> selectSysPermissionLogList(SysPermissionLog permissionLog) {
        List<SysPermissionLog> logList = permissionLogMapper.selectSysPermissionLogList(permissionLog);
        
        // 转换用户名为真实姓名
        if (logList != null && !logList.isEmpty()) {
            for (SysPermissionLog log : logList) {
                convertUserNames(log);
            }
        }
        
        return logList;
    }

    /**
     * 记录权限变更日志
     *
     * @param operationType 操作类型（如：GRANT, REVOKE, MODIFY）
     * @param targetType 目标类型（如：ROLE, USER, WAREHOUSE）
     * @param targetId 目标ID
     * @param permissionType 权限类型（如：MENU, DATA, WAREHOUSE）
     * @param oldValue 变更前的值
     * @param newValue 变更后的值
     * @param remark 备注
     * @return 结果
     */
    @Transactional
    public int logPermissionChange(String operationType, String targetType, Long targetId,
            String permissionType, Object oldValue, Object newValue, String remark) {
        try {
            SysPermissionLog log = new SysPermissionLog();
            
            // 设置基本信息
            log.setOperationType(operationType);
            log.setTargetType(targetType);
            log.setTargetId(targetId);
            log.setPermissionType(permissionType);
            
            // 获取当前操作人信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            log.setOperatorId(loginUser.getUserId());
            log.setOperatorName(loginUser.getUsername());
            
            // 设置操作时间
            log.setOperTime(DateUtils.getNowDate());
            
            // 转换并设置变更前后的值
            log.setOldValue(convertValueToString(oldValue));
            log.setNewValue(convertValueToString(newValue));
            
            // 计算并设置差异
            log.setChangeDiff(calculateDiff(oldValue, newValue));
            
            // 设置备注
            log.setRemark(remark);
            
            // 设置IP地址
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            log.setIp(SecurityUtils.getIpAddr(request));
            
            return permissionLogMapper.insertSysPermissionLog(log);
        } catch (Exception e) {
            // 记录日志时的异常不应影响主要业务流程
            return 0;
        }
    }

    /**
     * 删除权限变更日志
     *
     * @param logId 需要删除的权限变更日志ID
     * @return 结果
     */
    @Override
    public int deleteSysPermissionLogByLogId(Long logId) {
        return permissionLogMapper.deleteSysPermissionLogByLogId(logId);
    }

    /**
     * 批量删除权限变更日志
     *
     * @param logIds 需要删除的权限变更日志ID数组
     * @return 结果
     */
    @Override
    public int deleteSysPermissionLogByLogIds(Long[] logIds) {
        return permissionLogMapper.deleteSysPermissionLogByLogIds(logIds);
    }

    /**
     * 清空权限变更日志
     */
    @Override
    public void cleanSysPermissionLog() {
        permissionLogMapper.cleanSysPermissionLog();
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String convertValueToString(Object value) {
        if (value == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(value);
        } catch (Exception e) {
            return value.toString();
        }
    }

    /**
     * 计算变更前后的差异
     */
    @SuppressWarnings("unchecked")
    private String calculateDiff(Object oldValue, Object newValue) {
        try {
            // 如果是Map类型，计算详细的差异
            if (oldValue instanceof Map && newValue instanceof Map) {
                Map<String, Object> oldMap = (Map<String, Object>) oldValue;
                Map<String, Object> newMap = (Map<String, Object>) newValue;
                
                // 计算新增的键
                List<String> added = newMap.keySet().stream()
                        .filter(key -> !oldMap.containsKey(key))
                        .collect(Collectors.toList());
                
                // 计算删除的键
                List<String> removed = oldMap.keySet().stream()
                        .filter(key -> !newMap.containsKey(key))
                        .collect(Collectors.toList());
                
                // 计算修改的键
                List<String> modified = oldMap.keySet().stream()
                        .filter(key -> newMap.containsKey(key) && !oldMap.get(key).equals(newMap.get(key)))
                        .collect(Collectors.toList());
                
                StringBuilder diff = new StringBuilder();
                if (!added.isEmpty()) {
                    diff.append("新增: ").append(String.join(", ", added)).append("; ");
                }
                if (!removed.isEmpty()) {
                    diff.append("删除: ").append(String.join(", ", removed)).append("; ");
                }
                if (!modified.isEmpty()) {
                    diff.append("修改: ").append(String.join(", ", modified));
                }
                
                return diff.toString();
            }
            
            // 如果是List类型，计算数量变化
            if (oldValue instanceof List && newValue instanceof List) {
                List<?> oldList = (List<?>) oldValue;
                List<?> newList = (List<?>) newValue;
                
                int oldSize = oldList.size();
                int newSize = newList.size();
                
                if (oldSize == newSize) {
                    return "数量未变化: " + oldSize;
                } else {
                    return String.format("数量从 %d 变更为 %d", oldSize, newSize);
                }
            }
            
            // 其他类型，直接比较字符串
            String oldStr = convertValueToString(oldValue);
            String newStr = convertValueToString(newValue);
            
            if (StringUtils.equals(oldStr, newStr)) {
                return "无变化";
            } else {
                return "从 [" + oldStr + "] 变更为 [" + newStr + "]";
            }
            
        } catch (Exception e) {
            return "差异计算失败";
        }
    }

    /**
     * 查询指定时间范围内的权限变更日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 权限变更日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogByTimeRange(Date startTime, Date endTime) {
        return permissionLogMapper.selectSysPermissionLogByTimeRange(startTime, endTime);
    }

    /**
     * 查询指定操作人的权限变更日志
     *
     * @param operatorId 操作人ID
     * @return 权限变更日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogByOperator(Long operatorId) {
        return permissionLogMapper.selectSysPermissionLogByOperator(operatorId);
    }

    /**
     * 查询指定目标的权限变更日志
     *
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 权限变更日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogByTarget(String targetType, Long targetId) {
        return permissionLogMapper.selectSysPermissionLogByTarget(targetType, targetId);
    }

    /**
     * 查询权限模板应用日志列表
     *
     * @param templateLog 权限模板应用日志
     * @return 权限模板应用日志集合
     */
    @Override
    public List<SysPermissionTemplateLog> selectTemplateLogList(SysPermissionTemplateLog templateLog) {
        return permissionLogMapper.selectTemplateLogList(templateLog);
    }

    @Override
    public int insertTemplateLog(Long templateId, String templateName, String targetType, Long targetId, 
            String targetName, String applyType, String status, String createBy, String remark) {
        SysPermissionTemplateLog log = new SysPermissionTemplateLog();
        log.setTemplateId(templateId);
        log.setTemplateName(templateName);
        log.setTargetType(targetType);
        log.setTargetId(targetId);
        log.setTargetName(targetName);
        log.setApplyType(applyType);
        log.setStatus(status);
        log.setCreateBy(createBy);
        log.setRemark(remark);
        log.setCreateTime(DateUtils.getNowDate());
        return permissionLogMapper.insertTemplateLog(log);
    }

    @Override
    public void recordPermissionLog(String permissionType, String permission, String method, 
            String url, String result, String failReason) {
        SysPermissionLog log = new SysPermissionLog();
        log.setPermissionType(permissionType);
        log.setPermission(permission);
        log.setRequestMethod(method);
        log.setRequestUrl(url);
        log.setResult(result);
        log.setFailReason(failReason);
        log.setOperTime(DateUtils.getNowDate());
        permissionLogMapper.insertSysPermissionLog(log);
    }

    @Override
    public int insertPermissionLog(Long userId, String userName, String operation, 
            String permission, String status) {
        SysPermissionLog log = new SysPermissionLog();
        log.setOperatorId(userId);
        log.setOperatorName(userName);
        log.setOperationType(operation);
        log.setPermissionType(permission);
        log.setStatus(status);
        log.setOperTime(DateUtils.getNowDate());
        return permissionLogMapper.insertSysPermissionLog(log);
    }

    @Override
    public int insertSysPermissionLog(SysPermissionLog permissionLog) {
        if (permissionLog.getOperTime() == null) {
            permissionLog.setOperTime(DateUtils.getNowDate());
        }
        if (StringUtils.isEmpty(permissionLog.getIp())) {
            try {
                HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
                permissionLog.setIp(SecurityUtils.getIpAddr(request));
            } catch (Exception e) {
                permissionLog.setIp("127.0.0.1");
            }
        }
        return permissionLogMapper.insertSysPermissionLog(permissionLog);
    }

    @Override
    public int updateSysPermissionLog(SysPermissionLog permissionLog) {
        return permissionLogMapper.updateSysPermissionLog(permissionLog);
    }

    /**
     * 转换用户名为真实姓名
     *
     * @param log 权限日志
     */
    private void convertUserNames(SysPermissionLog log) {
        if (log == null) {
            return;
        }

        // 转换用户名称
        if (log.getUserName() != null && !log.getUserName().isEmpty()) {
            log.setUserName(getRealName(log.getUserName()));
        }

        // 转换创建人
        if (log.getCreateBy() != null && !log.getCreateBy().isEmpty()) {
            log.setCreateBy(getRealName(log.getCreateBy()));
        }

        // 转换更新人
        if (log.getUpdateBy() != null && !log.getUpdateBy().isEmpty()) {
            log.setUpdateBy(getRealName(log.getUpdateBy()));
        }
    }

    /**
     * 根据用户名获取真实姓名
     */
    private String getRealName(String username) {
        if (username == null || username.isEmpty()) {
            return username;
        }
        try {
            com.wanyu.common.core.domain.entity.SysUser user = userService.selectUserByUserName(username);
            if (user != null) {
                // 优先使用真实姓名，其次使用昵称
                if (user.getRealName() != null && !user.getRealName().isEmpty()) {
                    return user.getRealName();
                } else if (user.getNickName() != null && !user.getNickName().isEmpty()) {
                    return user.getNickName();
                }
            }
        } catch (Exception e) {
            // 查询失败时返回原用户名
            System.err.println("查询用户失败: " + username + ", 错误: " + e.getMessage());
        }
        return username;
    }
}