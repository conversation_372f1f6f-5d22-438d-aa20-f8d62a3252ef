@echo off
chcp 65001 >nul
echo ========================================
echo 库区货架删除后重启系统
echo ========================================

echo.
echo 1. 清理后端编译缓存...
cd /d "C:\CKGLXT\warehouse-system\backend"
if exist "target" (
    rmdir /s /q "target"
    echo 已清理后端target目录
)

echo.
echo 2. 重新编译后端项目...
call mvn clean compile -Dmaven.test.skip=true
if %errorlevel% neq 0 (
    echo 后端编译失败，请检查代码
    pause
    exit /b 1
)

echo.
echo 3. 清理前端缓存...
cd /d "C:\CKGLXT\warehouse-system\frontend"
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache"
    echo 已清理前端缓存
)

echo.
echo 4. 重新构建前端项目...
call npm run build:prod
if %errorlevel% neq 0 (
    echo 前端构建失败，请检查代码
    pause
    exit /b 1
)

echo.
echo 5. 启动后端服务...
cd /d "C:\CKGLXT\warehouse-system\backend\wanyu-admin\target"
start "后端服务" java -jar wanyu-admin.jar --server.port=8080

echo 等待后端服务启动...
timeout /t 10 /nobreak >nul

echo.
echo 6. 启动前端服务...
cd /d "C:\CKGLXT\warehouse-system\frontend"
start "前端服务" npm run dev

echo.
echo ========================================
echo 系统重启完成！
echo ========================================
echo.
echo 后端服务: http://localhost:8080
echo 前端服务: http://localhost:8081
echo.
echo 请验证以下功能：
echo - 仓库管理菜单只显示"仓库信息"
echo - 系统正常登录和访问
echo - 其他功能模块正常工作
echo.
pause