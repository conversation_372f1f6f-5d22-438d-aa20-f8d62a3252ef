-- 设置连接字符集
SET NAMES utf8mb4;

-- 选择数据库
USE warehouse_system;

-- 修改数据库字符集
ALTER DATABASE warehouse_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改所有表的字符集（按依赖顺序）
ALTER TABLE sys_api_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_config CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_data_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_data_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_data_permission_dept CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_data_permission_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dept CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dept_data_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dept_permission_template CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dept_template CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dict_data CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_dict_type CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_error_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_error_log_new CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_hardware_fingerprint CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_job CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_job_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_license CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_license_application CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_license_feature CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_license_history CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_audit CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_data CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_error CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_login CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_security CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_log_type CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_logininfor CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_menu_backup CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_notice CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_oper_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_operation_log_new CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission_audit CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission_template CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission_template_category CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission_template_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission_template_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_permission_template_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_api CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_data_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_dept CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_template CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_template_api CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_template_data CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_template_dept CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_template_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_template_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_role_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_security_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_security_log_new CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_template_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_template_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_template_report CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_template_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_api CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_data_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_menu CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_permission_inherit CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_user_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_warehouse_auth CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE sys_warehouse_data_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_alert_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_alert_rule CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_backup_duplicate CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_check CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_check_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_in CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_in_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_log CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_out CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_out_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_transfer CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_inventory_transfer_detail CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_category CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_product CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_specification CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_unit CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_role_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_user_warehouse CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_warehouse_area CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_warehouse_rack CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_barcode CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_barcode_template CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改Quartz相关表的字符集（按依赖顺序）
ALTER TABLE qrtz_job_details CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_triggers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_simple_triggers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_cron_triggers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_simprop_triggers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_blob_triggers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_calendars CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_paused_trigger_grps CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_fired_triggers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_scheduler_state CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE qrtz_locks CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复表注释
ALTER TABLE `wms_barcode` COMMENT='物品条码表';
ALTER TABLE `wms_barcode_template` COMMENT='物品条码模板表';
ALTER TABLE `qrtz_blob_triggers` COMMENT='Blob类型触发器表';
ALTER TABLE `qrtz_calendars` COMMENT='调度器日历信息表';
ALTER TABLE `qrtz_cron_triggers` COMMENT='Cron类型触发器表';
ALTER TABLE `qrtz_fired_triggers` COMMENT='已触发的触发器表';
ALTER TABLE `qrtz_job_details` COMMENT='任务详情表';
ALTER TABLE `qrtz_locks` COMMENT='调度器锁表';
ALTER TABLE `qrtz_paused_trigger_grps` COMMENT='暂停的触发器组表';
ALTER TABLE `qrtz_scheduler_state` COMMENT='调度器状态表';
ALTER TABLE `qrtz_simple_triggers` COMMENT='简单触发器表';
ALTER TABLE `qrtz_simprop_triggers` COMMENT='复杂属性触发器表';
ALTER TABLE `qrtz_triggers` COMMENT='触发器表';
ALTER TABLE `sys_api_permission` COMMENT='API权限表';
ALTER TABLE `sys_config` COMMENT='参数配置表';
ALTER TABLE `sys_data_log` COMMENT='数据日志表';
ALTER TABLE `sys_data_permission` COMMENT='数据权限表';
ALTER TABLE `sys_data_permission_dept` COMMENT '部门数据权限表';
ALTER TABLE `sys_data_permission_warehouse` COMMENT '仓库数据权限表';
ALTER TABLE `sys_dept` COMMENT='部门表';
ALTER TABLE `sys_dept_data_permission` COMMENT='部门数据权限表';
ALTER TABLE `sys_dept_permission_template` COMMENT='部门权限模板表';
ALTER TABLE `sys_dept_template` COMMENT='部门权限模板关联表';
ALTER TABLE `sys_dict_data` COMMENT='字典数据表';
ALTER TABLE `sys_dict_type` COMMENT='字典类型表';
ALTER TABLE `sys_error_log` COMMENT='错误日志表';
ALTER TABLE `sys_error_log_new` COMMENT='新错误日志表';
ALTER TABLE `sys_hardware_fingerprint` COMMENT='硬件指纹表';
ALTER TABLE `sys_job` COMMENT='定时任务调度表';
ALTER TABLE `sys_job_log` COMMENT='定时任务调度日志表';
ALTER TABLE `sys_license` COMMENT='许可证表';
ALTER TABLE `sys_license_application` COMMENT='许可证申请表';
ALTER TABLE `sys_license_feature` COMMENT='许可证功能表';
ALTER TABLE `sys_license_history` COMMENT='许可证历史表';
ALTER TABLE `sys_log_audit` COMMENT='审计日志表';
ALTER TABLE `sys_log_data` COMMENT='数据日志表';
ALTER TABLE `sys_log_error` COMMENT='错误日志表';
ALTER TABLE `sys_log_login` COMMENT='登录日志表';
ALTER TABLE `sys_log_permission` COMMENT='权限日志表';
ALTER TABLE `sys_log_security` COMMENT='安全日志表';
ALTER TABLE `sys_log_type` COMMENT='日志类型表';
ALTER TABLE `sys_logininfor` COMMENT='系统访问记录表';
ALTER TABLE `sys_menu` COMMENT='菜单权限表';
ALTER TABLE `sys_menu_backup` COMMENT='菜单权限备份表';
ALTER TABLE `sys_notice` COMMENT='通知公告表';
ALTER TABLE `sys_oper_log` COMMENT='操作日志记录表';
ALTER TABLE `sys_operation_log_new` COMMENT='新操作日志表';
ALTER TABLE `sys_permission` COMMENT='权限表';
ALTER TABLE `sys_permission_audit` COMMENT '权限审计表';
ALTER TABLE `sys_permission_log` COMMENT='权限日志表';
ALTER TABLE `sys_permission_template` COMMENT='权限模板表';
ALTER TABLE `sys_permission_template_category` COMMENT '权限模板分类表';
ALTER TABLE `sys_permission_template_log` COMMENT '权限模板日志表';
ALTER TABLE `sys_permission_template_menu` COMMENT '权限模板菜单表';
ALTER TABLE `sys_permission_template_warehouse` COMMENT '权限模板仓库表';
ALTER TABLE `sys_role` COMMENT='角色信息表';
ALTER TABLE `sys_role_api` COMMENT='角色API权限关联表';
ALTER TABLE `sys_role_data_permission` COMMENT '角色数据权限表';
ALTER TABLE `sys_role_dept` COMMENT='角色和部门关联表';
ALTER TABLE `sys_role_menu` COMMENT='角色和菜单关联表';
ALTER TABLE `sys_role_template` COMMENT '角色模板表';
ALTER TABLE `sys_role_template_api` COMMENT '角色模板API表';
ALTER TABLE `sys_role_template_data` COMMENT '角色模板数据表';
ALTER TABLE `sys_role_template_dept` COMMENT '角色模板部门表';
ALTER TABLE `sys_role_template_menu` COMMENT '角色模板菜单表';
ALTER TABLE `sys_role_template_warehouse` COMMENT '角色模板仓库表';
ALTER TABLE `sys_role_warehouse` COMMENT '角色仓库表';
ALTER TABLE `sys_security_log` COMMENT='安全日志表';
ALTER TABLE `sys_security_log_new` COMMENT='新安全日志表';
ALTER TABLE `sys_template_log` COMMENT '模板日志表';
ALTER TABLE `sys_template_menu` COMMENT '模板菜单表';
ALTER TABLE `sys_template_report` COMMENT '模板报表表';
ALTER TABLE `sys_template_warehouse` COMMENT '模板仓库表';
ALTER TABLE `sys_user` COMMENT='用户信息表';
ALTER TABLE `sys_user_api` COMMENT '用户API表';
ALTER TABLE `sys_user_data_permission` COMMENT='用户和数据权限关联表';
ALTER TABLE `sys_user_menu` COMMENT='用户和菜单关联表';
ALTER TABLE `sys_user_permission` COMMENT '用户权限表';
ALTER TABLE `sys_user_permission_inherit` COMMENT '用户权限继承记录表';
ALTER TABLE `sys_user_role` COMMENT='用户和角色关联表';
ALTER TABLE `sys_user_warehouse` COMMENT '用户仓库表';
ALTER TABLE `sys_warehouse` COMMENT='仓库表';
ALTER TABLE `sys_warehouse_auth` COMMENT '仓库权限表';
ALTER TABLE `sys_warehouse_data_permission` COMMENT '仓库数据权限表';
ALTER TABLE `wms_alert_log` COMMENT '库存预警日志表';
ALTER TABLE `wms_alert_rule` COMMENT '库存预警规则表';
ALTER TABLE `wms_inventory` COMMENT='库存表';
ALTER TABLE `wms_inventory_backup_duplicate` COMMENT '库存备份重复表';
ALTER TABLE `wms_inventory_check` COMMENT '库存盘点表';
ALTER TABLE `wms_inventory_check_detail` COMMENT '库存盘点明细表';
ALTER TABLE `wms_inventory_in` COMMENT='入库单表';
ALTER TABLE `wms_inventory_in_detail` COMMENT '入库明细表';
ALTER TABLE `wms_inventory_log` COMMENT='库存日志表';
ALTER TABLE `wms_inventory_out` COMMENT='出库单表';
ALTER TABLE `wms_inventory_out_detail` COMMENT '出库明细表';
ALTER TABLE `wms_inventory_transfer` COMMENT '调拨单表';
ALTER TABLE `wms_inventory_transfer_detail` COMMENT '调拨明细表';
ALTER TABLE `wms_category` COMMENT '物品分类表';
ALTER TABLE `wms_product` COMMENT '物品信息表';
ALTER TABLE `wms_specification` COMMENT '物品规格表';
ALTER TABLE `wms_unit` COMMENT '物品单位表';
ALTER TABLE `wms_role_warehouse` COMMENT '角色仓库表';
ALTER TABLE `wms_user_warehouse` COMMENT '用户仓库表';
ALTER TABLE `wms_warehouse_area` COMMENT '库区表';
ALTER TABLE `wms_warehouse_rack` COMMENT '货架表';

-- 验证修复结果
SELECT TABLE_NAME, TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND TABLE_NAME IN ('sys_menu', 'sys_dept', 'sys_user', 'sys_role', 'sys_warehouse');