import request from '@/utils/request'

// 获取HTTPS配置信息
export function getHttpsInfo() {
  return request({
    url: '/ssl/info',
    method: 'get'
  })
}

// 下载SSL证书 (CER格式，适用于Windows)
export function downloadCertificate() {
  return request({
    url: '/ssl/download/certificate',
    method: 'get',
    responseType: 'blob'
  })
}

// 下载SSL证书 (PEM格式，适用于Linux/Mac)
export function downloadCertificatePem() {
  return request({
    url: '/ssl/download/certificate/pem',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取证书安装指南
export function getCertificateInstallGuide() {
  return request({
    url: '/ssl/guide',
    method: 'get'
  })
}

// 下载证书文件的通用方法
export function downloadFile(blob, filename) {
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    // IE浏览器
    window.navigator.msSaveOrOpenBlob(blob, filename)
  } else {
    // 其他浏览器
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }
}