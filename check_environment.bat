@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: 环境检查诊断脚本
:: 用于诊断部署环境问题
:: ========================================

echo.
echo ========================================
echo 环境检查诊断脚本
echo ========================================
echo.

echo 正在检查部署所需的工具和环境...
echo.

:: 检查MySQL
echo [1/5] 检查MySQL客户端...
mysql --version >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ MySQL客户端: 已安装
    mysql --version
) else (
    echo ✗ MySQL客户端: 未安装或不在PATH中
    echo   解决方案: 安装MySQL并将bin目录添加到PATH
)
echo.

:: 检查Java
echo [2/5] 检查Java环境...
java -version >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ Java: 已安装
    java -version 2>&1 | findstr "version"
) else (
    echo ✗ Java: 未安装或不在PATH中
    echo   解决方案: 安装JDK并配置JAVA_HOME和PATH
)
echo.

:: 检查Maven
echo [3/5] 检查Maven...
mvn --version >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ Maven: 已安装
    mvn --version | findstr "Apache Maven"
) else (
    echo ✗ Maven: 未安装或不在PATH中
    echo   解决方案: 安装Apache Maven并配置MAVEN_HOME和PATH
)
echo.

:: 检查Node.js
echo [4/5] 检查Node.js...
node --version >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ Node.js: 已安装
    node --version
) else (
    echo ✗ Node.js: 未安装 (可选，仅前端需要)
    echo   解决方案: 如需前端部署，请安装Node.js
)
echo.

:: 检查项目目录
echo [5/5] 检查项目目录...
set "PROJECT_ROOT=C:\CKGLXT\warehouse-system"
set "BACKEND_PATH=%PROJECT_ROOT%\backend"

if exist "%PROJECT_ROOT%" (
    echo ✓ 项目根目录: %PROJECT_ROOT%
) else (
    echo ✗ 项目根目录不存在: %PROJECT_ROOT%
)

if exist "%BACKEND_PATH%" (
    echo ✓ 后端目录: %BACKEND_PATH%
) else (
    echo ✗ 后端目录不存在: %BACKEND_PATH%
)

if exist "%BACKEND_PATH%\pom.xml" (
    echo ✓ Maven项目文件: pom.xml
) else (
    echo ✗ Maven项目文件不存在: %BACKEND_PATH%\pom.xml
)
echo.

:: 检查数据库连接
echo 检查数据库连接...
mysql -h localhost -P 3306 -u root -p123456 -e "SELECT 1;" warehouse_system >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ 数据库连接: 成功
) else (
    echo ✗ 数据库连接: 失败
    echo   可能原因: MySQL服务未启动、用户名密码错误、数据库不存在
)
echo.

:: 总结
echo ========================================
echo 环境检查完成
echo ========================================
echo.
echo 如果所有项目都显示 ✓，则可以运行部署脚本
echo 如果有项目显示 ✗，请按照提示解决问题后重试
echo.

pause