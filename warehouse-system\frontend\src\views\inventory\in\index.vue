<template>
  <div class="app-container">
    <!-- 移动端结构 -->
    <div v-if="$store.getters.device === 'mobile'" class="mobile-inventory-container">
      <el-collapse v-model="mobileSearchVisible" class="mobile-search">
        <el-collapse-item title="搜索条件" name="search">
          <el-form :model="queryParams" ref="queryForm" size="small" label-width="80px">
            <el-form-item label="入库单号" prop="inCode">
              <el-input v-model="queryParams.inCode" placeholder="请输入入库单号" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="仓库名称" prop="warehouseId">
              <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable style="width: 100%">
                <el-option v-for="item in warehouseOptions" :key="item.warehouseId" :label="item.warehouseName" :value="item.warehouseId" />
              </el-select>
            </el-form-item>
            <el-form-item label="入库类型" prop="inType">
              <el-select v-model="queryParams.inType" placeholder="请选择入库类型" clearable style="width: 100%">
                <el-option v-for="dict in dict.type.inventory_in_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option v-for="dict in dict.type.inventory_in_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="入库时间">
              <el-date-picker v-model="dateRange" style="width: 100%" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <!-- 移动端操作按钮区 -->
      <div class="mobile-actions">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['inventory:in:add']">新增</el-button>
        <el-button type="success" icon="el-icon-edit" size="small" :disabled="single" @click="handleUpdate" v-hasPermi="['inventory:in:edit']">修改</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple" @click="handleDelete" v-hasPermi="['inventory:in:remove']">删除</el-button>
        <el-dropdown size="small" style="margin-left: 10px;">
          <el-button size="small">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="scan" icon="el-icon-camera" v-hasPermi="['inventory:in:add']" @click.native="handleScan">扫码入库</el-dropdown-item>
            <el-dropdown-item command="batchAdd" icon="el-icon-s-grid" v-hasPermi="['inventory:in:add']" @click.native="handleBatchAdd">批量入库</el-dropdown-item>
            <el-dropdown-item command="export" icon="el-icon-download" v-hasPermi="['inventory:in:export']" @click.native="handleExport('excel')">导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <!-- 移动端入库卡片列表 -->
      <div v-loading="loading" class="mobile-in-list">
        <div v-for="item in inList" :key="item.inId" class="in-card">
          <div class="in-card-header">
            <div class="in-title">{{ item.inCode }}</div>
            <el-tag size="mini" :type="item.status === '0' ? 'success' : 'info'" class="in-status">{{ getInStatusLabel(item.status) }}</el-tag>
          </div>
          <div class="in-card-body">
            <div class="in-detail"><span class="label">仓库:</span><span class="value">{{ item.warehouseName }}</span></div>
            <div class="in-detail"><span class="label">入库类型:</span><span class="value">{{ getInTypeLabel(item.inType) }}</span></div>
            <div class="in-detail"><span class="label">入库时间:</span><span class="value">{{ item.inTime }}</span></div>
          </div>
          <div class="in-card-actions" @click.stop>
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(item)" v-hasPermi="['inventory:in:query']">查看</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(item)" v-hasPermi="['inventory:in:edit']" v-if="item.status === '0'">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(item)" v-hasPermi="['inventory:in:remove']" v-if="item.status === '0'">删除</el-button>
            <el-button size="mini" type="text" icon="el-icon-check" @click="handleAudit(item)" v-hasPermi="['inventory:in:audit']" v-if="item.status === '0'">审核</el-button>
            <el-button size="mini" type="text" icon="el-icon-printer" @click="handlePrint(item)" v-hasPermi="['inventory:in:print']">打印</el-button>
          </div>
        </div>
      </div>
      <!-- 移动端分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      <!-- 移动端弹窗（新增/编辑） -->
      <el-dialog
        v-if="$store.getters.device === 'mobile'"
        :title="title"
        :visible.sync="open"
        width="100vw"
        top="0"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="仓库" prop="warehouseId">
                <el-select v-model="form.warehouseId" placeholder="请选择仓库">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="入库类型" prop="inType">
                <el-select v-model="form.inType" placeholder="请选择入库类型" @change="handleInTypeChange">
                  <el-option
                    v-for="dict in dict.type.inventory_in_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 添加选择申购单的表单项 -->
          <el-row v-if="isPurchaseInType">
            <el-col :span="24">
              <el-form-item label="关联申购单" prop="purchaseRequestId">
                <el-select 
                  v-model="form.purchaseRequestId" 
                  placeholder="请选择申购单" 
                  filterable
                  @change="handlePurchaseRequestChange"
                  style="width: 100%">
                  <el-option
                    v-for="item in approvedPurchaseRequests"
                    :key="item.requestId"
                    :label="item.requestNo + ' - ' + item.applicant"
                    :value="item.requestId">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="center">入库明细</el-divider>
          <el-row>
            <el-col :span="24">
              <el-button 
                v-if="form.inType !== '5'" 
                type="primary" 
                icon="el-icon-plus" 
                size="mini" 
                @click="handleAddDetail">添加明细</el-button>
              <el-table :data="form.details" style="margin-top: 10px;">
                <el-table-column label="序号" type="index" width="55" align="center" />
                <el-table-column label="物品" prop="productName" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" placeholder="请选择物品" @change="(val) => handleProductChange(val, scope.$index)">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.productId"
                        :label="item.productName"
                        :value="item.productId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" min-width="100">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.quantity" :min="1" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="单价" prop="price" min-width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.price" :min="0" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="金额" prop="amount" width="150">
                  <template slot-scope="scope">
                    <span>{{ (scope.row.quantity * scope.row.price).toFixed(2) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteDetail(scope.$index)"
                      v-hasPermi="['inventory:in:edit']"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer" style="display:flex;gap:4%;justify-content:space-between">
          <el-button type="primary" @click="submitForm" style="width:48%">确 定</el-button>
          <el-button @click="cancel" style="width:48%">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看入库单对话框 -->
      <el-dialog title="查看入库单" :visible.sync="viewOpen" width="780px" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="入库单号">{{ viewForm.inCode }}</el-descriptions-item>
          <el-descriptions-item label="仓库名称">{{ viewForm.warehouseName }}</el-descriptions-item>
          <el-descriptions-item label="入库类型">
            <dict-tag :options="dict.type.inventory_in_type" :value="viewForm.inType"/>
          </el-descriptions-item>
          <el-descriptions-item label="入库时间">{{ parseTime(viewForm.inTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.inventory_in_status" :value="viewForm.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ viewForm.createBy }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
          <!-- 添加显示关联申购单信息 -->
          <el-descriptions-item label="关联申购单" v-if="viewForm.inType === '5' && viewForm.purchaseRequestNo">
            {{ viewForm.purchaseRequestNo }}
          </el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="center">入库明细</el-divider>
        <el-table :data="viewForm.details" style="margin-top: 10px;">
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物品名称" prop="productName" min-width="180" />
          <el-table-column label="物品编码" prop="productCode" width="120" />
          <el-table-column label="数量" prop="quantity" width="100" />
          <el-table-column label="单价" prop="price" width="100" />
          <el-table-column label="金额" prop="amount" width="100" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 审核入库单对话框 -->
      <el-dialog title="审核入库单" :visible.sync="auditOpen" width="700px" append-to-body>
        <el-tabs v-model="auditActiveTab">
          <el-tab-pane label="入库单信息" name="info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="入库单号">{{ auditInfo.inCode }}</el-descriptions-item>
              <el-descriptions-item label="仓库名称">{{ auditInfo.warehouseName }}</el-descriptions-item>
              <el-descriptions-item label="入库类型">
                <dict-tag :options="dict.type.inventory_in_type" :value="auditInfo.inType"/>
              </el-descriptions-item>
              <el-descriptions-item label="入库时间">{{ parseTime(auditInfo.inTime) }}</el-descriptions-item>
              <el-descriptions-item label="创建人">{{ auditInfo.createBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ parseTime(auditInfo.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ auditInfo.remark }}</el-descriptions-item>
              <!-- 添加关联申购单信息显示 -->
              <el-descriptions-item label="关联申购单" :span="2" v-if="auditInfo.inType === '5' && auditInfo.purchaseRequestNo && auditInfo.purchaseRequestNo.trim() !== ''">
                {{ auditInfo.purchaseRequestNo }}
              </el-descriptions-item>
            </el-descriptions>
            <el-divider content-position="center">入库明细</el-divider>
            <el-table :data="auditInfo.details" style="margin-top: 10px;">
              <el-table-column label="序号" type="index" width="55" align="center" />
              <el-table-column label="物品名称" prop="productName" min-width="180" />
              <el-table-column label="物品编码" prop="productCode" width="120" />
              <el-table-column label="数量" prop="quantity" width="100" />
              <el-table-column label="单价" prop="price" width="100" />
              <el-table-column label="金额" prop="amount" width="100" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="审核操作" name="audit">
            <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
              <el-form-item label="入库单号">{{ auditForm.inCode }}</el-form-item>
              <el-form-item label="审核结果" prop="status">
                <el-radio-group v-model="auditForm.status">
                  <el-radio label="1">通过</el-radio>
                  <el-radio label="2">不通过</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="审核意见" prop="remark">
                <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入审核意见" rows="4" />
              </el-form-item>
              <el-form-item label="审核附注" prop="auditNote">
                <el-input v-model="auditForm.auditNote" type="textarea" placeholder="请输入审核附注（内部记录，不显示给用户）" rows="2" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAudit" :disabled="auditActiveTab !== 'audit'">提交审核</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>

    <div v-else>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="入库单号" prop="inCode">
          <el-input
            v-model="queryParams.inCode"
            placeholder="请输入入库单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库名称" prop="warehouseId">
          <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable>
            <el-option
              v-for="item in warehouseOptions"
              :key="item.warehouseId"
              :label="item.warehouseName"
              :value="item.warehouseId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入库类型" prop="inType">
          <el-select v-model="queryParams.inType" placeholder="请选择入库类型" clearable>
            <el-option
              v-for="dict in dict.type.inventory_in_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in dict.type.inventory_in_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入库时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['inventory:in:add']"
          >新增</el-button>
        </el-col>
        <!-- 修改按钮已移除 -->
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inventory:in:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport('excel')"
            v-hasPermi="['inventory:in:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-camera"
            size="mini"
            @click="handleScan"
            v-hasPermi="['inventory:in:add']"
          >扫码入库</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-s-grid"
            size="mini"
            @click="handleBatchAdd"
            v-hasPermi="['inventory:in:add']"
          >批量入库</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="inList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="入库ID" align="center" prop="inId" v-if="false" />
        <el-table-column label="入库单号" align="center" prop="inCode" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" :show-overflow-tooltip="true" />
        <el-table-column label="入库类型" align="center" prop="inType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.inventory_in_type" :value="scope.row.inType"/>
          </template>
        </el-table-column>
        <el-table-column label="入库时间" align="center" prop="inTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.inTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.inventory_in_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="审核人" align="center" prop="auditBy" />
        <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.auditTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding operation-column" width="280">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row)"
                v-hasPermi="['inventory:in:query']"
              >查看</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inventory:in:edit']"
                v-if="scope.row.status === '0'"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inventory:in:remove']"
                v-if="scope.row.status === '0'"
              >删除</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-check"
                @click="handleAudit(scope.row)"
                v-hasPermi="['inventory:in:audit']"
                v-if="scope.row.status === '0'"
              >审核</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-printer"
                @click="handlePrint(scope.row)"
                v-hasPermi="['inventory:in:print']"
              >打印</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改入库单对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body :close-on-click-modal="false">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="仓库" prop="warehouseId">
                <el-select v-model="form.warehouseId" placeholder="请选择仓库">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="入库类型" prop="inType">
                <el-select v-model="form.inType" placeholder="请选择入库类型" @change="handleInTypeChange">
                  <el-option
                    v-for="dict in dict.type.inventory_in_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 添加选择申购单的表单项 -->
          <el-row v-if="form.inType === '5'">
            <el-col :span="24">
              <el-form-item label="关联申购单" prop="purchaseRequestId">
                <el-select 
                  v-model="form.purchaseRequestId" 
                  placeholder="请选择申购单" 
                  filterable
                  @change="handlePurchaseRequestChange"
                  style="width: 100%">
                  <el-option
                    v-for="item in approvedPurchaseRequests"
                    :key="item.requestId"
                    :label="item.requestNo + ' - ' + item.applicant"
                    :value="item.requestId">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="center">入库明细</el-divider>
          <el-row>
            <el-col :span="24">
              <el-button 
                v-if="form.inType !== '5'" 
                type="primary" 
                icon="el-icon-plus" 
                size="mini" 
                @click="handleAddDetail">添加明细</el-button>
              <el-table :data="form.details" style="margin-top: 10px;">
                <el-table-column label="序号" type="index" width="55" align="center" />
                <el-table-column label="物品" prop="productName" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" placeholder="请选择物品" @change="(val) => handleProductChange(val, scope.$index)">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.productId"
                        :label="item.productName"
                        :value="item.productId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" min-width="100">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.quantity" :min="1" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="单价" prop="price" min-width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.price" :min="0" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="金额" prop="amount" width="150">
                  <template slot-scope="scope">
                    <span>{{ (scope.row.quantity * scope.row.price).toFixed(2) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteDetail(scope.$index)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看入库单对话框 -->
      <el-dialog title="查看入库单" :visible.sync="viewOpen" width="780px" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="入库单号">{{ viewForm.inCode }}</el-descriptions-item>
          <el-descriptions-item label="仓库名称">{{ viewForm.warehouseName }}</el-descriptions-item>
          <el-descriptions-item label="入库类型">
            <dict-tag :options="dict.type.inventory_in_type" :value="viewForm.inType"/>
          </el-descriptions-item>
          <el-descriptions-item label="入库时间">{{ parseTime(viewForm.inTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.inventory_in_status" :value="viewForm.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ viewForm.createBy }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
          <!-- 添加显示关联申购单信息 -->
          <el-descriptions-item label="关联申购单" v-if="viewForm.inType === '5' && viewForm.purchaseRequestNo">
            {{ viewForm.purchaseRequestNo }}
          </el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="center">入库明细</el-divider>
        <el-table :data="viewForm.details" style="margin-top: 10px;">
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物品名称" prop="productName" min-width="180" />
          <el-table-column label="物品编码" prop="productCode" width="120" />
          <el-table-column label="数量" prop="quantity" width="100" />
          <el-table-column label="单价" prop="price" width="100" />
          <el-table-column label="金额" prop="amount" width="100" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 审核入库单对话框 -->
      <el-dialog title="审核入库单" :visible.sync="auditOpen" width="700px" append-to-body>
        <el-tabs v-model="auditActiveTab">
          <el-tab-pane label="入库单信息" name="info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="入库单号">{{ auditInfo.inCode }}</el-descriptions-item>
              <el-descriptions-item label="仓库名称">{{ auditInfo.warehouseName }}</el-descriptions-item>
              <el-descriptions-item label="入库类型">
                <dict-tag :options="dict.type.inventory_in_type" :value="auditInfo.inType"/>
              </el-descriptions-item>
              <el-descriptions-item label="入库时间">{{ parseTime(auditInfo.inTime) }}</el-descriptions-item>
              <el-descriptions-item label="创建人">{{ auditInfo.createBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ parseTime(auditInfo.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ auditInfo.remark }}</el-descriptions-item>
              <!-- 添加关联申购单信息显示 -->
              <el-descriptions-item label="关联申购单" :span="2" v-if="auditInfo.inType === '5' && auditInfo.purchaseRequestNo">
                {{ auditInfo.purchaseRequestNo }}
              </el-descriptions-item>
            </el-descriptions>
            <el-divider content-position="center">入库明细</el-divider>
            <el-table :data="auditInfo.details" style="margin-top: 10px;">
              <el-table-column label="序号" type="index" width="55" align="center" />
              <el-table-column label="物品名称" prop="productName" min-width="180" />
              <el-table-column label="物品编码" prop="productCode" width="120" />
              <el-table-column label="数量" prop="quantity" width="100" />
              <el-table-column label="单价" prop="price" width="100" />
              <el-table-column label="金额" prop="amount" width="100" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="审核操作" name="audit">
            <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
              <el-form-item label="入库单号">{{ auditForm.inCode }}</el-form-item>
              <el-form-item label="审核结果" prop="status">
                <el-radio-group v-model="auditForm.status">
                  <el-radio label="1">通过</el-radio>
                  <el-radio label="2">不通过</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="审核意见" prop="remark">
                <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入审核意见" rows="4" />
              </el-form-item>
              <el-form-item label="审核附注" prop="auditNote">
                <el-input v-model="auditForm.auditNote" type="textarea" placeholder="请输入审核附注（内部记录，不显示给用户）" rows="2" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAudit" :disabled="auditActiveTab !== 'audit'">提交审核</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </el-dialog>

      <el-dialog title="选择导出字段" :visible.sync="exportFieldDialogVisible" width="400px">
        <el-checkbox-group v-model="selectedExportFields">
          <el-checkbox v-for="col in exportFieldOptions" :key="col.value" :label="col.value">{{ col.label }}</el-checkbox>
        </el-checkbox-group>
        <div slot="footer" class="dialog-footer">
          <el-button @click="exportFieldDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDoExport">导出</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listInventoryIn, getInventoryIn, delInventoryIn, addInventoryIn, updateInventoryIn, auditInventoryIn, getApprovedPurchaseRequests } from "@/api/inventory/in";
import { listProduct } from "@/api/product/info";
import { optionselect } from "@/api/system/warehouse";
// 添加申购单API导入
import { listPurchase, getPurchase } from "@/api/inventory/purchase";
import axios from 'axios';

export default {
  name: "InventoryIn",
  dicts: ['inventory_in_type', 'inventory_in_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 显示审核信息列
      showAuditInfo: true,
      // 总条数
      total: 0,
      // 入库表格数据
      inList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 审核选项卡激活名称
      auditActiveTab: 'info',
      // 日期范围
      dateRange: [],
      // 仓库选项
      warehouseOptions: [],
      // 物品选项
      productOptions: [],
      // 已批准的申购单列表
      approvedPurchaseRequests: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        inCode: null,
        warehouseId: null,
        inType: null,
        status: null
      },
      // 表单参数
      form: {
        details: []
      },
      // 表单校验
      rules: {
        warehouseId: [
          { required: true, message: "仓库不能为空", trigger: "change" }
        ],
        inType: [
          { required: true, message: "入库类型不能为空", trigger: "change" }
        ]
      },
      // 查看表单参数
      viewForm: {
        details: []
      },
      // 审核信息
      auditInfo: {
        inCode: "",
        warehouseName: "",
        inType: "",
        inTime: null,
        createBy: "",
        createTime: null,
        remark: "",
        details: []
      },
      // 审核表单参数
      auditForm: {
        inId: null,
        inCode: "",
        status: "1",
        remark: "",
        auditNote: ""
      },
      // 审核表单校验
      auditRules: {
        status: [
          { required: true, message: "审核结果不能为空", trigger: "change" }
        ]
      },
      // 移动端搜索折叠面板可见性
      mobileSearchVisible: ['search'],
      exportFieldDialogVisible: false,
      selectedExportFields: [],
      exportFieldOptions: [
        { label: '入库单号', value: 'inCode' },
        { label: '仓库名称', value: 'warehouseName' },
        { label: '入库类型', value: 'inTypeName' },
        { label: '物品名称', value: 'productName' },
        { label: '物品编码', value: 'productCode' },
        { label: '数量', value: 'quantity' },
        { label: '单价', value: 'price' },
        { label: '总金额', value: 'amount' },
        { label: '入库时间', value: 'inTime' },
        { label: '操作人员', value: 'operateBy' },
        { label: '状态', value: 'status' },
        { label: '审核人', value: 'auditBy' },
        { label: '审核时间', value: 'auditTime' }
      ],
      exportType: 'excel',
    };
  },
  created() {
    this.getList();
    this.getWarehouseOptions();
    this.getProductOptions();
  },
  methods: {
    /** 查询入库列表 */
    getList() {
      this.loading = true;
      console.log('开始调用入库管理API...');
      console.log('查询参数:', this.addDateRange(this.queryParams, this.dateRange));
      listInventoryIn(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        console.log('入库管理API响应:', response);
        console.log('入库列表数据:', response.rows);
        if (response.rows && response.rows.length > 0) {
          console.log('第一条记录的审核字段:', {
            auditBy: response.rows[0].auditBy,
            auditTime: response.rows[0].auditTime
          });
          console.log('第一条记录的完整数据:', response.rows[0]);
        }
        this.inList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('入库管理API调用失败:', error);
        this.loading = false;
      });
    },
    /** 获取已批准的申购单列表 */
    getApprovedPurchaseRequests() {
      // 查询状态为"已审批"(值为1)的申购单
      getApprovedPurchaseRequests().then(response => {
        this.approvedPurchaseRequests = response.data;
        console.log('获取到的申购单列表:', this.approvedPurchaseRequests); // 调试信息
      }).catch(error => {
        console.error('获取申购单列表失败:', error);
        this.$modal.msgError("获取申购单列表失败");
      });
    },
    
    /** 入库类型改变事件 */
    handleInTypeChange(value) {
      console.log('入库类型改变:', value);
      // 如果选择的是申购入库(值为5)，则获取已批准的申购单列表
      if (value === '5') {
        this.getApprovedPurchaseRequests();
      } else {
        // 清空申购单选择和明细
        this.form.purchaseRequestId = null;
        this.form.details = [];
      }
    },
    /** 获取仓库选项 */
    getWarehouseOptions() {
      optionselect().then(response => {
        console.log('入库管理页面 - optionselect 接口返回数据:', response);
        this.warehouseOptions = response.data || response.rows || [];
        console.log('入库管理页面 - 最终仓库选项:', this.warehouseOptions);
      }).catch(error => {
        console.error('入库管理页面 - optionselect 接口调用失败:', error);
      });
    },
    /** 获取物品选项 */
    getProductOptions() {
      listProduct().then(response => {
        this.productOptions = response.rows;
      });
    },
    // 物品选择事件
    handleProductChange(value, index) {
      const product = this.productOptions.find(item => item.productId === value);
      if (product) {
        this.form.details[index].productName = product.productName;
        this.form.details[index].productCode = product.productCode;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        inId: null,
        inCode: null,
        warehouseId: null,
        inType: "1",
        inTime: null,
        status: "0",
        remark: null,
        details: [],
        purchaseRequestId: null
      };
      this.resetForm("form");
      // 如果之前选择了申购入库类型，需要重新获取申购单列表
      if (this.form.inType === '5') {
        this.getApprovedPurchaseRequests();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.inId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加入库单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const inId = row.inId || this.ids[0];
      getInventoryIn(inId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改入库单";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getInventoryIn(row.inId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditActiveTab = 'info';
      this.auditForm = {
        inId: row.inId,
        inCode: row.inCode,
        status: "1",
        remark: "", // 审核意见为空，由用户选择是否填写
        auditNote: ""
      };

      // 获取入库单详细信息
      getInventoryIn(row.inId).then(response => {
        this.auditInfo = response.data;
        this.auditOpen = true;
      });
    },
    /** 打印按钮操作 */
    handlePrint(row) {
      this.$router.push({ path: `/inventory/in/print/${row.inId}` });
    },
    /** 扫码入库按钮操作 */
    handleScan() {
      this.$router.push({ path: "/inventory/in-scan/index" });
    },
    /** 批量入库按钮操作 */
    handleBatchAdd() {
      this.$router.push({ path: "/inventory/in/batch" });
    },
    /** 报表按钮操作 */
    handleReport() {
      this.$router.push({ path: "/report/in/index" });
    },
    /** 添加明细按钮操作 */
    handleAddDetail() {
      // 只有非申购入库才允许手动添加明细
      if (this.form.inType !== '5') {
        this.form.details.push({
          productId: null,
          productName: null,
          productCode: null,
          quantity: 1,
          price: 0,
          amount: 0
        });
      }
    },
    /** 删除明细 */
    handleDeleteDetail(index) {
      this.form.details.splice(index, 1);
    },
    /** 申购单选择改变事件 */
    handlePurchaseRequestChange(value) {
      console.log('选择的申购单ID:', value);
      // 查找选中的申购单
      const selectedPurchaseRequest = this.approvedPurchaseRequests.find(item => item.requestId === value);
      console.log('选中的申购单:', selectedPurchaseRequest);
      
      if (selectedPurchaseRequest) {
        // 获取申购单详细信息，包括明细
        getPurchase(value).then(response => {
          const purchaseData = response.data;
          console.log('申购单详细信息:', purchaseData); // 调试信息
          
          // 检查返回的数据结构
          if (!purchaseData) {
            this.$modal.msgError("获取申购单详细信息失败");
            return;
          }
          
          // 清空现有明细
          this.form.details = [];
          
          // 对于单个申购单，直接使用申购单本身作为明细项
          // 计算还可以入库的数量（申购数量 - 已入库数量）
          const quantity = parseFloat(purchaseData.quantity) || 0;
          const stockInCount = parseFloat(purchaseData.stockInCount) || 0;
          const remainingQuantity = quantity - stockInCount;
          const unitPrice = parseFloat(purchaseData.unitPrice) || 0;
          
          console.log(`申购单: 已入库数量=${stockInCount}, 申购数量=${quantity}, 可入库数量=${remainingQuantity}, 单价=${unitPrice}`);
          
          // 只有当还有可入库数量时才添加到明细中
          if (remainingQuantity > 0) {
            this.form.details.push({
              productId: purchaseData.productId,
              productName: purchaseData.productName,
              productCode: purchaseData.productCode,
              quantity: remainingQuantity, // 可入库数量
              price: unitPrice,
              amount: (remainingQuantity * unitPrice).toFixed(2)
            });
            // 同时设置申购单号和申购单ID
            this.form.purchaseRequestNo = purchaseData.requestNo;
            this.form.purchaseRequestId = purchaseData.requestId;
            console.log('填充后的入库单明细:', this.form.details); // 调试信息
          } else {
            console.log('申购单没有可入库的物品');
            this.$modal.msgWarning("该申购单没有可入库的明细项");
          }
        }).catch(error => {
          console.error('获取申购单详细信息失败:', error);
          this.$modal.msgError("获取申购单详细信息失败: " + (error.message || error));
          // 清空明细
          this.form.details = [];
        });
      } else {
        console.log('未找到选中的申购单');
        this.$modal.msgError("未找到选中的申购单");
        // 清空明细
        this.form.details = [];
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查明细是否为空
          if (this.form.details.length === 0) {
            this.$modal.msgError("请添加入库明细");
            return;
          }

          // 检查明细是否填写完整
          for (let i = 0; i < this.form.details.length; i++) {
            const detail = this.form.details[i];
            if (!detail.productId) {
              this.$modal.msgError("请选择物品");
              return;
            }
            if (!detail.quantity || detail.quantity <= 0) {
              this.$modal.msgError("请输入正确的数量");
              return;
            }
          }
          
          // 对于申购入库，需要检查是否选择了申购单
          if (this.form.inType === '5' && !this.form.purchaseRequestId) {
            this.$modal.msgError("请选择关联的申购单");
            return;
          }

          if (this.form.inId != null) {
            updateInventoryIn(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventoryIn(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交审核表单 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          // 提交审核时，包含入库单的详细信息（包括details）
          const submitData = {
            ...this.auditForm,
            details: this.auditInfo.details // 将明细列表添加到提交数据中
          };
          auditInventoryIn(submitData).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const inIds = row.inId || this.ids;
      this.$modal.confirm('是否确认删除入库单编号为"' + inIds + '"的数据项？').then(() => {
        return delInventoryIn(inIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport(command) {
      this.exportType = command;
      this.exportFieldDialogVisible = true;
      // 默认全选
      if (this.selectedExportFields.length === 0) {
        this.selectedExportFields = this.exportFieldOptions.map(opt => opt.value);
      }
    },
    handleDoExport() {
      if (!this.selectedExportFields.length) {
        this.$modal.msgError('请至少选择一个导出字段');
        return;
      }
      const params = { ...this.queryParams, columns: this.selectedExportFields.join(',') };
      let url = '';
      let fileName = '';
      if (this.exportType === 'excel') {
        url = '/api/v1/reports/export/in/detail/excel';
        fileName = `入库明细报表_${new Date().getTime()}.xlsx`;
      } else if (this.exportType === 'pdf') {
        url = '/api/v1/reports/export/in/detail/pdf';
        fileName = `入库明细报表_${new Date().getTime()}.pdf`;
      }
      this.exportReport(url, params, fileName);
      this.exportFieldDialogVisible = false;
    },
    exportReport(url, params, fileName) {
      this.$modal.loading("正在导出数据，请稍候...");
      const baseUrl = process.env.VUE_APP_BASE_API || '';
      const fullUrl = baseUrl + url;
      axios({
        method: 'get',
        url: fullUrl,
        params: params,
        responseType: 'blob',
        headers: {
          Authorization: this.$store.getters.token ? 'Bearer ' + this.$store.getters.token : undefined
        }
      }).then(response => {
        this.$modal.closeLoading();
        const blob = new Blob([response.data]);
        if (window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(blob, fileName);
        } else {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          window.URL.revokeObjectURL(link.href);
        }
        this.$modal.msgSuccess("导出成功");
      }).catch(() => {
        this.$modal.closeLoading();
        this.$modal.msgError("导出失败，请重试");
      });
    },
    getInStatusLabel: function(status) {
      var arr = this.dict.type.inventory_in_status;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].value == status) return arr[i].label;
      }
      return status;
    },
    getInTypeLabel: function(inType) {
      var arr = this.dict.type.inventory_in_type;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].value == inType) return arr[i].label;
      }
      return inType;
    }
  }
};
</script>

<style lang="scss" scoped>
.operation-column {
  .operation-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
  }
}

// 移动端操作按钮样式
@media screen and (max-width: 768px) {
  .operation-column {
    .operation-buttons {
      flex-direction: column;
      align-items: center;
      
      .el-button {
        margin: 2px 0;
        width: 100%;
        text-align: center;
      }
    }
  }
}

// 数字输入框样式优化
:deep(.el-input-number) {
  width: 100%;
  
  .el-input__inner {
    text-align: left;
    padding-right: 50px;
    width: 100%;
  }
  
  .el-input-number__decrease,
  .el-input-number__increase {
    width: 24px;
    height: 24px;
    line-height: 24px;
  }
}

// 移动端数字输入框样式
@media screen and (max-width: 768px) {
  :deep(.el-input-number) {
    width: 100%;
    
    .el-input__inner {
      padding-right: 60px;
      width: 100%;
    }
    
    .el-input-number__decrease,
    .el-input-number__increase {
      width: 28px;
      height: 28px;
      line-height: 28px;
    }
  }
  
  // 确保表格中的数字输入框有足够宽度
  .el-table {
    .el-input-number {
      width: 100%;
      min-width: 120px;
    }
  }
}

.mobile-inventory-container {
  .mobile-search {
    margin-bottom: 10px;
    .el-collapse-item__header {
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      padding: 10px 15px;
      font-size: 14px;
      font-weight: bold;
      color: #303133;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .el-collapse-item__arrow {
        transition: transform 0.3s;
      }
      &.is-active .el-collapse-item__arrow {
        transform: rotate(180deg);
      }
    }
    .el-collapse-item__content {
      padding: 0 15px 15px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 10px;
        .el-form-item__label {
          font-size: 13px;
          color: #606266;
        }
        .el-form-item__content {
          .el-input__inner, .el-select__inner, .el-date-editor .el-input__inner {
            font-size: 13px;
          }
          .el-input-number__decrease, .el-input-number__increase {
            font-size: 13px;
          }
        }
      }
      .el-button {
        font-size: 13px;
      }
    }
  }
  .mobile-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .el-button {
      font-size: 13px;
    }
  }
  .mobile-in-list {
    margin-top: 12px;
  }
  .in-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    margin-bottom: 12px;
    padding: 12px;
  }
  .in-card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }
  .in-title {
    font-weight: bold;
    font-size: 16px;
    flex: 1;
  }
  .in-status {
    margin-left: 8px;
  }
  .in-card-body {
    margin-bottom: 8px;
  }
  .in-detail {
    font-size: 13px;
    margin-bottom: 2px;
  }
  .in-detail .label {
    color: #888;
    margin-right: 4px;
  }
  .in-detail .value {
    color: #333;
  }
  .in-card-actions {
    display: flex;
    gap: 8px;
  }
}
</style>