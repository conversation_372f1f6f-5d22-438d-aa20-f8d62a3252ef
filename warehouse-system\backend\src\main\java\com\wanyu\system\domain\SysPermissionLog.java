package com.wanyu.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 权限日志对象 sys_permission_log
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class SysPermissionLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 权限类型 */
    @Excel(name = "权限类型", readConverterExp = "MENU=菜单,BUTTON=按钮,API=接口,DATA=数据")
    private String permType;

    /** 权限标识 */
    @Excel(name = "权限标识")
    private String permission;

    /** 操作类型 */
    @Excel(name = "操作类型", readConverterExp = "GRANT=授权,REVOKE=撤销,UPDATE=更新")
    private String operType;

    /** 操作状态 */
    @Excel(name = "操作状态", readConverterExp = "0=成功,1=失败")
    private String status;

    /** 操作IP */
    @Excel(name = "操作IP")
    private String operIp;

    /** 操作地点 */
    @Excel(name = "操作地点")
    private String operLocation;

    /** 操作消息 */
    @Excel(name = "操作消息")
    private String msg;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setPermType(String permType) 
    {
        this.permType = permType;
    }

    public String getPermType() 
    {
        return permType;
    }
    public void setPermission(String permission) 
    {
        this.permission = permission;
    }

    public String getPermission() 
    {
        return permission;
    }
    public void setOperType(String operType) 
    {
        this.operType = operType;
    }

    public String getOperType() 
    {
        return operType;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setOperIp(String operIp) 
    {
        this.operIp = operIp;
    }

    public String getOperIp() 
    {
        return operIp;
    }
    public void setOperLocation(String operLocation) 
    {
        this.operLocation = operLocation;
    }

    public String getOperLocation() 
    {
        return operLocation;
    }
    public void setMsg(String msg) 
    {
        this.msg = msg;
    }

    public String getMsg() 
    {
        return msg;
    }
    public void setOperTime(Date operTime) 
    {
        this.operTime = operTime;
    }

    public Date getOperTime() 
    {
        return operTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("permType", getPermType())
            .append("permission", getPermission())
            .append("operType", getOperType())
            .append("status", getStatus())
            .append("operIp", getOperIp())
            .append("operLocation", getOperLocation())
            .append("msg", getMsg())
            .append("operTime", getOperTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}