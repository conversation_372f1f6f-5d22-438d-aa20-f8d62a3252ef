package com.wanyu.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 物品条码模板对象 wms_barcode_template
 *
 * <AUTHOR>
 */
public class WmsBarcodeTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 模板ID */
    private Long templateId;

    /** 模板名称 */
    @Excel(name = "模板名称")
    private String templateName;

    /** 模板类型 */
    @Excel(name = "模板类型")
    private String templateType;

    /** 模板宽度(mm) */
    @Excel(name = "模板宽度(mm)")
    private Integer templateWidth;

    /** 模板高度(mm) */
    @Excel(name = "模板高度(mm)")
    private Integer templateHeight;

    /** 条码宽度(mm) */
    @Excel(name = "条码宽度(mm)")
    private Integer barcodeWidth;

    /** 条码高度(mm) */
    @Excel(name = "条码高度(mm)")
    private Integer barcodeHeight;

    /** 字体大小 */
    @Excel(name = "字体大小")
    private Integer fontSize;

    /** 是否显示文字 */
    @Excel(name = "是否显示文字", readConverterExp = "0=否,1=是")
    private String showText;

    /** 是否显示物品名称 */
    @Excel(name = "是否显示物品名称", readConverterExp = "0=否,1=是")
    private String showProductName;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setTemplateId(Long templateId)
    {
        this.templateId = templateId;
    }

    public Long getTemplateId()
    {
        return templateId;
    }

    public void setTemplateName(String templateName)
    {
        this.templateName = templateName;
    }

    public String getTemplateName()
    {
        return templateName;
    }

    public void setTemplateType(String templateType)
    {
        this.templateType = templateType;
    }

    public String getTemplateType()
    {
        return templateType;
    }

    public void setTemplateWidth(Integer templateWidth)
    {
        this.templateWidth = templateWidth;
    }

    public Integer getTemplateWidth()
    {
        return templateWidth;
    }

    public void setTemplateHeight(Integer templateHeight)
    {
        this.templateHeight = templateHeight;
    }

    public Integer getTemplateHeight()
    {
        return templateHeight;
    }

    public void setBarcodeWidth(Integer barcodeWidth)
    {
        this.barcodeWidth = barcodeWidth;
    }

    public Integer getBarcodeWidth()
    {
        return barcodeWidth;
    }

    public void setBarcodeHeight(Integer barcodeHeight)
    {
        this.barcodeHeight = barcodeHeight;
    }

    public Integer getBarcodeHeight()
    {
        return barcodeHeight;
    }

    public void setFontSize(Integer fontSize)
    {
        this.fontSize = fontSize;
    }

    public Integer getFontSize()
    {
        return fontSize;
    }

    public void setShowText(String showText)
    {
        this.showText = showText;
    }

    public String getShowText()
    {
        return showText;
    }

    public void setShowProductName(String showProductName)
    {
        this.showProductName = showProductName;
    }

    public String getShowProductName()
    {
        return showProductName;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("templateId", getTemplateId())
            .append("templateName", getTemplateName())
            .append("templateType", getTemplateType())
            .append("templateWidth", getTemplateWidth())
            .append("templateHeight", getTemplateHeight())
            .append("barcodeWidth", getBarcodeWidth())
            .append("barcodeHeight", getBarcodeHeight())
            .append("fontSize", getFontSize())
            .append("showText", getShowText())
            .append("showProductName", getShowProductName())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}