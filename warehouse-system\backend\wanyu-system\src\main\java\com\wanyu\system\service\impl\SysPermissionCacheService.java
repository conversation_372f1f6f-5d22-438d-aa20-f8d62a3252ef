package com.wanyu.system.service.impl;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import org.springframework.stereotype.Component;
import com.wanyu.common.utils.StringUtils;

/**
 * 权限缓存服务
 *
 * <AUTHOR>
 */
@Component
public class SysPermissionCacheService
{
    // 内存缓存用于存储权限数据
    private static final Map<String, Object> PERMISSION_CACHE = new HashMap<>();

    /**
     * 缓存前缀
     */
    private final String CACHE_PREFIX = "permission:";

    /**
     * 获取菜单权限缓存
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> getMenuPermission(Long userId)
    {
        String cacheKey = CACHE_PREFIX + "menu_perms:" + userId;
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存菜单权限
     * 
     * @param userId 用户ID
     * @param permissions 权限列表
     */
    public void setMenuPermission(Long userId, Set<String> permissions)
    {
        String cacheKey = CACHE_PREFIX + "menu_perms:" + userId;
        PERMISSION_CACHE.put(cacheKey, permissions);
    }

    /**
     * 移除菜单权限缓存
     * 
     * @param userId 用户ID
     */
    public void removeMenuPermission(Long userId)
    {
        String cacheKey = CACHE_PREFIX + "menu_perms:" + userId;
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取操作权限缓存
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> getOperationPermission(Long userId)
    {
        String cacheKey = CACHE_PREFIX + "operation_perms:" + userId;
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存操作权限
     * 
     * @param userId 用户ID
     * @param permissions 权限列表
     */
    public void setOperationPermission(Long userId, Set<String> permissions)
    {
        String cacheKey = CACHE_PREFIX + "operation_perms:" + userId;
        PERMISSION_CACHE.put(cacheKey, permissions);
    }

    /**
     * 移除操作权限缓存
     * 
     * @param userId 用户ID
     */
    public void removeOperationPermission(Long userId)
    {
        String cacheKey = CACHE_PREFIX + "operation_perms:" + userId;
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取数据权限缓存
     * 
     * @param userId 用户ID
     * @return 数据权限列表
     */
    public Set<Long> getDataPermission(Long userId)
    {
        String cacheKey = CACHE_PREFIX + "data_perms:" + userId;
        return (Set<Long>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存数据权限
     * 
     * @param userId 用户ID
     * @param dataPermissions 数据权限列表
     */
    public void setDataPermission(Long userId, Set<Long> dataPermissions)
    {
        String cacheKey = CACHE_PREFIX + "data_perms:" + userId;
        PERMISSION_CACHE.put(cacheKey, dataPermissions);
    }

    /**
     * 移除数据权限缓存
     * 
     * @param userId 用户ID
     */
    public void removeDataPermission(Long userId)
    {
        String cacheKey = CACHE_PREFIX + "data_perms:" + userId;
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取仓库权限缓存
     * 
     * @param userId 用户ID
     * @return 仓库权限列表
     */
    public Set<Long> getWarehousePermission(Long userId)
    {
        String cacheKey = CACHE_PREFIX + "warehouse_perms:" + userId;
        return (Set<Long>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存仓库权限
     * 
     * @param userId 用户ID
     * @param warehousePermissions 仓库权限列表
     */
    public void setWarehousePermission(Long userId, Set<Long> warehousePermissions)
    {
        String cacheKey = CACHE_PREFIX + "warehouse_perms:" + userId;
        PERMISSION_CACHE.put(cacheKey, warehousePermissions);
    }

    /**
     * 移除仓库权限缓存
     * 
     * @param userId 用户ID
     */
    public void removeWarehousePermission(Long userId)
    {
        String cacheKey = CACHE_PREFIX + "warehouse_perms:" + userId;
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 清空所有权限缓存
     */
    public void clearAllPermissionCache()
    {
        PERMISSION_CACHE.clear();
    }
}
