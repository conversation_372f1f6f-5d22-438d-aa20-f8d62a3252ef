package com.wanyu.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 操作日志对象 wms_operation_log
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class WmsOperationLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 操作类型 */
    @Excel(name = "操作类型")
    private String operationType;

    /** 操作描述 */
    @Excel(name = "操作描述")
    private String operationDesc;

    /** 操作状态（0成功 1失败） */
    @Excel(name = "操作状态", readConverterExp = "0=成功,1=失败")
    private String operationStatus;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    /** 操作人ID */
    @Excel(name = "操作人ID")
    private Long operatorId;

    /** 操作人姓名 */
    @Excel(name = "操作人姓名")
    private String operatorName;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ipAddress;

    /** 用户代理 */
    @Excel(name = "用户代理")
    private String userAgent;

    /** 请求URL */
    @Excel(name = "请求URL")
    private String requestUrl;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String requestMethod;

    /** 请求参数 */
    @Excel(name = "请求参数")
    private String requestParams;

    /** 响应数据 */
    @Excel(name = "响应数据")
    private String responseData;

    /** 执行时间(毫秒) */
    @Excel(name = "执行时间(毫秒)")
    private Long executionTime;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setOperationType(String operationType) 
    {
        this.operationType = operationType;
    }

    public String getOperationType() 
    {
        return operationType;
    }
    public void setOperationDesc(String operationDesc) 
    {
        this.operationDesc = operationDesc;
    }

    public String getOperationDesc() 
    {
        return operationDesc;
    }
    public void setOperationStatus(String operationStatus) 
    {
        this.operationStatus = operationStatus;
    }

    public String getOperationStatus() 
    {
        return operationStatus;
    }
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }
    public void setOperationTime(Date operationTime) 
    {
        this.operationTime = operationTime;
    }

    public Date getOperationTime() 
    {
        return operationTime;
    }
    public void setOperatorId(Long operatorId) 
    {
        this.operatorId = operatorId;
    }

    public Long getOperatorId() 
    {
        return operatorId;
    }
    public void setOperatorName(String operatorName) 
    {
        this.operatorName = operatorName;
    }

    public String getOperatorName() 
    {
        return operatorName;
    }
    public void setIpAddress(String ipAddress) 
    {
        this.ipAddress = ipAddress;
    }

    public String getIpAddress() 
    {
        return ipAddress;
    }
    public void setUserAgent(String userAgent) 
    {
        this.userAgent = userAgent;
    }

    public String getUserAgent() 
    {
        return userAgent;
    }
    public void setRequestUrl(String requestUrl) 
    {
        this.requestUrl = requestUrl;
    }

    public String getRequestUrl() 
    {
        return requestUrl;
    }
    public void setRequestMethod(String requestMethod) 
    {
        this.requestMethod = requestMethod;
    }

    public String getRequestMethod() 
    {
        return requestMethod;
    }
    public void setRequestParams(String requestParams) 
    {
        this.requestParams = requestParams;
    }

    public String getRequestParams() 
    {
        return requestParams;
    }
    public void setResponseData(String responseData) 
    {
        this.responseData = responseData;
    }

    public String getResponseData() 
    {
        return responseData;
    }
    public void setExecutionTime(Long executionTime) 
    {
        this.executionTime = executionTime;
    }

    public Long getExecutionTime() 
    {
        return executionTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("operationType", getOperationType())
            .append("operationDesc", getOperationDesc())
            .append("operationStatus", getOperationStatus())
            .append("errorMessage", getErrorMessage())
            .append("operationTime", getOperationTime())
            .append("operatorId", getOperatorId())
            .append("operatorName", getOperatorName())
            .append("ipAddress", getIpAddress())
            .append("userAgent", getUserAgent())
            .append("requestUrl", getRequestUrl())
            .append("requestMethod", getRequestMethod())
            .append("requestParams", getRequestParams())
            .append("responseData", getResponseData())
            .append("executionTime", getExecutionTime())
            .append("createTime", getCreateTime())
            .toString();
    }
}