<template>
  <div class="app-container">
    <!-- 移动端结构 -->
    <div v-if="$store.getters.device === 'mobile'" class="mobile-inventory-container">
      <el-collapse v-model="mobileSearchVisible" class="mobile-search">
        <el-collapse-item title="搜索条件" name="search">
          <el-form :model="queryParams" ref="queryForm" size="small" label-width="80px">
            <el-form-item label="盘点单号" prop="checkCode">
              <el-input v-model="queryParams.checkCode" placeholder="请输入盘点单号" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="仓库名称" prop="warehouseId">
              <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable style="width: 100%">
                <el-option v-for="item in warehouseOptions" :key="item.warehouseId" :label="item.warehouseName" :value="item.warehouseId" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option v-for="dict in dict.type.inventory_check_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="盘点时间">
              <el-date-picker v-model="dateRange" style="width: 100%" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <!-- 移动端操作按钮区 -->
      <div class="mobile-actions">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['inventory:check:add']">新增</el-button>
        <el-button type="success" icon="el-icon-edit" size="small" :disabled="single" @click="handleUpdate" v-hasPermi="['inventory:check:edit']">修改</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple" @click="handleDelete" v-hasPermi="['inventory:check:remove']">删除</el-button>
        <el-dropdown size="small" style="margin-left: 10px;">
          <el-button size="small">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="report" icon="el-icon-s-data" v-hasPermi="['inventory:check:report']" @click.native="handleReport">报表</el-dropdown-item>
            <el-dropdown-item command="export" icon="el-icon-download" v-hasPermi="['inventory:check:export']" @click.native="handleExport('excel')">导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <!-- 移动端盘点卡片列表 -->
      <div v-loading="loading" class="mobile-check-list">
        <div v-for="item in checkList" :key="item.checkId" class="check-card">
          <div class="check-card-header">
            <div class="check-title">{{ item.checkCode }}</div>
            <el-tag size="mini" :type="item.status === '0' ? 'success' : 'info'" class="check-status">{{ getCheckStatusLabel(item.status) }}</el-tag>
          </div>
          <div class="check-card-body">
            <div class="check-detail"><span class="label">仓库:</span><span class="value">{{ item.warehouseName }}</span></div>
            <div class="check-detail"><span class="label">盘点时间:</span><span class="value">{{ item.checkTime }}</span></div>
          </div>
          <div class="check-card-actions" @click.stop>
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(item)" v-hasPermi="['inventory:check:query']">查看</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(item)" v-hasPermi="['inventory:check:edit']" v-if="item.status === '0'">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(item)" v-hasPermi="['inventory:check:remove']" v-if="item.status === '0'">删除</el-button>
            <el-button size="mini" type="text" icon="el-icon-check" @click="handleAudit(item)" v-hasPermi="['inventory:check:audit']" v-if="item.status === '0'">审核</el-button>
            <el-button size="mini" type="text" icon="el-icon-printer" @click="handlePrint(item)" v-hasPermi="['inventory:check:print']">打印</el-button>
          </div>
        </div>
      </div>
      <!-- 移动端分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      <!-- 移动端弹窗（新增/编辑） -->
      <el-dialog
        v-if="$store.getters.device === 'mobile'"
        :title="title"
        :visible.sync="open"
        width="100vw"
        top="0"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="仓库" prop="warehouseId">
                <el-select v-model="form.warehouseId" placeholder="请选择仓库" @change="handleWarehouseChange">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="盘点时间" prop="checkTime">
                <el-date-picker
                  v-model="form.checkTime"
                  type="datetime"
                  placeholder="选择盘点时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="center">盘点明细</el-divider>
          <el-row>
            <el-col :span="24">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddDetail">添加明细</el-button>
              <el-button type="success" icon="el-icon-refresh" size="mini" @click="handleLoadInventory" :disabled="!form.warehouseId">加载库存</el-button>
              <el-table :data="form.details" style="margin-top: 10px;">
                <el-table-column label="序号" type="index" width="55" align="center" />
                <el-table-column label="物品" prop="productName" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" placeholder="请选择物品" @change="(val) => handleProductChange(val, scope.$index)">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.productId"
                        :label="item.productName"
                        :value="item.productId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="账面数量" prop="bookQuantity" width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.bookQuantity" :min="0" :precision="2" :step="1" disabled />
                  </template>
                </el-table-column>
                <el-table-column label="实际数量" prop="realQuantity" width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.realQuantity" :min="0" :precision="2" :step="1" @change="handleRealQuantityChange(scope.$index)" />
                  </template>
                </el-table-column>
                <el-table-column label="差异数量" prop="diffQuantity" width="120">
                  <template slot-scope="scope">
                    <span :class="{'text-red': scope.row.diffQuantity < 0, 'text-green': scope.row.diffQuantity > 0}">
                      {{ scope.row.diffQuantity }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteDetail(scope.$index)"
                      v-hasPermi="['inventory:check:edit']"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer" style="display:flex;gap:4%;justify-content:space-between">
          <el-button type="primary" @click="submitForm" style="width:48%">确 定</el-button>
          <el-button @click="cancel" style="width:48%">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看盘点单对话框 -->
      <el-dialog title="查看盘点单" :visible.sync="viewOpen" width="780px" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="盘点单号">{{ viewForm.checkCode }}</el-descriptions-item>
          <el-descriptions-item label="仓库名称">{{ viewForm.warehouseName }}</el-descriptions-item>
          <el-descriptions-item label="盘点时间">{{ parseTime(viewForm.checkTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.inventory_check_status" :value="viewForm.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ viewForm.createBy }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="center">盘点明细</el-divider>
        <el-table :data="viewForm.details" style="margin-top: 10px;">
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物品名称" prop="productName" min-width="180" />
          <el-table-column label="物品编码" prop="productCode" width="120" />
          <el-table-column label="账面数量" prop="bookQuantity" width="100" />
          <el-table-column label="实际数量" prop="realQuantity" width="100" />
          <el-table-column label="差异数量" prop="diffQuantity" width="100">
            <template slot-scope="scope">
              <span :class="{'text-red': scope.row.diffQuantity < 0, 'text-green': scope.row.diffQuantity > 0}">
                {{ scope.row.diffQuantity }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 审核盘点单对话框 -->
      <el-dialog title="审核盘点单" :visible.sync="auditOpen" width="500px" append-to-body>
        <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
          <el-form-item label="盘点单号">{{ auditForm.checkCode }}</el-form-item>
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="auditForm.status">
              <el-radio label="1">通过</el-radio>
              <el-radio label="2">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见" prop="remark">
            <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入审核意见" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAudit">确 定</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>

    <div v-else>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="盘点单号" prop="checkCode">
          <el-input
            v-model="queryParams.checkCode"
            placeholder="请输入盘点单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库名称" prop="warehouseId">
          <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable>
            <el-option
              v-for="item in warehouseOptions"
              :key="item.warehouseId"
              :label="item.warehouseName"
              :value="item.warehouseId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in dict.type.inventory_check_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="盘点时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['inventory:check:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['inventory:check:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inventory:check:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport('excel')"
            v-hasPermi="['inventory:check:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-edit"
            size="mini"
            @click="handleBatchCheck"
            v-hasPermi="['inventory:check:add']"
          >批量盘点</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-camera"
            size="mini"
            @click="handleScan"
            v-hasPermi="['inventory:check:add']"
          >扫码盘点</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="checkList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="盘点ID" align="center" prop="checkId" v-if="false" />
        <el-table-column label="盘点单号" align="center" prop="checkCode" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" :show-overflow-tooltip="true" />
        <el-table-column label="盘点时间" align="center" prop="checkTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.checkTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.inventory_check_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="审核人" align="center" prop="auditBy" />
        <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.auditTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['inventory:check:query']"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['inventory:check:edit']"
              v-if="scope.row.status === '0'"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['inventory:check:remove']"
              v-if="scope.row.status === '0'"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleAudit(scope.row)"
              v-hasPermi="['inventory:check:audit']"
              v-if="scope.row.status === '0'"
            >审核</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-printer"
              @click="handlePrint(scope.row)"
              v-hasPermi="['inventory:check:print']"
            >打印</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改盘点单对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="仓库" prop="warehouseId">
                <el-select v-model="form.warehouseId" placeholder="请选择仓库" @change="handleWarehouseChange">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="盘点时间" prop="checkTime">
                <el-date-picker
                  v-model="form.checkTime"
                  type="datetime"
                  placeholder="选择盘点时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="center">盘点明细</el-divider>
          <el-row>
            <el-col :span="24">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddDetail">添加明细</el-button>
              <el-button type="success" icon="el-icon-refresh" size="mini" @click="handleLoadInventory" :disabled="!form.warehouseId">加载库存</el-button>
              <el-table :data="form.details" style="margin-top: 10px;">
                <el-table-column label="序号" type="index" width="55" align="center" />
                <el-table-column label="物品" prop="productName" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" placeholder="请选择物品" @change="(val) => handleProductChange(val, scope.$index)">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.productId"
                        :label="item.productName"
                        :value="item.productId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="账面数量" prop="bookQuantity" width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.bookQuantity" :min="0" :precision="2" :step="1" disabled />
                  </template>
                </el-table-column>
                <el-table-column label="实际数量" prop="realQuantity" width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.realQuantity" :min="0" :precision="2" :step="1" @change="handleRealQuantityChange(scope.$index)" />
                  </template>
                </el-table-column>
                <el-table-column label="差异数量" prop="diffQuantity" width="120">
                  <template slot-scope="scope">
                    <span :class="{'text-red': scope.row.diffQuantity < 0, 'text-green': scope.row.diffQuantity > 0}">
                      {{ scope.row.diffQuantity }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteDetail(scope.$index)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看盘点单对话框 -->
      <el-dialog title="查看盘点单" :visible.sync="viewOpen" width="780px" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="盘点单号">{{ viewForm.checkCode }}</el-descriptions-item>
          <el-descriptions-item label="仓库名称">{{ viewForm.warehouseName }}</el-descriptions-item>
          <el-descriptions-item label="盘点时间">{{ parseTime(viewForm.checkTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.inventory_check_status" :value="viewForm.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ viewForm.createBy }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="center">盘点明细</el-divider>
        <el-table :data="viewForm.details" style="margin-top: 10px;">
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物品名称" prop="productName" min-width="180" />
          <el-table-column label="物品编码" prop="productCode" width="120" />
          <el-table-column label="账面数量" prop="bookQuantity" width="100" />
          <el-table-column label="实际数量" prop="realQuantity" width="100" />
          <el-table-column label="差异数量" prop="diffQuantity" width="100">
            <template slot-scope="scope">
              <span :class="{'text-red': scope.row.diffQuantity < 0, 'text-green': scope.row.diffQuantity > 0}">
                {{ scope.row.diffQuantity }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 审核盘点单对话框 -->
      <el-dialog title="审核盘点单" :visible.sync="auditOpen" width="500px" append-to-body>
        <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
          <el-form-item label="盘点单号">{{ auditForm.checkCode }}</el-form-item>
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="auditForm.status">
              <el-radio label="1">通过</el-radio>
              <el-radio label="2">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见" prop="remark">
            <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入审核意见" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAudit">确 定</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 导出字段选择弹窗 -->
      <el-dialog title="选择导出字段" :visible.sync="exportFieldDialogVisible" width="400px">
        <el-checkbox-group v-model="selectedExportFields">
          <el-checkbox v-for="col in exportFieldOptions" :key="col.value" :label="col.value">{{ col.label }}</el-checkbox>
        </el-checkbox-group>
        <div slot="footer" class="dialog-footer">
          <el-button @click="exportFieldDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDoExport">导出</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listInventoryCheck, getInventoryCheck, delInventoryCheck, addInventoryCheck, updateInventoryCheck, auditInventoryCheck } from "@/api/inventory/check";
import { optionselect } from "@/api/system/warehouse";
import { listProduct } from "@/api/product/info";
import { listStock } from "@/api/inventory/stock";
import { getToken } from "@/utils/auth";
import axios from "axios";

export default {
  name: "InventoryCheck",
  dicts: ['inventory_check_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 盘点表格数据
      checkList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 日期范围
      dateRange: [],
      // 仓库选项
      warehouseOptions: [],
      // 物品选项
      productOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkCode: null,
        warehouseId: null,
        status: null
      },
      // 表单参数
      form: {
        details: []
      },
      // 表单校验
      rules: {
        warehouseId: [
          { required: true, message: "仓库不能为空", trigger: "change" }
        ],
        checkTime: [
          { required: true, message: "盘点时间不能为空", trigger: "blur" }
        ]
      },
      // 查看表单参数
      viewForm: {
        details: []
      },
      // 审核表单参数
      auditForm: {},
      // 审核表单校验
      auditRules: {
        status: [
          { required: true, message: "审核结果不能为空", trigger: "change" }
        ]
      },
      // 移动端搜索折叠面板
      mobileSearchVisible: ['search'],
      // 导出字段选择
      exportFieldDialogVisible: false,
      selectedExportFields: [],
      exportFieldOptions: [
        { label: '盘点单号', value: 'checkCode' },
        { label: '仓库名称', value: 'warehouseName' },
        { label: '盘点时间', value: 'checkTime' },
        { label: '状态', value: 'status' },
        { label: '物品名称', value: 'productName' },
        { label: '物品编码', value: 'productCode' },
        { label: '账面数量', value: 'bookQuantity' },
        { label: '实际数量', value: 'realQuantity' },
        { label: '差异数量', value: 'diffQuantity' },
        { label: '操作人员', value: 'operateBy' },
        { label: '审核人', value: 'auditBy' },
        { label: '审核时间', value: 'auditTime' }
      ]
    };
  },
  created() {
    this.getList();
    this.getWarehouseOptions();
    this.getProductOptions();
  },
  methods: {
    /** 查询盘点列表 */
    getList() {
      this.loading = true;
      listInventoryCheck(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.checkList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取仓库选项 */
    getWarehouseOptions() {
      optionselect().then(response => {
        this.warehouseOptions = response.data || response.rows;
      });
    },
    /** 获取物品选项 */
    getProductOptions() {
      listProduct().then(response => {
        this.productOptions = response.rows;
      });
    },
    // 物品选择事件
    handleProductChange(value, index) {
      const product = this.productOptions.find(item => item.productId === value);
      if (product) {
        this.form.details[index].productName = product.productName;
        this.form.details[index].productCode = product.productCode;

        // 查询库存数量
        if (this.form.warehouseId) {
          const params = {
            productId: value,
            warehouseId: this.form.warehouseId
          };
          listStock(params).then(response => {
            if (response.rows && response.rows.length > 0) {
              this.form.details[index].bookQuantity = response.rows[0].quantity;
              this.handleRealQuantityChange(index);
            } else {
              this.form.details[index].bookQuantity = 0;
              this.handleRealQuantityChange(index);
            }
          });
        }
      }
    },
    // 仓库选择事件
    handleWarehouseChange() {
      // 清空明细
      this.form.details = [];
    },
    // 实际数量变更事件
    handleRealQuantityChange(index) {
      const detail = this.form.details[index];
      if (detail.bookQuantity !== undefined && detail.realQuantity !== undefined) {
        detail.diffQuantity = detail.realQuantity - detail.bookQuantity;
      }
    },
    // 加载库存
    handleLoadInventory() {
      if (!this.form.warehouseId) {
        this.$modal.msgError("请先选择仓库");
        return;
      }

      const params = {
        warehouseId: this.form.warehouseId
      };
      listStock(params).then(response => {
        if (response.rows && response.rows.length > 0) {
          this.form.details = [];
          response.rows.forEach(item => {
            this.form.details.push({
              productId: item.productId,
              productName: item.productName,
              productCode: item.productCode,
              bookQuantity: item.quantity,
              realQuantity: item.quantity,
              diffQuantity: 0
            });
          });
        } else {
          this.$modal.msgInfo("该仓库暂无库存");
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        checkId: null,
        checkCode: null,
        warehouseId: null,
        checkTime: null,
        status: "0",
        remark: null,
        details: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.checkId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 使用parseTime格式化日期为后端期望的格式
      this.form.checkTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
      this.open = true;
      this.title = "添加盘点单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const checkId = row.checkId || this.ids[0];
      getInventoryCheck(checkId).then(response => {
        const data = response.data;
        // 确保日期格式正确
        if (data.checkTime) {
          data.checkTime = this.parseTime(data.checkTime, '{y}-{m}-{d} {h}:{i}:{s}');
        }
        this.form = data;
        this.open = true;
        this.title = "修改盘点单";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getInventoryCheck(row.checkId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm = {
        checkId: row.checkId,
        checkCode: row.checkCode,
        status: "1",
        remark: ""
      };
      this.auditOpen = true;
    },
    /** 打印按钮操作 */
    handlePrint(row) {
      this.$router.push({ path: `/inventory/check/print/${row.checkId}` });
    },
    /** 扫码盘点按钮操作 */
    handleScan() {
      this.$router.push({ path: "/inventory/check-scan/index" });
    },
    /** 批量盘点按钮操作 */
    handleBatchCheck() {
      this.$router.push({ path: "/inventory/check/batch/index" });
    },
    /** 添加明细 */
    handleAddDetail() {
      this.form.details.push({
        productId: null,
        productName: null,
        productCode: null,
        bookQuantity: 0,
        realQuantity: 0,
        diffQuantity: 0
      });
    },
    /** 删除明细 */
    handleDeleteDetail(index) {
      this.form.details.splice(index, 1);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查明细是否为空
          if (this.form.details.length === 0) {
            this.$modal.msgError("请添加盘点明细");
            return;
          }

          // 检查明细是否填写完整
          for (let i = 0; i < this.form.details.length; i++) {
            const detail = this.form.details[i];
            if (!detail.productId) {
              this.$modal.msgError("请选择物品");
              return;
            }
            if (detail.realQuantity === undefined || detail.realQuantity === null) {
              this.$modal.msgError("请输入实际数量");
              return;
            }
          }

          // 确保日期格式正确
          const formData = JSON.parse(JSON.stringify(this.form));
          if (formData.checkTime && typeof formData.checkTime === 'string') {
            // 如果日期已经是字符串格式，确保格式正确
            if (!formData.checkTime.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
              formData.checkTime = this.parseTime(formData.checkTime, '{y}-{m}-{d} {h}:{i}:{s}');
            }
          }

          if (formData.checkId != null) {
            updateInventoryCheck(formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventoryCheck(formData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交审核 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditInventoryCheck(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const checkIds = row.checkId || this.ids;
      this.$modal.confirm('是否确认删除盘点单编号为"' + checkIds + '"的数据项？').then(() => {
        return delInventoryCheck(checkIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport(command) {
      this.exportType = command;
      this.exportFieldDialogVisible = true;
      // 默认全选
      if (this.selectedExportFields.length === 0) {
        this.selectedExportFields = this.exportFieldOptions.map(opt => opt.value);
      }
    },
    handleDoExport() {
      if (!this.selectedExportFields.length) {
        this.$modal.msgError('请至少选择一个导出字段');
        return;
      }
      const params = { ...this.queryParams, columns: this.selectedExportFields.join(',') };
      let url = '';
      let fileName = '';
      if (this.exportType === 'excel') {
        url = '/api/v1/reports/export/check/excel';
        fileName = `盘点明细报表_${new Date().getTime()}.xlsx`;
      } else if (this.exportType === 'pdf') {
        url = '/api/v1/reports/export/check/pdf';
        fileName = `盘点明细报表_${new Date().getTime()}.pdf`;
      }
      this.exportReport(url, params, fileName);
      this.exportFieldDialogVisible = false;
    },
    /**
     * 导出报表
     * @param {string} url - 导出URL
     * @param {object} params - 查询参数
     * @param {string} fileName - 文件名
     */
    exportReport(url, params, fileName) {
      this.$modal.loading("正在导出数据，请稍候...");
      const baseUrl = process.env.VUE_APP_BASE_API;
      const fullUrl = baseUrl + url;
      axios({
        method: 'get',
        url: fullUrl,
        params: params,
        responseType: 'blob',
        headers: { 'Authorization': 'Bearer ' + getToken() }
      }).then(response => {
        const blob = new Blob([response.data]);
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$modal.closeLoading();
        this.$modal.msgSuccess("导出成功");
      }).catch(() => {
        this.$modal.closeLoading();
        this.$modal.msgError("导出失败，请重试");
      });
    },
    getCheckStatusLabel: function(status) {
      var arr = this.dict.type.inventory_check_status;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].value == status) return arr[i].label;
      }
      return status;
    }
  }
};
</script>

<style scoped>
.text-red {
  color: #F56C6C;
}
.text-green {
  color: #67C23A;
}
.mobile-inventory-container {
  padding: 10px;
}
.mobile-search {
  margin-bottom: 10px;
}
.mobile-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.mobile-check-list {
  margin-top: 12px;
}
.check-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  margin-bottom: 12px;
  padding: 12px;
}
.check-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}
.check-title {
  font-weight: bold;
  font-size: 16px;
  flex: 1;
}
.check-status {
  margin-left: 8px;
}
.check-card-body {
  margin-bottom: 8px;
}
.check-detail {
  font-size: 13px;
  margin-bottom: 2px;
}
.check-detail .label {
  color: #888;
  margin-right: 4px;
}
.check-detail .value {
  color: #333;
}
.check-card-actions {
  display: flex;
  gap: 8px;
}
</style>