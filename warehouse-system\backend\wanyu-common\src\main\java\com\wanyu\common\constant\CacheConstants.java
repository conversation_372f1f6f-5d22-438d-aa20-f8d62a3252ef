package com.wanyu.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 登录用户 cache key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 cache key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 cache key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 cache key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 cache key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 用户权限列表 cache key
     */
    public static final String USER_PERMISSION_KEY = "user_permission:";

    /**
     * 角色权限列表 cache key
     */
    public static final String ROLE_PERMISSION_KEY = "role_permission:";

    /**
     * 数据权限 cache key
     */
    public static final String DATA_SCOPE_KEY = "data_scope:";

    /**
     * 仓库权限 cache key
     */
    public static final String WAREHOUSE_PERMISSION_KEY = "warehouse_permission:";

    /**
     * 权限日志 cache key
     */
    public static final String PERMISSION_LOG_KEY = "permission_log:";

    /**
     * 系统数据缓存 cache key
     */
    public static final String SYS_DATA_CACHE = "sys_data_cache:";

    /**
     * 系统权限缓存 cache key
     */
    public static final String SYS_AUTH_CACHE = "sys_auth_cache:";

    /**
     * 用户权限缓存 cache key
     */
    public static final String SYS_USER_PERMISSIONS = "sys_user_permissions:";

    /**
     * 用户数据权限缓存 cache key
     */
    public static final String SYS_USER_DATA_PERMISSIONS = "sys_user_data_permissions:";

    /**
     * 用户API权限缓存 cache key
     */
    public static final String SYS_USER_API_PERMISSIONS = "sys_user_api_permissions:";

    /**
     * 用户仓库权限缓存 cache key
     */
    public static final String SYS_USER_WAREHOUSE_PERMISSIONS = "sys_user_warehouse_permissions:";
}