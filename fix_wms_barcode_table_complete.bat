@echo off
chcp 65001 >nul
echo ========================================
echo 物品条码表修复脚本
echo 修复表名从 product_barcode 到 wms_barcode
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo 🔧 开始修复物品条码表...
echo.

echo 📋 执行修复SQL脚本...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < fix_wms_barcode_table_complete.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 数据库修复完成！
    echo.
    echo 📊 验证修复结果...
    echo.
    
    echo 查询wms_barcode表记录数:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as barcode_count FROM wms_barcode;"
    
    echo.
    echo 查询wms_barcode_template表记录数:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as template_count FROM wms_barcode_template;"
    
    echo.
    echo 查询条码类型字典数据:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'wms_barcode_type' ORDER BY dict_sort;"
    
    echo.
    echo ========================================
    echo ✅ 物品条码表修复完成！
    echo ========================================
    echo.
    echo 📝 修复内容:
    echo   1. ✅ 创建/验证 wms_barcode 表
    echo   2. ✅ 创建/验证 wms_barcode_template 表  
    echo   3. ✅ 迁移旧数据（如果存在）
    echo   4. ✅ 插入默认模板数据
    echo   5. ✅ 插入条码类型字典数据
    echo.
    echo 🔄 下一步操作:
    echo   1. 重新编译后端项目
    echo   2. 重启后端服务
    echo   3. 测试物品条码功能
    echo.
    echo 💡 提示: 新的实体类和服务已创建:
    echo   - WmsBarcode.java
    echo   - WmsBarcodeTemplate.java  
    echo   - WmsBarcodeMapper.java
    echo   - WmsBarcodeService.java
    echo   - ProductBarcodeController.java (已更新)
    echo.
) else (
    echo.
    echo ❌ 数据库修复失败！
    echo 请检查数据库连接和权限设置
    echo.
)

echo 按任意键退出...
pause >nul