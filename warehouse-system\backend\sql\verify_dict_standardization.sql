-- Verification script for dictionary data standardization

-- Check sys_normal_disable dictionary
SELECT 
    'sys_normal_disable Dictionary Check' as check_type,
    dict_label,
    dict_value,
    list_class,
    is_default,
    CASE 
        WHEN dict_value = '0' AND dict_label = 'Normal' THEN 'PASS - 0=Normal (Standard)'
        WHEN dict_value = '1' AND dict_label = 'Disabled' THEN 'PASS - 1=Disabled (Standard)'
        ELSE 'FAIL - Non-standard definition'
    END as validation_status
FROM sys_dict_data 
WHERE dict_type = 'sys_normal_disable'
ORDER BY dict_value;

-- Check operation_status dictionary
SELECT 
    'operation_status Dictionary Check' as check_type,
    dict_label,
    dict_value,
    list_class,
    is_default,
    CASE 
        WHEN dict_value = '0' AND dict_label = 'Success' THEN 'PASS - 0=Success (Standard)'
        WHEN dict_value = '1' AND dict_label = 'Failed' THEN 'PASS - 1=Failed (Standard)'
        ELSE 'FAIL - Non-standard definition'
    END as validation_status
FROM sys_dict_data 
WHERE dict_type = 'operation_status'
ORDER BY dict_value;

-- Summary of all status-related dictionaries
SELECT 
    'Dictionary Summary' as summary_type,
    dict_type,
    COUNT(*) as entry_count,
    GROUP_CONCAT(CONCAT(dict_value, '=', dict_label) ORDER BY dict_value SEPARATOR ', ') as value_mappings
FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status')
GROUP BY dict_type
ORDER BY dict_type;