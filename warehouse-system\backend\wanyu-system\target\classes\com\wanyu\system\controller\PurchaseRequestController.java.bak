package com.wanyu.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.system.domain.WmsPurchaseRequest;
import com.wanyu.system.service.IWmsPurchaseRequestService;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;

import java.math.BigDecimal;

/**
 * 申购管理Controller
 * 
 * <AUTHOR>
 * @date 2023-08-20
 */
@RestController
@RequestMapping("/inventory/purchaseRequest")
public class PurchaseRequestController extends BaseController
{
    @Autowired
    private IWmsPurchaseRequestService purchaseRequestService;

    /**
     * 查询申购管理列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:purchase:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmsPurchaseRequest purchaseRequest)
    {
        startPage();
        List<WmsPurchaseRequest> list = purchaseRequestService.selectWmsPurchaseRequestList(purchaseRequest);
        return getDataTable(list);
    }

    /**
     * 导出申购管理列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:purchase:export')")
    @Log(title = "申购管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmsPurchaseRequest purchaseRequest)
    {
        List<WmsPurchaseRequest> list = purchaseRequestService.selectWmsPurchaseRequestList(purchaseRequest);
        ExcelUtil<WmsPurchaseRequest> util = new ExcelUtil<WmsPurchaseRequest>(WmsPurchaseRequest.class);
        util.exportExcel(response, list, "申购管理数据");
    }

    /**
     * 获取申购管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:purchase:query')")
    @GetMapping(value = "/{requestId}")
    public AjaxResult getInfo(@PathVariable("requestId") Long requestId)
    {
        return AjaxResult.success(purchaseRequestService.selectWmsPurchaseRequestByRequestId(requestId));
    }

    /**
     * 新增申购管理
     */
    @PreAuthorize("@ss.hasPermi('inventory:purchase:add')")
    @Log(title = "申购管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmsPurchaseRequest purchaseRequest)
    {
        // 计算总金额
        if (purchaseRequest.getQuantity() != null && purchaseRequest.getUnitPrice() != null) {
            purchaseRequest.setTotalPrice(new BigDecimal(purchaseRequest.getQuantity()).multiply(purchaseRequest.getUnitPrice()));
        }
        return toAjax(purchaseRequestService.insertWmsPurchaseRequest(purchaseRequest));
    }

    /**
     * 修改申购管理
     */
    @PreAuthorize("@ss.hasPermi('inventory:purchase:edit')")
    @Log(title = "申购管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmsPurchaseRequest purchaseRequest)
    {
        // 计算总金额
        if (purchaseRequest.getQuantity() != null && purchaseRequest.getUnitPrice() != null) {
            purchaseRequest.setTotalPrice(new BigDecimal(purchaseRequest.getQuantity()).multiply(purchaseRequest.getUnitPrice()));
        }
        return toAjax(purchaseRequestService.updateWmsPurchaseRequest(purchaseRequest));
    }

    /**
     * 删除申购管理
     */
    @PreAuthorize("@ss.hasPermi('inventory:purchase:remove')")
    @Log(title = "申购管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{requestIds}")
    public AjaxResult remove(@PathVariable Long[] requestIds)
    {
        return toAjax(purchaseRequestService.deleteWmsPurchaseRequestByRequestIds(requestIds));
    }
}