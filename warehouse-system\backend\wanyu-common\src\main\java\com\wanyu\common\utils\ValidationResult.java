package com.wanyu.common.utils;

import java.util.List;

/**
 * 字段验证结果类
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class ValidationResult {
    
    /** 字段名称 */
    private String fieldName;
    
    /** 字段类型 */
    private FieldStandardValidator.FieldType fieldType;
    
    /** 字段值 */
    private String value;
    
    /** 是否有效 */
    private boolean valid;
    
    /** 期望的值列表 */
    private List<String> expectedValues;
    
    /** 错误信息 */
    private String errorMessage;
    
    public ValidationResult() {
    }
    
    public ValidationResult(String fieldName, FieldStandardValidator.FieldType fieldType, 
                          String value, boolean valid) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.value = value;
        this.valid = valid;
    }
    
    // Getter and Setter methods
    public String getFieldName() {
        return fieldName;
    }
    
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
    
    public FieldStandardValidator.FieldType getFieldType() {
        return fieldType;
    }
    
    public void setFieldType(FieldStandardValidator.FieldType fieldType) {
        this.fieldType = fieldType;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public boolean isValid() {
        return valid;
    }
    
    public void setValid(boolean valid) {
        this.valid = valid;
    }
    
    public List<String> getExpectedValues() {
        return expectedValues;
    }
    
    public void setExpectedValues(List<String> expectedValues) {
        this.expectedValues = expectedValues;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    @Override
    public String toString() {
        return "ValidationResult{" +
                "fieldName='" + fieldName + '\'' +
                ", fieldType=" + fieldType +
                ", value='" + value + '\'' +
                ", valid=" + valid +
                ", expectedValues=" + expectedValues +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}