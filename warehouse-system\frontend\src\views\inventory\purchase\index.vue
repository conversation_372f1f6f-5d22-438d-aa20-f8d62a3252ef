<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申购单号" prop="requestNo">
        <el-input
          v-model="queryParams.requestNo"
          placeholder="请输入申购单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.request_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inventory:purchase:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inventory:purchase:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inventory:purchase:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inventory:purchase:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purchaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申购ID" align="center" prop="requestId" />
      <el-table-column label="申购单号" align="center" prop="requestNo" />
      <el-table-column label="申请人" align="center" prop="applicant" />
      <el-table-column label="所属部门" align="center" prop="department" />
      <el-table-column label="商品名称" align="center" prop="productName" />
      <el-table-column label="商品编码" align="center" prop="productCode" />
      <el-table-column label="规格型号" align="center" prop="specifications" />
      <el-table-column label="单位" align="center" prop="unit" />
      <el-table-column label="申购数量" align="center" prop="quantity" />
      <el-table-column label="已入库数量" align="center" prop="stockInCount" />
      <el-table-column label="单价" align="center" prop="unitPrice" />
      <el-table-column label="总金额" align="center" prop="totalPrice" />
      <el-table-column label="紧急程度" align="center" prop="urgencyLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.urgency_level" :value="scope.row.urgencyLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="期望交付日期" align="center" prop="expectedDeliveryDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectedDeliveryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" :show-overflow-tooltip="false" min-width="120">
        <template slot-scope="scope">
          <span style="white-space: normal; word-wrap: break-word; display: block; line-height: 1.5;">
            <dict-tag :options="dict.type.request_status" :value="scope.row.status"/>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="关联入库单号" align="center" prop="relatedInboundOrderNo" :show-overflow-tooltip="false" min-width="150">
        <template slot-scope="scope">
          <span style="white-space: normal; word-wrap: break-word; display: block; line-height: 1.5;">
            <span v-if="scope.row.relatedInboundOrderNo">
              {{ scope.row.relatedInboundOrderNo }}
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['inventory:purchase:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-printer"
            @click="handlePrint(scope.row)"
            v-hasPermi="['inventory:purchase:print']"
          >打印</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-if="['0', '1'].includes(scope.row.status)"
            v-hasPermi="['inventory:purchase:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleApprove(scope.row)"
            v-if="['0', '1'].includes(scope.row.status)"
            v-hasPermi="['inventory:purchase:approve']"
          >通过</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleReject(scope.row)"
            v-if="['0', '1'].includes(scope.row.status)"
            v-hasPermi="['inventory:purchase:approve']"
          >拒绝</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-if="['0', '1'].includes(scope.row.status)"
            v-hasPermi="['inventory:purchase:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改申购管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="申购单号" prop="requestNo">
              <el-input v-model="form.requestNo" placeholder="系统自动生成" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-input v-model="form.applicant" placeholder="请输入申请人" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="department">
              <el-input v-model="form.department" placeholder="请输入所属部门" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="urgencyLevel">
              <el-select v-model="form.urgencyLevel" placeholder="请选择紧急程度" :disabled="formDisabled">
                <el-option
                  v-for="dict in dict.type.urgency_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="商品信息" prop="productId">
              <el-select 
                v-model="form.productId" 
                filterable 
                remote 
                reserve-keyword 
                placeholder="请输入商品名称搜索" 
                :remote-method="searchProducts" 
                :loading="productLoading"
                @change="handleProductChange"
                style="width: 100%"
                :disabled="formDisabled">
                <el-option
                  v-for="item in productList"
                  :key="item.productId"
                  :label="item.productName"
                  :value="item.productId">
                  <span style="float: left">{{ item.productName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.productCode }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入商品名称" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品编码" prop="productCode">
              <el-input v-model="form.productCode" placeholder="请输入商品编码" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规格型号" prop="specifications">
              <el-input v-model="form.specifications" placeholder="请输入规格型号" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入单位" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="申购数量" prop="quantity">
              <el-input-number v-model="form.quantity" placeholder="请输入申购数量" :min="1" @change="calculateTotalPrice" style="width: 100%" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单价" prop="unitPrice">
              <el-input-number v-model="form.unitPrice" placeholder="请输入单价" :min="0" :precision="2" :step="0.01" @change="calculateTotalPrice" style="width: 100%" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="总金额" prop="totalPrice">
              <el-input-number v-model="form.totalPrice" placeholder="请输入总金额" :min="0" :precision="2" :step="0.01" :disabled="true" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="期望交付日期" prop="expectedDeliveryDate">
              <el-date-picker clearable
                v-model="form.expectedDeliveryDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择期望交付日期"
                style="width: 100%"
                :disabled="formDisabled">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="用途说明" prop="purpose">
              <el-input v-model="form.purpose" type="textarea" placeholder="请输入内容" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.status === '3' && form.relatedInboundOrderNo">
          <el-col :span="24">
            <el-form-item label="关联入库单号">
              <el-input v-model="form.relatedInboundOrderNo" :disabled="formDisabled" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-if="!formDisabled">确 定</el-button>
        <el-button type="primary" @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 审批对话框 -->
    <el-dialog :title="approvalTitle" :visible.sync="approvalOpen" width="500px" append-to-body>
      <el-form ref="approvalForm" :model="approvalForm" label-width="80px">
        <el-form-item label="审批意见">
          <el-input type="textarea" v-model="approvalForm.approvalComment" placeholder="请输入审批意见（可选）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitApproval">确 定</el-button>
        <el-button @click="cancelApproval">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurchase, getPurchase, delPurchase, addPurchase, updatePurchase, approvePurchase, rejectPurchase, getCurrentUser } from "@/api/inventory/purchase";
import { listProduct } from "@/api/product/info";

export default {
  name: "Purchase",
  dicts: ['urgency_level', 'request_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 申购管理表格数据
      purchaseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示审批弹出层
      approvalOpen: false,
      // 审批弹出层标题
      approvalTitle: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        applicant: null,
        department: null,
        productName: null,
        productCode: null,
        specifications: null,
        unit: null,
        quantity: null,
        unitPrice: null,
        totalPrice: null,
        purpose: null,
        urgencyLevel: null,
        expectedDeliveryDate: null,
        status: null,
        approver: null,
        approvalTime: null,
        approvalComment: null,
        relatedInboundOrderNo: null
      },
      // 表单参数
      form: {},
      // 表单是否禁用
      formDisabled: false,
      // 商品列表
      productList: [],
      // 商品加载状态
      productLoading: false,
      // 审批表单参数
      approvalForm: {
        requestId: null,
        approvalComment: null,
        action: null // 'approve' or 'reject'
      },
      // 表单校验
      rules: {
        applicant: [
          { required: true, message: "申请人不能为空", trigger: "blur" }
        ],
        department: [
          { required: true, message: "所属部门不能为空", trigger: "blur" }
        ],
        productName: [
          { required: true, message: "商品名称不能为空", trigger: "blur" }
        ],
        quantity: [
          { required: true, message: "申购数量不能为空", trigger: "blur" }
        ],
        unitPrice: [
          { required: true, message: "单价不能为空", trigger: "blur" }
        ],
        totalPrice: [
          { required: true, message: "总金额不能为空", trigger: "blur" }
        ],
        urgencyLevel: [
          { required: true, message: "紧急程度不能为空", trigger: "change" }
        ],
        expectedDeliveryDate: [
          { required: true, message: "期望交付日期不能为空", trigger: "blur" }
        ]
      }
    };
    
  },
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },
  created() {
    this.getList();
  },
  activated() {
    // 每次页面激活时自动刷新数据，确保显示最新状态
    this.getList();
    console.log('申购管理页面已激活，数据已刷新');
  },
  deactivated() {
    // 页面失活时的操作
    console.log('申购管理页面已失活');
  },
  methods: {
    /** 查询申购管理列表 */
    getList() {
      this.loading = true;
      listPurchase(this.queryParams).then(response => {
        this.purchaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      this.formDisabled = false; // 确保表单状态恢复为可用
    },
    // 取消审批
    cancelApproval() {
      this.approvalOpen = false;
      this.resetApproval();
    },
    // 表单重置
    reset() {
      this.form = {
        requestId: null,
        requestNo: null,
        applicant: null,
        department: null,
        productName: null,
        productCode: null,
        specifications: null,
        unit: null,
        quantity: null,
        unitPrice: null,
        totalPrice: null,
        purpose: null,
        urgencyLevel: null,
        expectedDeliveryDate: null,
        status: null,
        approver: null,
        approvalTime: null,
        approvalComment: null,
        productId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        stockInCount: null,
        relatedInboundOrderNo: null
      };
      this.formDisabled = false;
      this.resetForm("form");
    },
    // 审批表单重置
    resetApproval() {
      this.approvalForm = {
        requestId: null,
        approvalComment: null,
        action: null
      };
      this.resetForm("approvalForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.requestId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 获取当前用户信息并填充到表单中
      getCurrentUser().then(response => {
        if (response.code === 200) {
          const userInfo = response.data;
          this.form.applicant = userInfo.realName || userInfo.userName;
          this.form.department = userInfo.deptName;
        }
        this.open = true;
        this.title = "添加申购管理";
      }).catch(() => {
        this.open = true;
        this.title = "添加申购管理";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const requestId = row.requestId || this.ids
      getPurchase(requestId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改申购管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 计算总金额
          this.calculateTotalPrice();
          
          if (this.form.requestId != null) {
            updatePurchase(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPurchase(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const requestIds = row.requestId || this.ids;
      this.$modal.confirm('是否确认删除申购管理编号为"' + requestIds + '"的数据项？').then(function() {
        return delPurchase(requestIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inventory/purchase/export', {
        ...this.queryParams
      }, `purchase_${new Date().getTime()}.xlsx`)
    },
    /** 查看详情 */
    handleView(row) {
      this.reset();
      getPurchase(row.requestId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "申购详情";
        this.formDisabled = true; // 仅在查看详情时禁用表单
      });
    },
    
    /** 打印申购单 */
    handlePrint(row) {
      this.$router.push(`/inventory/purchase/print/${row.requestId}`);
    },
    /** 处理审批通过 */
    handleApprove(row) {
      this.resetApproval();
      this.approvalForm.requestId = row.requestId;
      this.approvalForm.action = 'approve';
      this.approvalTitle = "审批通过";
      this.approvalOpen = true;
    },
    /** 处理审批拒绝 */
    handleReject(row) {
      this.resetApproval();
      this.approvalForm.requestId = row.requestId;
      this.approvalForm.action = 'reject';
      this.approvalTitle = "审批拒绝";
      this.approvalOpen = true;
    },
    /** 提交审批 */
    submitApproval() {
      if (this.approvalForm.action === 'approve') {
        approvePurchase(this.approvalForm.requestId, this.approvalForm.approvalComment).then(response => {
          this.$modal.msgSuccess("审批通过成功");
          this.approvalOpen = false;
          this.getList();
        });
      } else if (this.approvalForm.action === 'reject') {
        rejectPurchase(this.approvalForm.requestId, this.approvalForm.approvalComment).then(response => {
          this.$modal.msgSuccess("审批拒绝成功");
          this.approvalOpen = false;
          this.getList();
        });
      }
    },
    /** 计算总金额 */
    calculateTotalPrice() {
      if (this.form.quantity && this.form.unitPrice) {
        this.form.totalPrice = this.form.quantity * this.form.unitPrice;
      } else {
        this.form.totalPrice = 0;
      }
    },
    /** 搜索商品 */
    searchProducts(query) {
      if (query !== '') {
        this.productLoading = true;
        // 使用防抖避免频繁请求
        if (this.searchTimer) {
          clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
          listProduct({ productName: query }).then(response => {
            this.productList = response.rows;
            this.productLoading = false;
          });
        }, 300);
      } else {
        this.productList = [];
      }
    },
    /** 商品选择变化 */
    handleProductChange(value) {
      if (!value) {
        // 如果没有选择商品，清空表单中的商品相关信息
        this.form.productName = '';
        this.form.productCode = '';
        this.form.specifications = '';
        this.form.unit = '';
        return;
      }
      
      const selected = this.productList.find(item => item.productId === value);
      if (selected) {
        this.form.productName = selected.productName;
        this.form.productCode = selected.productCode;
        this.form.specifications = selected.specName;
        this.form.unit = selected.unitName;
      }
    },
    /** 商品选择框清空 */
    handleProductClear() {
      this.form.productName = '';
      this.form.productCode = '';
      this.form.specifications = '';
      this.form.unit = '';
    },
  }
};
</script>