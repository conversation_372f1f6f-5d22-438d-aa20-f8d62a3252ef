<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.SysSecurityLogMapper">
    
    <resultMap type="SysSecurityLog" id="SysSecurityLogResult">
        <result property="logId"    column="log_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="eventType"    column="event_type"    />
        <result property="eventDesc"    column="event_desc"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="clientIp"    column="client_ip"    />
        <result property="clientLocation"    column="client_location"    />
        <result property="userAgent"    column="user_agent"    />
        <result property="status"    column="status"    />
        <result property="handleBy"    column="handle_by"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleRemark"    column="handle_remark"    />
        <result property="eventTime"    column="event_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysSecurityLogVo">
        select log_id, user_name, nick_name, event_type, event_desc, risk_level, client_ip, client_location, user_agent, status, handle_by, handle_time, handle_remark, event_time, create_by, create_time, update_by, update_time, remark from sys_security_log
    </sql>

    <select id="selectSysSecurityLogList" parameterType="SysSecurityLog" resultMap="SysSecurityLogResult">
        <include refid="selectSysSecurityLogVo"/>
        <where>  
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(event_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(event_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by event_time desc
    </select>
    
    <select id="selectSysSecurityLogByLogId" parameterType="Long" resultMap="SysSecurityLogResult">
        <include refid="selectSysSecurityLogVo"/>
        where log_id = #{logId}
    </select>
        
    <insert id="insertSysSecurityLog" parameterType="SysSecurityLog" useGeneratedKeys="true" keyProperty="logId">
        insert into sys_security_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="eventType != null">event_type,</if>
            <if test="eventDesc != null">event_desc,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="clientIp != null">client_ip,</if>
            <if test="clientLocation != null">client_location,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="status != null">status,</if>
            <if test="handleBy != null">handle_by,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handleRemark != null">handle_remark,</if>
            <if test="eventTime != null">event_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="eventDesc != null">#{eventDesc},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="clientIp != null">#{clientIp},</if>
            <if test="clientLocation != null">#{clientLocation},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="status != null">#{status},</if>
            <if test="handleBy != null">#{handleBy},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handleRemark != null">#{handleRemark},</if>
            <if test="eventTime != null">#{eventTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysSecurityLog" parameterType="SysSecurityLog">
        update sys_security_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="eventDesc != null">event_desc = #{eventDesc},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="clientIp != null">client_ip = #{clientIp},</if>
            <if test="clientLocation != null">client_location = #{clientLocation},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="handleBy != null">handle_by = #{handleBy},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleRemark != null">handle_remark = #{handleRemark},</if>
            <if test="eventTime != null">event_time = #{eventTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteSysSecurityLogByLogId" parameterType="Long">
        delete from sys_security_log where log_id = #{logId}
    </delete>

    <delete id="deleteSysSecurityLogByLogIds" parameterType="String">
        delete from sys_security_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
</mapper>
