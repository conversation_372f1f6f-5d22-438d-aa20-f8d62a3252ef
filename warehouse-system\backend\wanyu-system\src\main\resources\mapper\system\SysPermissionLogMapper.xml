<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.SysPermissionLogMapper">
    
    <resultMap type="SysPermissionLog" id="SysPermissionLogResult">
        <result property="logId"    column="log_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="permission"    column="permission"    />
        <result property="method"    column="method"    />
        <result property="url"    column="url"    />
        <result property="ip"    column="ip"    />
        <result property="result"    column="result"    />
        <result property="failReason"    column="fail_reason"    />
        <result property="operTime"    column="oper_time"    />
    </resultMap>

    <sql id="selectSysPermissionLogVo">
        select log_id, user_id, user_name, permission_type, permission, method, url, ip, result, fail_reason, oper_time from sys_permission_log
    </sql>

    <select id="selectSysPermissionLogList" parameterType="SysPermissionLog" resultMap="SysPermissionLogResult">
        <include refid="selectSysPermissionLogVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="permissionType != null  and permissionType != ''"> and permission_type = #{permissionType}</if>
            <if test="permission != null  and permission != ''"> and permission like concat('%', #{permission}, '%')</if>
            <if test="method != null  and method != ''"> and method = #{method}</if>
            <if test="url != null  and url != ''"> and url like concat('%', #{url}, '%')</if>
            <if test="ip != null  and ip != ''"> and ip like concat('%', #{ip}, '%')</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(oper_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(oper_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by oper_time desc
    </select>
    
    <select id="selectSysPermissionLogByLogId" parameterType="Long" resultMap="SysPermissionLogResult">
        <include refid="selectSysPermissionLogVo"/>
        where log_id = #{logId}
    </select>
        
    <insert id="insertSysPermissionLog" parameterType="SysPermissionLog" useGeneratedKeys="true" keyProperty="logId">
        insert into sys_permission_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="permissionType != null">permission_type,</if>
            <if test="permission != null">permission,</if>
            <if test="method != null">method,</if>
            <if test="url != null">url,</if>
            <if test="ip != null">ip,</if>
            <if test="result != null">result,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="operTime != null">oper_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="permissionType != null">#{permissionType},</if>
            <if test="permission != null">#{permission},</if>
            <if test="method != null">#{method},</if>
            <if test="url != null">#{url},</if>
            <if test="ip != null">#{ip},</if>
            <if test="result != null">#{result},</if>
            <if test="failReason != null">#{failReason},</if>
            <if test="operTime != null">#{operTime},</if>
         </trim>
    </insert>

    <update id="updateSysPermissionLog" parameterType="SysPermissionLog">
        update sys_permission_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="permissionType != null">permission_type = #{permissionType},</if>
            <if test="permission != null">permission = #{permission},</if>
            <if test="method != null">method = #{method},</if>
            <if test="url != null">url = #{url},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="result != null">result = #{result},</if>
            <if test="failReason != null">fail_reason = #{failReason},</if>
            <if test="operTime != null">oper_time = #{operTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteSysPermissionLogByLogId" parameterType="Long">
        delete from sys_permission_log where log_id = #{logId}
    </delete>

    <delete id="deleteSysPermissionLogByLogIds" parameterType="String">
        delete from sys_permission_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
    
    <delete id="cleanSysPermissionLog">
        truncate table sys_permission_log
    </delete>

    <resultMap type="SysPermissionTemplateLog" id="SysPermissionTemplateLogResult">
        <result property="logId" column="log_id" />
        <result property="templateId" column="template_id" />
        <result property="templateName" column="template_name" />
        <result property="targetType" column="target_type" />
        <result property="targetId" column="target_id" />
        <result property="targetName" column="target_name" />
        <result property="applyType" column="apply_type" />
        <result property="status" column="status" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectSysPermissionTemplateLogVo">
        select log_id, template_id, template_name, target_type, target_id, target_name, 
               apply_type, status, create_by, create_time, remark 
        from sys_permission_template_log
    </sql>

    <select id="selectTemplateLogList" parameterType="SysPermissionTemplateLog" resultMap="SysPermissionTemplateLogResult">
        <include refid="selectSysPermissionTemplateLogVo"/>
        <where>
            <if test="templateId != null"> and template_id = #{templateId}</if>
            <if test="templateName != null and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="targetType != null and targetType != ''"> and target_type = #{targetType}</if>
            <if test="targetId != null"> and target_id = #{targetId}</if>
            <if test="targetName != null and targetName != ''"> and target_name like concat('%', #{targetName}, '%')</if>
            <if test="applyType != null and applyType != ''"> and apply_type = #{applyType}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertTemplateLog" parameterType="com.wanyu.system.domain.SysPermissionTemplateLog" useGeneratedKeys="true" keyProperty="logId">
        INSERT INTO sys_permission_template_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null">template_id,</if>
            <if test="templateName != null">template_name,</if>
            <if test="targetType != null">target_type,</if>
            <if test="targetId != null">target_id,</if>
            <if test="targetName != null">target_name,</if>
            <if test="applyType != null">apply_type,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateId != null">#{templateId},</if>
            <if test="templateName != null">#{templateName},</if>
            <if test="targetType != null">#{targetType},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="targetName != null">#{targetName},</if>
            <if test="applyType != null">#{applyType},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
</mapper>
