package com.wanyu.system;

import com.wanyu.common.utils.FieldStandardValidator;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 字段标准验证器测试类
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@SpringBootTest
public class FieldStandardValidatorTest {
    
    @Test
    public void testValidateStatusField() {
        // 测试状态字段验证
        FieldStandardValidator.ValidationResult result1 = 
            FieldStandardValidator.validateFieldValue("status", "0");
        assertTrue(result1.isValid());
        
        FieldStandardValidator.ValidationResult result2 = 
            FieldStandardValidator.validateFieldValue("status", "1");
        assertTrue(result2.isValid());
        
        FieldStandardValidator.ValidationResult result3 = 
            FieldStandardValidator.validateFieldValue("status", "2");
        assertTrue(result3.isValid()); // 2 is valid for delete flag
        
        FieldStandardValidator.ValidationResult result4 = 
            FieldStandardValidator.validateFieldValue("status", "3");
        assertFalse(result4.isValid()); // 3 is invalid
    }
    
    @Test
    public void testValidateOperationStatusField() {
        // 测试操作状态字段验证
        FieldStandardValidator.ValidationResult result1 = 
            FieldStandardValidator.validateFieldValue("operation_status", "0");
        assertTrue(result1.isValid());
        
        FieldStandardValidator.ValidationResult result2 = 
            FieldStandardValidator.validateFieldValue("operation_status", "1");
        assertTrue(result2.isValid());
        
        FieldStandardValidator.ValidationResult result3 = 
            FieldStandardValidator.validateFieldValue("operation_status", "2");
        assertFalse(result3.isValid());
    }
    
    @Test
    public void testValidateBooleanField() {
        // 测试布尔字段验证
        FieldStandardValidator.ValidationResult result1 = 
            FieldStandardValidator.validateFieldValue("is_enabled", "0");
        assertTrue(result1.isValid());
        
        FieldStandardValidator.ValidationResult result2 = 
            FieldStandardValidator.validateFieldValue("is_enabled", "1");
        assertTrue(result2.isValid());
        
        FieldStandardValidator.ValidationResult result3 = 
            FieldStandardValidator.validateFieldValue("is_enabled", "2");
        assertFalse(result3.isValid());
    }
    
    @Test
    public void testValidateDeleteFlagField() {
        // 测试删除标记字段验证
        FieldStandardValidator.ValidationResult result1 = 
            FieldStandardValidator.validateFieldValue("del_flag", "0");
        assertTrue(result1.isValid());
        
        FieldStandardValidator.ValidationResult result2 = 
            FieldStandardValidator.validateFieldValue("del_flag", "2");
        assertTrue(result2.isValid());
        
        FieldStandardValidator.ValidationResult result3 = 
            FieldStandardValidator.validateFieldValue("del_flag", "1");
        assertFalse(result3.isValid());
    }
    
    @Test
    public void testGetFieldType() {
        // 测试字段类型识别
        assertEquals(FieldStandardValidator.FieldType.STATUS, 
                    FieldStandardValidator.getFieldType("status"));
        assertEquals(FieldStandardValidator.FieldType.STATUS, 
                    FieldStandardValidator.getFieldType("user_status"));
        assertEquals(FieldStandardValidator.FieldType.OPERATION_STATUS, 
                    FieldStandardValidator.getFieldType("operation_status"));
        assertEquals(FieldStandardValidator.FieldType.BOOLEAN, 
                    FieldStandardValidator.getFieldType("is_enabled"));
        assertEquals(FieldStandardValidator.FieldType.DELETE_FLAG, 
                    FieldStandardValidator.getFieldType("del_flag"));
        assertEquals(FieldStandardValidator.FieldType.FLAG, 
                    FieldStandardValidator.getFieldType("sync_flag"));
        assertEquals(FieldStandardValidator.FieldType.UNKNOWN, 
                    FieldStandardValidator.getFieldType("user_name"));
    }
    
    @Test
    public void testBatchValidation() {
        // 测试批量验证
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("status", "0");
        fieldValues.put("is_enabled", "1");
        fieldValues.put("del_flag", "0");
        fieldValues.put("operation_status", "0");
        
        List<FieldStandardValidator.ValidationResult> results = 
            FieldStandardValidator.validateFields(fieldValues);
        
        assertEquals(4, results.size());
        assertTrue(results.stream().allMatch(FieldStandardValidator.ValidationResult::isValid));
    }
    
    @Test
    public void testInvalidFieldValues() {
        // 测试无效字段值
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("status", "3");  // 无效值
        fieldValues.put("is_enabled", "2");  // 无效值
        fieldValues.put("del_flag", "1");  // 无效值
        
        List<FieldStandardValidator.ValidationResult> invalidFields = 
            FieldStandardValidator.getInvalidFields(fieldValues);
        
        assertEquals(3, invalidFields.size());
        assertFalse(FieldStandardValidator.isAllFieldsValid(fieldValues));
    }
    
    @Test
    public void testGenerateCheckScript() {
        // 测试生成检查脚本
        String script = FieldStandardValidator.generateFieldStandardCheckScript("warehouse_system");
        
        assertNotNull(script);
        assertTrue(script.contains("SELECT"));
        assertTrue(script.contains("INFORMATION_SCHEMA.COLUMNS"));
        assertTrue(script.contains("warehouse_system"));
    }
}