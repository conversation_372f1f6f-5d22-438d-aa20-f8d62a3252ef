-- 修复菜单层级关系的SQL脚本
-- 设置连接字符集
SET NAMES utf8mb4;

-- 选择数据库
USE warehouse_system;

-- 创建临时表来存储按钮和它们应该属于的菜单的映射关系
CREATE TEMPORARY TABLE IF NOT EXISTS temp_button_menu_mapping (
    button_id BIGINT,
    menu_id BIGINT,
    button_name VARCHAR(50),
    menu_name VARCHAR(50)
);

-- 插入用户管理相关的按钮映射
INSERT INTO temp_button_menu_mapping (button_id, menu_id, button_name, menu_name)
VALUES 
(4001, 4, '用户查询', '用户管理'),
(4002, 4, '用户新增', '用户管理'),
(4003, 4, '用户修改', '用户管理'),
(4004, 4, '用户删除', '用户管理'),
(4005, 4, '用户导出', '用户管理'),
(4006, 4, '用户导入', '用户管理'),
(4007, 4, '重置密码', '用户管理'),
(5012, 4, '分配角色', '用户管理'),
(5013, 4, '分配权限', '用户管理'),
(5014, 4, '分配仓库', '用户管理'),
(5015, 4, '分配日志', '用户管理'),
(5016, 4, '分配菜单', '用户管理');

-- 插入角色管理相关的按钮映射
INSERT INTO temp_button_menu_mapping (button_id, menu_id, button_name, menu_name)
VALUES 
(5001, 5, '角色查询', '角色管理'),
(5002, 5, '角色新增', '角色管理'),
(5003, 5, '角色修改', '角色管理'),
(5004, 5, '角色删除', '角色管理'),
(5005, 5, '角色导出', '角色管理'),
(5145, 5, '菜单权限管理', '角色管理'),
(5146, 5, '数据权限', '角色管理'),
(5149, 5, '分配用户', '角色管理'),
(5150, 5, '分配仓库', '角色管理'),
(5151, 5, '角色模板', '角色管理');

-- 插入物品分类相关的按钮映射
INSERT INTO temp_button_menu_mapping (button_id, menu_id, button_name, menu_name)
VALUES 
(5125, 18, '新增分类', '物品分类'),
(5126, 18, '修改分类', '物品分类'),
(5127, 18, '删除分类', '物品分类'),
(5128, 18, '查询分类', '物品分类');

-- 插入物品规格相关的按钮映射
INSERT INTO temp_button_menu_mapping (button_id, menu_id, button_name, menu_name)
VALUES 
(5129, 20, '新增规格', '物品规格'),
(5130, 20, '修改规格', '物品规格'),
(5131, 20, '删除规格', '物品规格'),
(5132, 20, '查询规格', '物品规格');

-- 插入物品单位相关的按钮映射
INSERT INTO temp_button_menu_mapping (button_id, menu_id, button_name, menu_name)
VALUES 
(5141, 21, '新增单位', '物品单位'),
(5142, 21, '修改单位', '物品单位'),
(5143, 21, '删除单位', '物品单位'),
(5144, 21, '查询单位', '物品单位');

-- 插入库存管理相关的按钮映射
INSERT INTO temp_button_menu_mapping (button_id, menu_id, button_name, menu_name)
VALUES 
(5086, 28, '库存查询', '库存管理'),
(5087, 28, '库存修改', '库存管理'),
(5092, 28, '库存导出', '库存管理');

-- 更新按钮的父ID
UPDATE sys_menu sm
JOIN temp_button_menu_mapping tbmm ON sm.menu_id = tbmm.button_id
SET sm.parent_id = tbmm.menu_id
WHERE sm.menu_type = 'F';

-- 查看更新结果
SELECT menu_id, menu_name, parent_id, menu_type 
FROM sys_menu 
WHERE menu_type = 'F' 
ORDER BY parent_id, menu_id;

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS temp_button_menu_mapping;

-- 提交事务
COMMIT;
