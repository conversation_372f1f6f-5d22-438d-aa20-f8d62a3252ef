package com.wanyu.system.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.wanyu.common.core.domain.entity.SysUser;
import com.wanyu.common.core.domain.model.LoginUser;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.system.domain.ProductInfo;
import com.wanyu.system.domain.WmsPurchaseRequest;
import com.wanyu.system.mapper.WmsPurchaseRequestMapper;
import com.wanyu.system.service.IProductInfoService;
import com.wanyu.system.service.ISysDeptService;
import com.wanyu.system.service.ISysUserService;
import com.wanyu.system.service.IWmsPurchaseRequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.common.utils.StringUtils;

/**
 * 申购管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-20
 */
@Service
public class WmsPurchaseRequestServiceImpl implements IWmsPurchaseRequestService 
{
    @Autowired
    private WmsPurchaseRequestMapper wmsPurchaseRequestMapper;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private ISysDeptService deptService;
    
    @Autowired
    private IProductInfoService productInfoService;

    /**
     * 查询申购管理
     * 
     * @param requestId 申购管理主键
     * @return 申购管理
     */
    @Override
    public WmsPurchaseRequest selectWmsPurchaseRequestByRequestId(Long requestId)
    {
        return wmsPurchaseRequestMapper.selectWmsPurchaseRequestByRequestId(requestId);
    }

    /**
     * 查询申购管理列表
     * 
     * @param wmsPurchaseRequest 申购管理
     * @return 申购管理
     */
    @Override
    public List<WmsPurchaseRequest> selectWmsPurchaseRequestList(WmsPurchaseRequest wmsPurchaseRequest)
    {
        return wmsPurchaseRequestMapper.selectWmsPurchaseRequestList(wmsPurchaseRequest);
    }
    
    /**
     * 根据状态查询申购管理列表
     * 
     * @param status 状态
     * @return 申购管理列表
     */
    @Override
    public List<WmsPurchaseRequest> selectWmsPurchaseRequestListByStatus(String status)
    {
        WmsPurchaseRequest query = new WmsPurchaseRequest();
        query.setStatus(status);
        return wmsPurchaseRequestMapper.selectWmsPurchaseRequestList(query);
    }
    
    /**
     * 新增申购管理
     * 
     * @param wmsPurchaseRequest 申购管理
     * @return 结果
     */
    @Override
    public int insertWmsPurchaseRequest(WmsPurchaseRequest wmsPurchaseRequest)
    {
        // 自动生成申购单号
        if (StringUtils.isEmpty(wmsPurchaseRequest.getRequestNo())) {
            wmsPurchaseRequest.setRequestNo(generateRequestNo());
        }
        
        // 设置默认状态为待审批
        if (StringUtils.isEmpty(wmsPurchaseRequest.getStatus())) {
            wmsPurchaseRequest.setStatus(WmsPurchaseRequest.STATUS_PENDING);
        }
        
        // 自动填充申请人和所属部门信息
        fillUserInfo(wmsPurchaseRequest);
        
        // 自动填充物品信息
        fillProductInfo(wmsPurchaseRequest);
        
        wmsPurchaseRequest.setCreateTime(DateUtils.getNowDate());
        return wmsPurchaseRequestMapper.insertWmsPurchaseRequest(wmsPurchaseRequest);
    }

    /**
     * 修改申购管理
     * 
     * @param wmsPurchaseRequest 申购管理
     * @return 结果
     */
    @Override
    public int updateWmsPurchaseRequest(WmsPurchaseRequest wmsPurchaseRequest)
    {
        // 自动填充物品信息
        fillProductInfo(wmsPurchaseRequest);
        
        wmsPurchaseRequest.setUpdateTime(DateUtils.getNowDate());
        return wmsPurchaseRequestMapper.updateWmsPurchaseRequest(wmsPurchaseRequest);
    }

    /**
     * 批量删除申购管理
     * 
     * @param requestIds 需要删除的申购管理主键
     * @return 结果
     */
    @Override
    public int deleteWmsPurchaseRequestByRequestIds(Long[] requestIds)
    {
        return wmsPurchaseRequestMapper.deleteWmsPurchaseRequestByRequestIds(requestIds);
    }

    /**
     * 删除申购管理信息
     * 
     * @param requestId 申购管理主键
     * @return 结果
     */
    @Override
    public int deleteWmsPurchaseRequestByRequestId(Long requestId)
    {
        return wmsPurchaseRequestMapper.deleteWmsPurchaseRequestByRequestId(requestId);
    }
    
    /**
     * 审批申购请求
     * 
     * @param requestId 申购ID
     * @param status 审批状态
     * @param approvalComment 审批意见
     * @param loginUser 当前登录用户
     * @return 结果
     */
    @Override
    public int approveWmsPurchaseRequest(Long requestId, String status, String approvalComment, LoginUser loginUser) {
        WmsPurchaseRequest wmsPurchaseRequest = new WmsPurchaseRequest();
        wmsPurchaseRequest.setRequestId(requestId);
        wmsPurchaseRequest.setStatus(status);
        wmsPurchaseRequest.setApprover(loginUser.getUsername());
        wmsPurchaseRequest.setApprovalTime(new Date());
        wmsPurchaseRequest.setApprovalComment(approvalComment);
        wmsPurchaseRequest.setUpdateTime(new Date());
        return wmsPurchaseRequestMapper.updateWmsPurchaseRequest(wmsPurchaseRequest);
    }
    
    /**
     * 生成申购单号
     * 格式: SG + 当前日期 + 6位随机数
     * @return 申购单号
     */
    private String generateRequestNo() {
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        String randomStr = String.format("%06d", (int)(Math.random() * 1000000));
        return "SG" + dateStr + randomStr;
    }
    
    /**
     * 自动填充用户信息
     * @param wmsPurchaseRequest 申购请求对象
     */
    private void fillUserInfo(WmsPurchaseRequest wmsPurchaseRequest) {
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                SysUser user = loginUser.getUser();
                if (user != null) {
                    // 如果申请人为空，使用登录用户的真实姓名
                    if (StringUtils.isEmpty(wmsPurchaseRequest.getApplicant())) {
                        if (StringUtils.isNotEmpty(user.getRealName())) {
                            wmsPurchaseRequest.setApplicant(user.getRealName());
                        } else {
                            wmsPurchaseRequest.setApplicant(user.getUserName());
                        }
                    }
                    
                    // 如果所属部门为空，使用登录用户的部门
                    if (StringUtils.isEmpty(wmsPurchaseRequest.getDepartment())) {
                        if (user.getDeptId() != null) {
                            // 获取部门完整路径
                            String deptFullPath = deptService.buildDeptFullPath(user.getDeptId());
                            if (StringUtils.isNotEmpty(deptFullPath)) {
                                wmsPurchaseRequest.setDepartment(deptFullPath);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 如果获取用户信息失败，不进行填充，保持原有逻辑
            System.err.println("自动填充用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 自动填充物品信息
     * @param wmsPurchaseRequest 申购请求对象
     */
    private void fillProductInfo(WmsPurchaseRequest wmsPurchaseRequest) {
        try {
            // 如果商品名称、编码、规格、单位都为空，且提供了productId，则从产品信息中获取
            if (wmsPurchaseRequest.getProductId() != null && 
                StringUtils.isAllEmpty(wmsPurchaseRequest.getProductName(), 
                                      wmsPurchaseRequest.getProductCode(), 
                                      wmsPurchaseRequest.getSpecifications(), 
                                      wmsPurchaseRequest.getUnit())) {
                
                ProductInfo productInfo = productInfoService.selectProductInfoByProductId(wmsPurchaseRequest.getProductId());
                if (productInfo != null) {
                    wmsPurchaseRequest.setProductName(productInfo.getProductName());
                    wmsPurchaseRequest.setProductCode(productInfo.getProductCode());
                    wmsPurchaseRequest.setSpecifications(productInfo.getSpecName());
                    wmsPurchaseRequest.setUnit(productInfo.getUnitName());
                }
            }
        } catch (Exception e) {
            // 如果获取物品信息失败，不进行填充，保持原有逻辑
            System.err.println("自动填充物品信息失败: " + e.getMessage());
        }
    }
}