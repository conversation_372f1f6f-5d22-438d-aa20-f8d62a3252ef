-- ----------------------------
-- Table structure for wms_unit
-- ----------------------------
DROP TABLE IF EXISTS `wms_unit`;
CREATE TABLE `wms_unit` (
  `unit_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Unit ID',
  `unit_name` varchar(50) NOT NULL COMMENT 'Unit Name',
  `unit_code` varchar(20) NOT NULL COMMENT 'Unit Code',
  `status` char(1) DEFAULT '0' COMMENT 'Status (0:Normal 1:Disabled)',
  `create_by` varchar(64) DEFAULT '' COMMENT 'Created By',
  `create_time` datetime DEFAULT NULL COMMENT 'Create Time',
  `update_by` varchar(64) DEFAULT '' COMMENT 'Updated By',
  `update_time` datetime DEFAULT NULL COMMENT 'Update Time',
  `remark` varchar(500) DEFAULT NULL COMMENT 'Remark',
  PRIMARY KEY (`unit_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='Product Unit Table';

-- ----------------------------
-- Records of wms_unit
-- ----------------------------
INSERT INTO `wms_unit` VALUES ('1', 'Piece', 'PCS', '0', 'admin', '2023-07-01 11:33:00', 'admin', '2023-07-01 11:33:00', 'Piece');
INSERT INTO `wms_unit` VALUES ('2', 'Box', 'BOX', '0', 'admin', '2023-07-01 11:33:00', 'admin', '2023-07-01 11:33:00', 'Box');
INSERT INTO `wms_unit` VALUES ('3', 'Kilogram', 'KG', '0', 'admin', '2023-07-01 11:33:00', 'admin', '2023-07-01 11:33:00', 'Kilogram');
