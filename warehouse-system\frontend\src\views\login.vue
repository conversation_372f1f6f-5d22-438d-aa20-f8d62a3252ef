<template>
  <div class="login-container">
    <div class="login-register-card">
      <div class="logo-container">
        <img src="@/assets/logo/logo.png" alt="系统logo" class="logo" />
        <h1 class="system-name">万裕物业仓库管理系统</h1>
      </div>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-register-form"
        @submit.native.prevent="handleLogin"
      >
        <div class="login-title-container">
          <h3 class="title">用户登录</h3>
          <p class="login-subtitle">智能识别登录方式，支持用户名、手机号、真实姓名</p>
        </div>
        
        <!-- 登录标识输入 -->
        <el-form-item prop="loginIdentity" class="input-item">
          <div class="input-wrapper">
            <el-input
              v-model="loginForm.loginIdentity"
              :type="'text'"
              :placeholder="inputPlaceholder"
              prefix-icon="el-icon-user"
              clearable
              aria-label="登录标识"
              @keyup.enter.native="handleLogin"
              tabindex="1"
              class="modern-input"
            />
            <div class="input-focus-border"></div>
          </div>
        </el-form-item>

        <!-- 密码输入 -->
        <el-form-item prop="password" class="input-item">
          <div class="input-wrapper">
            <el-input
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              prefix-icon="el-icon-lock"
              clearable
              aria-label="密码"
              @keyup.enter.native="handleLogin"
              tabindex="2"
              class="modern-input"
            >
              <template #suffix>
                <i
                  :class="showPassword ? 'el-icon-view' : 'el-icon-hide'"
                  @click="togglePasswordVisibility"
                  class="password-toggle"
                  tabindex="3"
                  aria-label="切换密码可见性"
                  @keydown.enter.prevent="togglePasswordVisibility"
                ></i>
              </template>
            </el-input>
            <div class="input-focus-border"></div>
          </div>
        </el-form-item>

        <!-- 验证码 -->
        <el-form-item prop="code" v-if="captchaEnabled" class="input-item">
          <div class="captcha-container">
            <div class="input-wrapper captcha-input">
              <el-input
                v-model="loginForm.code"
                placeholder="请输入验证码"
                prefix-icon="el-icon-picture-outline"
                clearable
                aria-label="验证码"
                @keyup.enter.native="handleLogin"
                tabindex="4"
                class="modern-input"
              />
              <div class="input-focus-border"></div>
            </div>
            <div class="captcha-image-wrapper">
              <img
                :src="codeUrl"
                @click="refreshCaptcha"
                class="captcha-image"
                alt="验证码"
                title="点击刷新验证码"
                @error="onCaptchaImgError"
              />
            </div>
          </div>
        </el-form-item>

        <!-- 登录选项 -->
        <div class="login-options">
          <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
          <el-link type="primary" @click="forgotPassword">忘记密码？</el-link>
        </div>

        <!-- 登录按钮 -->
        <el-form-item style="width:100%;" class="login-button-item">
          <el-button
            :loading="loading"
            type="primary"
            native-type="submit"
            class="modern-login-btn"
            style="width:100%;"
            tabindex="5"
          >
            <i v-if="loading" class="el-icon-loading"></i>
            <span>{{ loading ? '登录中...' : '登 录' }}</span>
          </el-button>
        </el-form-item>

        <!-- 注册链接 -->
        <div class="login-link">
          还没有账号? <router-link to="/register" class="register-link">立即注册</router-link>
        </div>
      </el-form>
    </div>
    <div class="login-register-footer">
      <span>Copyright © 2016-2025 万裕物业仓库管理系统 All Rights Reserved.</span>
    </div>
    
    <!-- 登录加载指示器 -->
    <LoginLoadingIndicator 
      :visible="loading" 
      :loginType="finalLoginType"
    />
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt';
import loginHistoryManager from '@/utils/loginHistory';
import LoginLoadingIndicator from '@/components/LoginLoadingIndicator.vue';

export default {
  name: "Login",
  components: {
    LoginLoadingIndicator
  },
  data() {
    return {
      showPassword: false,
      codeUrl: "",
      loading: false,
      captchaEnabled: true,
      redirect: undefined,
      detectionTimer: null, // 智能识别防抖定时器
      lastDetectedType: '', // 上次检测到的类型
      loginForm: {
        loginIdentity: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
        loginType: "auto"
      },
      loginRules: {
        loginIdentity: [
          { required: true, message: "请输入用户名/真实姓名/手机号", trigger: "blur" }
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "密码长度不能少于6位", trigger: "blur" }
        ],
        code: [
          { required: true, message: "请输入验证码", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    /**
     * 动态输入框提示文本
     */
    inputPlaceholder() {
      switch (this.loginForm.loginType) {
        case 'username':
          return '请输入用户名';
        case 'phone':
          return '请输入手机号';
        case 'realname':
          return '请输入真实姓名';
        case 'auto':
        default:
          return '请输入用户名/真实姓名/手机号';
      }
    },

    /**
     * 检测到的登录类型
     */
    detectedLoginType() {
      return this.detectLoginType(this.loginForm.loginIdentity);
    },

    /**
     * 最终的登录类型（用于加载指示器）
     */
    finalLoginType() {
      if (this.loginForm.loginType === 'auto') {
        return this.detectLoginType(this.loginForm.loginIdentity);
      }
      return this.loginForm.loginType;
    }
  },

  watch: {
    $route: {
      handler(route) {
        this.redirect = route.query?.redirect;
      },
      immediate: true
    },

    /**
     * 监听登录标识变化，自动检测登录类型
     */
    'loginForm.loginIdentity'(newVal) {
      if (this.loginForm.loginType === 'auto') {
        // 清除之前的定时器
        if (this.detectionTimer) {
          clearTimeout(this.detectionTimer);
        }
        
        if (newVal && newVal.length >= 2) {
          // 防抖处理，避免频繁检测
          this.detectionTimer = setTimeout(() => {
            const detectedType = this.detectLoginType(newVal);
            
            // 只有当检测类型发生变化时才显示提示
            if (detectedType !== this.lastDetectedType) {
              this.lastDetectedType = detectedType;
              this.showDetectionTip(detectedType);
            }
          }, 500); // 500ms 防抖延迟
        } else {
          // 输入为空或太短时重置
          this.lastDetectedType = '';
        }
      }
    },

    /**
     * 监听登录类型变化，更新验证规则
     */
    'loginForm.loginType'(newType) {
      this.updateValidationRules(newType);
    }
  },
  created() {
    // 首先确保登录类型为智能识别模式
    this.loginForm.loginType = 'auto';
    
    this.refreshCaptcha();
    this.loadRememberedCredentials();
    this.loadLoginHistory();
    
    // 确保智能识别模式的验证规则正确设置
    this.updateValidationRules('auto');
    
    // 添加验证码失效事件监听
    window.addEventListener('captchaInvalid', this.refreshCaptcha);
    
    console.log('登录页面初始化完成，登录类型:', this.loginForm.loginType);
  },
  
  beforeDestroy() {
    // 移除事件监听，防止内存泄漏
    window.removeEventListener('captchaInvalid', this.refreshCaptcha);
    
    // 清除智能识别定时器
    if (this.detectionTimer) {
      clearTimeout(this.detectionTimer);
    }
  },
  methods: {
    /**
     * 刷新验证码
     */
    refreshCaptcha() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled ?? true;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      }).catch(error => {
        console.error("获取验证码失败:", error);
        this.$message.error("获取验证码失败，请刷新页面重试");
      });
    },

    /**
     * 加载记住的登录信息
     */
    loadRememberedCredentials() {
      const account = Cookies.get("account");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe');
      if (account && password && rememberMe === 'true') {
        this.loginForm.loginIdentity = account;
        this.loginForm.password = decrypt(password);
        this.loginForm.rememberMe = true;
      }
    },

    /**
     * 加载登录历史
     */
    loadLoginHistory() {
      try {
        // 保持智能识别模式，不从历史记录中覆盖登录类型
        // 智能识别模式应该始终保持为 'auto'
        
        // 保存当前页面路径（用于登录后跳转）
        if (this.$route.path !== '/login') {
          loginHistoryManager.saveLastVisitPage(this.$route.path);
        }
      } catch (error) {
        console.warn('加载登录历史失败:', error);
      }
    },

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },

    /**
     * 忘记密码处理
     */
    forgotPassword() {
      this.$message.info("请联系系统管理员重置密码");
    },

    /**
     * 处理登录
     */
    handleLogin() {
      this.$refs.loginFormRef.validate(valid => {
        if (valid) {
          this.loading = true;

          // 处理记住我功能
          this.handleRememberMe();
          
          // 构建登录数据
          const loginData = this.buildLoginData();
          
          // 调试信息
          console.log('登录数据:', loginData);
          console.log('登录类型:', loginData.loginType);
          console.log('登录标识:', loginData.realName || loginData.phone || loginData.username);
          
          // 保存登录尝试记录
          this.saveLoginAttempt(loginData, false);
          
          this.$store
            .dispatch("Login", loginData)
            .then(() => {
              // 登录成功处理
              this.handleLoginSuccess(loginData);
            })
            .catch(error => {
              console.error('Login failed:', error);
              console.error('Error details:', error.response?.data || error.message);
              this.handleLoginError(error);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    /**
     * 处理登录成功
     */
    handleLoginSuccess(loginData) {
      // 保存成功的登录记录
      this.saveLoginAttempt(loginData, true);
      
      // 保存登录类型偏好
      loginHistoryManager.saveLastLoginType(loginData.loginType);
      loginHistoryManager.saveLastLoginIdentity(loginData.username, loginData.loginType);
      
      // 显示成功消息
      this.$message.success("登录成功");
      
      // 智能跳转
      this.performSmartRedirect();
    },

    /**
     * 执行智能跳转
     */
    performSmartRedirect() {
      let targetPath = '/';
      
      // 优先级1: URL参数中的redirect
      if (this.redirect) {
        targetPath = this.redirect;
      }
      // 优先级2: 上次访问的页面
      else {
        const lastVisitPage = loginHistoryManager.getLastVisitPage();
        if (lastVisitPage && lastVisitPage !== '/') {
          targetPath = lastVisitPage;
        }
      }
      
      // 清除保存的访问页面
      loginHistoryManager.clearLastVisitPage();
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        this.$router.push({ path: targetPath }).catch(() => {});
      }, 1000);
    },

    /**
     * 保存登录尝试记录
     */
    saveLoginAttempt(loginData, success) {
      try {
        loginHistoryManager.addLoginHistory({
          loginType: loginData.loginType,
          loginIdentity: loginData.username,
          success: success
        });
      } catch (error) {
        console.warn('保存登录记录失败:', error);
      }
    },

    /**
     * 处理记住我功能
     */
    handleRememberMe() {
      if (this.loginForm.rememberMe) {
        Cookies.set("account", this.loginForm.loginIdentity, { expires: 30 });
        Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
        Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
      } else {
        Cookies.remove("account");
        Cookies.remove("password");
        Cookies.remove('rememberMe');
      }
    },

    /**
     * 构建登录数据
     */
    buildLoginData() {
      const { loginIdentity, password, code, uuid, loginType } = this.loginForm;
      
      // 确定最终的登录类型
      let finalLoginType = loginType;
      if (loginType === 'auto') {
        finalLoginType = this.detectLoginType(loginIdentity);
      }
      
      // 根据登录类型构建不同的数据结构
      const loginData = {
        password,
        code,
        uuid,
        loginType: finalLoginType
      };

      // 根据登录类型设置对应的字段
      switch (finalLoginType) {
        case 'phone':
          loginData.phone = loginIdentity;
          loginData.username = loginIdentity; // 兼容后端
          break;
        case 'realname':
          loginData.realName = loginIdentity;
          loginData.username = loginIdentity; // 兼容后端
          break;
        case 'username':
        default:
          loginData.username = loginIdentity;
          break;
      }

      // 添加调试日志
      console.log('构建登录数据:', {
        loginIdentity,
        password: password ? '******' : null,
        code,
        uuid,
        loginType: finalLoginType,
        ...loginData
      });

      return loginData;
    },

    /**
     * 检测登录类型
     */
    detectLoginType(loginIdentity) {
      if (!loginIdentity) return 'username';

      // 1. 手机号检测：1开头的11位数字
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (phoneRegex.test(loginIdentity)) {
        return 'phone';
      }

      // 2. 用户名特征检测（优先级高于真实姓名）
      // 包含数字、下划线、或者是常见的用户名模式
      const usernamePatterns = [
        /\d/, // 包含数字
        /_/, // 包含下划线
        /^(admin|user|test|guest|root|superadmin|administrator)/i, // 常见用户名前缀
        /^[a-zA-Z]+\d+$/, // 字母+数字组合
        /^[a-zA-Z]{6,}$/ // 6位以上纯英文（可能是用户名）
      ];
      
      for (let pattern of usernamePatterns) {
        if (pattern.test(loginIdentity)) {
          return 'username';
        }
      }

      // 3. 真实姓名检测
      // 中文姓名：2-4位中文字符
      const chineseNameRegex = /^[\u4e00-\u9fa5]{2,4}$/;
      if (chineseNameRegex.test(loginIdentity)) {
        return 'realname';
      }
      
      // 英文姓名：包含空格的英文名（如 "John Smith"）或常见英文名模式
      const englishNameRegex = /^[A-Z][a-z]+ [A-Z][a-z]+$|^[A-Z][a-z]{2,8}$/;
      if (englishNameRegex.test(loginIdentity)) {
        return 'realname';
      }

      // 4. 默认为用户名
      return 'username';
    },

    /**
     * 显示检测结果提示
     */
    showDetectionTip(detectedType) {
      const typeNames = {
        'username': '用户名',
        'phone': '手机号',
        'realname': '真实姓名'
      };
      
      if (detectedType && typeNames[detectedType]) {
        // 控制台日志用于调试
        console.log(`智能识别: 检测到${typeNames[detectedType]}格式`);
        
        // 显示友好的识别提示
        this.$message({
          message: `✓ 已识别为${typeNames[detectedType]}登录`,
          type: 'success',
          duration: 1500,
          showClose: false
        });
      }
    },

    /**
     * 更新验证规则
     */
    updateValidationRules(loginType) {
      const rules = {
        loginIdentity: [
          { required: true, message: this.getValidationMessage(loginType), trigger: "blur" }
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "密码长度不能少于6位", trigger: "blur" }
        ],
        code: [
          { required: true, message: "请输入验证码", trigger: "blur" }
        ]
      };

      // 重要：智能识别模式(auto)下不添加格式验证器
      // 这样可以避免输入手机号时提示真实姓名格式错误的问题
      console.log('更新验证规则，当前登录类型:', loginType);
      
      if (loginType !== 'auto') {
        console.log('添加格式验证器');
        rules.loginIdentity.push({
          validator: this.validateLoginIdentity,
          trigger: 'blur'
        });
      } else {
        console.log('智能识别模式，跳过格式验证器');
      }

      this.loginRules = rules;
      
      // 强制更新表单验证规则
      this.$nextTick(() => {
        if (this.$refs.loginFormRef) {
          this.$refs.loginFormRef.clearValidate();
        }
      });
    },

    /**
     * 获取验证消息
     */
    getValidationMessage(loginType) {
      switch (loginType) {
        case 'username':
          return '请输入用户名';
        case 'phone':
          return '请输入手机号';
        case 'realname':
          return '请输入真实姓名';
        case 'auto':
        default:
          return '请输入用户名/真实姓名/手机号';
      }
    },

    /**
     * 验证登录标识格式
     */
    validateLoginIdentity(rule, value, callback) {
      if (!value) {
        callback(new Error(this.getValidationMessage(this.loginForm.loginType)));
        return;
      }

      const loginType = this.loginForm.loginType;
      
      // 智能识别模式下不进行严格验证，直接通过
      if (loginType === 'auto') {
        callback();
        return;
      }

      let isValid = true;
      let errorMessage = '';

      switch (loginType) {
        case 'username':
          // 用户名：3-30位字母、数字、下划线
          const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
          isValid = usernameRegex.test(value);
          errorMessage = '用户名格式不正确，请输入3-30位字母、数字或下划线';
          break;
        case 'phone':
          // 手机号：1开头的11位数字
          const phoneRegex = /^1[3-9]\d{9}$/;
          isValid = phoneRegex.test(value);
          errorMessage = '手机号格式不正确，请输入正确的11位手机号';
          break;
        case 'realname':
          // 真实姓名：2-20位中文或英文
          const realNameRegex = /^[\u4e00-\u9fa5a-zA-Z\s]{2,20}$/;
          isValid = realNameRegex.test(value);
          errorMessage = '真实姓名格式不正确，请输入2-20位中文或英文';
          break;
        default:
          // 其他情况直接通过
          isValid = true;
          break;
      }

      if (isValid) {
        callback();
      } else {
        callback(new Error(errorMessage));
      }
    },

    /**
     * 处理登录错误
     */
    handleLoginError(error) {
      if (this.captchaEnabled) {
        this.refreshCaptcha();
      }
      
      const msg = error.msg || error.message || "登录失败，请检查登录信息";
      this.$message.error(msg);
    },

    onCaptchaImgError(e) {
      e.target.src = require('@/assets/logo/logo.png');
      this.$message.error('验证码加载失败，请点击图片刷新');
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/styles/login-register.scss";

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="20" cy="60" r="0.5" fill="white" opacity="0.15"/><circle cx="80" cy="40" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
  }
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.logo-container {
  @extend .login-register-logo;
  
  .logo {
    transition: transform 0.3s ease;
    &:hover {
      transform: scale(1.05) rotate(5deg);
    }
  }
  
  .system-name {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: 1px;
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
  
  .el-checkbox {
    .el-checkbox__label {
      color: #666;
      font-size: 14px;
    }
    
    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #667eea;
      border-color: #667eea;
    }
  }
  
  .el-link {
    font-size: 14px;
    color: #667eea;
    font-weight: 500;
    
    &:hover {
      color: #764ba2;
    }
  }
}

// 现代化输入框样式
.input-item {
  margin-bottom: 24px;
  
  .input-wrapper {
    position: relative;
    
    .modern-input {
      .el-input__inner {
        border: 2px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        height: 50px;
        font-size: 16px;
        transition: all 0.3s ease;
        
        &:focus {
          border-color: rgba(102, 126, 234, 0.6);
          background: rgba(255, 255, 255, 0.95);
          box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }
        
        &::placeholder {
          color: rgba(0, 0, 0, 0.4);
        }
      }
      
      .el-input__prefix {
        color: rgba(102, 126, 234, 0.7);
      }
    }
    
    .input-focus-border {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      transition: all 0.3s ease;
      transform: translateX(-50%);
    }
    
    &:focus-within .input-focus-border {
      width: 100%;
    }
  }
}

// 验证码容器样式
.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
  
  .captcha-input {
    flex: 1;
  }
  
  .captcha-image-wrapper {
    .captcha-image {
      height: 50px;
      width: 120px;
      border-radius: 8px;
      border: 2px solid rgba(255, 255, 255, 0.2);
      cursor: pointer;
      transition: all 0.3s ease;
      object-fit: cover;
      
      &:hover {
        border-color: rgba(102, 126, 234, 0.6);
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
        transform: scale(1.02);
      }
    }
  }
}

// 现代化登录按钮
.login-button-item {
  margin-top: 32px;
  
  .modern-login-btn {
    height: 52px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      
      &::before {
        left: 100%;
      }
    }
    
    &:active {
      transform: translateY(0);
    }
    
    span {
      position: relative;
      z-index: 1;
    }
  }
}

.password-toggle {
  cursor: pointer;
  color: rgba(102, 126, 234, 0.7);
  transition: all 0.3s ease;
  
  &:hover {
    color: #667eea;
    transform: scale(1.1);
  }
}

.login-title-container {
  text-align: center;
  margin-bottom: 32px;
  
  .title {
    color: #333;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .login-subtitle {
    color: #666;
    font-size: 14px;
    margin: 0;
    opacity: 0.8;
  }
}
</style>
// 登
录链接样式
.login-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;
  
  .register-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    
    &:hover {
      color: #764ba2;
      text-decoration: underline;
    }
  }
}