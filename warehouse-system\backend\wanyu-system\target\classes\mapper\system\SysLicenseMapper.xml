<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.SysLicenseMapper">
    
    <resultMap type="SysLicense" id="SysLicenseResult">
        <result property="licenseId"    column="license_id"    />
        <result property="licenseKey"    column="license_key"    />
        <result property="companyName"    column="company_name"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="licenseType"    column="license_type"    />
        <result property="maxUsers"    column="max_users"    />
        <result property="maxWarehouses"    column="max_warehouses"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="hardwareFingerprint"    column="hardware_fingerprint"    />
        <result property="status"    column="status"    />
        <result property="features"    column="features"    />
        <result property="current"    column="current"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysLicenseVo">
        select license_id, license_key, company_name, contact_info, license_type, max_users, max_warehouses, start_date, end_date, hardware_fingerprint, status, features, current, create_time, update_time, create_by, update_by, remark from sys_license
    </sql>

    <select id="selectSysLicenseList" parameterType="SysLicense" resultMap="SysLicenseResult">
        <include refid="selectSysLicenseVo"/>
        <where>  
            <if test="licenseKey != null  and licenseKey != ''"> and license_key = #{licenseKey}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="contactInfo != null  and contactInfo != ''"> and contact_info = #{contactInfo}</if>
            <if test="licenseType != null  and licenseType != ''"> and license_type = #{licenseType}</if>
            <if test="maxUsers != null "> and max_users = #{maxUsers}</if>
            <if test="maxWarehouses != null "> and max_warehouses = #{maxWarehouses}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="hardwareFingerprint != null  and hardwareFingerprint != ''"> and hardware_fingerprint = #{hardwareFingerprint}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="features != null  and features != ''"> and features = #{features}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSysLicenseByLicenseId" parameterType="Long" resultMap="SysLicenseResult">
        <include refid="selectSysLicenseVo"/>
        where license_id = #{licenseId}
    </select>

    <select id="selectSysLicenseByKey" parameterType="String" resultMap="SysLicenseResult">
        <include refid="selectSysLicenseVo"/>
        where license_key = #{licenseKey} and status = '0'
    </select>

    <select id="selectActiveLicenses" resultMap="SysLicenseResult">
        <include refid="selectSysLicenseVo"/>
        where status = '0' 
        and current = 1
        and (end_date is null or end_date > now())
        order by 
            case when end_date is null then 0 else 1 end,
            end_date desc,
            create_time desc
        limit 1
    </select>
        
    <insert id="insertSysLicense" parameterType="SysLicense" useGeneratedKeys="true" keyProperty="licenseId">
        insert into sys_license
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licenseKey != null and licenseKey != ''">license_key,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="licenseType != null and licenseType != ''">license_type,</if>
            <if test="maxUsers != null">max_users,</if>
            <if test="maxWarehouses != null">max_warehouses,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="hardwareFingerprint != null">hardware_fingerprint,</if>
            <if test="status != null">status,</if>
            <if test="features != null">features,</if>
            <if test="current != null">current,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licenseKey != null and licenseKey != ''">#{licenseKey},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="licenseType != null and licenseType != ''">#{licenseType},</if>
            <if test="maxUsers != null">#{maxUsers},</if>
            <if test="maxWarehouses != null">#{maxWarehouses},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="hardwareFingerprint != null">#{hardwareFingerprint},</if>
            <if test="status != null">#{status},</if>
            <if test="features != null">#{features},</if>
            <if test="current != null">#{current},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysLicense" parameterType="SysLicense">
        update sys_license
        <trim prefix="SET" suffixOverrides=",">
            <if test="licenseKey != null and licenseKey != ''">license_key = #{licenseKey},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="licenseType != null and licenseType != ''">license_type = #{licenseType},</if>
            <if test="maxUsers != null">max_users = #{maxUsers},</if>
            <if test="maxWarehouses != null">max_warehouses = #{maxWarehouses},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="hardwareFingerprint != null">hardware_fingerprint = #{hardwareFingerprint},</if>
            <if test="status != null">status = #{status},</if>
            <if test="features != null">features = #{features},</if>
            <if test="current != null">current = #{current},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where license_id = #{licenseId}
    </update>

    <delete id="deleteSysLicenseByLicenseId" parameterType="Long">
        delete from sys_license where license_id = #{licenseId}
    </delete>

    <delete id="deleteSysLicenseByLicenseIds" parameterType="String">
        delete from sys_license where license_id in 
        <foreach item="licenseId" collection="array" open="(" separator="," close=")">
            #{licenseId}
        </foreach>
    </delete>
    
    <update id="updateAllLicensesCurrent" parameterType="int">
        update sys_license set current = #{current}
    </update>
</mapper>