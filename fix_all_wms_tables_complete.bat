@echo off
chcp 65001 >nul
echo ========================================
echo WMS表名规范化综合修复脚本
echo ========================================
echo 📋 修复内容:
echo   • product_barcode → wms_barcode
echo   • wms_product_category → wms_category
echo   • wms_product_specification → wms_specification  
echo   • wms_product_unit → wms_unit
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo 🔧 开始WMS表名规范化修复...
echo.

echo 📋 执行综合修复SQL脚本...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < fix_all_wms_tables_complete.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 数据库修复完成！
    echo.
    echo 📊 验证修复结果...
    echo.
    
    echo 📋 表记录统计:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT 'wms_barcode' as table_name, COUNT(*) as record_count FROM wms_barcode UNION ALL SELECT 'wms_barcode_template', COUNT(*) FROM wms_barcode_template UNION ALL SELECT 'wms_category', COUNT(*) FROM wms_category WHERE del_flag = '0' UNION ALL SELECT 'wms_specification', COUNT(*) FROM wms_specification UNION ALL SELECT 'wms_unit', COUNT(*) FROM wms_unit;"
    
    echo.
    echo 📋 条码类型字典数据:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'wms_barcode_type' ORDER BY dict_sort;"
    
    echo.
    echo 📋 分类数据示例:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT category_id, category_name, category_code, parent_id FROM wms_category WHERE del_flag = '0' ORDER BY order_num LIMIT 5;"
    
    echo.
    echo 📋 规格数据示例:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT spec_id, spec_name, spec_code FROM wms_specification ORDER BY spec_id LIMIT 5;"
    
    echo.
    echo 📋 单位数据示例:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT unit_id, unit_name, unit_code FROM wms_unit ORDER BY unit_id LIMIT 5;"
    
    echo.
    echo ========================================
    echo ✅ WMS表名规范化修复完成！
    echo ========================================
    echo.
    echo 📝 修复内容总结:
    echo   1. ✅ 物品条码表 (wms_barcode, wms_barcode_template)
    echo   2. ✅ 物品分类表 (wms_category)  
    echo   3. ✅ 物品规格表 (wms_specification)
    echo   4. ✅ 物品单位表 (wms_unit)
    echo   5. ✅ 数据迁移和默认数据插入
    echo   6. ✅ 字典数据和外键引用更新
    echo.
    echo 🔄 下一步操作:
    echo   1. 重新编译后端项目
    echo   2. 重启后端服务  
    echo   3. 测试所有物品相关功能
    echo.
    echo 💡 已创建的实体类和服务:
    echo   • WmsBarcode.java + WmsBarcodeService
    echo   • WmsBarcodeTemplate.java
    echo   • ProductCategory.java (已更新注释)
    echo   • ProductSpecification.java  
    echo   • ProductUnit.java
    echo   • 所有Mapper XML文件已使用正确表名
    echo.
    echo 🧪 运行测试: test_all_wms_tables_fix.bat
    echo.
) else (
    echo.
    echo ❌ 数据库修复失败！
    echo 请检查数据库连接和权限设置
    echo.
)

echo 按任意键退出...
pause >nul