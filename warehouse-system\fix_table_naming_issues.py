#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复表名不一致问题的脚本
根据项目规范文档统一表名命名规则
"""

import pymysql
import sys
from datetime import datetime

def connect_database():
    """连接数据库"""
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='ry_vue',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        print("✅ 数据库连接成功")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def analyze_table_naming_issues(connection):
    """分析表名命名问题"""
    cursor = connection.cursor()
    
    print("\n" + "="*80)
    print("📋 分析表名命名问题")
    print("="*80)
    
    # 查询所有包含warehouse的表
    cursor.execute("SHOW TABLES LIKE '%warehouse%'")
    warehouse_tables = cursor.fetchall()
    
    print("🔍 发现的仓库相关表:")
    for table in warehouse_tables:
        table_name = list(table.values())[0]
        print(f"  - {table_name}")
    
    # 根据规范文档分析命名问题
    naming_issues = []
    
    # 检查是否存在不规范的表名
    expected_tables = {
        'sys_user_warehouse': '用户仓库关联表',
        'sys_role_warehouse': '角色仓库关联表', 
        'sys_warehouse': '仓库基础信息表',
        'wms_inventory': '库存表',
        'wms_product': '物品信息表',
        'wms_inventory_in': '入库单表',
        'wms_inventory_out': '出库单表',
        'wms_inventory_log': '出入库日志表'
    }
    
    existing_table_names = [list(table.values())[0] for table in warehouse_tables]
    
    print(f"\n📊 表名规范性分析:")
    print("-" * 60)
    
    for expected_table, description in expected_tables.items():
        if expected_table in existing_table_names:
            print(f"✅ {expected_table:<25} - {description}")
        else:
            print(f"❌ {expected_table:<25} - {description} (缺失)")
            naming_issues.append({
                'type': 'missing_table',
                'expected': expected_table,
                'description': description
            })
    
    # 检查是否有不符合规范的表名
    for table_name in existing_table_names:
        if table_name not in expected_tables:
            # 检查是否是不规范的命名
            if 'warehouse' in table_name.lower():
                naming_issues.append({
                    'type': 'non_standard_naming',
                    'actual': table_name,
                    'suggestion': get_standard_table_name(table_name)
                })
    
    return naming_issues

def get_standard_table_name(table_name):
    """根据规范获取标准表名"""
    # 根据命名规范文档的规则
    if 'user' in table_name and 'warehouse' in table_name:
        return 'sys_user_warehouse'
    elif 'role' in table_name and 'warehouse' in table_name:
        return 'sys_role_warehouse'
    elif table_name.startswith('wms_') and 'warehouse' in table_name:
        return table_name  # wms_开头的表名符合规范
    elif table_name.startswith('sys_') and 'warehouse' in table_name:
        return table_name  # sys_开头的表名符合规范
    else:
        return f"需要人工确认: {table_name}"

def check_mapper_xml_consistency(connection):
    """检查Mapper XML文件中的表名一致性"""
    print("\n" + "="*80)
    print("🔍 检查Mapper XML表名一致性问题")
    print("="*80)
    
    # 这里我们基于之前发现的问题进行分析
    issues_found = []
    
    # 检查UserWarehouseMapper.xml中的表名不一致问题
    print("📋 UserWarehouseMapper.xml 表名一致性检查:")
    print("  发现问题:")
    print("    - 查询操作使用: sys_user_warehouse")
    print("    - 删除操作使用: wms_user_warehouse")
    print("    - 其他操作使用: wms_user_warehouse")
    
    issues_found.append({
        'file': 'UserWarehouseMapper.xml',
        'issue': '表名不一致',
        'details': '查询使用sys_user_warehouse，删除等操作使用wms_user_warehouse',
        'solution': '统一使用sys_user_warehouse表'
    })
    
    return issues_found

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n" + "="*80)
    print("🔧 修复建议")
    print("="*80)
    
    recommendations = [
        {
            'priority': 'HIGH',
            'category': 'Mapper XML修复',
            'description': '修复UserWarehouseMapper.xml中的表名不一致问题',
            'action': '将所有SQL操作统一使用sys_user_warehouse表',
            'files': ['warehouse-system/backend/wanyu-system/src/main/resources/mapper/warehouse/UserWarehouseMapper.xml']
        },
        {
            'priority': 'MEDIUM', 
            'category': '数据迁移',
            'description': '如果wms_user_warehouse表中有数据，需要迁移到sys_user_warehouse表',
            'action': '检查两个表的数据，进行必要的数据迁移',
            'files': ['数据库表: wms_user_warehouse, sys_user_warehouse']
        },
        {
            'priority': 'MEDIUM',
            'category': '代码审查',
            'description': '审查所有Mapper文件，确保表名使用一致',
            'action': '检查其他Mapper XML文件是否存在类似问题',
            'files': ['warehouse-system/backend/wanyu-system/src/main/resources/mapper/**/*.xml']
        },
        {
            'priority': 'LOW',
            'category': '文档更新',
            'description': '更新API文档和数据库设计文档',
            'action': '确保文档中的表名与实际使用的表名一致',
            'files': ['warehouse-system/docs/API文档.md', 'warehouse-system/docs/数据库设计.md']
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. 【{rec['priority']}】{rec['category']}")
        print(f"   问题: {rec['description']}")
        print(f"   解决: {rec['action']}")
        print(f"   文件: {', '.join(rec['files'])}")
    
    return recommendations

def generate_sql_fix_script():
    """生成SQL修复脚本"""
    print("\n" + "="*80)
    print("📝 生成SQL修复脚本")
    print("="*80)
    
    sql_script = """
-- 仓库管理系统表名规范化修复脚本
-- 执行前请备份数据库！

-- 1. 检查表是否存在
SELECT 'sys_user_warehouse' as table_name, COUNT(*) as record_count 
FROM information_schema.tables 
WHERE table_schema = 'ry_vue' AND table_name = 'sys_user_warehouse'
UNION ALL
SELECT 'wms_user_warehouse' as table_name, COUNT(*) as record_count 
FROM information_schema.tables 
WHERE table_schema = 'ry_vue' AND table_name = 'wms_user_warehouse';

-- 2. 检查数据情况
-- 检查sys_user_warehouse表数据
SELECT 'sys_user_warehouse' as table_name, COUNT(*) as record_count 
FROM sys_user_warehouse;

-- 检查wms_user_warehouse表数据（如果存在）
-- SELECT 'wms_user_warehouse' as table_name, COUNT(*) as record_count 
-- FROM wms_user_warehouse;

-- 3. 数据迁移（如果需要）
-- 如果wms_user_warehouse表中有数据需要迁移到sys_user_warehouse表
-- INSERT INTO sys_user_warehouse (user_id, warehouse_id, permission_type, source)
-- SELECT user_id, warehouse_id, permission_type, source 
-- FROM wms_user_warehouse 
-- WHERE NOT EXISTS (
--     SELECT 1 FROM sys_user_warehouse s 
--     WHERE s.user_id = wms_user_warehouse.user_id 
--     AND s.warehouse_id = wms_user_warehouse.warehouse_id
-- );

-- 4. 清理重复数据（如果有）
-- DELETE FROM sys_user_warehouse 
-- WHERE (user_id, warehouse_id) IN (
--     SELECT user_id, warehouse_id FROM (
--         SELECT user_id, warehouse_id, COUNT(*) as cnt
--         FROM sys_user_warehouse 
--         GROUP BY user_id, warehouse_id 
--         HAVING cnt > 1
--     ) as duplicates
-- ) AND id NOT IN (
--     SELECT MIN(id) FROM sys_user_warehouse 
--     GROUP BY user_id, warehouse_id
-- );

-- 5. 验证数据完整性
SELECT 
    u.user_name,
    w.warehouse_name,
    uw.permission_type,
    uw.source
FROM sys_user_warehouse uw
LEFT JOIN sys_user u ON uw.user_id = u.user_id
LEFT JOIN sys_warehouse w ON uw.warehouse_id = w.warehouse_id
ORDER BY uw.user_id, uw.warehouse_id;
"""
    
    # 保存SQL脚本到文件
    with open('warehouse-system/fix_table_naming.sql', 'w', encoding='utf-8') as f:
        f.write(sql_script)
    
    print("✅ SQL修复脚本已生成: warehouse-system/fix_table_naming.sql")
    return sql_script

def main():
    """主函数"""
    print("🔧 仓库管理系统表名规范化修复工具")
    print("=" * 80)
    print("基于项目规范文档分析和修复表名不一致问题")
    print("=" * 80)
    
    # 连接数据库
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 分析表名命名问题
        naming_issues = analyze_table_naming_issues(connection)
        
        # 检查Mapper XML一致性
        mapper_issues = check_mapper_xml_consistency(connection)
        
        # 生成修复建议
        recommendations = generate_fix_recommendations()
        
        # 生成SQL修复脚本
        sql_script = generate_sql_fix_script()
        
        # 总结报告
        print("\n" + "="*80)
        print("📊 问题总结")
        print("="*80)
        print(f"发现命名问题: {len(naming_issues)} 个")
        print(f"发现Mapper问题: {len(mapper_issues)} 个")
        print(f"生成修复建议: {len(recommendations)} 条")
        
        print(f"\n✅ 分析完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n📋 下一步操作:")
        print("1. 查看生成的SQL修复脚本: warehouse-system/fix_table_naming.sql")
        print("2. 备份数据库后执行SQL脚本")
        print("3. 修复UserWarehouseMapper.xml文件中的表名")
        print("4. 重启后端服务并测试功能")
        
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
    finally:
        connection.close()
        print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()