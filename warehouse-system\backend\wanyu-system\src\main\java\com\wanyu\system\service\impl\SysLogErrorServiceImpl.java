package com.wanyu.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.system.mapper.SysLogErrorMapper;
import com.wanyu.system.domain.SysLogError;
import com.wanyu.system.service.ISysLogErrorService;

/**
 * 错误日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class SysLogErrorServiceImpl implements ISysLogErrorService 
{
    @Autowired
    private SysLogErrorMapper logErrorMapper;

    /**
     * 查询错误日志
     * 
     * @param id 错误日志主键
     * @return 错误日志
     */
    @Override
    public SysLogError selectLogErrorById(Long id)
    {
        return logErrorMapper.selectLogErrorById(id);
    }

    /**
     * 查询错误日志列表
     * 
     * @param logError 错误日志
     * @return 错误日志
     */
    @Override
    public List<SysLogError> selectLogErrorList(SysLogError logError)
    {
        return logErrorMapper.selectLogErrorList(logError);
    }

    /**
     * 新增错误日志
     * 
     * @param logError 错误日志
     * @return 结果
     */
    @Override
    public int insertLogError(SysLogError logError)
    {
        return logErrorMapper.insertLogError(logError);
    }

    /**
     * 修改错误日志
     * 
     * @param logError 错误日志
     * @return 结果
     */
    @Override
    public int updateLogError(SysLogError logError)
    {
        return logErrorMapper.updateLogError(logError);
    }

    /**
     * 批量删除错误日志
     * 
     * @param ids 需要删除的错误日志主键
     * @return 结果
     */
    @Override
    public int deleteLogErrorByIds(Long[] ids)
    {
        return logErrorMapper.deleteLogErrorByIds(ids);
    }

    /**
     * 删除错误日志信息
     * 
     * @param id 错误日志主键
     * @return 结果
     */
    @Override
    public int deleteLogErrorById(Long id)
    {
        return logErrorMapper.deleteLogErrorById(id);
    }

    /**
     * 清空错误日志
     */
    @Override
    public void cleanLogError()
    {
        logErrorMapper.cleanLogError();
    }
}