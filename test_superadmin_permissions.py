#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试superadmin用户权限的脚本
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8080"
USERNAME = "superadmin"
PASSWORD = "admin123"

def test_login():
    """测试登录"""
    print("=== 测试superadmin用户登录 ===")
    
    login_url = f"{BASE_URL}/login"
    login_data = {
        "username": USERNAME,
        "password": PASSWORD,
        "loginType": "username"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"登录响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('code') == 200:
                token = result.get('token')
                print(f"登录成功，获取到token: {token[:50]}...")
                return token
            else:
                print(f"登录失败: {result.get('msg')}")
                return None
        else:
            print(f"登录请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"登录请求异常: {e}")
        return None

def test_get_info(token):
    """测试获取用户信息"""
    print("\n=== 测试获取用户信息 ===")
    
    if not token:
        print("没有有效的token，跳过测试")
        return None
    
    info_url = f"{BASE_URL}/getInfo"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(info_url, headers=headers)
        print(f"获取用户信息响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"用户信息响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('code') == 200:
                data = result.get('data', {})
                user = data.get('user', {})
                roles = data.get('roles', [])
                permissions = data.get('permissions', [])
                
                print(f"\n用户信息:")
                print(f"  用户ID: {user.get('userId')}")
                print(f"  用户名: {user.get('userName')}")
                print(f"  昵称: {user.get('nickName')}")
                print(f"  真实姓名: {user.get('realName')}")
                
                print(f"\n角色信息:")
                for role in roles:
                    print(f"  - {role}")
                
                print(f"\n权限信息 (前10个):")
                for i, perm in enumerate(permissions[:10]):
                    print(f"  - {perm}")
                if len(permissions) > 10:
                    print(f"  ... 还有 {len(permissions) - 10} 个权限")
                
                print(f"\n权限统计:")
                print(f"  总角色数: {len(roles)}")
                print(f"  总权限数: {len(permissions)}")
                print(f"  是否有超级权限: {'*:*:*' in permissions}")
                print(f"  是否有admin角色: {'admin' in roles}")
                print(f"  是否有super_admin角色: {'super_admin' in roles}")
                
                return data
            else:
                print(f"获取用户信息失败: {result.get('msg')}")
                return None
        else:
            print(f"获取用户信息请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"获取用户信息请求异常: {e}")
        return None

def test_get_routers(token):
    """测试获取路由信息"""
    print("\n=== 测试获取路由信息 ===")
    
    if not token:
        print("没有有效的token，跳过测试")
        return None
    
    routers_url = f"{BASE_URL}/getRouters"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(routers_url, headers=headers)
        print(f"获取路由信息响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('code') == 200:
                routers = result.get('data', [])
                print(f"路由信息获取成功，共 {len(routers)} 个顶级路由")
                
                def count_menus(menu_list):
                    count = 0
                    for menu in menu_list:
                        count += 1
                        if menu.get('children'):
                            count += count_menus(menu['children'])
                    return count
                
                total_menus = count_menus(routers)
                print(f"总菜单数量: {total_menus}")
                
                print(f"\n顶级路由:")
                for router in routers:
                    name = router.get('name', '未知')
                    path = router.get('path', '未知')
                    children_count = len(router.get('children', []))
                    print(f"  - {name} ({path}) - {children_count} 个子菜单")
                
                return routers
            else:
                print(f"获取路由信息失败: {result.get('msg')}")
                return None
        else:
            print(f"获取路由信息请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"获取路由信息请求异常: {e}")
        return None

def main():
    """主函数"""
    print("开始测试superadmin用户权限...")
    
    # 测试登录
    token = test_login()
    
    # 测试获取用户信息
    user_info = test_get_info(token)
    
    # 测试获取路由信息
    routers = test_get_routers(token)
    
    print("\n=== 测试总结 ===")
    if token:
        print("✓ 登录成功")
    else:
        print("✗ 登录失败")
        
    if user_info:
        print("✓ 获取用户信息成功")
        roles = user_info.get('roles', [])
        permissions = user_info.get('permissions', [])
        
        if 'super_admin' in roles or 'admin' in roles:
            print("✓ 拥有管理员角色")
        else:
            print("✗ 缺少管理员角色")
            
        if '*:*:*' in permissions:
            print("✓ 拥有超级权限")
        else:
            print("✗ 缺少超级权限")
    else:
        print("✗ 获取用户信息失败")
        
    if routers:
        print("✓ 获取路由信息成功")
    else:
        print("✗ 获取路由信息失败")

if __name__ == "__main__":
    main()