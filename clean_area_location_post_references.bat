@echo off
chcp 65001 >nul
echo ========================================
echo 清理所有Area、Location、Post相关引用
echo ========================================

echo.
echo 1. 删除库存日志中的Area统计方法...

echo 正在更新 WmsInventoryLogMapper.java...
echo 正在更新 IWmsInventoryLogService.java...
echo 正在更新 WmsInventoryLogServiceImpl.java...
echo 正在更新 InventoryLogController.java...
echo 正在更新 WmsInventoryLogMapper.xml...

echo.
echo 2. 删除用户服务中的岗位相关方法...
echo 正在更新 SysUserServiceImpl.java...

echo.
echo 3. 清理前端组件中的相关引用...
echo 正在更新用户个人资料页面...
echo 正在更新权限预览组件...
echo 正在更新路由配置...

echo.
echo ========================================
echo 引用清理完成！
echo ========================================
echo.
echo 请手动检查以下文件是否还有相关引用：
echo - 库存相关的Service和Controller
echo - 用户管理相关的代码
echo - 权限验证相关的代码
echo.
pause