<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsInventoryTransferDetailMapper">
    
    <resultMap type="WmsInventoryTransferDetail" id="WmsInventoryTransferDetailResult">
        <result property="detailId"       column="detail_id"       />
        <result property="transferId"     column="transfer_id"     />
        <result property="productId"      column="product_id"      />
        <result property="productName"    column="product_name"    />
        <result property="productCode"    column="product_code"    />
        <result property="quantity"       column="quantity"        />
        <result property="remark"         column="remark"          />
    </resultMap>

    <sql id="selectWmsInventoryTransferDetailVo">
        select d.detail_id, d.transfer_id, d.product_id, p.product_name, p.product_code, d.quantity, d.remark
        from wms_inventory_transfer_detail d
        left join wms_product p on d.product_id = p.product_id
    </sql>

    <select id="selectWmsInventoryTransferDetailList" parameterType="WmsInventoryTransferDetail" resultMap="WmsInventoryTransferDetailResult">
        <include refid="selectWmsInventoryTransferDetailVo"/>
        <where>  
            <if test="transferId != null "> and d.transfer_id = #{transferId}</if>
            <if test="productId != null "> and d.product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and p.product_code like concat('%', #{productCode}, '%')</if>
        </where>
    </select>
    
    <select id="selectWmsInventoryTransferDetailByDetailId" parameterType="Long" resultMap="WmsInventoryTransferDetailResult">
        <include refid="selectWmsInventoryTransferDetailVo"/>
        where d.detail_id = #{detailId}
    </select>
    
    <select id="selectWmsInventoryTransferDetailByTransferId" parameterType="Long" resultMap="WmsInventoryTransferDetailResult">
        <include refid="selectWmsInventoryTransferDetailVo"/>
        where d.transfer_id = #{transferId}
    </select>
        
    <insert id="insertWmsInventoryTransferDetail" parameterType="WmsInventoryTransferDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into wms_inventory_transfer_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="transferId != null">transfer_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="transferId != null">#{transferId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertWmsInventoryTransferDetail">
        insert into wms_inventory_transfer_detail(transfer_id, product_id, quantity, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.transferId}, #{item.productId}, #{item.quantity}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateWmsInventoryTransferDetail" parameterType="WmsInventoryTransferDetail">
        update wms_inventory_transfer_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="transferId != null">transfer_id = #{transferId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteWmsInventoryTransferDetailByDetailId" parameterType="Long">
        delete from wms_inventory_transfer_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteWmsInventoryTransferDetailByDetailIds" parameterType="String">
        delete from wms_inventory_transfer_detail where detail_id in 
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
    
    <delete id="deleteWmsInventoryTransferDetailByTransferId" parameterType="Long">
        delete from wms_inventory_transfer_detail where transfer_id = #{transferId}
    </delete>
</mapper>
