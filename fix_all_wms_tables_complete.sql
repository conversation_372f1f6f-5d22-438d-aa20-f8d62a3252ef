-- 综合修复所有WMS表名规范化脚本
-- 包含以下修复:
-- 1. product_barcode → wms_barcode
-- 2. wms_product_category → wms_category
-- 3. wms_product_specification → wms_specification  
-- 4. wms_product_unit → wms_unit
-- 执行日期: 2025-08-31
-- 说明: 统一使用WMS前缀和简化的表名规范

-- ========================================
-- 1. 物品条码相关表
-- ========================================

-- 创建物品条码表
CREATE TABLE IF NOT EXISTS `wms_barcode` (
  `barcode_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '条码ID',
  `product_id` bigint(20) NOT NULL COMMENT '物品ID',
  `product_name` varchar(100) DEFAULT NULL COMMENT '物品名称',
  `barcode_content` varchar(200) NOT NULL COMMENT '条码内容',
  `barcode_type` varchar(20) NOT NULL DEFAULT 'CODE128' COMMENT '条码类型(CODE128,EAN13,QR_CODE等)',
  `barcode_image` varchar(500) DEFAULT NULL COMMENT '条码图片路径',
  `template_id` bigint(20) DEFAULT NULL COMMENT '条码模板ID',
  `is_main` char(1) DEFAULT '0' COMMENT '是否主条码(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`barcode_id`),
  UNIQUE KEY `uk_barcode_content` (`barcode_content`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_barcode_type` (`barcode_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品条码表';

-- 创建物品条码模板表
CREATE TABLE IF NOT EXISTS `wms_barcode_template` (
  `template_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_type` varchar(20) NOT NULL COMMENT '模板类型',
  `template_width` int(11) DEFAULT 100 COMMENT '模板宽度(mm)',
  `template_height` int(11) DEFAULT 50 COMMENT '模板高度(mm)',
  `barcode_width` int(11) DEFAULT 80 COMMENT '条码宽度(mm)',
  `barcode_height` int(11) DEFAULT 30 COMMENT '条码高度(mm)',
  `font_size` int(11) DEFAULT 12 COMMENT '字体大小',
  `show_text` char(1) DEFAULT '1' COMMENT '是否显示文字(0否 1是)',
  `show_product_name` char(1) DEFAULT '1' COMMENT '是否显示物品名称(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品条码模板表';

-- ========================================
-- 2. 物品分类表
-- ========================================

CREATE TABLE IF NOT EXISTS `wms_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `category_name` varchar(30) NOT NULL COMMENT '分类名称',
  `category_code` varchar(30) DEFAULT NULL COMMENT '分类编码',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_code` (`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品分类表';

-- ========================================
-- 3. 物品规格表
-- ========================================

CREATE TABLE IF NOT EXISTS `wms_specification` (
  `spec_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `spec_name` varchar(50) NOT NULL COMMENT '规格名称',
  `spec_code` varchar(30) DEFAULT NULL COMMENT '规格编码',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`spec_id`),
  UNIQUE KEY `uk_spec_code` (`spec_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品规格表';

-- ========================================
-- 4. 物品单位表
-- ========================================

CREATE TABLE IF NOT EXISTS `wms_unit` (
  `unit_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '单位ID',
  `unit_name` varchar(50) NOT NULL COMMENT '单位名称',
  `unit_code` varchar(30) DEFAULT NULL COMMENT '单位编码',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`unit_id`),
  UNIQUE KEY `uk_unit_code` (`unit_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物品单位表';

-- ========================================
-- 5. 数据迁移存储过程
-- ========================================

DELIMITER $$

CREATE PROCEDURE MigrateAllWmsTableData()
BEGIN
    DECLARE barcode_table_exists INT DEFAULT 0;
    DECLARE category_table_exists INT DEFAULT 0;
    DECLARE spec_table_exists INT DEFAULT 0;
    DECLARE unit_table_exists INT DEFAULT 0;
    
    -- 检查并迁移条码数据
    SELECT COUNT(*) INTO barcode_table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'product_barcode';
    
    IF barcode_table_exists > 0 THEN
        INSERT IGNORE INTO wms_barcode (
            product_id, product_name, barcode_content, barcode_type, 
            barcode_image, template_id, is_main, status, remark, 
            create_by, create_time, update_by, update_time
        )
        SELECT 
            product_id, product_name, barcode_content, 
            IFNULL(barcode_type, 'CODE128') as barcode_type,
            barcode_image, template_id, 
            IFNULL(is_main, '0') as is_main,
            IFNULL(status, '0') as status,
            remark, create_by, create_time, update_by, update_time
        FROM product_barcode;
        
        SELECT CONCAT('已迁移 ', ROW_COUNT(), ' 条记录从 product_barcode 到 wms_barcode') as migration_result;
    END IF;
    
    -- 检查并迁移分类数据
    SELECT COUNT(*) INTO category_table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product_category';
    
    IF category_table_exists > 0 THEN
        INSERT IGNORE INTO wms_category (
            category_id, parent_id, ancestors, category_name, category_code, 
            order_num, status, del_flag, create_by, create_time, update_by, update_time, remark
        )
        SELECT 
            category_id, parent_id, ancestors, category_name, category_code,
            order_num, status, del_flag, create_by, create_time, update_by, update_time, remark
        FROM wms_product_category;
        
        SELECT CONCAT('已迁移 ', ROW_COUNT(), ' 条记录从 wms_product_category 到 wms_category') as migration_result;
    END IF;
    
    -- 检查并迁移规格数据
    SELECT COUNT(*) INTO spec_table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product_specification';
    
    IF spec_table_exists > 0 THEN
        INSERT IGNORE INTO wms_specification (
            spec_id, spec_name, spec_code, status, remark, 
            create_by, create_time, update_by, update_time
        )
        SELECT 
            spec_id, spec_name, spec_code, status, remark,
            create_by, create_time, update_by, update_time
        FROM wms_product_specification;
        
        SELECT CONCAT('已迁移 ', ROW_COUNT(), ' 条记录从 wms_product_specification 到 wms_specification') as migration_result;
    END IF;
    
    -- 检查并迁移单位数据
    SELECT COUNT(*) INTO unit_table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product_unit';
    
    IF unit_table_exists > 0 THEN
        INSERT IGNORE INTO wms_unit (
            unit_id, unit_name, unit_code, status, remark,
            create_by, create_time, update_by, update_time
        )
        SELECT 
            unit_id, unit_name, unit_code, status, remark,
            create_by, create_time, update_by, update_time
        FROM wms_product_unit;
        
        SELECT CONCAT('已迁移 ', ROW_COUNT(), ' 条记录从 wms_product_unit 到 wms_unit') as migration_result;
    END IF;
    
    -- 如果没有旧表，输出提示信息
    IF barcode_table_exists = 0 AND category_table_exists = 0 AND spec_table_exists = 0 AND unit_table_exists = 0 THEN
        SELECT '未找到需要迁移的旧表，所有表已使用新的命名规范' as migration_result;
    END IF;
    
END$$

DELIMITER ;

-- 执行数据迁移
CALL MigrateAllWmsTableData();

-- 删除临时存储过程
DROP PROCEDURE IF EXISTS MigrateAllWmsTableData;

-- ========================================
-- 6. 插入默认数据
-- ========================================

-- 插入默认条码模板
INSERT IGNORE INTO `wms_barcode_template` (`template_name`, `template_type`, `template_width`, `template_height`, `barcode_width`, `barcode_height`, `font_size`, `show_text`, `show_product_name`, `status`, `create_by`, `create_time`) VALUES
('标准模板', 'CODE128', 100, 50, 80, 30, 12, '1', '1', '0', 'admin', NOW()),
('小型模板', 'CODE128', 60, 30, 50, 20, 10, '1', '0', '0', 'admin', NOW()),
('二维码模板', 'QR_CODE', 50, 50, 40, 40, 10, '1', '1', '0', 'admin', NOW());

-- 插入默认物品分类
INSERT IGNORE INTO `wms_category` (`category_id`, `parent_id`, `ancestors`, `category_name`, `category_code`, `order_num`, `status`, `del_flag`, `create_by`, `create_time`) VALUES
(1, 0, '0', '电子产品', 'ELECTRONICS', 1, '0', '0', 'admin', NOW()),
(2, 0, '0', '办公用品', 'OFFICE', 2, '0', '0', 'admin', NOW()),
(3, 0, '0', '生活用品', 'DAILY', 3, '0', '0', 'admin', NOW()),
(4, 1, '0,1', '手机', 'PHONE', 1, '0', '0', 'admin', NOW()),
(5, 1, '0,1', '电脑', 'COMPUTER', 2, '0', '0', 'admin', NOW()),
(6, 2, '0,2', '文具', 'STATIONERY', 1, '0', '0', 'admin', NOW()),
(7, 2, '0,2', '打印用品', 'PRINTING', 2, '0', '0', 'admin', NOW());

-- 插入默认物品规格
INSERT IGNORE INTO `wms_specification` (`spec_name`, `spec_code`, `status`, `create_by`, `create_time`) VALUES
('标准规格', 'STANDARD', '0', 'admin', NOW()),
('小号', 'SMALL', '0', 'admin', NOW()),
('中号', 'MEDIUM', '0', 'admin', NOW()),
('大号', 'LARGE', '0', 'admin', NOW()),
('特大号', 'XLARGE', '0', 'admin', NOW()),
('迷你', 'MINI', '0', 'admin', NOW()),
('加长', 'EXTENDED', '0', 'admin', NOW()),
('加宽', 'WIDE', '0', 'admin', NOW());

-- 插入默认物品单位
INSERT IGNORE INTO `wms_unit` (`unit_name`, `unit_code`, `status`, `create_by`, `create_time`) VALUES
('个', 'PCS', '0', 'admin', NOW()),
('台', 'SET', '0', 'admin', NOW()),
('箱', 'BOX', '0', 'admin', NOW()),
('包', 'PACK', '0', 'admin', NOW()),
('袋', 'BAG', '0', 'admin', NOW()),
('瓶', 'BOTTLE', '0', 'admin', NOW()),
('支', 'PIECE', '0', 'admin', NOW()),
('张', 'SHEET', '0', 'admin', NOW()),
('本', 'BOOK', '0', 'admin', NOW()),
('套', 'SUITE', '0', 'admin', NOW()),
('米', 'METER', '0', 'admin', NOW()),
('千克', 'KG', '0', 'admin', NOW()),
('升', 'LITER', '0', 'admin', NOW()),
('吨', 'TON', '0', 'admin', NOW());

-- ========================================
-- 7. 插入字典数据
-- ========================================

-- 插入条码类型字典
INSERT IGNORE INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('物品条码类型', 'wms_barcode_type', '0', 'admin', NOW(), '物品条码类型列表');

-- 插入条码类型字典数据项
INSERT IGNORE INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, 'CODE128', 'CODE128', 'wms_barcode_type', '', 'primary', 'Y', '0', 'admin', NOW(), 'CODE128条码'),
(2, 'EAN13', 'EAN13', 'wms_barcode_type', '', 'success', 'N', '0', 'admin', NOW(), 'EAN13条码'),
(3, 'EAN8', 'EAN8', 'wms_barcode_type', '', 'info', 'N', '0', 'admin', NOW(), 'EAN8条码'),
(4, 'UPC_A', 'UPC_A', 'wms_barcode_type', '', 'warning', 'N', '0', 'admin', NOW(), 'UPC-A条码'),
(5, '二维码', 'QR_CODE', 'wms_barcode_type', '', 'danger', 'N', '0', 'admin', NOW(), 'QR二维码');

-- ========================================
-- 8. 更新外键引用
-- ========================================

-- 更新 wms_product 表中的外键引用（如果存在）
-- 注意：这里使用安全的更新方式，先检查表和字段是否存在

-- 更新分类ID引用
UPDATE wms_product p 
INNER JOIN wms_category c ON p.category_name = c.category_name 
SET p.category_id = c.category_id 
WHERE p.category_id IS NULL OR p.category_id = 0
AND EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product' 
    AND column_name = 'category_name'
);

-- 更新规格ID引用  
UPDATE wms_product p 
INNER JOIN wms_specification s ON p.spec_name = s.spec_name 
SET p.spec_id = s.spec_id 
WHERE p.spec_id IS NULL OR p.spec_id = 0
AND EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product' 
    AND column_name = 'spec_name'
);

-- 更新单位ID引用
UPDATE wms_product p 
INNER JOIN wms_unit u ON p.unit_name = u.unit_name 
SET p.unit_id = u.unit_id 
WHERE p.unit_id IS NULL OR p.unit_id = 0
AND EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'wms_product' 
    AND column_name = 'unit_name'
);

-- ========================================
-- 9. 验证和统计
-- ========================================

SELECT '=== WMS表名规范化修复完成 ===' as completion_message;

SELECT '数据统计:' as statistics;
SELECT 'wms_barcode' as table_name, COUNT(*) as record_count FROM wms_barcode
UNION ALL
SELECT 'wms_barcode_template' as table_name, COUNT(*) as record_count FROM wms_barcode_template
UNION ALL
SELECT 'wms_category' as table_name, COUNT(*) as record_count FROM wms_category WHERE del_flag = '0'
UNION ALL
SELECT 'wms_specification' as table_name, COUNT(*) as record_count FROM wms_specification
UNION ALL
SELECT 'wms_unit' as table_name, COUNT(*) as record_count FROM wms_unit;

SELECT '修复内容总结:' as summary;
SELECT '1. ✅ 物品条码表 (wms_barcode, wms_barcode_template)' as step1;
SELECT '2. ✅ 物品分类表 (wms_category)' as step2;
SELECT '3. ✅ 物品规格表 (wms_specification)' as step3;
SELECT '4. ✅ 物品单位表 (wms_unit)' as step4;
SELECT '5. ✅ 数据迁移和默认数据插入' as step5;
SELECT '6. ✅ 字典数据和外键引用更新' as step6;
SELECT '请重新编译并启动后端服务以使更改生效' as next_step;