package com.wanyu.system.service;

import java.util.List;
import com.wanyu.system.domain.WmsBarcode;
import com.wanyu.system.domain.WmsBarcodeTemplate;

/**
 * 物品条码Service接口
 *
 * <AUTHOR>
 */
public interface IWmsBarcodeService
{
    /**
     * 查询物品条码
     *
     * @param barcodeId 物品条码主键
     * @return 物品条码
     */
    public WmsBarcode selectWmsBarcodeByBarcodeId(Long barcodeId);

    /**
     * 查询物品条码列表
     *
     * @param wmsBarcode 物品条码
     * @return 物品条码集合
     */
    public List<WmsBarcode> selectWmsBarcodeList(WmsBarcode wmsBarcode);

    /**
     * 新增物品条码
     *
     * @param wmsBarcode 物品条码
     * @return 结果
     */
    public int insertWmsBarcode(WmsBarcode wmsBarcode);

    /**
     * 修改物品条码
     *
     * @param wmsBarcode 物品条码
     * @return 结果
     */
    public int updateWmsBarcode(WmsBarcode wmsBarcode);

    /**
     * 批量删除物品条码
     *
     * @param barcodeIds 需要删除的物品条码主键集合
     * @return 结果
     */
    public int deleteWmsBarcodeByBarcodeIds(Long[] barcodeIds);

    /**
     * 删除物品条码信息
     *
     * @param barcodeId 物品条码主键
     * @return 结果
     */
    public int deleteWmsBarcodeByBarcodeId(Long barcodeId);

    /**
     * 生成物品条码
     *
     * @param wmsBarcode 物品条码
     * @return 结果
     */
    public WmsBarcode generateWmsBarcode(WmsBarcode wmsBarcode);

    /**
     * 批量生成物品条码
     *
     * @param wmsBarcodeList 物品条码列表
     * @return 结果
     */
    public List<WmsBarcode> batchGenerateWmsBarcode(List<WmsBarcode> wmsBarcodeList);

    /**
     * 获取物品条码图片
     *
     * @param barcodeId 物品条码主键
     * @return 条码图片信息
     */
    public String getWmsBarcodeImage(Long barcodeId);

    /**
     * 根据条码内容查询物品条码
     *
     * @param barcodeContent 条码内容
     * @return 物品条码
     */
    public WmsBarcode selectWmsBarcodeByContent(String barcodeContent);

    /**
     * 根据物品ID查询物品条码列表
     *
     * @param productId 物品ID
     * @return 物品条码集合
     */
    public List<WmsBarcode> selectWmsBarcodeByProductId(Long productId);

    /**
     * 查询物品条码模板列表
     *
     * @return 物品条码模板集合
     */
    public List<WmsBarcodeTemplate> selectWmsBarcodeTemplateList();
}