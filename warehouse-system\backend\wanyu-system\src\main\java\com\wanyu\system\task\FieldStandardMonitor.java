package com.wanyu.system.task;

import com.wanyu.common.utils.FieldStandardValidator;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.system.service.INotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 字段标准监控组件
 * 定时监控数据库字段定义标准合规性
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Component
public class FieldStandardMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(FieldStandardMonitor.class);
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private INotificationService notificationService;
    
    // 需要监控的关键表
    private static final List<String> MONITORED_TABLES = Arrays.asList(
        "sys_license",
        "sys_license_feature", 
        "wms_operation_log",
        "sys_user",
        "sys_role",
        "sys_menu",
        "sys_dict_data",
        "wms_inventory",
        "wms_inventory_in",
        "wms_inventory_out"
    );
    
    /**
     * 每天凌晨2点执行字段标准检查
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void checkFieldStandards() {
        log.info("开始执行字段标准合规性检查...");
        
        try {
            FieldStandardValidator.ComplianceReport report = FieldStandardValidator.generateComplianceReport(MONITORED_TABLES);
            
            // 记录检查结果
            logComplianceReport(report);
            
            // 如果发现不合规问题，发送告警
            if (report.getNonCompliantTables() > 0) {
                sendComplianceAlert(report);
            }
            
            log.info("字段标准合规性检查完成，合规率: {}%", String.format("%.2f", report.getComplianceRate()));
            
        } catch (Exception e) {
            log.error("字段标准合规性检查失败", e);
            sendErrorAlert("字段标准合规性检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 每小时检查关键表的字段值合规性
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkFieldValues() {
        log.info("开始执行字段值合规性检查...");
        
        try {
            List<FieldValueViolation> violations = checkCriticalFieldValues();
            
            if (!violations.isEmpty()) {
                log.warn("发现 {} 个字段值违规问题", violations.size());
                sendFieldValueAlert(violations);
            } else {
                log.info("字段值合规性检查通过，未发现违规数据");
            }
            
        } catch (Exception e) {
            log.error("字段值合规性检查失败", e);
            sendErrorAlert("字段值合规性检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动触发字段标准检查
     */
    public FieldStandardValidator.ComplianceReport manualCheckStandards() {
        log.info("手动触发字段标准合规性检查");
        return FieldStandardValidator.generateComplianceReport(MONITORED_TABLES);
    }
    
    /**
     * 检查关键字段值的合规性
     */
    private List<FieldValueViolation> checkCriticalFieldValues() {
        List<FieldValueViolation> violations = new ArrayList<>();
        
        try (Connection conn = dataSource.getConnection()) {
            // 检查sys_license表的status字段
            violations.addAll(checkTableFieldValues(conn, "sys_license", "status", "license_id"));
            
            // 检查sys_license_feature表的status字段
            violations.addAll(checkTableFieldValues(conn, "sys_license_feature", "status", "feature_id"));
            
            // 检查wms_operation_log表的operation_status字段
            violations.addAll(checkTableFieldValues(conn, "wms_operation_log", "operation_status", "log_id"));
            
            // 检查sys_user表的status字段
            violations.addAll(checkTableFieldValues(conn, "sys_user", "status", "user_id"));
            
            // 检查sys_user表的del_flag字段
            violations.addAll(checkTableFieldValues(conn, "sys_user", "del_flag", "user_id"));
            
        } catch (SQLException e) {
            log.error("检查字段值合规性时数据库操作失败", e);
        }
        
        return violations;
    }
    
    /**
     * 检查指定表的字段值
     */
    private List<FieldValueViolation> checkTableFieldValues(Connection conn, String tableName, 
                                                           String fieldName, String primaryKey) throws SQLException {
        List<FieldValueViolation> violations = new ArrayList<>();
        
        String sql = String.format("SELECT %s, %s FROM %s WHERE %s IS NOT NULL", 
                                 primaryKey, fieldName, tableName, fieldName);
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String primaryKeyValue = rs.getString(primaryKey);
                String fieldValue = rs.getString(fieldName);
                
                // 验证字段值是否符合标准
                if (StringUtils.isNotEmpty(fieldValue)) {
                    FieldStandardValidator.ValidationResult result = 
                        FieldStandardValidator.validateFieldValue(fieldName, fieldValue);
                    
                    if (!result.isValid()) {
                        FieldValueViolation violation = new FieldValueViolation();
                        violation.setTableName(tableName);
                        violation.setFieldName(fieldName);
                        violation.setPrimaryKeyValue(primaryKeyValue);
                        violation.setFieldValue(fieldValue);
                        violation.setErrorMessage(result.getErrorMessage());
                        violation.setExpectedValues(result.getExpectedValues());
                        violation.setDetectionTime(LocalDateTime.now());
                        
                        violations.add(violation);
                    }
                }
            }
        }
        
        return violations;
    }
    
    /**
     * 记录合规性报告
     */
    private void logComplianceReport(FieldStandardValidator.ComplianceReport report) {
        log.info("字段标准合规性报告:");
        log.info("报告ID: {}", report.getReportId());
        log.info("检查表数: {}", report.getTotalTables());
        log.info("合规表数: {}", report.getCompliantTables());
        log.info("不合规表数: {}", report.getNonCompliantTables());
        log.info("总问题数: {}", report.getTotalIssues());
        log.info("合规率: {}%", String.format("%.2f", report.getComplianceRate()));
        
        // 记录不合规的表详情
        for (FieldStandardValidator.TableStandardResult tableResult : report.getTableResults()) {
            if (!tableResult.isCompliant()) {
                log.warn("表 {} 不符合字段标准，问题数: {}", 
                        tableResult.getTableName(), tableResult.getIssues().size());
                for (String issue : tableResult.getIssues()) {
                    log.warn("  - {}", issue);
                }
            }
        }
    }
    
    /**
     * 发送合规性告警
     */
    private void sendComplianceAlert(FieldStandardValidator.ComplianceReport report) {
        String alertMessage = String.format(
            "字段标准合规性告警\n" +
            "检查时间: %s\n" +
            "不合规表数: %d/%d\n" +
            "合规率: %.2f%%\n" +
            "总问题数: %d\n\n" +
            "不合规表列表:\n%s",
            report.getReportTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            report.getNonCompliantTables(),
            report.getTotalTables(),
            report.getComplianceRate(),
            report.getTotalIssues(),
            getNonCompliantTablesList(report)
        );
        
        log.warn("字段标准合规性告警: {}", alertMessage);
        
        // 发送告警通知
        notificationService.sendAlert("字段标准合规性告警", alertMessage);
    }
    
    /**
     * 发送字段值违规告警
     */
    private void sendFieldValueAlert(List<FieldValueViolation> violations) {
        StringBuilder alertMessage = new StringBuilder();
        alertMessage.append("字段值违规告警\n");
        alertMessage.append("检查时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        alertMessage.append("违规记录数: ").append(violations.size()).append("\n\n");
        alertMessage.append("违规详情:\n");
        
        for (FieldValueViolation violation : violations) {
            alertMessage.append(String.format("- 表: %s, 字段: %s, 主键: %s, 值: %s, 错误: %s\n",
                violation.getTableName(),
                violation.getFieldName(),
                violation.getPrimaryKeyValue(),
                violation.getFieldValue(),
                violation.getErrorMessage()
            ));
        }
        
        log.warn("字段值违规告警: {}", alertMessage.toString());
        
        // 发送告警通知
        notificationService.sendAlert("字段值违规告警", alertMessage.toString());
    }
    
    /**
     * 发送错误告警
     */
    private void sendErrorAlert(String errorMessage) {
        String alertMessage = String.format(
            "字段标准监控错误告警\n" +
            "时间: %s\n" +
            "错误信息: %s",
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            errorMessage
        );
        
        log.error("字段标准监控错误告警: {}", alertMessage);
        
        // 发送错误告警通知
        notificationService.sendAlert("字段标准监控错误", alertMessage);
    }
    
    /**
     * 获取不合规表列表
     */
    private String getNonCompliantTablesList(FieldStandardValidator.ComplianceReport report) {
        StringBuilder list = new StringBuilder();
        for (FieldStandardValidator.TableStandardResult tableResult : report.getTableResults()) {
            if (!tableResult.isCompliant()) {
                list.append("- ").append(tableResult.getTableName())
                    .append(" (").append(tableResult.getIssues().size()).append("个问题)\n");
            }
        }
        return list.toString();
    }
    
    /**
     * 获取监控统计信息
     */
    public MonitoringStats getMonitoringStats() {
        MonitoringStats stats = new MonitoringStats();
        stats.setLastCheckTime(LocalDateTime.now());
        stats.setMonitoredTablesCount(MONITORED_TABLES.size());
        
        try {
            FieldStandardValidator.ComplianceReport report = FieldStandardValidator.generateComplianceReport(MONITORED_TABLES);
            stats.setCompliantTablesCount(report.getCompliantTables());
            stats.setNonCompliantTablesCount(report.getNonCompliantTables());
            stats.setComplianceRate(report.getComplianceRate());
            stats.setTotalIssuesCount(report.getTotalIssues());
        } catch (Exception e) {
            log.error("获取监控统计信息失败", e);
            stats.setError("获取统计信息失败: " + e.getMessage());
        }
        
        return stats;
    }
    
    /**
     * 字段值违规记录类
     */
    public static class FieldValueViolation {
        private String tableName;
        private String fieldName;
        private String primaryKeyValue;
        private String fieldValue;
        private String errorMessage;
        private List<String> expectedValues;
        private LocalDateTime detectionTime;
        
        // Getters and Setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }
        
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }
        
        public String getPrimaryKeyValue() { return primaryKeyValue; }
        public void setPrimaryKeyValue(String primaryKeyValue) { this.primaryKeyValue = primaryKeyValue; }
        
        public String getFieldValue() { return fieldValue; }
        public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public List<String> getExpectedValues() { return expectedValues; }
        public void setExpectedValues(List<String> expectedValues) { this.expectedValues = expectedValues; }
        
        public LocalDateTime getDetectionTime() { return detectionTime; }
        public void setDetectionTime(LocalDateTime detectionTime) { this.detectionTime = detectionTime; }
    }
    
    /**
     * 监控统计信息类
     */
    public static class MonitoringStats {
        private LocalDateTime lastCheckTime;
        private int monitoredTablesCount;
        private int compliantTablesCount;
        private int nonCompliantTablesCount;
        private double complianceRate;
        private int totalIssuesCount;
        private String error;
        
        // Getters and Setters
        public LocalDateTime getLastCheckTime() { return lastCheckTime; }
        public void setLastCheckTime(LocalDateTime lastCheckTime) { this.lastCheckTime = lastCheckTime; }
        
        public int getMonitoredTablesCount() { return monitoredTablesCount; }
        public void setMonitoredTablesCount(int monitoredTablesCount) { this.monitoredTablesCount = monitoredTablesCount; }
        
        public int getCompliantTablesCount() { return compliantTablesCount; }
        public void setCompliantTablesCount(int compliantTablesCount) { this.compliantTablesCount = compliantTablesCount; }
        
        public int getNonCompliantTablesCount() { return nonCompliantTablesCount; }
        public void setNonCompliantTablesCount(int nonCompliantTablesCount) { this.nonCompliantTablesCount = nonCompliantTablesCount; }
        
        public double getComplianceRate() { return complianceRate; }
        public void setComplianceRate(double complianceRate) { this.complianceRate = complianceRate; }
        
        public int getTotalIssuesCount() { return totalIssuesCount; }
        public void setTotalIssuesCount(int totalIssuesCount) { this.totalIssuesCount = totalIssuesCount; }
        
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}