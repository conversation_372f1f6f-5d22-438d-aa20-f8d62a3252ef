package com.wanyu.common.core.cache;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 验证码内存缓存
 * 为保持兼容性而保留，推荐使用CaptchaCacheService
 *
 * <AUTHOR>
 */
public class CaptchaMemoryCache {
    private static final Logger log = LoggerFactory.getLogger(CaptchaMemoryCache.class);
    
    private static final ConcurrentHashMap<String, String> CACHE = new ConcurrentHashMap<>();
    
    /**
     * 设置验证码缓存
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间（分钟）
     */
    public static void setCaptcha(String key, String value, long timeout) {
        CACHE.put(key, value);
        log.info("设置验证码缓存 key={}, value={}, timeout={}", key, value, timeout);
        
        // 启动一个定时任务在指定时间后删除缓存
        Thread cleaner = new Thread(() -> {
            try {
                Thread.sleep(timeout * 60 * 1000); // 转换为毫秒
                CACHE.remove(key);
                log.debug("清理验证码 key={}", key);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        cleaner.setDaemon(true);
        cleaner.start();
    }
    
    /**
     * 获取验证码缓存
     * 
     * @param key 缓存键
     * @return 缓存值，如果不存在返回null
     */
    public static String getCaptcha(String key) {
        String value = CACHE.get(key);
        if (value != null) {
            log.info("获取验证码缓存 key={}, value={}", key, value);
        } else {
            log.info("验证码不存在 key={}", key);
        }
        return value;
    }
    
    /**
     * 删除验证码缓存（兼容方法）
     * 
     * @param key 缓存键
     */
    public static void removeCaptcha(String key) {
        deleteCaptcha(key);
    }
    
    /**
     * 删除验证码缓存
     * 
     * @param key 缓存键
     */
    public static void deleteCaptcha(String key) {
        String removed = CACHE.remove(key);
        if (removed != null) {
            log.debug("删除验证码缓存 key={}", key);
        }
    }
    
    // 保留原有兼容方法
    public static int getCacheSize() {
        return CACHE.size();
    }
    
    public static void clearCache() {
        int size = CACHE.size();
        CACHE.clear();
        log.debug("清空验证码缓存 size={}", size);
    }
}
