package com.wanyu.web.controller.common;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.google.code.kaptcha.Producer;
import com.wanyu.common.config.WanYuConfig;
import com.wanyu.common.constant.CacheConstants;
import com.wanyu.common.constant.Constants;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.cache.CaptchaCacheService;
import com.wanyu.common.utils.sign.Base64;
import com.wanyu.common.utils.uuid.IdUtils;
import com.wanyu.system.service.ISysConfigService;

/**
 * 验证码操作处理
 *
 * <AUTHOR>
 */
@RestController
public class CaptchaController
{
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private CaptchaCacheService captchaCacheService;

    @Autowired
    private ISysConfigService configService;
    /**
     * 生成验证码
     */
    @GetMapping({"/captchaImage", "/api/v1/auth/captcha"})
    public AjaxResult getCode(HttpServletResponse response) throws IOException
    {
        AjaxResult ajax = AjaxResult.success();
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled)
        {
            return ajax;
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        // 生成验证码
        String captchaType = WanYuConfig.getCaptchaType();
        if ("math".equals(captchaType))
        {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        }
        else if ("char".equals(captchaType))
        {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        // 使用统一的验证码缓存服务
        captchaCacheService.setCaptcha(verifyKey, code, Constants.CAPTCHA_EXPIRATION);
        
        // 记录详细验证码信息
        System.out.println("生成验证码 - UUID: " + uuid);
        System.out.println("验证码Key: " + verifyKey);
        System.out.println("验证码值: " + code);
        System.out.println("有效期: " + Constants.CAPTCHA_EXPIRATION + "分钟");
        
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try
        {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e)
        {
            return AjaxResult.error(e.getMessage());
        }

        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }
}