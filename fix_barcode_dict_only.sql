-- 仅修复条码字典数据的脚本
-- 解决前端404错误: /system/dict/data/type/wms_barcode_type

-- 1. 检查并插入条码类型字典类型
INSERT IGNORE INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('物品条码类型', 'wms_barcode_type', '0', 'admin', NOW(), '物品条码类型列表');

-- 2. 插入条码类型字典数据项
INSERT IGNORE INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, 'CODE128', 'CODE128', 'wms_barcode_type', '', 'primary', 'Y', '0', 'admin', NOW(), 'CODE128条码'),
(2, 'EAN13', 'EAN13', 'wms_barcode_type', '', 'success', 'N', '0', 'admin', NOW(), 'EAN13条码'),
(3, 'EAN8', 'EAN8', 'wms_barcode_type', '', 'info', 'N', '0', 'admin', NOW(), 'EAN8条码'),
(4, 'UPC_A', 'UPC_A', 'wms_barcode_type', '', 'warning', 'N', '0', 'admin', NOW(), 'UPC-A条码'),
(5, '二维码', 'QR_CODE', 'wms_barcode_type', '', 'danger', 'N', '0', 'admin', NOW(), 'QR二维码');

-- 3. 验证字典数据
SELECT '条码类型字典验证:' as verification;
SELECT dict_label, dict_value, dict_type FROM sys_dict_data WHERE dict_type = 'wms_barcode_type' ORDER BY dict_sort;

SELECT '修复完成！' as result;