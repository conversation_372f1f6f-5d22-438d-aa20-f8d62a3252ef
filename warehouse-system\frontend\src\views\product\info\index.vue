<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入物品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物品编码" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入物品编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物品分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="物品分类" clearable>
          <el-option
            v-for="item in categoryOptions"
            :key="item.categoryId"
            :label="item.categoryName"
            :value="item.categoryId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="物品状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['product:info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['product:info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['product:info:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['product:info:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['product:info:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="物品ID" align="center" prop="productId" />
      <el-table-column label="物品图片" align="center" prop="imageUrl" width="100">
        <template slot-scope="scope">
            <el-image
            v-if="scope.row.imageUrl"
            :src="getImageUrl(scope.row.imageUrl)"
            style="width: 60px; height: 60px"
            :preview-src-list="[getImageUrl(scope.row.imageUrl)]">
          </el-image>
          <el-image
            v-else
            src="@/assets/images/no-image.svg"
            style="width: 60px; height: 60px">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="物品名称" align="center" prop="productName" />
      <el-table-column label="物品编码" align="center" prop="productCode" />
      <el-table-column label="物品分类" align="center" prop="categoryName" />
      <el-table-column label="规格" align="center" prop="specName" />
      <el-table-column label="单位" align="center" prop="unitName" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['product:info:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['product:info:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物品信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="物品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入物品名称" />
        </el-form-item>
        <el-form-item label="物品编码" prop="productCode">
          <el-input v-model="form.productCode" placeholder="请输入物品编码" />
        </el-form-item>
        <el-form-item label="物品分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择物品分类">
            <el-option
              v-for="item in categoryOptions"
              :key="item.categoryId"
              :label="item.categoryName"
              :value="item.categoryId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规格" prop="specId">
          <el-select v-model="form.specId" placeholder="请选择物品规格">
            <el-option
              v-for="item in specOptions"
              :key="item.specId"
              :label="item.specName"
              :value="item.specId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位" prop="unitId">
          <el-select v-model="form.unitId" placeholder="请选择物品单位">
            <el-option
              v-for="item in unitOptions"
              :key="item.unitId"
              :label="item.unitName"
              :value="item.unitId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="物品图片" prop="imageUrl">
          <image-upload 
            v-model="form.imageUrl" 
            :limit="1"
            :fileSize="1"
            :fileType="['png', 'jpg', 'jpeg']"
            :isShowTip="true"
            @error="handleUploadError"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 物品导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileUploadError"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-checkbox v-model="upload.updateSupport" />更新已有数据
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProduct, getProduct, delProduct, addProduct, updateProduct, importProduct } from "@/api/product/info";
import { listCategory } from "@/api/product/category";
import { listSpec } from "@/api/product/spec";
import { listUnit } from "@/api/product/unit";
import { getToken } from "@/utils/auth";
import ImageUpload from "@/components/ImageUpload";
import axios from 'axios';

export default {
  name: "Product",
  components: { ImageUpload },
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物品信息表格数据
      productList: [],
      // 物品分类选项
      categoryOptions: [],
      // 物品规格选项
      specOptions: [],
      // 物品单位选项
      unitOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: null,
        productCode: null,
        categoryId: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productName: [
          { required: true, message: "物品名称不能为空", trigger: "blur" }
        ],
        productCode: [
          { required: true, message: "物品编码不能为空", trigger: "blur" }
        ],
        categoryId: [
          { required: true, message: "物品分类不能为空", trigger: "change" }
        ],
        specId: [
          { required: true, message: "物品规格不能为空", trigger: "change" }
        ],
        unitId: [
          { required: true, message: "物品单位不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      // 物品导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已有数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/product/info/importData"
      }
    };
  },
  created() {
    this.getList();
    this.getCategoryOptions();
    this.getSpecOptions();
    this.getUnitOptions();
  },
  methods: {
    /** 查询物品信息列表 */
    getList() {
      this.loading = true;
      listProduct(this.queryParams).then(response => {
        this.productList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error("获取物品列表失败:", error);
        this.$modal.msgError("获取物品列表失败，请稍后重试");
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 查询物品分类选项 */
    getCategoryOptions() {
      listCategory().then(response => {
        this.categoryOptions = response.data;
      }).catch(error => {
        console.error("获取物品分类失败:", error);
        this.$modal.msgError("获取物品分类失败，请稍后重试");
      });
    },
    /** 查询物品规格选项 */
    getSpecOptions() {
      listSpec().then(response => {
        this.specOptions = response.rows;
      }).catch(error => {
        console.error("获取物品规格失败:", error);
        this.$modal.msgError("获取物品规格失败，请稍后重试");
      });
    },
    /** 查询物品单位选项 */
    getUnitOptions() {
      listUnit().then(response => {
        this.unitOptions = response.rows;
      }).catch(error => {
        console.error("获取物品单位失败:", error);
        this.$modal.msgError("获取物品单位失败，请稍后重试");
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        productId: null,
        productName: null,
        productCode: null,
        categoryId: null,
        specId: null,
        unitId: null,
        imageUrl: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.productId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物品信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const productId = row.productId || this.ids[0]
      getProduct(productId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物品信息";
      }).catch(error => {
        console.error("获取物品详情失败:", error);
        this.$modal.msgError("获取物品详情失败，请稍后重试");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.productId != null) {
            updateProduct(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error("修改失败:", error);
              this.$modal.msgError("修改失败，请稍后重试");
            });
          } else {
            addProduct(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error("新增失败:", error);
              this.$modal.msgError("新增失败，请稍后重试");
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const productIds = row.productId || this.ids;
      this.$modal.confirm('是否确认删除物品信息编号为"' + productIds + '"的数据项？').then(function() {
        return delProduct(productIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        if (error && error !== 'cancel') {
          console.error("删除物品失败:", error);
          this.$modal.msgError("删除物品失败，请稍后重试");
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('api/v1/products/export', {
        ...this.queryParams
      }, `物品信息_${new Date().getTime()}.xlsx`);
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "物品导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      // 使用axios直接下载，确保携带认证信息
      const url = process.env.VUE_APP_BASE_API + '/product/info/importTemplate';
      let loading = null;

      try {
        // 显示加载中
        loading = this.$loading({
          lock: true,
          text: '正在下载模板...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 使用axios发送GET请求下载文件
        axios({
          method: 'get',
          url: url,
          responseType: 'blob',
          headers: {
            'Authorization': 'Bearer ' + getToken()
          },
          timeout: 10000 // 设置10秒超时
        }).then(response => {
          if (response.data.type && response.data.type.includes('application/json')) {
            // 处理服务器返回的错误信息
            const reader = new FileReader();
            reader.onload = () => {
              const error = JSON.parse(reader.result);
              this.$modal.msgError(error.msg || '下载模板失败');
            };
            reader.readAsText(response.data);
          } else {
            // 创建blob链接并下载
            const blob = new Blob([response.data]);
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `物品信息模板_${new Date().getTime()}.xlsx`;
            link.click();
            URL.revokeObjectURL(link.href);
            this.$modal.msgSuccess('模板下载成功');
          }
        }).catch(error => {
          console.error('下载模板失败:', error);
          this.$modal.msgError(error.message === 'Network Error' 
            ? '网络连接失败，请检查网络设置'
            : '下载模板失败，请稍后重试');
        }).finally(() => {
          if (loading) {
            loading.close();
          }
        });
      } catch (error) {
        console.error('下载模板出现异常:', error);
        this.$modal.msgError('下载模板出现异常，请稍后重试');
        if (loading) {
          loading.close();
        }
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
      // 显示上传进度
      const percent = Math.floor(event.percent);
      this.$message({
        message: `正在上传: ${percent}%`,
        type: 'info',
        duration: 1000
      });
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      
      if (response.code === 200) {
        this.$alert(response.msg, "导入结果", { 
          dangerouslyUseHTMLString: true,
          callback: () => {
            this.getList(); // 刷新列表
          }
        });
      } else {
        this.$alert(response.msg || '导入失败', "导入结果", { 
          dangerouslyUseHTMLString: true,
          type: 'error'
        });
      }
    },
    // 提交上传文件
    submitFileForm() {
      if (!this.$refs.upload.uploadFiles || this.$refs.upload.uploadFiles.length === 0) {
        this.$modal.msgError("请先选择要上传的文件");
        return;
      }
      this.$refs.upload.submit();
    },
    // 处理图片上传错误
    handleUploadError(error) {
      console.error("图片上传失败:", error);
      this.$modal.msgError("图片上传失败，请检查文件格式和大小");
    },
    // 处理文件上传错误
    handleFileUploadError(error, file, fileList) {
      this.upload.isUploading = false;
      console.error("文件上传失败:", error, file);
      this.$modal.msgError("文件上传失败，请检查文件格式和网络连接");
    },
    // 获取图片完整URL
    getImageUrl(url) {
      if (!url) return '';
      
      // 已经是完整URL
      if (/^https?:\/\//.test(url)) {
        return url;
      }
      
      // /profile开头的路径，需要通过后端静态资源服务访问
      if (url.startsWith('/profile')) {
        // 开发环境直接访问后端服务，生产环境使用当前域名
        if (process.env.NODE_ENV === 'development') {
          return 'http://localhost:8080' + url;
        } else {
          return window.location.origin + url;
        }
      }
      
      // 其他情况，拼接完整路径
      const baseUrl = process.env.NODE_ENV === 'development' 
        ? 'http://localhost:8080' 
        : window.location.origin;
      return baseUrl + (url.startsWith('/') ? url : '/' + url);
    }
  }
};
</script>