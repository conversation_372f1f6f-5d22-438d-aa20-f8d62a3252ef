package com.wanyu.system.service.impl;

import java.util.*;
import java.security.MessageDigest;
import java.net.NetworkInterface;
import java.net.InetAddress;
import com.alibaba.fastjson2.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.system.mapper.SysLicenseMapper;
import com.wanyu.system.domain.SysLicense;
import com.wanyu.system.service.ISysLicenseService;
import com.wanyu.system.utils.ModernLicenseManager;

/**
 * 授权管理Service业务层处理（简化版）
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@Service
public class SysLicenseServiceImpl implements ISysLicenseService 
{
    private static final Logger log = LoggerFactory.getLogger(SysLicenseServiceImpl.class);
    
    @Autowired
    private SysLicenseMapper sysLicenseMapper;
    
    @Autowired
    private ModernLicenseManager modernLicenseManager;

    private static final int TRIAL_DAYS = 30;
    
    // 内存缓存当前授权信息
    private SysLicense currentLicense = null;
    private Date trialStartDate = null;
    
    // 启动时初始化现代授权管理器
    @PostConstruct
    public void initLicenseManager() {
        try {
            modernLicenseManager.startAutoCheckService();
            log.info("现代化授权管理服务启动成功");
        } catch (Exception e) {
            log.error("启动授权管理服务失败", e);
        }
    }
    
    @PreDestroy
    public void shutdownLicenseManager() {
        try {
            modernLicenseManager.stopAutoCheckService();
            log.info("现代化授权管理服务关闭成功");
        } catch (Exception e) {
            log.error("关闭授权管理服务失败", e);
        }
    }

    /**
     * 查询授权管理
     */
    @Override
    public SysLicense selectSysLicenseByLicenseId(Long licenseId)
    {
        SysLicense license = sysLicenseMapper.selectSysLicenseByLicenseId(licenseId);
        if (license != null) {
            enrichLicenseInfo(license);
        }
        return license;
    }

    /**
     * 查询授权管理列表
     */
    @Override
    public List<SysLicense> selectSysLicenseList(SysLicense sysLicense)
    {
        List<SysLicense> licenses = sysLicenseMapper.selectSysLicenseList(sysLicense);
        for (SysLicense license : licenses) {
            enrichLicenseInfo(license);
        }
        return licenses;
    }

    /**
     * 新增授权管理
     */
    @Override
    public int insertSysLicense(SysLicense sysLicense)
    {
        sysLicense.setCreateTime(DateUtils.getNowDate());
        try {
            sysLicense.setCreateBy(SecurityUtils.getUsername());
        } catch (Exception e) {
            sysLicense.setCreateBy("system");
        }
        return sysLicenseMapper.insertSysLicense(sysLicense);
    }

    /**
     * 修改授权管理
     */
    @Override
    public int updateSysLicense(SysLicense sysLicense)
    {
        sysLicense.setUpdateTime(DateUtils.getNowDate());
        try {
            sysLicense.setUpdateBy(SecurityUtils.getUsername());
        } catch (Exception e) {
            sysLicense.setUpdateBy("system");
        }
        
        int result = sysLicenseMapper.updateSysLicense(sysLicense);
        
        // 清除内存缓存
        currentLicense = null;
        
        return result;
    }

    /**
     * 批量删除授权管理
     */
    @Override
    public int deleteSysLicenseByLicenseIds(Long[] licenseIds)
    {
        // 检查是否包含当前使用的授权
        SysLicense current = getCurrentLicense();
        if (current != null) {
            for (Long licenseId : licenseIds) {
                if (licenseId.equals(current.getLicenseId())) {
                    throw new RuntimeException("不能删除当前正在使用的授权密钥");
                }
            }
        }
        
        return sysLicenseMapper.deleteSysLicenseByLicenseIds(licenseIds);
    }

    /**
     * 删除授权管理信息
     */
    @Override
    public int deleteSysLicenseByLicenseId(Long licenseId)
    {
        return sysLicenseMapper.deleteSysLicenseByLicenseId(licenseId);
    }

    /**
     * 验证授权密钥（使用现代化验证机制）
     */
    @Override
    public boolean verifyLicense(String licenseKey)
    {
        try {
            // 1. 使用现代化授权管理器验证
            ModernLicenseManager.LicenseVerificationResult result = modernLicenseManager.verifyLicense(licenseKey);
            
            if (result.isValid()) {
                // 验证成功，同步更新数据库
                syncLicenseToDatabase(licenseKey, result);
                return true;
            }
            
            // 2. 如果现代验证失败，回退到传统验证（兼容性）
            log.warn("现代验证失败，尝试传统验证: {}", result.getMessage());
            return verifyLicenseTraditional(licenseKey);
            
        } catch (Exception e) {
            log.error("授权验证异常", e);
            // 异常时回退到传统验证
            return verifyLicenseTraditional(licenseKey);
        }
    }
    
    /**
     * 传统授权验证方法（作为回退方案）
     */
    private boolean verifyLicenseTraditional(String licenseKey)
    {
        try {
            // 查询数据库中的授权信息
            SysLicense license = sysLicenseMapper.selectSysLicenseByKey(licenseKey);
            if (license == null) {
                return false;
            }

            // 检查状态 - 修复：0=启用，1=禁用
            if (!"0".equals(license.getStatus())) {
                return false;
            }

            // 检查是否过期
            if (license.getEndDate() != null && license.getEndDate().before(new Date())) {
                return false;
            }

            // 检查硬件指纹（简化版，可选）
            String currentFingerprint = getHardwareFingerprint();
            if (license.getHardwareFingerprint() != null && 
                !license.getHardwareFingerprint().equals(currentFingerprint)) {
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("传统授权验证异常", e);
            return false;
        }
    }

    /**
     * 激活授权密钥（支持现代化和兼容格式）
     */
    @Override
    public boolean activateLicense(String licenseKey)
    {
        return activateLicenseWithCompany(licenseKey, null);
    }
    
    /**
     * 激活授权密钥（支持指定公司名称）
     */
    @Override
    public boolean activateLicenseWithCompany(String licenseKey, String companyName)
    {
        try {
            // 1. 首先尝试兼容格式验证
            Map<String, Object> compatibleResult = com.wanyu.system.utils.LicenseUtils.parseLicenseKey(licenseKey);
            
            if (compatibleResult != null && (Boolean) compatibleResult.get("valid")) {
                log.info("检测到兼容格式密钥，使用兼容激活流程");
                // 对于兼容格式密钥，尝试从系统环境或者默认配置中获取公司名称
                // 这里可以根据实际需求修改成从配置文件或用户输入中获取
                return activateCompatibleLicenseWithCompany(licenseKey, compatibleResult, companyName);
            }
            // 2. 如果兼容格式失败，尝试现代化验证
            ModernLicenseManager.LicenseVerificationResult result = modernLicenseManager.verifyLicense(licenseKey);
            
            if (!result.isValid()) {
                log.warn("授权激活失败: {}", result.getMessage());
                return false;
            }
            
            // 3. 同步授权信息到数据库
            SysLicense license = syncLicenseToDatabase(licenseKey, result);
            
            // 4. 更新硬件指纹
            String currentFingerprint = getHardwareFingerprint();
            if (license.getHardwareFingerprint() == null || 
                !license.getHardwareFingerprint().equals(currentFingerprint)) {
                license.setHardwareFingerprint(currentFingerprint);
                license.setUpdateTime(DateUtils.getNowDate());
                try {
                    license.setUpdateBy(SecurityUtils.getUsername());
                } catch (Exception e) {
                    license.setUpdateBy("system");
                }
                sysLicenseMapper.updateSysLicense(license);
            }

            // 5. 缓存当前授权信息
            currentLicense = license;
            
            // 6. 记录激活日志
            recordLicenseOperation("ACTIVATE", licenseKey, "授权激活成功", true);

            return true;
        } catch (Exception e) {
            log.error("授权激活异常", e);
            recordLicenseOperation("ACTIVATE", licenseKey, "授权激活失败: " + e.getMessage(), false);
            return false;
        }
    }
    
    /**
     * 激活兼容格式的授权密钥（支持指定公司名称）
     */
    private boolean activateCompatibleLicenseWithCompany(String licenseKey, Map<String, Object> licenseInfo, String companyName)
    {
        try {
            // 1. 检查是否已存在该密钥
            SysLicense existingLicense = sysLicenseMapper.selectSysLicenseByKey(licenseKey);
            
            if (existingLicense != null) {
                // 密钥已存在，更新为当前使用
                log.info("密钥已存在，更新为当前使用");
                
                // 将所有授权设为非当前使用
                sysLicenseMapper.updateAllLicensesCurrent(0);
                
                // 设置此密钥为当前使用
                existingLicense.setCurrent(true);
                existingLicense.setStatus("0"); // 修复：0=启用状态
                
                // 如果提供了公司名称，更新公司名称
                if (companyName != null && !companyName.trim().isEmpty()) {
                    log.info("更新公司名称从 '{}' 到 '{}'", existingLicense.getCompanyName(), companyName);
                    existingLicense.setCompanyName(companyName);
                }
                
                existingLicense.setUpdateTime(DateUtils.getNowDate());
                try {
                    existingLicense.setUpdateBy(SecurityUtils.getUsername());
                } catch (Exception e) {
                    existingLicense.setUpdateBy("system");
                }
                
                sysLicenseMapper.updateSysLicense(existingLicense);
                currentLicense = existingLicense;
                
                return true;
            }
            
            // 2. 创建新的授权记录
            SysLicense newLicense = new SysLicense();
            newLicense.setLicenseKey(licenseKey);
            
            // 优先使用传入的公司名称，如果没有则使用默认值
            String effectiveCompanyName = companyName;
            if (effectiveCompanyName == null || effectiveCompanyName.trim().isEmpty()) {
                // 从兼容格式解析中获取公司名称（但兼容格式密钥本身不包含公司名称）
                effectiveCompanyName = (String) licenseInfo.get("companyName");
                if (effectiveCompanyName == null || effectiveCompanyName.trim().isEmpty()) {
                    // 对于兼容格式密钥，公司名称需要通过其他方式提供
                    // 这里使用默认值，实际应用中可以从用户输入或配置中获取
                    effectiveCompanyName = "万裕科技"; // 兼容格式默认公司名称
                }
            }
            newLicense.setCompanyName(effectiveCompanyName);
            newLicense.setLicenseType((String) licenseInfo.get("licenseType"));
            newLicense.setHardwareFingerprint(getHardwareFingerprint());
            newLicense.setMaxUsers(0); // 兼容格式默认无限制
            newLicense.setMaxWarehouses(0); // 兼容格式默认无限制
            
            // 设置时间
            Date startDate = new Date();
            Date endDate = (Date) licenseInfo.get("endDate");
            newLicense.setStartDate(startDate);
            newLicense.setEndDate(endDate);
            
            // 设置状态
            newLicense.setStatus("0"); // 修复：0=启用状态
            newLicense.setCurrent(true);
            
            // 设置创建信息
            newLicense.setCreateTime(DateUtils.getNowDate());
            try {
                newLicense.setCreateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                newLicense.setCreateBy("system");
            }
            
            // 设置功能列表
            @SuppressWarnings("unchecked")
            List<String> features = (List<String>) licenseInfo.get("features");
            if (features != null && !features.isEmpty()) {
                newLicense.setFeatures(String.join(",", features));
            }
            
            // 设置备注
            String licenseType = (String) licenseInfo.get("licenseType");
            newLicense.setRemark("兼容格式授权密钥 - " + licenseType);
            
            // 3. 将所有现有授权设为非当前使用
            sysLicenseMapper.updateAllLicensesCurrent(0);
            
            // 4. 插入新授权记录
            int result = sysLicenseMapper.insertSysLicense(newLicense);
            
            if (result > 0) {
                currentLicense = newLicense;
                log.info("兼容格式授权密钥激活成功: {}", licenseKey);
                return true;
            } else {
                log.error("插入授权记录失败");
                return false;
            }
            
        } catch (Exception e) {
            log.error("兼容格式授权激活异常", e);
            return false;
        }
    }
    
    /**
     * 激活兼容格式的授权密钥（向后兼容）
     */
    private boolean activateCompatibleLicense(String licenseKey, Map<String, Object> licenseInfo)
    {
        return activateCompatibleLicenseWithCompany(licenseKey, licenseInfo, null);
    }

    /**
     * 获取当前授权状态
     */
    @Override
    public Map<String, Object> getCurrentLicenseStatus()
    {
        Map<String, Object> status = new HashMap<>();
        
        SysLicense current = getCurrentLicense();
        if (current != null) {
            status.put("hasLicense", true);
            status.put("licenseType", current.getLicenseType());
            status.put("companyName", current.getCompanyName());
            status.put("maxUsers", current.getMaxUsers());
            status.put("maxWarehouses", current.getMaxWarehouses());
            status.put("startDate", current.getStartDate());
            status.put("endDate", current.getEndDate());
            status.put("features", parseFeatures(current.getFeatures()));
            status.put("currentLicenseId", current.getLicenseId());
            
            // 计算剩余天数
            if (current.getEndDate() != null) {
                long diffTime = current.getEndDate().getTime() - System.currentTimeMillis();
                int remainingDays = (int) (diffTime / (24 * 60 * 60 * 1000));
                status.put("remainingDays", Math.max(0, remainingDays));
                status.put("expired", remainingDays <= 0);
            } else {
                status.put("remainingDays", -1); // 永久授权
                status.put("expired", false);
            }
        } else {
            // 检查试用期
            status.put("hasLicense", false);
            status.put("isTrial", true);
            status.put("licenseType", "trial");
            
            int trialRemaining = getTrialRemainingDays();
            status.put("remainingDays", trialRemaining);
            status.put("expired", trialRemaining <= 0);
            
            if (trialRemaining <= 0) {
                status.put("message", "试用期已结束，请联系管理员获取正式授权");
            } else {
                status.put("message", "试用期剩余 " + trialRemaining + " 天");
            }
        }
        
        return status;
    }

    /**
     * 更新授权状态
     */
    @Override
    public int updateLicenseStatus(Long licenseId, String status)
    {
        SysLicense license = new SysLicense();
        license.setLicenseId(licenseId);
        license.setStatus(status);
        license.setUpdateTime(DateUtils.getNowDate());
        try {
            license.setUpdateBy(SecurityUtils.getUsername());
        } catch (Exception e) {
            license.setUpdateBy("system");
        }
        
        int result = sysLicenseMapper.updateSysLicense(license);
        
        // 清除缓存
        currentLicense = null;
        
        return result;
    }

    /**
     * 更新授权信息（安全版本）
     * 只允许修改非关键字段，关键字段必须从授权密钥重新解析
     */
    @Override
    public int updateLicenseInfo(SysLicense sysLicense)
    {
        // 验证输入参数
        if (sysLicense.getLicenseId() == null) {
            throw new RuntimeException("授权ID不能为空");
        }
        
        // 获取原始授权记录
        SysLicense originalLicense = sysLicenseMapper.selectSysLicenseByLicenseId(sysLicense.getLicenseId());
        if (originalLicense == null) {
            throw new RuntimeException("授权记录不存在");
        }
        
        // 重新解析授权密钥，确保关键信息的正确性
        Map<String, Object> licenseInfo = com.wanyu.system.utils.LicenseUtils.parseLicenseKey(originalLicense.getLicenseKey());
        if (licenseInfo == null || !(Boolean) licenseInfo.get("valid")) {
            throw new RuntimeException("授权密钥解析失败，无法更新信息");
        }
        
        // 创建更新对象，只包含允许修改的字段
        SysLicense updateLicense = new SysLicense();
        updateLicense.setLicenseId(sysLicense.getLicenseId());
        
        // === 允许修改的字段（非关键字段） ===
        // 公司名称（仅在兼容格式时允许修改）
        if (sysLicense.getCompanyName() != null && !sysLicense.getCompanyName().trim().isEmpty()) {
            updateLicense.setCompanyName(sysLicense.getCompanyName().trim());
        }
        
        // 联系信息和备注（可以修改）
        if (sysLicense.getContactInfo() != null) {
            updateLicense.setContactInfo(sysLicense.getContactInfo());
        }
        if (sysLicense.getRemark() != null) {
            updateLicense.setRemark(sysLicense.getRemark());
        }
        
        // === 从授权密钥重新解析的关键字段（确保数据一致性） ===
        // 授权类型
        String licenseType = (String) licenseInfo.get("licenseType");
        if (licenseType != null) {
            updateLicense.setLicenseType(licenseType);
        }
        
        // 最大用户数和仓库数
        Integer maxUsers = (Integer) licenseInfo.get("maxUsers");
        Integer maxWarehouses = (Integer) licenseInfo.get("maxWarehouses");
        if (maxUsers != null) {
            updateLicense.setMaxUsers(maxUsers);
        }
        if (maxWarehouses != null) {
            updateLicense.setMaxWarehouses(maxWarehouses);
        }
        
        // 功能列表
        @SuppressWarnings("unchecked")
        List<String> features = (List<String>) licenseInfo.get("features");
        if (features != null && !features.isEmpty()) {
            updateLicense.setFeatures(String.join(",", features));
        }
        
        // 到期时间
        String endDateStr = (String) licenseInfo.get("endDate");
        if (endDateStr != null && !"99999999".equals(endDateStr)) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
                Date endDate = sdf.parse(endDateStr);
                updateLicense.setEndDate(endDate);
            } catch (Exception e) {
                log.warn("解析到期时间失败: {}", endDateStr, e);
            }
        }
        
        // 设置更新信息
        updateLicense.setUpdateTime(DateUtils.getNowDate());
        try {
            updateLicense.setUpdateBy(SecurityUtils.getUsername());
        } catch (Exception e) {
            updateLicense.setUpdateBy("system");
        }
        
        // 执行更新
        int result = sysLicenseMapper.updateSysLicense(updateLicense);
        
        // 清除缓存
        currentLicense = null;
        
        // 记录安全日志
        log.info("授权信息更新: 用户={}, 授权ID={}, 更新字段=[公司名称,联系信息,备注], 关键字段已从密钥重新解析", 
            updateLicense.getUpdateBy(), sysLicense.getLicenseId());
        
        return result;
    }
    
    /**
     * 重新验证和同步授权密钥信息
     * 确保数据库中的授权信息与密钥内容一致
     */
    @Override
    public boolean revalidateAndSyncLicense(Long licenseId)
    {
        try {
            // 获取授权记录
            SysLicense license = sysLicenseMapper.selectSysLicenseByLicenseId(licenseId);
            if (license == null) {
                log.error("授权记录不存在: {}", licenseId);
                return false;
            }
            
            // 重新解析授权密钥
            Map<String, Object> licenseInfo = com.wanyu.system.utils.LicenseUtils.parseLicenseKey(license.getLicenseKey());
            if (licenseInfo == null || !(Boolean) licenseInfo.get("valid")) {
                log.error("授权密钥解析失败: {}", license.getLicenseKey());
                return false;
            }
            
            // 创建同步更新对象
            SysLicense syncLicense = new SysLicense();
            syncLicense.setLicenseId(licenseId);
            
            // 同步关键字段
            String licenseType = (String) licenseInfo.get("licenseType");
            if (licenseType != null) {
                syncLicense.setLicenseType(licenseType);
            }
            
            Integer maxUsers = (Integer) licenseInfo.get("maxUsers");
            Integer maxWarehouses = (Integer) licenseInfo.get("maxWarehouses");
            if (maxUsers != null) {
                syncLicense.setMaxUsers(maxUsers);
            }
            if (maxWarehouses != null) {
                syncLicense.setMaxWarehouses(maxWarehouses);
            }
            
            @SuppressWarnings("unchecked")
            List<String> features = (List<String>) licenseInfo.get("features");
            if (features != null && !features.isEmpty()) {
                syncLicense.setFeatures(String.join(",", features));
            }
            
            String endDateStr = (String) licenseInfo.get("endDate");
            if (endDateStr != null && !"99999999".equals(endDateStr)) {
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
                    Date endDate = sdf.parse(endDateStr);
                    syncLicense.setEndDate(endDate);
                } catch (Exception e) {
                    log.warn("解析到期时间失败: {}", endDateStr, e);
                }
            }
            
            // 设置更新信息
            syncLicense.setUpdateTime(DateUtils.getNowDate());
            syncLicense.setUpdateBy("system_sync");
            syncLicense.setRemark(license.getRemark() + " [系统同步: " + DateUtils.getNowDate() + "]");
            
            // 执行同步
            int result = sysLicenseMapper.updateSysLicense(syncLicense);
            
            if (result > 0) {
                log.info("授权信息同步成功: 授权ID={}, 密钥={}", licenseId, license.getLicenseKey());
                
                // 清除缓存
                currentLicense = null;
                
                return true;
            } else {
                log.error("授权信息同步失败: 授权ID={}", licenseId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("重新验证和同步授权失败: 授权ID={}", licenseId, e);
            return false;
        }
    }

    /**
     * 重新生成授权密钥
     */
    @Override
    public SysLicense regenerateLicense(SysLicense sysLicense)
    {
        // 简化版：暂不支持重新生成，返回错误
        throw new RuntimeException("重新生成密钥功能暂未实现，请使用外部工具生成新密钥");
    }

    /**
     * 检查功能权限
     */
    @Override
    public boolean hasFeaturePermission(String featureCode)
    {
        SysLicense current = getCurrentLicense();
        if (current == null) {
            // 试用版默认功能
            List<String> trialFeatures = Arrays.asList(
                "user_management", "inventory_management", 
                "product_management", "barcode_qrcode"
            );
            return trialFeatures.contains(featureCode) && getTrialRemainingDays() > 0;
        }
        
        List<String> features = parseFeatures(current.getFeatures());
        return features.contains(featureCode);
    }

    /**
     * 获取授权功能列表
     */
    @Override
    public List<Map<String, Object>> getLicenseFeatures()
    {
        List<Map<String, Object>> featureList = new ArrayList<>();
        
        // 所有可用功能
        Map<String, String> allFeatures = new LinkedHashMap<>();
        allFeatures.put("user_management", "用户管理");
        allFeatures.put("warehouse_management", "仓库管理");
        allFeatures.put("inventory_management", "库存管理");
        allFeatures.put("product_management", "物品管理");
        allFeatures.put("report_export", "报表导出");
        allFeatures.put("data_backup", "数据备份");
        allFeatures.put("api_access", "API接口");
        allFeatures.put("advanced_analytics", "高级分析");
        allFeatures.put("multi_warehouse", "多仓库支持");
        allFeatures.put("barcode_qrcode", "条码二维码");
        
        SysLicense current = getCurrentLicense();
        List<String> enabledFeatures;
        
        if (current == null) {
            // 试用版功能
            enabledFeatures = Arrays.asList(
                "user_management", "inventory_management", 
                "product_management", "barcode_qrcode"
            );
        } else {
            enabledFeatures = parseFeatures(current.getFeatures());
        }
        
        for (Map.Entry<String, String> entry : allFeatures.entrySet()) {
            Map<String, Object> feature = new HashMap<>();
            feature.put("code", entry.getKey());
            feature.put("name", entry.getValue());
            feature.put("enabled", enabledFeatures.contains(entry.getKey()));
            featureList.add(feature);
        }
        
        return featureList;
    }

    /**
     * 获取试用期剩余天数
     */
    @Override
    public int getTrialRemainingDays()
    {
        // 简化版：使用内存存储试用开始时间
        if (trialStartDate == null) {
            // 首次启动，设置试用开始时间
            trialStartDate = new Date();
        }
        
        // 计算剩余天数
        long diffTime = System.currentTimeMillis() - trialStartDate.getTime();
        int usedDays = (int) (diffTime / (24 * 60 * 60 * 1000));
        
        return Math.max(0, TRIAL_DAYS - usedDays);
    }

    /**
     * 检查是否为试用版
     */
    @Override
    public boolean isTrialVersion()
    {
        SysLicense current = getCurrentLicense();
        return current == null || "trial".equals(current.getLicenseType());
    }

    /**
     * 检查授权是否过期
     */
    @Override
    public boolean isLicenseExpired()
    {
        SysLicense current = getCurrentLicense();
        if (current == null) {
            return getTrialRemainingDays() <= 0;
        }
        
        if (current.getEndDate() == null) {
            return false; // 永久授权
        }
        
        return current.getEndDate().before(new Date());
    }

    /**
     * 获取硬件指纹
     */
    @Override
    public String getHardwareFingerprint()
    {
        try {
            StringBuilder fingerprint = new StringBuilder();
            
            // 获取MAC地址
            try {
                InetAddress ip = InetAddress.getLocalHost();
                NetworkInterface network = NetworkInterface.getByInetAddress(ip);
                if (network != null) {
                    byte[] mac = network.getHardwareAddress();
                    if (mac != null) {
                        for (byte b : mac) {
                            fingerprint.append(String.format("%02X", b));
                        }
                    }
                }
            } catch (Exception e) {
                // 忽略网络接口异常
            }
            
            // 添加系统信息
            fingerprint.append(System.getProperty("os.name", "unknown"));
            fingerprint.append(System.getProperty("os.arch", "unknown"));
            fingerprint.append(System.getProperty("user.name", "unknown"));
            
            // 生成MD5哈希
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(fingerprint.toString().getBytes());
            
            StringBuilder result = new StringBuilder();
            for (byte b : hash) {
                result.append(String.format("%02x", b));
            }
            
            return result.toString().toUpperCase();
        } catch (Exception e) {
            return "UNKNOWN_FINGERPRINT";
        }
    }

    /**
     * 初始化试用授权
     */
    @Override
    public boolean initTrialLicense()
    {
        try {
            // 设置试用开始时间
            trialStartDate = new Date();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前授权
     */
    private SysLicense getCurrentLicense()
    {
        // 先从内存缓存获取
        if (currentLicense != null) {
            log.debug("从缓存获取当前授权: {}", currentLicense.getLicenseKey());
            return currentLicense;
        }
        
        // 从数据库查询有效授权
        try {
            log.debug("从数据库查询当前激活的授权...");
            List<SysLicense> licenses = sysLicenseMapper.selectActiveLicenses();
            log.debug("查询到 {} 条激活授权记录", licenses != null ? licenses.size() : 0);
            
            if (!licenses.isEmpty()) {
                currentLicense = licenses.get(0);
                log.info("加载当前授权: key={}, company={}, features={}", 
                    currentLicense.getLicenseKey(), 
                    currentLicense.getCompanyName(),
                    currentLicense.getFeatures());
                return currentLicense;
            }
        } catch (Exception e) {
            log.error("数据库查询异常", e);
        }
        
        log.warn("未找到当前激活的授权");
        return null;
    }

    /**
     * 解析功能列表
     */
    private List<String> parseFeatures(String features)
    {
        log.debug("解析功能列表: {}", features);
        
        if (features == null || features.trim().isEmpty()) {
            log.debug("功能列表为空，返回空数组");
            return new ArrayList<>();
        }
        
        try {
            // 首先尝试逗号分隔解析（兼容格式）
            if (features.contains(",") && !features.trim().startsWith("[")) {
                List<String> result = Arrays.asList(features.split(","))
                    .stream()
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(java.util.stream.Collectors.toList());
                log.debug("逗号分隔解析成功: {}", result);
                return result;
            }
            
            // 尝试JSON数组解析（现代化格式）
            List<String> result = JSONArray.parseArray(features, String.class);
            log.debug("JSON解析成功: {}", result);
            return result;
        } catch (Exception e) {
            log.warn("JSON解析失败，尝试逗号分隔解析: {}", e.getMessage());
            // 如果JSON解析失败，回退到逗号分隔解析
            try {
                List<String> result = Arrays.asList(features.split(","))
                    .stream()
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(java.util.stream.Collectors.toList());
                log.debug("回退逗号分隔解析成功: {}", result);
                return result;
            } catch (Exception ex) {
                log.warn("功能列表解析失败: {}", features, ex);
                return new ArrayList<>();
            }
        }
    }

    /**
     * 丰富授权信息
     */
    private void enrichLicenseInfo(SysLicense license)
    {
        if (license == null) return;
        
        // 解析功能列表
        license.setFeatureList(parseFeatures(license.getFeatures()));
        
        // 计算剩余天数
        if (license.getEndDate() != null) {
            long diffTime = license.getEndDate().getTime() - System.currentTimeMillis();
            int remainingDays = (int) (diffTime / (24 * 60 * 60 * 1000));
            license.setRemainingDays(Math.max(0, remainingDays));
            license.setExpired(remainingDays <= 0);
        } else {
            license.setRemainingDays(-1); // 永久授权
            license.setExpired(false);
        }
        
        // 检查是否为当前使用的授权
        SysLicense current = getCurrentLicense();
        license.setCurrent(current != null && 
            license.getLicenseId().equals(current.getLicenseId()));
    }
    
    /**
     * 远程撤销授权
     * 
     * @param licenseKey 授权密钥
     * @param reason 撤销原因
     * @return 撤销结果
     */
    @Override
    public boolean revokeLicense(String licenseKey, String reason)
    {
        try {
            // 1. 使用现代化授权管理器远程撤销
            boolean remoteResult = modernLicenseManager.revokeLicense(licenseKey, reason);
            
            // 2. 本地数据库撤销
            SysLicense license = sysLicenseMapper.selectSysLicenseByKey(licenseKey);
            if (license != null) {
                license.setStatus("0"); // 禁用状态
                license.setRemark("撤销原因: " + reason);
                license.setUpdateTime(DateUtils.getNowDate());
                try {
                    license.setUpdateBy(SecurityUtils.getUsername());
                } catch (Exception e) {
                    license.setUpdateBy("system");
                }
                sysLicenseMapper.updateSysLicense(license);
            }
            
            // 3. 清除本地缓存
            if (currentLicense != null && licenseKey.equals(currentLicense.getLicenseKey())) {
                currentLicense = null;
            }
            
            // 4. 记录操作日志
            recordLicenseOperation("REVOKE", licenseKey, "授权撤销: " + reason, true);
            
            return remoteResult;
            
        } catch (Exception e) {
            recordLicenseOperation("REVOKE", licenseKey, "授权撤销失败: " + e.getMessage(), false);
            return false;
        }
    }
    
    /**
     * 获取授权详细验证结果
     * 
     * @param licenseKey 授权密钥
     * @return 详细验证结果
     */
    @Override
    public Object getLicenseVerificationDetails(String licenseKey)
    {
        return modernLicenseManager.verifyLicense(licenseKey);
    }
    
    /**
     * 同步授权信息到数据库
     */
    private SysLicense syncLicenseToDatabase(String licenseKey, ModernLicenseManager.LicenseVerificationResult result)
    {
        try {
            SysLicense license = sysLicenseMapper.selectSysLicenseByKey(licenseKey);
            
            if (license == null) {
                // 创建新的授权记录
                license = new SysLicense();
                license.setLicenseKey(licenseKey);
                license.setCreateTime(DateUtils.getNowDate());
                try {
                    license.setCreateBy(SecurityUtils.getUsername());
                } catch (Exception e) {
                    license.setCreateBy("system");
                }
            }
            
            // 更新授权信息
            Map<String, Object> metadata = result.getMetadata();
            if (metadata != null) {
                license.setLicenseType((String) metadata.get("licenseType"));
                license.setCompanyName((String) metadata.get("companyName"));
                
                Object maxUsers = metadata.get("maxUsers");
                if (maxUsers instanceof Integer) {
                    license.setMaxUsers((Integer) maxUsers);
                }
                
                Object maxWarehouses = metadata.get("maxWarehouses");
                if (maxWarehouses instanceof Integer) {
                    license.setMaxWarehouses((Integer) maxWarehouses);
                }
                
                Object expireTime = metadata.get("expireTime");
                if (expireTime != null) {
                    // 转换LocalDateTime到Date
                    if (expireTime instanceof java.time.LocalDateTime) {
                        java.time.LocalDateTime ldt = (java.time.LocalDateTime) expireTime;
                        license.setEndDate(Date.from(ldt.atZone(java.time.ZoneId.systemDefault()).toInstant()));
                    }
                }
            }
            
            license.setStatus(result.isValid() ? "0" : "1"); // 修复：有效时设为0(启用)，无效时设为1(禁用)
            license.setUpdateTime(DateUtils.getNowDate());
            try {
                license.setUpdateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                license.setUpdateBy("system");
            }
            
            // 保存或更新
            if (license.getLicenseId() == null) {
                sysLicenseMapper.insertSysLicense(license);
            } else {
                sysLicenseMapper.updateSysLicense(license);
            }
            
            return license;
            
        } catch (Exception e) {
            throw new RuntimeException("同步授权信息失败", e);
        }
    }
    
    /**
     * 记录授权操作日志
     */
    private void recordLicenseOperation(String operation, String licenseKey, String message, boolean success)
    {
        try {
            // 这里可以集成到系统的操作日志中
            String logMessage = String.format("授权操作: %s, 密钥: %s, 结果: %s, 消息: %s", 
                operation, 
                licenseKey != null ? licenseKey.substring(0, Math.min(16, licenseKey.length())) + "..." : "null",
                success ? "成功" : "失败", 
                message);
            
            // 使用日志记录（这里需要添加适当的日志记录逻辑）
            System.out.println(logMessage); // 临时使用System.out，应该使用专业的日志框架
            
        } catch (Exception e) {
            // 记录日志失败不应该影响主流程
        }
    }
}