-- 检查并删除其他表中的post相关字段
USE warehouse_system;

-- 检查所有表中是否有post相关字段
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND COLUMN_NAME LIKE '%post%'
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 检查用户表结构
DESCRIBE sys_user;

-- 如果发现有相关字段，请根据实际情况执行以下删除语句：

-- 示例：删除用户表中的岗位相关字段
-- ALTER TABLE sys_user DROP COLUMN IF EXISTS post_id;
-- ALTER TABLE sys_user DROP COLUMN IF EXISTS post_ids;

-- 检查是否有岗位相关的外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND (REFERENCED_TABLE_NAME LIKE '%post%' OR TABLE_NAME LIKE '%post%');

-- 检查岗位相关的菜单和权限
SELECT menu_id, menu_name, perms FROM sys_menu WHERE menu_name LIKE '%岗位%' OR perms LIKE '%post%';