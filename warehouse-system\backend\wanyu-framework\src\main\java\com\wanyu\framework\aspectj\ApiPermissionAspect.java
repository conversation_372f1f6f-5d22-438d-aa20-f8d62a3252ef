package com.wanyu.framework.aspectj;

import java.lang.reflect.Method;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.wanyu.common.annotation.ApiPermission;
import com.wanyu.common.constant.Constants;
import com.wanyu.common.core.domain.model.LoginUser;
import com.wanyu.common.exception.ServiceException;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.ServletUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.system.service.ISysPermissionLogService;

/**
 * API权限控制处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class ApiPermissionAspect
{
    @Autowired
    private ISysPermissionLogService permissionLogService;

    /**
     * 全部权限
     */
    private static final String ALL_PERMISSION = "*:*:*";

    /**
     * 权限验证模式：AND
     */
    private static final String MODE_AND = "AND";

    /**
     * 权限验证模式：OR
     */
    private static final String MODE_OR = "OR";

    /**
     * API权限验证
     */
    @Before("@annotation(apiPermission)")
    public void doApiPermission(JoinPoint point, ApiPermission apiPermission)
    {
        // 如果不需要验证，直接返回
        if (!apiPermission.required())
        {
            return;
        }

        // 获取权限字符串
        String permission = apiPermission.value();
        if (StringUtils.isEmpty(permission))
        {
            // 如果没有指定权限字符串，则从方法名推断
            permission = getPermissionFromMethod(point);
        }

        // 获取验证模式
        String mode = apiPermission.mode();

        // 验证权限
        checkApiPermission(permission, mode);
    }

    /**
     * 从方法名推断权限字符串
     *
     * @param point 切点
     * @return 权限字符串
     */
    private String getPermissionFromMethod(JoinPoint point)
    {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        String methodName = method.getName();
        String className = point.getTarget().getClass().getSimpleName();

        // 去掉类名中的Controller后缀
        if (className.endsWith("Controller"))
        {
            className = className.substring(0, className.length() - 10);
        }

        // 将类名转换为小写，并用冒号分隔
        StringBuilder permissionBuilder = new StringBuilder();
        permissionBuilder.append(toLowerCaseWithSeparator(className, ":"));

        // 根据方法名推断操作类型
        if (methodName.startsWith("list") || methodName.startsWith("select") || methodName.startsWith("get") || methodName.startsWith("query"))
        {
            permissionBuilder.append(":list");
        }
        else if (methodName.startsWith("add") || methodName.startsWith("insert") || methodName.startsWith("save"))
        {
            permissionBuilder.append(":add");
        }
        else if (methodName.startsWith("update") || methodName.startsWith("edit") || methodName.startsWith("modify"))
        {
            permissionBuilder.append(":edit");
        }
        else if (methodName.startsWith("delete") || methodName.startsWith("remove"))
        {
            permissionBuilder.append(":remove");
        }
        else if (methodName.startsWith("export"))
        {
            permissionBuilder.append(":export");
        }
        else if (methodName.startsWith("import"))
        {
            permissionBuilder.append(":import");
        }
        else
        {
            permissionBuilder.append(":").append(methodName);
        }

        return permissionBuilder.toString();
    }

    /**
     * 将驼峰命名转换为小写，并用指定的分隔符分隔
     *
     * @param str 字符串
     * @param separator 分隔符
     * @return 转换后的字符串
     */
    private String toLowerCaseWithSeparator(String str, String separator)
    {
        if (StringUtils.isEmpty(str))
        {
            return str;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++)
        {
            char c = str.charAt(i);
            if (Character.isUpperCase(c))
            {
                if (i > 0)
                {
                    sb.append(separator);
                }
                sb.append(Character.toLowerCase(c));
            }
            else
            {
                sb.append(c);
            }
        }

        return sb.toString();
    }

    /**
     * 验证API权限
     *
     * @param permission 权限字符串
     * @param mode 验证模式
     */
    private void checkApiPermission(String permission, String mode)
    {
        if (StringUtils.isEmpty(permission))
        {
            return;
        }

        // 获取当前用户的权限列表
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null)
        {
            // 记录权限验证失败日志
            recordPermissionLog(permission, "3", "用户未登录", "1", "用户未登录");
            throw new ServiceException("用户未登录", 401);
        }

        Set<String> permissions = loginUser.getPermissions();
        if (permissions == null || permissions.isEmpty())
        {
            // 记录权限验证失败日志
            recordPermissionLog(permission, "3", "用户没有任何权限", "1", "用户没有任何权限");
            throw new ServiceException("用户没有任何权限", 403);
        }

        // 如果用户具有所有权限，则直接返回
        if (permissions.contains(ALL_PERMISSION))
        {
            // 记录权限验证成功日志
            recordPermissionLog(permission, "3", "用户具有所有权限", "0", null);
            return;
        }

        // 权限验证
        if (MODE_AND.equals(mode))
        {
            // AND模式：所有权限都要有
            String[] permissionArray = permission.split(Constants.PERMISSION_DELIMETER);
            for (String perm : permissionArray)
            {
                if (!permissions.contains(perm.trim()))
                {
                    // 记录权限验证失败日志
                    recordPermissionLog(permission, "3", "AND模式验证失败", "1", "没有权限访问：" + perm);
                    throw new ServiceException("没有权限访问：" + perm, 403);
                }
            }

            // 记录权限验证成功日志
            recordPermissionLog(permission, "3", "AND模式验证成功", "0", null);
        }
        else if (MODE_OR.equals(mode))
        {
            // OR模式：有一个权限即可
            String[] permissionArray = permission.split(Constants.PERMISSION_DELIMETER);
            boolean hasPermission = false;
            for (String perm : permissionArray)
            {
                if (permissions.contains(perm.trim()))
                {
                    hasPermission = true;
                    break;
                }
            }

            if (!hasPermission)
            {
                // 记录权限验证失败日志
                recordPermissionLog(permission, "3", "OR模式验证失败", "1", "没有权限访问：" + permission);
                throw new ServiceException("没有权限访问：" + permission, 403);
            }

            // 记录权限验证成功日志
            recordPermissionLog(permission, "3", "OR模式验证成功", "0", null);
        }
        else
        {
            // 默认模式：单个权限验证
            if (!permissions.contains(permission))
            {
                // 记录权限验证失败日志
                recordPermissionLog(permission, "3", "默认模式验证失败", "1", "没有权限访问：" + permission);
                throw new ServiceException("没有权限访问：" + permission, 403);
            }

            // 记录权限验证成功日志
            recordPermissionLog(permission, "3", "默认模式验证成功", "0", null);
        }
    }

    /**
     * 记录权限验证日志
     *
     * @param permission 权限标识
     * @param permissionType 权限类型
     * @param method 请求方法
     * @param result 验证结果
     * @param failReason 失败原因
     */
    private void recordPermissionLog(String permission, String permissionType, String method, String result, String failReason)
    {
        try
        {
            // 获取请求信息
            HttpServletRequest request = ServletUtils.getRequest();
            String url = request != null ? request.getRequestURI() : "";

            // 记录权限日志
            permissionLogService.recordPermissionLog(permissionType, permission, method, url, result, failReason);
        }
        catch (Exception e)
        {
            // 记录日志出错，不影响业务
        }
    }
}
