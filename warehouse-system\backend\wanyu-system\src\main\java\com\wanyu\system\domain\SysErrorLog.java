package com.wanyu.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 错误日志对象 sys_error_log
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public class SysErrorLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 错误编号 */
    private Long errorId;

    /** 错误标题 */
    @Excel(name = "错误标题")
    private String errorTitle;

    /** 错误类型 */
    @Excel(name = "错误类型", readConverterExp = "SYSTEM=系统错误,BUSINESS=业务错误,DATABASE=数据库错误,NETWORK=网络错误,VALIDATION=验证错误")
    private String errorType;

    /** 错误级别 */
    @Excel(name = "错误级别", readConverterExp = "INFO=信息,WARN=警告,ERROR=错误,FATAL=致命")
    private String errorLevel;

    /** 错误消息 */
    @Excel(name = "错误消息")
    private String errorMessage;

    /** 错误堆栈 */
    private String errorStack;

    /** 请求URI */
    @Excel(name = "请求URI")
    private String requestUri;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String requestMethod;

    /** 请求参数 */
    private String requestParams;

    /** 用户代理 */
    @Excel(name = "用户代理")
    private String userAgent;

    /** 客户端IP */
    @Excel(name = "客户端IP")
    private String clientIp;

    /** 操作用户 */
    @Excel(name = "操作用户")
    private String operUser;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /** 处理状态 */
    @Excel(name = "处理状态", readConverterExp = "0=未处理,1=已处理,2=已忽略")
    private String status;

    /** 处理人 */
    @Excel(name = "处理人")
    private String handleBy;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 处理备注 */
    @Excel(name = "处理备注")
    private String handleRemark;

    public void setErrorId(Long errorId) 
    {
        this.errorId = errorId;
    }

    public Long getErrorId() 
    {
        return errorId;
    }
    public void setErrorTitle(String errorTitle) 
    {
        this.errorTitle = errorTitle;
    }

    public String getErrorTitle() 
    {
        return errorTitle;
    }
    public void setErrorType(String errorType) 
    {
        this.errorType = errorType;
    }

    public String getErrorType() 
    {
        return errorType;
    }
    public void setErrorLevel(String errorLevel) 
    {
        this.errorLevel = errorLevel;
    }

    public String getErrorLevel() 
    {
        return errorLevel;
    }
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }
    public void setErrorStack(String errorStack) 
    {
        this.errorStack = errorStack;
    }

    public String getErrorStack() 
    {
        return errorStack;
    }
    public void setRequestUri(String requestUri) 
    {
        this.requestUri = requestUri;
    }

    public String getRequestUri() 
    {
        return requestUri;
    }
    public void setRequestMethod(String requestMethod) 
    {
        this.requestMethod = requestMethod;
    }

    public String getRequestMethod() 
    {
        return requestMethod;
    }
    public void setRequestParams(String requestParams) 
    {
        this.requestParams = requestParams;
    }

    public String getRequestParams() 
    {
        return requestParams;
    }
    public void setUserAgent(String userAgent) 
    {
        this.userAgent = userAgent;
    }

    public String getUserAgent() 
    {
        return userAgent;
    }
    public void setClientIp(String clientIp) 
    {
        this.clientIp = clientIp;
    }

    public String getClientIp() 
    {
        return clientIp;
    }
    public void setOperUser(String operUser) 
    {
        this.operUser = operUser;
    }

    public String getOperUser() 
    {
        return operUser;
    }
    public void setOperTime(Date operTime) 
    {
        this.operTime = operTime;
    }

    public Date getOperTime() 
    {
        return operTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setHandleBy(String handleBy) 
    {
        this.handleBy = handleBy;
    }

    public String getHandleBy() 
    {
        return handleBy;
    }
    public void setHandleTime(Date handleTime) 
    {
        this.handleTime = handleTime;
    }

    public Date getHandleTime() 
    {
        return handleTime;
    }
    public void setHandleRemark(String handleRemark) 
    {
        this.handleRemark = handleRemark;
    }

    public String getHandleRemark() 
    {
        return handleRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("errorId", getErrorId())
            .append("errorTitle", getErrorTitle())
            .append("errorType", getErrorType())
            .append("errorLevel", getErrorLevel())
            .append("errorMessage", getErrorMessage())
            .append("errorStack", getErrorStack())
            .append("requestUri", getRequestUri())
            .append("requestMethod", getRequestMethod())
            .append("requestParams", getRequestParams())
            .append("userAgent", getUserAgent())
            .append("clientIp", getClientIp())
            .append("operUser", getOperUser())
            .append("operTime", getOperTime())
            .append("status", getStatus())
            .append("handleBy", getHandleBy())
            .append("handleTime", getHandleTime())
            .append("handleRemark", getHandleRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
