-- 完整删除库区和货架相关的数据库表和字段
-- 执行前请先备份数据库

USE warehouse_system;

-- 1. 删除库区表 (wms_warehouse_area)
DROP TABLE IF EXISTS `wms_warehouse_area`;

-- 2. 删除货架表 (wms_warehouse_rack) 
DROP TABLE IF EXISTS `wms_warehouse_rack`;

-- 3. 删除系统库区表 (sys_warehouse_area)
DROP TABLE IF EXISTS `sys_warehouse_area`;

-- 4. 删除系统货架表 (sys_warehouse_rack)
DROP TABLE IF EXISTS `sys_warehouse_rack`;

-- 5. 检查其他表中是否有area_id或rack_id字段的引用，如果有则删除
-- 检查库存表是否有area_id或rack_id字段
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND (COLUMN_NAME LIKE '%area_id%' OR COLUMN_NAME LIKE '%rack_id%');

-- 如果产品信息表有area_id或rack_id字段，删除它们
-- ALTER TABLE product_info DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE product_info DROP COLUMN IF EXISTS rack_id;

-- 如果库存表有area_id或rack_id字段，删除它们  
-- ALTER TABLE inventory_info DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE inventory_info DROP COLUMN IF EXISTS rack_id;

-- 如果入库表有area_id或rack_id字段，删除它们
-- ALTER TABLE inventory_in DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE inventory_in DROP COLUMN IF EXISTS rack_id;

-- 如果出库表有area_id或rack_id字段，删除它们
-- ALTER TABLE inventory_out DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE inventory_out DROP COLUMN IF EXISTS rack_id;

-- 删除相关菜单项
DELETE FROM sys_menu WHERE menu_name = '仓库区域' OR menu_name = '库位管理' OR menu_name = '货架管理';
DELETE FROM sys_menu WHERE perms LIKE '%area%' OR perms LIKE '%rack%' OR perms LIKE '%location%';

-- 删除相关权限
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE menu_name IN ('仓库区域', '库位管理', '货架管理')
);

COMMIT;