-- 插入条码类型字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('物品条码类型', 'wms_barcode_type', '0', 'admin', NOW(), '物品条码类型列表');

-- 插入条码类型字典数据项
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, 'CODE128', 'CODE128', 'wms_barcode_type', '', 'primary', 'Y', '0', 'admin', NOW(), 'CODE128条码'),
(2, 'EAN13', 'EAN13', 'wms_barcode_type', '', 'success', 'N', '0', 'admin', NOW(), 'EAN13条码'),
(3, 'EAN8', 'EAN8', 'wms_barcode_type', '', 'info', 'N', '0', 'admin', NOW(), 'EAN8条码'),
(4, 'UPC_A', 'UPC_A', 'wms_barcode_type', '', 'warning', 'N', '0', 'admin', NOW(), 'UPC-A条码'),
(5, 'QR码', 'QR_CODE', 'wms_barcode_type', '', 'danger', 'N', '0', 'admin', NOW(), 'QR二维码');

-- 插入默认条码模板
INSERT INTO `wms_barcode_template` (`template_name`, `template_type`, `template_width`, `template_height`, `barcode_width`, `barcode_height`, `font_size`, `show_text`, `show_product_name`, `status`, `create_by`, `create_time`) VALUES
('标准模板', 'CODE128', 100, 50, 80, 30, 12, '1', '1', '0', 'admin', NOW()),
('小型模板', 'CODE128', 60, 30, 50, 20, 10, '1', '0', '0', 'admin', NOW()),
('二维码模板', 'QR_CODE', 50, 50, 40, 40, 10, '1', '1', '0', 'admin', NOW());