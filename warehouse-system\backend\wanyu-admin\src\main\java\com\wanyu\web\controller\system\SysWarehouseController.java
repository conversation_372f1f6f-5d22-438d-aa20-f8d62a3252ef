package com.wanyu.web.controller.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.warehouse.domain.WarehouseInfo;
import com.wanyu.warehouse.service.IWarehouseInfoService;
import com.wanyu.system.service.ISysUserWarehouseService;
import org.springframework.jdbc.core.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 系统仓库信息操作处理
 */
@RestController
@RequestMapping("/system/warehouse")
public class SysWarehouseController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(SysWarehouseController.class);
    
    @Autowired
    private IWarehouseInfoService warehouseInfoService;
    
    @Autowired
    private ISysUserWarehouseService userWarehouseService;
    
    @Autowired(required = false)
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询仓库信息列表
     */
    @PreAuthorize("@ss.hasPermi('warehouse:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(WarehouseInfo warehouseInfo) {
        startPage();
        List<WarehouseInfo> list = warehouseInfoService.selectWarehouseInfoList(warehouseInfo);
        return getDataTable(list);
    }

    /**
     * 获取仓库信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('warehouse:info:query')")
    @GetMapping(value = "/{warehouseId}")
    public AjaxResult getInfo(@PathVariable("warehouseId") Long warehouseId) {
        return success(warehouseInfoService.selectWarehouseInfoById(warehouseId));
    }

    /**
     * 新增仓库信息
     */
    @PreAuthorize("@ss.hasPermi('warehouse:info:add')")
    @Log(title = "仓库信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WarehouseInfo warehouseInfo) {
        return toAjax(warehouseInfoService.insertWarehouseInfo(warehouseInfo));
    }

    /**
     * 修改仓库信息
     */
    @PreAuthorize("@ss.hasPermi('warehouse:info:edit')")
    @Log(title = "仓库信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WarehouseInfo warehouseInfo) {
        return toAjax(warehouseInfoService.updateWarehouseInfo(warehouseInfo));
    }

    /**
     * 删除仓库信息
     */
    @PreAuthorize("@ss.hasPermi('warehouse:info:remove')")
    @Log(title = "仓库信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{warehouseIds}")
    public AjaxResult remove(@PathVariable Long[] warehouseIds) {
        return toAjax(warehouseInfoService.deleteWarehouseInfoByIds(warehouseIds));
    }
    
    /**
     * 获取仓库选项列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect() {
        List<WarehouseInfo> warehouseInfos = warehouseInfoService.selectWarehouseInfoAll();
        List<Map<String, Object>> warehouseList = new ArrayList<>();
        
        for (WarehouseInfo info : warehouseInfos) {
            Map<String, Object> warehouse = new HashMap<>();
            warehouse.put("warehouseId", info.getWarehouseId());
            warehouse.put("warehouseName", info.getWarehouseName());
            warehouse.put("warehouseCode", info.getWarehouseCode());
            warehouse.put("status", info.getStatus());
            warehouseList.add(warehouse);
        }
        
        return AjaxResult.success(warehouseList);
    }
    
    /**
     * 创建仓库Map对象（备用方法）
     */
    private Map<String, Object> createWarehouseMap(Long id, String name, String code, String status) {
        Map<String, Object> warehouse = new HashMap<>();
        warehouse.put("warehouse_id", id);
        warehouse.put("warehouse_name", name);
        warehouse.put("warehouse_code", code);
        warehouse.put("warehouse_status", status);
        return warehouse;
    }
}