@echo off
chcp 65001 >nul
echo ========================================
echo 执行完整的岗位组件删除
echo ========================================

echo.
echo 步骤1: 检查数据库中的岗位相关字段...
mysql -h localhost -P 3306 -u root -p123456 warehouse_system < check_and_remove_post_fields.sql > post_fields_check.txt
echo 检查结果已保存到 post_fields_check.txt

echo.
echo 步骤2: 执行完整删除脚本...
call complete_remove_post_components.bat

echo.
echo 步骤3: 验证删除结果...
echo 正在验证数据库表是否已删除...
mysql -h localhost -P 3306 -u root -p123456 -e "USE warehouse_system; SHOW TABLES LIKE '%post%';"

echo.
echo 步骤4: 验证菜单是否已删除...
mysql -h localhost -P 3306 -u root -p123456 -e "USE warehouse_system; SELECT menu_name FROM sys_menu WHERE menu_name LIKE '%岗位%' OR perms LIKE '%post%';"

echo.
echo 步骤5: 清理项目缓存...
if exist "warehouse-system\backend\target" (
    rmdir /s /q "warehouse-system\backend\target"
    echo 已清理后端编译缓存
)

if exist "warehouse-system\frontend\node_modules\.cache" (
    rmdir /s /q "warehouse-system\frontend\node_modules\.cache"
    echo 已清理前端缓存
)

if exist "warehouse-system\frontend\dist" (
    rmdir /s /q "warehouse-system\frontend\dist"
    echo 已清理前端构建文件
)

echo.
echo ========================================
echo 岗位组件完整删除完成！
echo ========================================
echo.
echo 删除内容包括：
echo - 数据库表: sys_post, sys_user_post
echo - 前端页面: system/post 相关组件
echo - 后端代码: Controller, Service, Mapper, Domain
echo - 路由配置: 已更新相关路由文件
echo - 菜单权限: 已删除相关菜单项
echo - 用户界面: 已移除岗位显示
echo.
echo 请检查 post_fields_check.txt 文件
echo 如果发现其他表有相关字段，请手动删除
echo.
pause