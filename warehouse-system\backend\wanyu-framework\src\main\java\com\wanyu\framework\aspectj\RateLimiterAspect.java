package com.wanyu.framework.aspectj;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.wanyu.common.annotation.RateLimiter;
import com.wanyu.common.enums.LimitType;
import com.wanyu.common.exception.ServiceException;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.common.utils.ip.IpUtils;
import com.wanyu.common.utils.ServletUtils;

/**
 * 限流处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class RateLimiterAspect
{
    private static final Logger log = LoggerFactory.getLogger(RateLimiterAspect.class);

    // 内存限流计数器
    private static final Map<String, RateLimiterInfo> RATE_LIMITER_MAP = new ConcurrentHashMap<>();

    @Before("@annotation(rateLimiter)")
    public void doBefore(JoinPoint point, RateLimiter rateLimiter) throws Throwable
    {
        String key = rateLimiter.key();
        int time = rateLimiter.time();
        int count = rateLimiter.count();

        String combineKey = getCombineKey(rateLimiter, point);
        RateLimiterInfo limiterInfo = RATE_LIMITER_MAP.computeIfAbsent(combineKey, 
            k -> new RateLimiterInfo(time, count));
        
        if (!limiterInfo.tryAcquire()) {
            throw new ServiceException("访问过于频繁，请稍候再试");
        }
    }
    
    /**
     * 限流信息类
     */
    private static class RateLimiterInfo {
        private final int time; // 时间窗口（秒）
        private final int count; // 允许的最大请求数
        private final AtomicInteger currentCount; // 当前请求数
        private long lastResetTime; // 上次重置时间

        public RateLimiterInfo(int time, int count) {
            this.time = time;
            this.count = count;
            this.currentCount = new AtomicInteger(0);
            this.lastResetTime = System.currentTimeMillis();
        }

        /**
         * 尝试获取令牌
         * @return true表示获取成功，false表示被限流
         */
        public boolean tryAcquire() {
            long now = System.currentTimeMillis();
            
            // 检查是否需要重置计数器
            if (now - lastResetTime >= time * 1000L) {
                synchronized (this) {
                    if (now - lastResetTime >= time * 1000L) {
                        currentCount.set(0);
                        lastResetTime = now;
                    }
                }
            }

            // 尝试增加计数
            int current = currentCount.incrementAndGet();
            return current <= count;
        }
    }

    public String getCombineKey(RateLimiter rateLimiter, JoinPoint point)
    {
        StringBuffer stringBuffer = new StringBuffer(rateLimiter.key());
        if (rateLimiter.limitType() == LimitType.IP)
        {
            stringBuffer.append(IpUtils.getIpAddr(ServletUtils.getRequest())).append("-");
        }
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Class<?> targetClass = method.getDeclaringClass();
        stringBuffer.append(targetClass.getName()).append("-").append(method.getName());
        return stringBuffer.toString();
    }
}
