# 控制器启动问题调试

## 问题描述
Spring Boot应用在启动时遇到以下错误：
```
at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:266)
at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:225)
at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:213)
```

## 可能原因
1. 请求映射路径冲突
2. 控制器方法定义错误
3. 注解使用不当

## 已采取的调试步骤

### 1. 删除重复的控制器
- ✅ 删除了重复的 `SysJobController`
- ✅ 删除了重复的 `SysJobLogController`  
- ✅ 删除了重复的 `WmsTransferController`（与现有的 `WmsInventoryTransferController` 冲突）

### 2. 临时禁用新创建的控制器
为了排查问题，临时注释了以下控制器的注解：
- `ProductInfoController` - `/product/info`
- `WmsStockController` - `/inventory/stock`
- `SysWarehouseController` - `/system/warehouse`
- `SysDictDataController` - `/system/dict/data`

### 3. 已确认的现有控制器
- `WmsInventoryTransferController` - `/inventory/transfer` (已存在，功能完整)

## 下一步调试计划
1. 测试应用是否能在禁用新控制器后启动
2. 逐个启用控制器来定位问题
3. 检查是否有隐藏的路径冲突

## 解决方案
一旦确定问题控制器，需要：
1. 检查请求映射路径是否与现有控制器冲突
2. 验证控制器方法签名是否正确
3. 确保所有必要的依赖都已注入