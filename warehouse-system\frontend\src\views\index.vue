<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="24" :lg="24">
        <el-card shadow="hover" class="welcome-card">
          <div class="welcome-header">
            <h1>万裕物业仓库管理系统</h1>
            <p>欢迎使用万裕物业仓库管理系统，高效管理您的仓库和物品信息</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card shadow="hover" class="statistics-card">
          <div class="statistics-icon">
            <i class="el-icon-s-home"></i>
          </div>
          <div class="statistics-info">
            <div class="statistics-title">仓库总数</div>
            <div class="statistics-value">{{ statisticsData.warehouseCount }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card shadow="hover" class="statistics-card">
          <div class="statistics-icon">
            <i class="el-icon-shopping-cart-full"></i>
          </div>
          <div class="statistics-info">
            <div class="statistics-title">物品总数</div>
            <div class="statistics-value">{{ statisticsData.productCount }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card shadow="hover" class="statistics-card">
          <div class="statistics-icon">
            <i class="el-icon-upload"></i>
          </div>
          <div class="statistics-info">
            <div class="statistics-title">入库总数</div>
            <div class="statistics-value">{{ statisticsData.inboundCount }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card shadow="hover" class="statistics-card">
          <div class="statistics-icon">
            <i class="el-icon-download"></i>
          </div>
          <div class="statistics-info">
            <div class="statistics-title">出库总数</div>
            <div class="statistics-value">{{ statisticsData.outboundCount }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>库存统计</span>
          </div>
          <div class="chart-container" ref="inventoryChart"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>出入库趋势</span>
          </div>
          <div class="chart-container" ref="trendChart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20" class="activity-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card shadow="hover" class="activity-card">
          <div slot="header" class="clearfix">
            <span>最近入库记录</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="viewMoreInbound">查看更多</el-button>
          </div>
          <el-table :data="recentInbound" style="width: 100%" :show-header="false">
            <el-table-column width="50">
              <template slot-scope="scope">
                <el-avatar icon="el-icon-upload" size="small" style="background-color: #67C23A;"></el-avatar>
              </template>
            </el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div class="activity-item">
                  <div class="activity-title">{{ scope.row.productName }} 入库</div>
                  <div class="activity-desc">数量: {{ scope.row.quantity }} | 仓库: {{ scope.row.warehouseName }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="150" align="right">
              <template slot-scope="scope">
                <span class="activity-time">{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card shadow="hover" class="activity-card">
          <div slot="header" class="clearfix">
            <span>最近出库记录</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="viewMoreOutbound">查看更多</el-button>
          </div>
          <el-table :data="recentOutbound" style="width: 100%" :show-header="false">
            <el-table-column width="50">
              <template slot-scope="scope">
                <el-avatar icon="el-icon-download" size="small" style="background-color: #F56C6C;"></el-avatar>
              </template>
            </el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div class="activity-item">
                  <div class="activity-title">{{ scope.row.productName }} 出库</div>
                  <div class="activity-desc">数量: {{ scope.row.quantity }} | 仓库: {{ scope.row.warehouseName }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="150" align="right">
              <template slot-scope="scope">
                <span class="activity-time">{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: "Index",
  data() {
    return {
      // 统计数据
      statisticsData: {
        warehouseCount: 3,
        productCount: 128,
        inboundCount: 256,
        outboundCount: 198
      },
      // 最近入库记录
      recentInbound: [
        { id: 1, productName: "笔记本电脑", quantity: 10, warehouseName: "主仓库", createTime: "2023-05-16 14:30:00" },
        { id: 2, productName: "办公桌", quantity: 5, warehouseName: "主仓库", createTime: "2023-05-16 11:20:00" },
        { id: 3, productName: "打印机", quantity: 3, warehouseName: "备用仓库", createTime: "2023-05-15 16:45:00" },
        { id: 4, productName: "投影仪", quantity: 2, warehouseName: "主仓库", createTime: "2023-05-15 09:15:00" },
        { id: 5, productName: "办公椅", quantity: 8, warehouseName: "备用仓库", createTime: "2023-05-14 14:30:00" }
      ],
      // 最近出库记录
      recentOutbound: [
        { id: 1, productName: "笔记本电脑", quantity: 2, warehouseName: "主仓库", createTime: "2023-05-16 15:30:00" },
        { id: 2, productName: "打印机", quantity: 1, warehouseName: "备用仓库", createTime: "2023-05-16 10:20:00" },
        { id: 3, productName: "办公桌", quantity: 2, warehouseName: "主仓库", createTime: "2023-05-15 13:45:00" },
        { id: 4, productName: "投影仪", quantity: 1, warehouseName: "主仓库", createTime: "2023-05-15 08:15:00" },
        { id: 5, productName: "办公椅", quantity: 3, warehouseName: "备用仓库", createTime: "2023-05-14 11:30:00" }
      ],
      // 图表实例
      inventoryChart: null,
      trendChart: null
    };
  },
  mounted() {
    this.initInventoryChart();
    this.initTrendChart();
    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', this.resizeCharts);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.resizeCharts);
    // 销毁图表实例
    if (this.inventoryChart) {
      this.inventoryChart.dispose();
    }
    if (this.trendChart) {
      this.trendChart.dispose();
    }
  },
  methods: {
    // 初始化库存统计图表
    initInventoryChart() {
      this.inventoryChart = echarts.init(this.$refs.inventoryChart);
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['电子产品', '办公用品', '生活用品', '其他']
        },
        series: [
          {
            name: '库存分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 45, name: '电子产品' },
              { value: 30, name: '办公用品' },
              { value: 15, name: '生活用品' },
              { value: 10, name: '其他' }
            ]
          }
        ]
      };
      this.inventoryChart.setOption(option);
    },
    // 初始化出入库趋势图表
    initTrendChart() {
      this.trendChart = echarts.init(this.$refs.trendChart);
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['入库', '出库']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '入库',
            type: 'line',
            stack: '总量',
            data: [15, 12, 18, 9, 23, 7, 13],
            itemStyle: {
              color: '#67C23A'
            }
          },
          {
            name: '出库',
            type: 'line',
            stack: '总量',
            data: [8, 10, 15, 7, 12, 5, 9],
            itemStyle: {
              color: '#F56C6C'
            }
          }
        ]
      };
      this.trendChart.setOption(option);
    },
    // 重新调整图表大小
    resizeCharts() {
      if (this.inventoryChart) {
        this.inventoryChart.resize();
      }
      if (this.trendChart) {
        this.trendChart.resize();
      }
    },
    // 查看更多入库记录
    viewMoreInbound() {
      this.$router.push('/inventory/in');
    },
    // 查看更多出库记录
    viewMoreOutbound() {
      this.$router.push('/inventory/out');
    }
  }
};
</script>

<style lang="scss" scoped>
.home {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .welcome-card {
    margin-bottom: 20px;
    background: linear-gradient(to right, #1890ff, #36cfc9);
    color: #fff;

    .welcome-header {
      text-align: center;
      padding: 20px 0;

      h1 {
        font-size: 28px;
        margin-bottom: 10px;
      }

      p {
        font-size: 16px;
        opacity: 0.8;
      }
    }
  }

  .statistics-row {
    margin-bottom: 20px;

    .statistics-card {
      height: 120px;
      display: flex;
      align-items: center;
      padding: 20px;
      margin-bottom: 20px;

      .statistics-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #f0f9eb;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
          font-size: 30px;
          color: #67C23A;
        }
      }

      .statistics-info {
        flex: 1;

        .statistics-title {
          font-size: 16px;
          color: #606266;
          margin-bottom: 10px;
        }

        .statistics-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
      }
    }
  }

  .chart-row {
    margin-bottom: 20px;

    .chart-card {
      margin-bottom: 20px;

      .chart-container {
        height: 300px;
      }
    }
  }

  .activity-row {
    .activity-card {
      margin-bottom: 20px;

      .activity-item {
        .activity-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .activity-desc {
          font-size: 12px;
          color: #909399;
        }
      }

      .activity-time {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>