package com.wanyu.system.mapper;

import java.util.List;
import java.util.Map;
import com.wanyu.system.domain.SysOperLog;

/**
 * 操作日志 数据层
 * 
 * <AUTHOR>
 */
public interface SysOperLogMapper
{
    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     */
    public void insertOperlog(SysOperLog operLog);

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    public List<SysOperLog> selectOperLogList(SysOperLog operLog);

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public int deleteOperLogByIds(Long[] operIds);

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLog selectOperLogById(Long operId);

    /**
     * 清空操作日志
     */
    public void cleanOperLog();

    /**
     * 获取操作日志统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getOperLogStatistics();

    /**
     * 获取操作日志趋势数据
     * 
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> getOperLogTrend(int days);
}
