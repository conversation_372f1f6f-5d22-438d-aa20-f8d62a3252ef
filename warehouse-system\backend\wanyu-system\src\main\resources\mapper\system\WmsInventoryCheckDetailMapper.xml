<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsInventoryCheckDetailMapper">
    
    <resultMap type="WmsInventoryCheckDetail" id="WmsInventoryCheckDetailResult">
        <result property="detailId"       column="detail_id"       />
        <result property="checkId"        column="check_id"        />
        <result property="productId"      column="product_id"      />
        <result property="productName"    column="product_name"    />
        <result property="productCode"    column="product_code"    />
        <result property="bookQuantity"   column="book_quantity"   />
        <result property="realQuantity"   column="real_quantity"   />
        <result property="diffQuantity"   column="diff_quantity"   />
        <result property="remark"         column="remark"          />
    </resultMap>

    <sql id="selectWmsInventoryCheckDetailVo">
        select d.detail_id, d.check_id, d.product_id, p.product_name, p.product_code, 
        d.book_quantity, d.real_quantity, d.diff_quantity, d.remark
        from wms_inventory_check_detail d
        left join wms_product p on d.product_id = p.product_id
    </sql>

    <select id="selectWmsInventoryCheckDetailList" parameterType="WmsInventoryCheckDetail" resultMap="WmsInventoryCheckDetailResult">
        <include refid="selectWmsInventoryCheckDetailVo"/>
        <where>  
            <if test="checkId != null "> and d.check_id = #{checkId}</if>
            <if test="productId != null "> and d.product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and p.product_code like concat('%', #{productCode}, '%')</if>
        </where>
    </select>
    
    <select id="selectWmsInventoryCheckDetailByDetailId" parameterType="Long" resultMap="WmsInventoryCheckDetailResult">
        <include refid="selectWmsInventoryCheckDetailVo"/>
        where d.detail_id = #{detailId}
    </select>
    
    <select id="selectWmsInventoryCheckDetailByCheckId" parameterType="Long" resultMap="WmsInventoryCheckDetailResult">
        <include refid="selectWmsInventoryCheckDetailVo"/>
        where d.check_id = #{checkId}
    </select>
        
    <insert id="insertWmsInventoryCheckDetail" parameterType="WmsInventoryCheckDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into wms_inventory_check_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checkId != null">check_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="bookQuantity != null">book_quantity,</if>
            <if test="realQuantity != null">real_quantity,</if>
            <if test="diffQuantity != null">diff_quantity,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checkId != null">#{checkId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="bookQuantity != null">#{bookQuantity},</if>
            <if test="realQuantity != null">#{realQuantity},</if>
            <if test="diffQuantity != null">#{diffQuantity},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertWmsInventoryCheckDetail">
        insert into wms_inventory_check_detail(check_id, product_id, book_quantity, real_quantity, diff_quantity, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.checkId}, #{item.productId}, #{item.bookQuantity}, #{item.realQuantity}, #{item.diffQuantity}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateWmsInventoryCheckDetail" parameterType="WmsInventoryCheckDetail">
        update wms_inventory_check_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="checkId != null">check_id = #{checkId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="bookQuantity != null">book_quantity = #{bookQuantity},</if>
            <if test="realQuantity != null">real_quantity = #{realQuantity},</if>
            <if test="diffQuantity != null">diff_quantity = #{diffQuantity},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteWmsInventoryCheckDetailByDetailId" parameterType="Long">
        delete from wms_inventory_check_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteWmsInventoryCheckDetailByDetailIds" parameterType="String">
        delete from wms_inventory_check_detail where detail_id in 
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
    
    <delete id="deleteWmsInventoryCheckDetailByCheckId" parameterType="Long">
        delete from wms_inventory_check_detail where check_id = #{checkId}
    </delete>
</mapper>
