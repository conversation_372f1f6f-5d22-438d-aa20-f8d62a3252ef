-- 字段标准检查脚本
-- 用于检查当前数据库字段定义是否符合项目标准

USE warehouse_system;

-- 1. 检查sys_license表字段定义
SELECT 
    'sys_license_field_check' as check_type,
    'status字段检查' as check_item,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    CASE 
        WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 
             AND COLUMN_DEFAULT = '0' 
             AND COLUMN_COMMENT LIKE '%0%正常%1%停用%'
        THEN '符合标准'
        ELSE '不符合标准'
    END as standard_compliance
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME = 'sys_license' 
  AND COLUMN_NAME = 'status';

-- 2. 检查sys_license_feature表字段定义
SELECT 
    'sys_license_feature_field_check' as check_type,
    'status字段检查' as check_item,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    CASE 
        WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 
             AND COLUMN_DEFAULT = '0' 
             AND COLUMN_COMMENT LIKE '%0%正常%1%停用%'
        THEN '符合标准'
        ELSE '不符合标准'
    END as standard_compliance
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME = 'sys_license_feature' 
  AND COLUMN_NAME = 'status';

-- 3. 检查操作日志表字段定义（如果存在）
SELECT 
    'operation_log_field_check' as check_type,
    'operation_status字段检查' as check_item,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    CASE 
        WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 
             AND COLUMN_DEFAULT = '0' 
             AND COLUMN_COMMENT LIKE '%0%成功%1%失败%'
        THEN '符合标准'
        ELSE '不符合标准'
    END as standard_compliance
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME = 'wms_operation_log' 
  AND COLUMN_NAME = 'operation_status';

-- 4. 检查所有状态字段的数据分布
SELECT 
    'sys_license_data_distribution' as check_type,
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sys_license), 2) as percentage,
    CASE 
        WHEN status IN ('0', '1') THEN '值范围正确'
        ELSE '值范围异常'
    END as value_check
FROM sys_license 
GROUP BY status
ORDER BY status;

SELECT 
    'sys_license_feature_data_distribution' as check_type,
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sys_license_feature), 2) as percentage,
    CASE 
        WHEN status IN ('0', '1') THEN '值范围正确'
        ELSE '值范围异常'
    END as value_check
FROM sys_license_feature 
GROUP BY status
ORDER BY status;

-- 5. 检查数据字典配置
SELECT 
    'dict_data_check' as check_type,
    dict_type,
    dict_label,
    dict_value,
    CASE 
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '0' AND dict_label = '正常' THEN '符合标准'
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '1' AND dict_label = '停用' THEN '符合标准'
        WHEN dict_type = 'operation_status' AND dict_value = '0' AND dict_label = '成功' THEN '符合标准'
        WHEN dict_type = 'operation_status' AND dict_value = '1' AND dict_label = '失败' THEN '符合标准'
        ELSE '需要检查'
    END as standard_compliance
FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status')
ORDER BY dict_type, dict_sort;

-- 6. 生成字段标准合规报告
SELECT '=== 字段标准合规检查报告 ===' as report_title, NOW() as check_time;

-- 检查表结构合规性
SELECT 
    '表结构合规性' as check_category,
    table_name,
    field_name,
    current_definition,
    standard_definition,
    compliance_status
FROM (
    SELECT 
        'sys_license' as table_name,
        'status' as field_name,
        CONCAT(DATA_TYPE, '(', IFNULL(CHARACTER_MAXIMUM_LENGTH, ''), ') DEFAULT ', IFNULL(COLUMN_DEFAULT, 'NULL'), ' COMMENT ''', IFNULL(COLUMN_COMMENT, ''), '''') as current_definition,
        'CHAR(1) DEFAULT ''0'' COMMENT ''状态（0正常 1停用）''' as standard_definition,
        CASE 
            WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 AND COLUMN_DEFAULT = '0' 
            THEN '符合标准'
            ELSE '不符合标准'
        END as compliance_status
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = 'warehouse_system' AND TABLE_NAME = 'sys_license' AND COLUMN_NAME = 'status'
    
    UNION ALL
    
    SELECT 
        'sys_license_feature' as table_name,
        'status' as field_name,
        CONCAT(DATA_TYPE, '(', IFNULL(CHARACTER_MAXIMUM_LENGTH, ''), ') DEFAULT ', IFNULL(COLUMN_DEFAULT, 'NULL'), ' COMMENT ''', IFNULL(COLUMN_COMMENT, ''), '''') as current_definition,
        'CHAR(1) DEFAULT ''0'' COMMENT ''状态（0正常 1停用）''' as standard_definition,
        CASE 
            WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 AND COLUMN_DEFAULT = '0' 
            THEN '符合标准'
            ELSE '不符合标准'
        END as compliance_status
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = 'warehouse_system' AND TABLE_NAME = 'sys_license_feature' AND COLUMN_NAME = 'status'
) as structure_check;

-- 检查数据值合规性
SELECT 
    '数据值合规性' as check_category,
    table_name,
    invalid_values,
    total_records,
    compliance_rate
FROM (
    SELECT 
        'sys_license' as table_name,
        (SELECT COUNT(*) FROM sys_license WHERE status NOT IN ('0', '1')) as invalid_values,
        (SELECT COUNT(*) FROM sys_license) as total_records,
        CONCAT(ROUND((SELECT COUNT(*) FROM sys_license WHERE status IN ('0', '1')) * 100.0 / (SELECT COUNT(*) FROM sys_license), 2), '%') as compliance_rate
    
    UNION ALL
    
    SELECT 
        'sys_license_feature' as table_name,
        (SELECT COUNT(*) FROM sys_license_feature WHERE status NOT IN ('0', '1')) as invalid_values,
        (SELECT COUNT(*) FROM sys_license_feature) as total_records,
        CONCAT(ROUND((SELECT COUNT(*) FROM sys_license_feature WHERE status IN ('0', '1')) * 100.0 / (SELECT COUNT(*) FROM sys_license_feature), 2), '%') as compliance_rate
) as data_check;

-- 7. 输出需要修复的问题清单
SELECT 
    '需要修复的问题' as issue_category,
    issue_description,
    affected_table,
    recommended_action
FROM (
    SELECT 
        'status字段定义不符合标准' as issue_description,
        'sys_license' as affected_table,
        '执行字段标准化修复脚本' as recommended_action
    WHERE EXISTS (
        SELECT 1 FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'warehouse_system' 
          AND TABLE_NAME = 'sys_license' 
          AND COLUMN_NAME = 'status'
          AND NOT (DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 AND COLUMN_DEFAULT = '0')
    )
    
    UNION ALL
    
    SELECT 
        'status字段定义不符合标准' as issue_description,
        'sys_license_feature' as affected_table,
        '执行字段标准化修复脚本' as recommended_action
    WHERE EXISTS (
        SELECT 1 FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'warehouse_system' 
          AND TABLE_NAME = 'sys_license_feature' 
          AND COLUMN_NAME = 'status'
          AND NOT (DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 AND COLUMN_DEFAULT = '0')
    )
    
    UNION ALL
    
    SELECT 
        '数据字典配置不完整' as issue_description,
        'sys_dict_data' as affected_table,
        '更新数据字典配置' as recommended_action
    WHERE NOT EXISTS (
        SELECT 1 FROM sys_dict_data 
        WHERE dict_type = 'sys_normal_disable' 
          AND dict_value = '0' 
          AND dict_label = '正常'
    )
) as issues_list;