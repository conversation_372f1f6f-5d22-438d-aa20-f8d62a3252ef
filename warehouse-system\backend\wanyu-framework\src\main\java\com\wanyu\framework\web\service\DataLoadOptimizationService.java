package com.wanyu.framework.web.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wanyu.common.constant.CacheConstants;
import com.wanyu.common.utils.StringUtils;

/**
 * 数据加载优化服务
 * 
 * <AUTHOR>
 */
@Component
public class DataLoadOptimizationService
{
    private static final Logger log = LoggerFactory.getLogger(DataLoadOptimizationService.class);

    // 内存缓存用于存储数据
    private static final Map<String, Object> DATA_CACHE = new HashMap<>();

    /**
     * 缓存有效期（默认10分钟）
     */
    private final long EXPIRATION = 10;

    /**
     * 缓存时间单位
     */
    private final TimeUnit TIMEUNIT = TimeUnit.MINUTES;

    /**
     * 数据缓存前缀
     */
    private final String CACHE_PREFIX = CacheConstants.SYS_DATA_CACHE;

    /**
     * 线程池
     */
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 异步加载数据
     * 
     * @param <T> 数据类型
     * @param key 缓存键
     * @param dataLoader 数据加载器
     * @return 异步任务
     */
    public <T> CompletableFuture<T> loadDataAsync(String key, DataLoader<T> dataLoader)
    {
        return CompletableFuture.supplyAsync(() -> {
            try
            {
                // 尝试从缓存获取数据
                String cacheKey = getCacheKey(key);
                T cachedData = (T) DATA_CACHE.get(cacheKey);
                
                if (cachedData != null)
                {
                    return cachedData;
                }
                
                // 从数据源加载数据
                T data = dataLoader.load();
                
                // 缓存数据
                if (data != null)
                {
                    DATA_CACHE.put(cacheKey, data);
                    
                    // 启动一个定时任务在指定时间后删除缓存
                    CompletableFuture.runAsync(() -> {
                        try {
                            Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                            DATA_CACHE.remove(cacheKey);
                            log.debug("内存缓存数据已过期并删除: {}", cacheKey);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }, executorService);
                }
                
                return data;
            }
            catch (Exception e)
            {
                log.error("异步加载数据失败: {}", e.getMessage());
                throw new RuntimeException("加载数据失败", e);
            }
        }, executorService);
    }

    /**
     * 批量异步加载数据
     * 
     * @param <T> 数据类型
     * @param keys 缓存键列表
     * @param dataLoader 数据加载器
     * @return 异步任务
     */
    public <T> CompletableFuture<Map<String, T>> loadDataBatchAsync(List<String> keys, BatchDataLoader<T> dataLoader)
    {
        return CompletableFuture.supplyAsync(() -> {
            try
            {
                Map<String, T> result = new HashMap<>();
                List<String> keysToLoad = new ArrayList<>();
                
                // 尝试从缓存获取数据
                for (String key : keys)
                {
                    String cacheKey = getCacheKey(key);
                    T cachedData = (T) DATA_CACHE.get(cacheKey);
                    
                    if (cachedData != null)
                    {
                        result.put(key, cachedData);
                    }
                    else
                    {
                        keysToLoad.add(key);
                    }
                }
                
                // 如果所有数据都在缓存中，直接返回
                if (keysToLoad.isEmpty())
                {
                    return result;
                }
                
                // 从数据源批量加载数据
                Map<String, T> loadedData = dataLoader.loadBatch(keysToLoad);
                
                // 缓存数据并添加到结果
                for (Map.Entry<String, T> entry : loadedData.entrySet())
                {
                    String key = entry.getKey();
                    T data = entry.getValue();
                    
                    if (data != null)
                    {
                        String cacheKey = getCacheKey(key);
                        DATA_CACHE.put(cacheKey, data);
                        
                        // 启动一个定时任务在指定时间后删除缓存
                        CompletableFuture.runAsync(() -> {
                            try {
                                Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                                DATA_CACHE.remove(cacheKey);
                                log.debug("内存缓存数据已过期并删除: {}", cacheKey);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }, executorService);
                        
                        result.put(key, data);
                    }
                }
                
                return result;
            }
            catch (Exception e)
            {
                log.error("批量异步加载数据失败: {}", e.getMessage());
                throw new RuntimeException("批量加载数据失败", e);
            }
        }, executorService);
    }

    /**
     * 获取缓存键值
     * 
     * @param key 键
     * @return 缓存键值
     */
    private String getCacheKey(String key)
    {
        return CACHE_PREFIX + key;
    }
    
    /**
     * 获取缓存数据
     * 
     * @param key 缓存键
     * @return 缓存数据
     */
    public Object getCacheData(String key) {
        String cacheKey = getCacheKey(key);
        return DATA_CACHE.get(cacheKey);
    }
    
    /**
     * 设置缓存数据
     * 
     * @param key 缓存键
     * @param data 缓存数据
     */
    public void setCacheData(String key, Object data) {
        String cacheKey = getCacheKey(key);
        DATA_CACHE.put(cacheKey, data);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                DATA_CACHE.remove(cacheKey);
                log.debug("内存缓存数据已过期并删除: {}", cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, executorService);
    }
    
    /**
     * 删除缓存数据
     * 
     * @param key 缓存键
     */
    public void removeCacheData(String key) {
        String cacheKey = getCacheKey(key);
        DATA_CACHE.remove(cacheKey);
    }
    
    /**
     * 清空所有缓存数据
     */
    public void clearAllCacheData() {
        DATA_CACHE.clear();
    }
    
    /**
     * 获取所有缓存键
     * 
     * @return 缓存键列表
     */
    public List<String> getAllCacheKeys() {
        return new ArrayList<>(DATA_CACHE.keySet());
    }

    /**
     * 数据加载器接口
     * 
     * @param <T> 数据类型
     */
    public interface DataLoader<T>
    {
        /**
         * 加载数据
         * 
         * @return 数据
         */
        T load();
    }

    /**
     * 批量数据加载器接口
     * 
     * @param <T> 数据类型
     */
    public interface BatchDataLoader<T>
    {
        /**
         * 批量加载数据
         * 
         * @param keys 键列表
         * @return 数据映射
         */
        Map<String, T> loadBatch(List<String> keys);
    }
}

