<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.ProductInfoMapper">

    <resultMap type="ProductInfo" id="ProductInfoResult">
        <result property="productId"       column="product_id"       />
        <result property="productName"     column="product_name"     />
        <result property="productCode"     column="product_code"     />
        <result property="productSpec"     column="product_spec"     />
        <result property="specId"          column="spec_id"          />
        <result property="specName"        column="spec_name"        />
        <result property="productUnit"     column="product_unit"     />
        <result property="unitId"          column="unit_id"          />
        <result property="unitName"        column="unit_name"        />
        <result property="productCategory" column="product_category" />
        <result property="categoryId"      column="category_id"      />
        <result property="categoryName"    column="category_name"    />
        <result property="price"           column="price"            />
        <result property="status"          column="status"           />
        <result property="imageUrl"        column="image_url"        />
        <result property="remark"          column="remark"           />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
    </resultMap>

    <sql id="selectProductInfoVo">
        select p.product_id, p.product_name, p.product_code, p.product_spec, p.spec_id, s.spec_name,
        p.product_unit, p.unit_id, u.unit_name, p.product_category, p.category_id, c.category_name,
        p.price, p.status, p.image_url, p.remark, p.create_by, p.create_time, p.update_by, p.update_time
        from wms_product p
        left join wms_category c on p.category_id = c.category_id
        left join wms_specification s on p.spec_id = s.spec_id
        left join wms_unit u on p.unit_id = u.unit_id
    </sql>

    <select id="selectProductInfoList" parameterType="ProductInfo" resultMap="ProductInfoResult">
        <include refid="selectProductInfoVo"/>
        <where>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and p.product_code like concat('%', #{productCode}, '%')</if>
            <if test="productSpec != null  and productSpec != ''"> and p.product_spec like concat('%', #{productSpec}, '%')</if>
            <if test="productUnit != null  and productUnit != ''"> and p.product_unit = #{productUnit}</if>
            <if test="productCategory != null  and productCategory != ''"> and p.product_category = #{productCategory}</if>
            <if test="categoryId != null "> and p.category_id = #{categoryId}</if>
            <if test="specId != null "> and p.spec_id = #{specId}</if>
            <if test="unitId != null "> and p.unit_id = #{unitId}</if>
            <if test="price != null "> and p.price = #{price}</if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
        </where>
        order by p.product_id desc
    </select>

    <select id="selectProductInfoByProductId" parameterType="Long" resultMap="ProductInfoResult">
        <include refid="selectProductInfoVo"/>
        where p.product_id = #{productId}
    </select>

    <insert id="insertProductInfo" parameterType="ProductInfo" useGeneratedKeys="true" keyProperty="productId">
        insert into wms_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productCode != null and productCode != ''">product_code,</if>
            <if test="productSpec != null">product_spec,</if>
            <if test="specId != null">spec_id,</if>
            <if test="productUnit != null">product_unit,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="productCategory != null">product_category,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="price != null">price,</if>
            <if test="status != null">status,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productCode != null and productCode != ''">#{productCode},</if>
            <if test="productSpec != null">#{productSpec},</if>
            <if test="specId != null">#{specId},</if>
            <if test="productUnit != null">#{productUnit},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="productCategory != null">#{productCategory},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="price != null">#{price},</if>
            <if test="status != null">#{status},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProductInfo" parameterType="ProductInfo">
        update wms_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productCode != null and productCode != ''">product_code = #{productCode},</if>
            <if test="productSpec != null">product_spec = #{productSpec},</if>
            <if test="specId != null">spec_id = #{specId},</if>
            <if test="productUnit != null">product_unit = #{productUnit},</if>
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="productCategory != null">product_category = #{productCategory},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="status != null">status = #{status},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where product_id = #{productId}
    </update>

    <delete id="deleteProductInfoByProductId" parameterType="Long">
        delete from wms_product where product_id = #{productId}
    </delete>

    <delete id="deleteProductInfoByProductIds" parameterType="String">
        delete from wms_product where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>
</mapper>
