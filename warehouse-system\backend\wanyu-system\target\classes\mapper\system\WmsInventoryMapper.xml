<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsInventoryMapper">

    <resultMap type="WmsInventory" id="WmsInventoryResult">
        <result property="inventoryId"    column="inventory_id"    />
        <result property="productId"      column="product_id"      />
        <result property="productName"    column="product_name"    />
        <result property="productCode"    column="product_code"    />
        <result property="categoryId"     column="category_id"     />
        <result property="categoryName"   column="category_name"   />
        <result property="specId"         column="spec_id"         />
        <result property="specName"       column="spec_name"       />
        <result property="specification"  column="specification"   />
        <result property="unitId"         column="unit_id"         />
        <result property="unitName"       column="unit_name"       />
        <result property="unit"           column="unit"            />
        <result property="price"          column="price"           />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseName"  column="warehouse_name"  />
        <result property="quantity"       column="quantity"        />
        <result property="minQuantity"    column="min_quantity"    />
        <result property="maxQuantity"    column="max_quantity"    />
        <result property="status"         column="status"          />
        <result property="remark"         column="remark"          />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
    </resultMap>

    <sql id="selectWmsInventoryVo">
        select i.inventory_id, i.product_id, p.product_name, p.product_code, p.category_id, c.category_name, p.spec_id, s.spec_name,
        COALESCE(s.spec_name, p.product_spec, '') as specification,
        p.unit_id, u.unit_name,
        COALESCE(u.unit_name, p.product_unit, '') as unit,
        p.price,
        i.warehouse_id, w.warehouse_name,
        i.quantity, i.min_quantity, i.max_quantity, i.status, i.remark, i.create_by, i.create_time, i.update_by, i.update_time
        from wms_inventory i
        left join wms_product p on i.product_id = p.product_id
        left join wms_category c on p.category_id = c.category_id
        left join wms_specification s on p.spec_id = s.spec_id
        left join wms_unit u on p.unit_id = u.unit_id
        left join sys_warehouse w on i.warehouse_id = w.warehouse_id
    </sql>

    <select id="selectWmsInventoryList" parameterType="WmsInventory" resultMap="WmsInventoryResult">
        <include refid="selectWmsInventoryVo"/>
        <where>
            ${params.warehouseScope}
            <if test="productId != null "> and i.product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and p.product_code like concat('%', #{productCode}, '%')</if>
            <if test="warehouseId != null "> and i.warehouse_id = #{warehouseId}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and w.warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="status != null  and status != ''"> and i.status = #{status}</if>
        </where>
    </select>

    <select id="selectWmsInventoryAlertList" parameterType="WmsInventory" resultMap="WmsInventoryResult">
        <include refid="selectWmsInventoryVo"/>
        <where>
            i.status != '0'
            <if test="productId != null "> and i.product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and p.product_code like concat('%', #{productCode}, '%')</if>
            <if test="warehouseId != null "> and i.warehouse_id = #{warehouseId}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and w.warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="status != null  and status != ''"> and i.status = #{status}</if>
        </where>
    </select>

    <select id="selectWmsInventoryByInventoryId" parameterType="Long" resultMap="WmsInventoryResult">
        <include refid="selectWmsInventoryVo"/>
        where i.inventory_id = #{inventoryId}
    </select>

    <select id="selectWmsInventoryByProductIdAndWarehouseId" parameterType="map" resultMap="WmsInventoryResult">
        <include refid="selectWmsInventoryVo"/>
        where i.product_id = #{productId} and i.warehouse_id = #{warehouseId}
        limit 1
    </select>

    <insert id="insertWmsInventory" parameterType="WmsInventory" useGeneratedKeys="true" keyProperty="inventoryId">
        insert into wms_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="minQuantity != null">min_quantity,</if>
            <if test="maxQuantity != null">max_quantity,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="minQuantity != null">#{minQuantity},</if>
            <if test="maxQuantity != null">#{maxQuantity},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmsInventory" parameterType="WmsInventory">
        update wms_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="minQuantity != null">min_quantity = #{minQuantity},</if>
            <if test="maxQuantity != null">max_quantity = #{maxQuantity},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where inventory_id = #{inventoryId}
    </update>

    <update id="updateWmsInventoryQuantity">
        update wms_inventory
        set quantity = quantity + #{quantity},
            update_time = sysdate()
        where inventory_id = #{inventoryId}
    </update>

    <delete id="deleteWmsInventoryByInventoryId" parameterType="Long">
        delete from wms_inventory where inventory_id = #{inventoryId}
    </delete>

    <delete id="deleteWmsInventoryByInventoryIds" parameterType="String">
        delete from wms_inventory where inventory_id in
        <foreach item="inventoryId" collection="array" open="(" separator="," close=")">
            #{inventoryId}
        </foreach>
    </delete>
</mapper>