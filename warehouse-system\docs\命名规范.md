# 仓库管理系统命名规范

## 目录

1. [数据库命名规范](#1-数据库命名规范)
   - [1.1 表命名规范](#11-表命名规范)
   - [1.2 字段命名规范](#12-字段命名规范)
   - [1.3 索引命名规范](#13-索引命名规范)
   - [1.4 常见表前缀说明](#14-常见表前缀说明)
2. [Java代码命名规范](#2-java代码命名规范)
   - [2.1 包命名规范](#21-包命名规范)
   - [2.2 类命名规范](#22-类命名规范)
   - [2.3 方法命名规范](#23-方法命名规范)
   - [2.4 变量命名规范](#24-变量命名规范)
   - [2.5 常量命名规范](#25-常量命名规范)
3. [前端命名规范](#3-前端命名规范)
   - [3.1 文件命名规范](#31-文件命名规范)
   - [3.2 变量命名规范](#32-变量命名规范)
   - [3.3 CSS命名规范](#33-css命名规范)
   - [3.4 组件命名规范](#34-组件命名规范)
4. [API接口命名规范](#4-api接口命名规范)
   - [4.1 接口路径规范](#41-接口路径规范)
   - [4.2 请求参数命名规范](#42-请求参数命名规范)
   - [4.3 响应字段命名规范](#43-响应字段命名规范)
5. [权限字符命名规范](#5-权限字符命名规范)
   - [5.1 菜单权限字符命名规范](#51-菜单权限字符命名规范)
   - [5.2 角色权限字符命名规范](#52-角色权限字符命名规范)
   - [5.3 API权限字符命名规范](#53-api权限字符命名规范)
6. [代码注释规范](#6-代码注释规范)
   - [6.1 Java代码注释规范](#61-java代码注释规范)
   - [6.2 JavaScript代码注释规范](#62-javascript代码注释规范)
7. [版本控制规范](#7-版本控制规范)
   - [7.1 分支命名规范](#71-分支命名规范)
   - [7.2 提交信息规范](#72-提交信息规范)
8. [附录](#8-附录)
   - [8.1 常见缩写词汇表](#81-常见缩写词汇表)
   - [8.2 命名规范检查工具](#82-命名规范检查工具)

## 1. 数据库命名规范

### 1.1 表命名规范

- 使用小写字母，单词间用下划线分隔
- 使用模块前缀区分不同模块的表
- 表名应当使用名词或名词短语
- 表名应当清晰表达表的用途和内容

**示例：**
- `wms_inventory` (仓库模块的库存表)
- `sys_user` (系统模块的用户表)
- `wms_product` (仓库模块的产品信息表)
- `wms_inventory_in` (仓库模块的入库单表)
- `wms_inventory_out` (仓库模块的出库单表)

**不良示例：**
- `Inventory` (不使用小写和下划线)
- `wmsInventory` (不使用下划线分隔单词)
- `inventory_table` (使用了多余的后缀)

### 1.2 字段命名规范

- 使用小写字母，单词间用下划线分隔
- 主键统一命名为`id`或表名加`_id`
- 外键命名为关联表名加`_id`
- 创建和更新信息的字段统一命名为：
  - `create_by`: 创建者
  - `create_time`: 创建时间
  - `update_by`: 更新者
  - `update_time`: 更新时间
- 状态字段统一使用`status`
- 备注字段统一使用`remark`
- 删除标志字段统一使用`del_flag`
- 字段名应当清晰表达字段的用途和内容

**示例：**
```sql
CREATE TABLE `wms_product` (
  `product_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '物品ID',
  `product_name` varchar(100) NOT NULL COMMENT '物品名称',
  `product_code` varchar(50) NOT NULL COMMENT '物品编码',
  `category_id` bigint(20) NOT NULL COMMENT '物品分类ID',
  `spec_id` bigint(20) NOT NULL COMMENT '规格ID',
  `unit_id` bigint(20) NOT NULL COMMENT '单位ID',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '物品单价',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`product_id`),
  UNIQUE KEY `uk_product_code` (`product_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='物品信息表';
```

### 1.3 索引命名规范

- 主键索引：使用`pk_表名`
- 普通索引：使用`idx_表名_字段名`
- 唯一索引：使用`uk_表名_字段名`
- 外键索引：使用`fk_表名_关联表名`
- 索引名应当清晰表达索引的用途和内容

**示例：**
```sql
-- 主键索引
ALTER TABLE `wms_product` ADD CONSTRAINT `pk_product_info` PRIMARY KEY (`product_id`);

-- 普通索引
CREATE INDEX `idx_product_info_category_id` ON `wms_product` (`category_id`);

-- 唯一索引
CREATE UNIQUE INDEX `uk_product_info_code` ON `wms_product` (`product_code`);

-- 外键索引
ALTER TABLE `product_image` ADD CONSTRAINT `fk_product_image_info` FOREIGN KEY (`product_id`) REFERENCES `wms_product` (`product_id`) ON DELETE CASCADE;
```

### 1.4 常见表前缀说明

- `sys_`: 系统管理模块表
- `wms_`: 仓库管理模块表
- `gen_`: 代码生成模块表
- `job_`: 定时任务模块表
- `monitor_`: 系统监控模块表

## 2. Java代码命名规范

### 2.1 包命名规范

- 全部小写字母
- 点分隔
- 包名应当与模块结构相对应
- 包名应当简洁明了

**示例：**
- `com.wanyu.system.user` (系统模块的用户包)
- `com.wanyu.warehouse.inventory` (仓库模块的库存包)
- `com.wanyu.common.utils` (公共工具类包)

**不良示例：**
- `com.wanyu.System.User` (使用了大写字母)
- `com.wanyu.warehouse_inventory` (使用了下划线)

### 2.2 类命名规范

- 使用大驼峰命名法
- Controller类：以`Controller`结尾
- Service接口：以`Service`结尾
- Service实现类：以`ServiceImpl`结尾
- Entity类：与数据库表名对应，去掉前缀
- Mapper接口：以`Mapper`结尾
- VO类：以`VO`结尾
- DTO类：以`DTO`结尾
- 类名应当清晰表达类的用途和内容

**示例：**
```java
// Controller类
public class ProductController {
    // 实现代码
}

// Service接口
public interface ProductService {
    // 接口定义
}

// Service实现类
public class ProductServiceImpl implements ProductService {
    // 实现代码
}

// Entity类
public class Product {
    // 属性和方法
}

// Mapper接口
public interface ProductMapper {
    // 接口定义
}

// VO类
public class ProductVO {
    // 属性和方法
}

// DTO类
public class ProductDTO {
    // 属性和方法
}
```

### 2.3 方法命名规范

- 使用小驼峰命名法
- 方法名应当使用动词或动词短语
- 查询方法：以`get`/`find`/`query`/`list`开头
- 更新方法：以`update`开头
- 删除方法：以`delete`/`remove`开头
- 插入方法：以`insert`/`add`/`save`开头
- 方法名应当清晰表达方法的用途和功能

**示例：**
```java
// 查询方法
public List<Product> getProductList(Product product);
public Product getProductById(Long productId);
public List<Product> findProductsByCategory(Long categoryId);
public List<Product> queryProductsByName(String productName);

// 更新方法
public int updateProduct(Product product);
public int updateProductStatus(Long productId, String status);

// 删除方法
public int deleteProductById(Long productId);
public int deleteProductByIds(Long[] productIds);
public int removeProduct(Long productId);

// 插入方法
public int insertProduct(Product product);
public int addProduct(Product product);
public int saveProduct(Product product);
```

### 2.4 变量命名规范

- 使用小驼峰命名法
- 变量名应当使用名词或名词短语
- 布尔类型变量应当以`is`/`has`/`can`等开头
- 集合类型变量应当使用复数形式
- 变量名应当清晰表达变量的用途和内容

**示例：**
```java
// 普通变量
private String productName;
private Long categoryId;
private BigDecimal price;

// 布尔类型变量
private boolean isValid;
private boolean hasPermission;
private boolean canEdit;

// 集合类型变量
private List<Product> products;
private Map<String, Object> params;
private Set<Long> productIds;
```

### 2.5 常量命名规范

- 使用大写字母，单词间用下划线分隔
- 常量名应当清晰表达常量的用途和内容

**示例：**
```java
public static final String STATUS_NORMAL = "0";
public static final String STATUS_DISABLE = "1";
public static final int MAX_LENGTH = 100;
public static final String DEFAULT_PASSWORD = "123456";
```

## 3. 前端命名规范

### 3.1 文件命名规范

- Vue组件文件：使用大驼峰命名法
- 路由文件：使用小写字母，单词间用连字符分隔
- API文件：使用小写字母，单词间用连字符分隔
- 工具类文件：使用小写字母，单词间用连字符分隔
- 文件名应当清晰表达文件的用途和内容

**示例：**
```
// Vue组件文件
ProductList.vue
ProductDetail.vue
UserProfile.vue

// 路由文件
product-router.js
user-router.js

// API文件
product-api.js
user-api.js

// 工具类文件
date-util.js
string-util.js
```

### 3.2 变量命名规范

- 普通变量：使用小驼峰命名法
- 常量：使用大写字母，单词间用下划线分隔
- 组件名：使用大驼峰命名法
- Props：使用小驼峰命名法
- 变量名应当清晰表达变量的用途和内容

**示例：**
```javascript
// 普通变量
const productName = 'iPhone';
let categoryId = 1;
let price = 999.99;

// 常量
const API_URL = 'http://api.example.com';
const MAX_COUNT = 100;
const DEFAULT_CONFIG = {
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// 组件名
const ProductList = {
  // 组件定义
};

// Props
props: {
  productId: {
    type: Number,
    required: true
  },
  productName: {
    type: String,
    default: ''
  }
}
```

### 3.3 CSS命名规范

- 使用小写字母
- 单词间用连字符分隔
- 使用BEM命名方法论
  - Block：块，独立的组件
  - Element：元素，块的一部分，用`__`连接
  - Modifier：修饰符，块或元素的状态，用`--`连接
- CSS类名应当清晰表达样式的用途和内容

**示例：**
```css
/* Block */
.product-card {
  /* 样式定义 */
}

/* Element */
.product-card__title {
  /* 样式定义 */
}

.product-card__image {
  /* 样式定义 */
}

/* Modifier */
.product-card--featured {
  /* 样式定义 */
}

.product-card__title--large {
  /* 样式定义 */
}
```

### 3.4 组件命名规范

- 组件名使用大驼峰命名法
- 基础组件以`Base`开头
- 单例组件以`The`开头
- 紧密耦合的组件名应包含父组件名作为前缀
- 组件名应当清晰表达组件的用途和内容

**示例：**
```javascript
// 基础组件
Vue.component('BaseButton', {
  // 组件定义
});

Vue.component('BaseIcon', {
  // 组件定义
});

// 单例组件
Vue.component('TheHeader', {
  // 组件定义
});

Vue.component('TheSidebar', {
  // 组件定义
});

// 紧密耦合的组件
Vue.component('ProductList', {
  // 组件定义
});

Vue.component('ProductListItem', {
  // 组件定义
});

Vue.component('ProductListItemPrice', {
  // 组件定义
});
```

## 4. API接口命名规范

### 4.1 接口路径规范

- 使用小写字母
- 单词间用连字符分隔
- 使用复数形式表示资源集合
- 使用HTTP方法表示操作类型
- 接口路径应当清晰表达接口的用途和内容

**示例：**
```
// RESTful API
GET    /api/v1/products          # 获取产品列表
GET    /api/v1/products/{id}     # 获取单个产品
POST   /api/v1/products          # 创建产品
PUT    /api/v1/products/{id}     # 更新产品
DELETE /api/v1/products/{id}     # 删除产品

// 嵌套资源
GET    /api/v1/warehouses/{id}/stocks    # 获取仓库的库存列表
POST   /api/v1/warehouses/{id}/stocks    # 在仓库中创建库存

// 操作
POST   /api/v1/products/{id}/publish     # 发布产品
POST   /api/v1/inventory/in              # 入库操作
POST   /api/v1/inventory/out             # 出库操作
```

### 4.2 请求参数命名规范

- 查询参数：使用小驼峰命名法
- 路径参数：使用小驼峰命名法
- 请求体参数：使用小驼峰命名法
- 参数名应当清晰表达参数的用途和内容

**示例：**
```
// 查询参数
GET /api/v1/products?pageNum=1&pageSize=10&productName=iPhone

// 路径参数
GET /api/v1/products/{productId}

// 请求体参数
POST /api/v1/products
{
  "productName": "iPhone",
  "categoryId": 1,
  "price": 999.99,
  "status": "0"
}
```

### 4.3 响应字段命名规范

- 统一使用小驼峰命名法
- 保持与数据库字段的映射关系
- 使用清晰的语义化命名
- 响应字段应当清晰表达字段的用途和内容

**示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "productId": 1,
    "productName": "iPhone",
    "categoryId": 1,
    "categoryName": "手机",
    "price": 999.99,
    "status": "0",
    "createTime": "2023-01-01 12:00:00"
  }
}
```

## 5. 权限字符命名规范

### 5.1 菜单权限字符命名规范

- 格式：`模块:功能:操作`
- 模块：表示系统模块，如system、warehouse等
- 功能：表示功能模块，如user、role、product等
- 操作：表示操作类型，如list、add、edit、remove等
- 权限字符应当清晰表达权限的用途和内容

**示例：**
```
system:user:list      # 用户管理列表权限
system:user:add       # 用户管理新增权限
system:user:edit      # 用户管理修改权限
system:user:remove    # 用户管理删除权限
warehouse:product:list # 物品管理列表权限
warehouse:in:add      # 入库单新增权限
```

### 5.2 角色权限字符命名规范

- 格式：使用简单的英文单词或短语，表示角色的职责或权限范围
- 角色权限字符应当清晰表达角色的用途和内容

**示例：**
```
admin               # 管理员角色
common              # 普通用户角色
warehouse_admin     # 仓库管理员角色
inventory_manager   # 库存管理员角色
```

### 5.3 API权限字符命名规范

- API权限字符通常与菜单权限字符保持一致
- 格式：`模块:功能:操作`
- API权限字符应当清晰表达权限的用途和内容

**示例：**
```
system:user:list      # 用户管理列表API权限
system:user:add       # 用户管理新增API权限
warehouse:product:list # 物品管理列表API权限
warehouse:in:add      # 入库单新增API权限
```

## 6. 代码注释规范

### 6.1 Java代码注释规范

- 类注释：描述类的用途、作者、创建日期等信息
- 方法注释：描述方法的功能、参数、返回值、异常等信息
- 字段注释：描述字段的用途和含义
- 代码块注释：描述复杂代码块的逻辑和用途
- 注释应当清晰、简洁、准确

**示例：**
```java
/**
 * 物品信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-01-01
 */
public interface ProductService {
    
    /**
     * 查询物品信息列表
     * 
     * @param product 物品信息查询条件
     * @return 物品信息集合
     */
    public List<Product> selectProductList(Product product);
    
    /**
     * 根据物品ID查询物品信息
     * 
     * @param productId 物品ID
     * @return 物品信息
     */
    public Product selectProductById(Long productId);
    
    /**
     * 新增物品信息
     * 
     * @param product 物品信息
     * @return 结果
     */
    public int insertProduct(Product product);
    
    /**
     * 修改物品信息
     * 
     * @param product 物品信息
     * @return 结果
     */
    public int updateProduct(Product product);
    
    /**
     * 批量删除物品信息
     * 
     * @param productIds 需要删除的物品ID数组
     * @return 结果
     */
    public int deleteProductByIds(Long[] productIds);
}
```

### 6.2 JavaScript代码注释规范

- 文件注释：描述文件的用途、作者、创建日期等信息
- 函数注释：描述函数的功能、参数、返回值等信息
- 变量注释：描述变量的用途和含义
- 代码块注释：描述复杂代码块的逻辑和用途
- 注释应当清晰、简洁、准确

**示例：**
```javascript
/**
 * 物品管理API
 * 
 * <AUTHOR>
 * @date 2023-01-01
 */

/**
 * 查询物品列表
 * 
 * @param {Object} query 查询参数
 * @returns {Promise} 请求Promise对象
 */
export function listProduct(query) {
  return request({
    url: '/api/v1/products',
    method: 'get',
    params: query
  })
}

/**
 * 获取物品详情
 * 
 * @param {Number} productId 物品ID
 * @returns {Promise} 请求Promise对象
 */
export function getProduct(productId) {
  return request({
    url: `/api/v1/products/${productId}`,
    method: 'get'
  })
}

/**
 * 新增物品
 * 
 * @param {Object} data 物品数据
 * @returns {Promise} 请求Promise对象
 */
export function addProduct(data) {
  return request({
    url: '/api/v1/products',
    method: 'post',
    data: data
  })
}
```

## 7. 版本控制规范

### 7.1 分支命名规范

- `master`/`main`：主分支，用于生产环境
- `develop`：开发分支，用于开发环境
- `feature/功能名称`：功能分支，用于开发新功能
- `bugfix/问题描述`：修复分支，用于修复非紧急bug
- `hotfix/问题描述`：热修复分支，用于修复紧急bug
- `release/版本号`：发布分支，用于准备发布
- 分支名应当清晰表达分支的用途和内容

**示例：**
```
master
develop
feature/product-management
feature/inventory-in-out
bugfix/product-list-pagination
hotfix/login-security-issue
release/v1.0.0
```

### 7.2 提交信息规范

- 格式：`类型(范围): 描述`
- 类型：表示提交的类型，如feat、fix、docs等
- 范围：表示提交影响的范围，如product、inventory等
- 描述：表示提交的简短描述
- 提交信息应当清晰表达提交的用途和内容

**示例：**
```
feat(product): 添加物品管理功能
fix(inventory): 修复库存统计计算错误
docs(api): 更新API文档
style(ui): 优化用户界面样式
refactor(service): 重构服务层代码
test(unit): 添加单元测试
chore(build): 更新构建脚本
```

## 8. 附录

### 8.1 常见缩写词汇表

为了保持命名的一致性，以下是一些常见的缩写词汇：

| 全称 | 缩写 |
| --- | --- |
| identification | id |
| password | pwd |
| sequence | seq |
| administrator | admin |
| information | info |
| configuration | config |
| initialize | init |
| maximum | max |
| minimum | min |
| message | msg |
| parameter | param |
| database | db |
| value | val |
| reference | ref |
| object | obj |
| package | pkg |
| description | desc |
| error | err |
| source | src |
| destination | dest |
| directory | dir |
| document | doc |
| extension | ext |
| function | func |
| implementation | impl |
| interface | intf |
| number | num |
| position | pos |
| previous | prev |
| property | prop |
| resource | res |
| result | res |
| statistics | stats |
| string | str |
| temporary | temp |
| utility | util |

### 8.2 命名规范检查工具

为了确保命名规范的一致性，可以使用以下工具进行检查：

1. **Java代码检查工具**：
   - CheckStyle：检查Java代码是否符合编码规范
   - PMD：检查Java代码中的潜在问题
   - SonarQube：代码质量管理平台

2. **JavaScript代码检查工具**：
   - ESLint：检查JavaScript/TypeScript代码是否符合编码规范
   - Prettier：代码格式化工具

3. **数据库命名检查工具**：
   - SchemaSpy：生成数据库文档，可以检查命名是否一致
   - Flyway：数据库版本控制工具，可以检查SQL脚本中的命名

4. **Git提交信息检查工具**：
   - commitlint：检查Git提交信息是否符合规范
   - husky：Git钩子工具，可以在提交前运行检查

通过使用这些工具，可以自动化检查命名规范，提高代码质量和一致性。