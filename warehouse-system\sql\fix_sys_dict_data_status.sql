-- =====================================================
-- 修复sys_dict_data表中的状态字典数据
-- 
-- 问题描述：
-- sys_normal_disable字典的标签和值需要与新标准保持一致
-- 确保前端显示正确的状态含义
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 步骤1：备份字典数据
CREATE TABLE IF NOT EXISTS sys_dict_data_backup_before_fix AS 
SELECT * FROM sys_dict_data WHERE dict_type = 'sys_normal_disable';

-- 步骤2：显示修复前的字典数据
SELECT 
    'sys_normal_disable修复前状态' as check_name,
    dict_type,
    dict_label,
    dict_value,
    dict_sort,
    list_class,
    is_default
FROM sys_dict_data 
WHERE dict_type = 'sys_normal_disable'
ORDER BY dict_sort;

-- 步骤3：更新字典数据以符合新标准
-- 0=正常/启用，1=停用/禁用

-- 更新 dict_value='0' 的记录（正常状态）
UPDATE sys_dict_data 
SET 
    dict_label = '正常',
    list_class = 'primary',
    is_default = 'Y',
    remark = '正常状态'
WHERE dict_type = 'sys_normal_disable' 
    AND dict_value = '0';

-- 更新 dict_value='1' 的记录（停用状态）  
UPDATE sys_dict_data 
SET 
    dict_label = '停用',
    list_class = 'danger',
    is_default = 'N',
    remark = '停用状态'
WHERE dict_type = 'sys_normal_disable' 
    AND dict_value = '1';

-- 步骤4：如果字典记录不存在，则插入标准记录
INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, css_class, list_class, 
    is_default, status, create_by, create_time, remark
) VALUES 
(1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', NOW(), '正常状态'),
(2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', NOW(), NOW(), '停用状态');

-- 步骤5：验证修复结果
SELECT 
    'sys_normal_disable修复后状态' as check_name,
    dict_type,
    dict_label,
    dict_value,
    dict_sort,
    list_class,
    is_default,
    remark
FROM sys_dict_data 
WHERE dict_type = 'sys_normal_disable'
ORDER BY dict_sort;

-- 步骤6：检查其他可能需要修复的状态字典
SELECT 
    '其他状态字典检查' as check_name,
    dict_type,
    COUNT(*) as record_count,
    GROUP_CONCAT(CONCAT(dict_value, '=', dict_label) ORDER BY dict_sort) as value_label_pairs
FROM sys_dict_data 
WHERE dict_type LIKE '%status%' 
    OR dict_type LIKE '%disable%'
    OR dict_type LIKE '%enable%'
GROUP BY dict_type
ORDER BY dict_type;

-- 提交事务
COMMIT;

-- 最终成功提示
SELECT 
    '=== sys_dict_data状态字典修复完成 ===' as message,
    '修复内容：sys_normal_disable字典 0=正常，1=停用' as fix_content,
    '备份表：sys_dict_data_backup_before_fix' as backup_table,
    NOW() as completion_time;