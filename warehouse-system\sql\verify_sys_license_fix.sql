-- =====================================================
-- sys_license表字段标准化修复验证脚本
-- 
-- 用途：验证sys_license表status字段修复是否成功
-- =====================================================

-- 1. 验证表结构是否正确
SELECT 
    '=== 表结构验证 ===' as section,
    COLUMN_NAME as field_name,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment,
    DATA_TYPE as data_type
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
    AND TABLE_NAME = 'sys_license' 
    AND COLUMN_NAME = 'status';

-- 2. 验证数据分布是否合理
SELECT 
    '=== 数据分布验证 ===' as section,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as enabled_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as disabled_count,
    SUM(CASE WHEN status NOT IN ('0', '1') THEN 1 ELSE 0 END) as invalid_count,
    CASE 
        WHEN SUM(CASE WHEN status NOT IN ('0', '1') THEN 1 ELSE 0 END) = 0 
        THEN '数据有效性：通过' 
        ELSE '数据有效性：失败' 
    END as validity_check
FROM sys_license;

-- 3. 验证业务逻辑是否正确
SELECT 
    '=== 业务逻辑验证 ===' as section,
    license_id,
    company_name,
    license_type,
    status,
    current,
    CASE 
        WHEN current = 1 AND status = '0' THEN '✓ 正确：当前许可证已启用'
        WHEN current = 1 AND status = '1' THEN '✗ 错误：当前许可证被禁用'
        WHEN current = 0 AND status = '0' THEN '✓ 正确：非当前许可证已启用'
        WHEN current = 0 AND status = '1' THEN '✓ 正确：非当前许可证已禁用'
        ELSE '? 状态异常'
    END as logic_check
FROM sys_license
ORDER BY current DESC, license_id;

-- 4. 验证备份表是否存在
SELECT 
    '=== 备份验证 ===' as section,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system' AND table_name = 'sys_license_backup_before_fix')
        THEN '✓ 备份表存在'
        ELSE '✗ 备份表不存在'
    END as backup_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system' AND table_name = 'sys_license_backup_before_fix')
        THEN (SELECT COUNT(*) FROM sys_license_backup_before_fix)
        ELSE 0
    END as backup_record_count;

-- 5. 对比修复前后的数据
SELECT 
    '=== 修复前后对比 ===' as section,
    'sys_license当前数据' as table_name,
    COUNT(*) as record_count,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as status_0_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as status_1_count
FROM sys_license

UNION ALL

SELECT 
    '=== 修复前后对比 ===' as section,
    'sys_license_backup_before_fix备份数据' as table_name,
    COUNT(*) as record_count,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as status_0_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as status_1_count
FROM sys_license_backup_before_fix
WHERE license_id != 999999;  -- 排除日志记录

-- 6. 综合验证结果
SELECT 
    '=== 综合验证结果 ===' as section,
    CASE 
        WHEN (
            -- 检查字段定义
            (SELECT COLUMN_DEFAULT FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE TABLE_SCHEMA = 'warehouse_system' AND TABLE_NAME = 'sys_license' AND COLUMN_NAME = 'status') = '0'
            AND 
            (SELECT COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE TABLE_SCHEMA = 'warehouse_system' AND TABLE_NAME = 'sys_license' AND COLUMN_NAME = 'status') LIKE '%0正常%1停用%'
            AND
            -- 检查数据有效性
            (SELECT SUM(CASE WHEN status NOT IN ('0', '1') THEN 1 ELSE 0 END) FROM sys_license) = 0
            AND
            -- 检查备份存在
            EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system' AND table_name = 'sys_license_backup_before_fix')
        )
        THEN '✓ 修复成功：所有验证通过'
        ELSE '✗ 修复失败：存在问题'
    END as overall_result,
    NOW() as check_time;