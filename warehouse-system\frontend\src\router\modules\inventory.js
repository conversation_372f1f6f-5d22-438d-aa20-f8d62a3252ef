import Layout from '@/layout'

const inventoryRouter = {
  path: '/inventory',
  component: Layout,
  redirect: '/inventory/in',
  name: 'Inventory',
  meta: {
    title: '库存管理',
    icon: 'excel'
  },
  children: [
    {
      path: 'in',
      component: () => import('@/views/inventory/in/index'),
      name: 'InventoryIn',
      meta: { title: '入库管理', icon: 'in' }
    },
    {
      path: 'in-print',
      component: () => import('@/views/inventory/in/print'),
      name: 'InventoryInPrint',
      meta: { title: '入库单打印', icon: 'print' },
      hidden: true
    },
    {
      path: 'out',
      component: () => import('@/views/inventory/out/index'),
      name: 'InventoryOut',
      meta: { title: '出库管理', icon: 'out' }
    },
    {
      path: 'out-print',
      component: () => import('@/views/inventory/out/print'),
      name: 'InventoryOutPrint',
      meta: { title: '出库单打印', icon: 'print' },
      hidden: true
    },
    {
      path: 'check',
      component: () => import('@/views/inventory/check/index'),
      name: 'InventoryCheck',
      meta: { title: '库存盘点', icon: 'check' }
    },
    {
      path: 'check-print',
      component: () => import('@/views/inventory/check/print'),
      name: 'InventoryCheckPrint',
      meta: { title: '盘点单打印', icon: 'print' },
      hidden: true
    },
    {
      path: 'transfer',
      component: () => import('@/views/inventory/transfer/index'),
      name: 'InventoryTransfer',
      meta: { title: '库存调拨', icon: 'transfer' }
    },
    {
      path: 'transfer-print',
      component: () => import('@/views/inventory/transfer/print'),
      name: 'InventoryTransferPrint',
      meta: { title: '调拨单打印', icon: 'print' },
      hidden: true
    },
    {
      path: 'purchase',
      component: () => import('@/views/inventory/purchase/index'),
      name: 'Purchase',
      meta: { title: '申购管理', icon: 'purchase', noCache: true }
    },
    {
      path: 'purchase-print/:requestId',
      component: () => import('@/views/inventory/purchase/print'),
      name: 'PurchasePrint',
      meta: { title: '申购单打印', icon: 'print' },
      hidden: true
    }
  ]
}
export default inventoryRouter