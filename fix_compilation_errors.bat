@echo off
chcp 65001 >nul
echo ========================================
echo 修复编译错误
echo ========================================

echo.
echo 1. 清理编译缓存...
cd /d "C:\CKGLXT\warehouse-system\backend"
if exist "target" (
    rmdir /s /q "target"
    echo 已清理后端target目录
)

echo.
echo 2. 重新编译项目...
call mvn clean compile -Dmaven.test.skip=true
if %errorlevel% equ 0 (
    echo 编译成功！
) else (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 3. 验证编译结果...
if exist "target\classes" (
    echo 编译文件生成成功
) else (
    echo 编译文件生成失败
    exit /b 1
)

echo.
echo ========================================
echo 编译错误修复完成！
echo ========================================
pause