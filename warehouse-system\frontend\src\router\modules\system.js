import Layout from '@/layout'

// 系统管理路由
export default {
  path: '/system',
  component: Layout,
  redirect: '/system/user',
  name: 'SystemModule',
  meta: {
    title: '系统管理',
    icon: 'system'
  },
  children: [
    {
      path: 'user',
      component: () => import('@/views/system/user/index'),
      name: 'SystemUser',
      meta: { title: '用户管理', icon: 'user' }
    },
    {
      path: 'role',
      component: () => import('@/views/system/role/index'),
      name: 'SystemRole',
      meta: { title: '角色管理', icon: 'peoples' }
    },
    {
      path: 'menu',
      component: () => import('@/views/system/menu/index'),
      name: 'SystemMenu',
      meta: { title: '菜单管理', icon: 'tree-table' }
    },
    {
      path: 'dept',
      component: () => import('@/views/system/dept/index'),
      name: 'SystemDept',
      meta: { title: '部门管理', icon: 'tree' }
    },
    {
      path: 'dept-warehouse',
      component: () => import('@/views/system/dept/dept-warehouse-permission'),
      name: 'SystemDeptWarehouse',
      meta: { title: '部门仓库权限', icon: 'warehouse' }
    },
    {
      path: 'dict',
      component: () => import('@/views/system/dict/index'),
      name: 'SystemDict',
      meta: { title: '字典管理', icon: 'dict' }
    },
    {
      path: 'dict-data/index/:dictId(\\d+)',
      component: () => import('@/views/system/dict/data'),
      name: 'SystemData',
      meta: { title: '字典数据', icon: 'dict', activeMenu: '/system/dict' },
      hidden: true
    },
    {
      path: 'login-style',
      component: () => import('@/views/system/login-style/index'),
      name: 'SystemLoginStyle',
      meta: { title: '登录方式管理', icon: 'user' }
    },
    {
      path: 'config',
      component: () => import('@/views/system/config/index'),
      name: 'SystemConfig',
      meta: { title: '参数设置', icon: 'edit' }
    },
    {
      path: 'notice',
      component: () => import('@/views/system/notice/index'),
      name: 'SystemNotice',
      meta: { title: '通知公告', icon: 'message' }
    },

    {
      path: 'permission',
      component: () => import('@/views/system/permission/index'),
      name: 'SystemPermission',
      meta: { title: '权限管理', icon: 'validCode' },
      redirect: '/system/permission/role',
      children: [

        {
          path: 'assignment',
          component: () => import('@/views/system/permission/PermissionAssignment'),
          name: 'SystemPermissionAssignment',
          meta: { title: '权限分配', icon: 'tree-table' }
        },

      ]
    },
    {
      path: 'ssl',
      component: () => import('@/views/system/ssl/index'),
      name: 'SystemSsl',
      meta: { title: 'SSL证书管理', icon: 'lock' }
    },
    {
      path: 'role-warehouse/:roleId/:roleName',
      component: () => import('@/views/system/role/role-warehouse.vue'),
      name: 'SystemRoleWarehouse',
      meta: { title: '分配仓库', icon: 's-home' },
      hidden: true
    }
  ]
}