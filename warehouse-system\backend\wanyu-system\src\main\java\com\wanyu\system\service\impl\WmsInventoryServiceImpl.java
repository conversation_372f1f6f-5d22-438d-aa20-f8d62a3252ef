package com.wanyu.system.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wanyu.common.annotation.WarehouseScope;
import com.wanyu.common.exception.ServiceException;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.common.core.domain.model.LoginUser;
import com.wanyu.system.domain.WmsInventory;
import com.wanyu.system.domain.vo.ProductStockVO;
import com.wanyu.system.domain.vo.WarehouseStockVO;
import com.wanyu.system.mapper.WmsInventoryMapper;
import com.wanyu.system.mapper.WmsWarehouseMapper;
import com.wanyu.system.service.IWmsInventoryLogService;
import com.wanyu.system.service.IWmsInventoryService;
import com.wanyu.system.service.IWhInventoryAlertRuleService;
import com.wanyu.system.service.IWhInventoryAlertLogService;
import com.wanyu.system.domain.WhInventoryAlertRule;
import com.wanyu.system.domain.WhInventoryAlertLog;

/**
 * 库存信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class WmsInventoryServiceImpl implements IWmsInventoryService
{
    private static final Logger logger = LoggerFactory.getLogger(WmsInventoryServiceImpl.class);

    @Autowired
    private WmsInventoryMapper wmsInventoryMapper;

    @Autowired
    private WmsWarehouseMapper wmsWarehouseMapper;
    
    @Autowired
    private IWmsInventoryLogService wmsInventoryLogService;
    
    @Autowired
    private IWhInventoryAlertRuleService whInventoryAlertRuleService;
    
    @Autowired
    private IWhInventoryAlertLogService whInventoryAlertLogService;

    /**
     * 查询库存信息
     *
     * @param inventoryId 库存信息主键
     * @return 库存信息
     */
    @Override
    public WmsInventory selectWmsInventoryByInventoryId(Long inventoryId)
    {
        return wmsInventoryMapper.selectWmsInventoryByInventoryId(inventoryId);
    }

    /**
     * 查询库存信息列表
     *
     * @param wmsInventory 库存信息
     * @return 库存信息
     */
    @Override
    @WarehouseScope(warehouseAlias = "w")
    public List<WmsInventory> selectWmsInventoryList(WmsInventory wmsInventory)
    {
        // 获取当前登录用户，加容错，导出接口无token时不过滤
        boolean isAdmin = true;
        Long userId = null;
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && loginUser.getUser() != null) {
                userId = loginUser.getUserId();
                isAdmin = userId == 1L
                    || loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()));
            }
        } catch (Exception e) {
            // 获取不到用户信息，默认超级管理员逻辑（不过滤）
            isAdmin = true;
        }
        if (!isAdmin && userId != null) {
            // 非超级管理员，只返回授权仓库
            wmsInventory.getParams().put("warehouseScope", " AND w.warehouse_id IN (SELECT warehouse_id FROM sys_user_warehouse WHERE user_id = " + userId + ") ");
        }
        List<WmsInventory> result = wmsInventoryMapper.selectWmsInventoryList(wmsInventory);
        logger.info("Query returned {} inventory records", result.size());
        return result;
    }

    /**
     * 查询库存预警信息列表
     *
     * @param wmsInventory 库存信息
     * @return 库存信息
     */
    @Override
    public List<WmsInventory> selectWmsInventoryAlertList(WmsInventory wmsInventory)
    {
        return wmsInventoryMapper.selectWmsInventoryAlertList(wmsInventory);
    }

    /**
     * 新增库存信息
     *
     * @param wmsInventory 库存信息
     * @return 结果
     */
    @Override
    public int insertWmsInventory(WmsInventory wmsInventory)
    {
        // 检查是否已存在相同产品和仓库的库存记录
        WmsInventory existingInventory = null;
        try {
            existingInventory = wmsInventoryMapper.selectWmsInventoryByProductIdAndWarehouseId(
                wmsInventory.getProductId(), wmsInventory.getWarehouseId());
        } catch (Exception e) {
            // 如果查询出现异常（比如多条记录），记录日志但继续处理
            logger.warn("查询现有库存记录时出现异常: {}", e.getMessage());
        }
        
        if (existingInventory != null) {
            // 如果已存在记录，则更新现有记录而不是新增
            logger.info("库存记录已存在，更新现有记录。产品ID: {}, 仓库ID: {}", 
                wmsInventory.getProductId(), wmsInventory.getWarehouseId());
            
            // 累加数量
            BigDecimal newQuantity = existingInventory.getQuantity().add(wmsInventory.getQuantity());
            existingInventory.setQuantity(newQuantity);
            
            // 更新阈值（如果新记录有设置的话）
            if (wmsInventory.getMinQuantity() != null && wmsInventory.getMinQuantity().compareTo(BigDecimal.ZERO) > 0) {
                existingInventory.setMinQuantity(wmsInventory.getMinQuantity());
            }
            if (wmsInventory.getMaxQuantity() != null && wmsInventory.getMaxQuantity().compareTo(BigDecimal.ZERO) > 0) {
                existingInventory.setMaxQuantity(wmsInventory.getMaxQuantity());
            }
            
            // 更新备注
            if (wmsInventory.getRemark() != null && !wmsInventory.getRemark().isEmpty()) {
                String newRemark = existingInventory.getRemark() != null ? 
                    existingInventory.getRemark() + "; " + wmsInventory.getRemark() : 
                    wmsInventory.getRemark();
                existingInventory.setRemark(newRemark);
            }
            
            // 设置库存状态
            updateInventoryStatus(existingInventory);
            
            return wmsInventoryMapper.updateWmsInventory(existingInventory);
        } else {
            // 不存在记录，正常新增
            updateInventoryStatus(wmsInventory);
            return wmsInventoryMapper.insertWmsInventory(wmsInventory);
        }
    }

    /**
     * 修改库存信息
     *
     * @param wmsInventory 库存信息
     * @return 结果
     */
    @Override
    public int updateWmsInventory(WmsInventory wmsInventory)
    {
        // 获取修改前的库存信息
        WmsInventory oldInventory = wmsInventoryMapper.selectWmsInventoryByInventoryId(wmsInventory.getInventoryId());
        BigDecimal beforeQuantity = (oldInventory != null) ? oldInventory.getQuantity() : BigDecimal.ZERO;
        
        // 设置库存状态
        updateInventoryStatus(wmsInventory);
        int result = wmsInventoryMapper.updateWmsInventory(wmsInventory);
        
        // 如果库存数量发生变化，记录日志并检查预警
        if (result > 0 && wmsInventory.getQuantity() != null && 
            (oldInventory == null || !wmsInventory.getQuantity().equals(beforeQuantity))) {
            
            // 记录库存变动日志
            wmsInventoryLogService.recordInventoryLog(
                wmsInventory.getWarehouseId(),
                wmsInventory.getProductId(),
                "ADJUST", // 库存调整
                wmsInventory.getQuantity(),
                beforeQuantity,
                wmsInventory.getQuantity(),
                null, // 关联单据ID
                null, // 关联单据类型
                SecurityUtils.getUsername(),
                "系统自动记录-直接修改库存"
            );
            
            // 检查库存预警
            checkInventoryAlert(wmsInventory.getWarehouseId(), wmsInventory.getProductId(), wmsInventory.getQuantity());
        }
        
        return result;
    }

    /**
     * 批量删除库存信息
     *
     * @param inventoryIds 需要删除的库存信息主键
     * @return 结果
     */
    @Override
    public int deleteWmsInventoryByInventoryIds(Long[] inventoryIds)
    {
        return wmsInventoryMapper.deleteWmsInventoryByInventoryIds(inventoryIds);
    }

    /**
     * 删除库存信息信息
     *
     * @param inventoryId 库存信息主键
     * @return 结果
     */
    @Override
    public int deleteWmsInventoryByInventoryId(Long inventoryId)
    {
        return wmsInventoryMapper.deleteWmsInventoryByInventoryId(inventoryId);
    }

    /**
     * 根据物品ID和仓库ID查询库存信息
     *
     * @param productId 物品ID
     * @param warehouseId 仓库ID
     * @return 库存信息
     */
    @Override
    public WmsInventory selectWmsInventoryByProductIdAndWarehouseId(Long productId, Long warehouseId)
    {
        return wmsInventoryMapper.selectWmsInventoryByProductIdAndWarehouseId(productId, warehouseId);
    }

    /**
     * 更新库存数量
     *
     * @param inventoryId 库存ID
     * @param quantity 变更数量（正数为增加，负数为减少）
     * @return 结果
     */
    @Override
    public int updateWmsInventoryQuantity(Long inventoryId, BigDecimal quantity)
    {
        int rows = wmsInventoryMapper.updateWmsInventoryQuantity(inventoryId, quantity);
        if (rows > 0)
        {
            // 更新库存状态
            WmsInventory inventory = wmsInventoryMapper.selectWmsInventoryByInventoryId(inventoryId);
            updateInventoryStatus(inventory);
            wmsInventoryMapper.updateWmsInventory(inventory);
        }
        return rows;
    }

    /**
     * 入库操作
     *
     * @param productId 物品ID
     * @param warehouseId 仓库ID
     * @param quantity 入库数量
     * @return 结果
     */
    @Override
    public int inStock(Long productId, Long warehouseId, BigDecimal quantity)
    {
        WmsInventory inventory = null;
        try {
            inventory = wmsInventoryMapper.selectWmsInventoryByProductIdAndWarehouseId(productId, warehouseId);
        } catch (Exception e) {
            logger.error("查询库存记录时出现异常: {}", e.getMessage());
        }
        
        BigDecimal beforeQuantity = BigDecimal.ZERO;
        BigDecimal afterQuantity;
        int result;
        
        if (inventory != null)
        {
            // 更新库存
            beforeQuantity = inventory.getQuantity();
            afterQuantity = beforeQuantity.add(quantity);
            result = updateWmsInventoryQuantity(inventory.getInventoryId(), quantity);
        }
        else
        {
            // 新增库存
            inventory = new WmsInventory();
            inventory.setProductId(productId);
            inventory.setWarehouseId(warehouseId);
            inventory.setQuantity(quantity);
            inventory.setMinQuantity(new BigDecimal(0));
            inventory.setMaxQuantity(new BigDecimal(0));
            inventory.setStatus("0");
            afterQuantity = quantity;
            result = insertWmsInventory(inventory);
        }
        
        // 记录库存变动日志
        wmsInventoryLogService.recordInventoryLog(
            warehouseId,
            productId,
            "IN", // 入库操作
            quantity,
            beforeQuantity,
            afterQuantity,
            null, // 关联单据ID，可以根据实际情况传入
            null, // 关联单据类型，可以根据实际情况传入
            SecurityUtils.getUsername(),
            "系统自动记录-入库操作"
        );
        
        // 检查库存预警
        checkInventoryAlert(warehouseId, productId, afterQuantity);
        
        return result;
    }

    /**
     * 出库操作
     *
     * @param productId 物品ID
     * @param warehouseId 仓库ID
     * @param quantity 出库数量
     * @return 结果
     * @throws com.wanyu.common.exception.ServiceException 当库存不足或库存不存在时抛出异常
     */
    @Override
    public int outStock(Long productId, Long warehouseId, BigDecimal quantity)
    {
        WmsInventory inventory = null;
        try {
            inventory = wmsInventoryMapper.selectWmsInventoryByProductIdAndWarehouseId(productId, warehouseId);
        } catch (Exception e) {
            logger.error("查询库存记录时出现异常: {}", e.getMessage());
            throw new ServiceException("查询库存记录失败");
        }
        
        if (inventory != null)
        {
            // 检查库存是否足够
            if (inventory.getQuantity().compareTo(quantity) >= 0)
            {
                // 记录库存变动前的数量
                BigDecimal beforeQuantity = inventory.getQuantity();
                BigDecimal afterQuantity = beforeQuantity.subtract(quantity);
                
                // 更新库存
                int result = updateWmsInventoryQuantity(inventory.getInventoryId(), quantity.negate());
                
                // 记录库存变动日志
                wmsInventoryLogService.recordInventoryLog(
                    warehouseId,
                    productId,
                    "OUT", // 出库操作
                    quantity,
                    beforeQuantity,
                    afterQuantity,
                    null, // 关联单据ID，可以根据实际情况传入
                    null, // 关联单据类型，可以根据实际情况传入
                    SecurityUtils.getUsername(),
                    "系统自动记录-出库操作"
                );
        
                // 检查库存预警
                checkInventoryAlert(warehouseId, productId, afterQuantity);
        
                return result;
            }
            else
            {
                String errorMsg = String.format("库存不足，物品ID：%d，仓库ID：%d，当前库存：%s，请求出库：%s",
                    productId, warehouseId, inventory.getQuantity(), quantity);
                logger.error(errorMsg);
                throw new ServiceException(errorMsg);
            }
        }
        else
        {
            String errorMsg = String.format("库存不存在，物品ID：%d，仓库ID：%d", productId, warehouseId);
            logger.error(errorMsg);
            throw new ServiceException(errorMsg);
        }
    }

    /**
     * 更新库存状态
     *
     * @param inventory 库存信息
     */
    @Override
    public void updateInventoryStatus(WmsInventory inventory)
    {
        if (inventory.getQuantity() == null)
        {
            inventory.setStatus("0");
            return;
        }

        // 库存为0，设置为缺货状态
        if (inventory.getQuantity().compareTo(BigDecimal.ZERO) == 0)
        {
            inventory.setStatus("2");
            return;
        }

        // 库存小于最小库存量，设置为预警状态
        if (inventory.getMinQuantity() != null && inventory.getMinQuantity().compareTo(BigDecimal.ZERO) > 0
                && inventory.getQuantity().compareTo(inventory.getMinQuantity()) < 0)
        {
            inventory.setStatus("1");
            return;
        }

        // 库存大于最大库存量，设置为预警状态（超储）
        if (inventory.getMaxQuantity() != null && inventory.getMaxQuantity().compareTo(BigDecimal.ZERO) > 0
                && inventory.getQuantity().compareTo(inventory.getMaxQuantity()) > 0)
        {
            inventory.setStatus("3");
            return;
        }

        // 正常状态
        inventory.setStatus("0");
    }

    /**
     * 刷新库存状态
     *
     * @return 更新的记录数
     */
    @Override
    public int refreshInventoryStatus()
    {
        // 查询所有库存
        List<WmsInventory> inventoryList = wmsInventoryMapper.selectWmsInventoryList(new WmsInventory());
        int count = 0;

        // 更新库存状态
        for (WmsInventory inventory : inventoryList)
        {
            String oldStatus = inventory.getStatus();
            updateInventoryStatus(inventory);

            // 如果状态有变化，则更新库存
            if (!inventory.getStatus().equals(oldStatus))
            {
                wmsInventoryMapper.updateWmsInventory(inventory);
                count++;
            }
            
            // 检查库存预警（无论状态是否变化）
            checkInventoryAlert(inventory.getWarehouseId(), inventory.getProductId(), inventory.getQuantity());
        }

        return count;
    }

    /**
     * 获取库存统计数据
     *
     * @return 统计数据
     */
    @Override
    public Object getInventoryStatistics()
    {
        // 查询所有库存
        List<WmsInventory> inventoryList = wmsInventoryMapper.selectWmsInventoryList(new WmsInventory());

        // 统计数据
        BigDecimal totalQuantity = BigDecimal.ZERO;
        int totalProducts = 0;
        int warningCount = 0;
        int outOfStockCount = 0;

        // 计算统计数据
        if (inventoryList != null && !inventoryList.isEmpty())
        {
            totalProducts = inventoryList.size();

            for (WmsInventory inventory : inventoryList)
            {
                // 总库存数量
                if (inventory.getQuantity() != null)
                {
                    totalQuantity = totalQuantity.add(inventory.getQuantity());
                }

                // 预警数量
                if ("1".equals(inventory.getStatus()))
                {
                    warningCount++;
                }

                // 缺货数量
                if ("2".equals(inventory.getStatus()))
                {
                    outOfStockCount++;
                }
            }
        }

        // 返回统计数据
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("totalQuantity", totalQuantity);
        statistics.put("totalProducts", totalProducts);
        statistics.put("warningCount", warningCount);
        statistics.put("outOfStockCount", outOfStockCount);

        return statistics;
    }

    /**
     * 查询物品库存列表（按物品分组）
     *
     * @param wmsInventory 库存信息
     * @return 物品库存列表
     */
    @Override
    @WarehouseScope(warehouseAlias = "w")
    public List<ProductStockVO> selectProductStockList(WmsInventory wmsInventory)
    {
        // 查询库存列表 - 这里已经应用了仓库权限过滤，只会返回用户有权限的仓库数据
        List<WmsInventory> inventoryList = wmsInventoryMapper.selectWmsInventoryList(wmsInventory);

        // 按物品ID分组 - 只会包含用户有权限的仓库中的物品
        Map<Long, List<WmsInventory>> productInventoryMap = inventoryList.stream()
                .collect(Collectors.groupingBy(WmsInventory::getProductId));

        // 转换为物品库存视图对象
        List<ProductStockVO> productStockList = new ArrayList<>();

        for (Map.Entry<Long, List<WmsInventory>> entry : productInventoryMap.entrySet())
        {
            Long productId = entry.getKey();
            List<WmsInventory> productInventories = entry.getValue();

            // 创建物品库存视图对象
            ProductStockVO productStock = new ProductStockVO();
            productStock.setProductId(productId);

            // 设置物品信息
            if (!productInventories.isEmpty())
            {
                WmsInventory firstInventory = productInventories.get(0);
                productStock.setProductName(firstInventory.getProductName());
                productStock.setProductCode(firstInventory.getProductCode());
                productStock.setCategoryName(firstInventory.getCategoryName());
                productStock.setSpecName(firstInventory.getSpecification());
                productStock.setUnitName(firstInventory.getUnit());
            }

            // 计算总库存数量
            BigDecimal totalQuantity = BigDecimal.ZERO;
            List<WarehouseStockVO> warehouseStocks = new ArrayList<>();

            for (WmsInventory inventory : productInventories)
            {
                // 累加总库存数量
                if (inventory.getQuantity() != null)
                {
                    totalQuantity = totalQuantity.add(inventory.getQuantity());
                }

                // 创建仓库库存视图对象
                WarehouseStockVO warehouseStock = new WarehouseStockVO();
                warehouseStock.setInventoryId(inventory.getInventoryId());
                warehouseStock.setWarehouseId(inventory.getWarehouseId());
                warehouseStock.setWarehouseName(inventory.getWarehouseName());
                warehouseStock.setQuantity(inventory.getQuantity());
                warehouseStock.setMinQuantity(inventory.getMinQuantity());
                warehouseStock.setMaxQuantity(inventory.getMaxQuantity());
                warehouseStock.setStatus(inventory.getStatus());

                warehouseStocks.add(warehouseStock);
            }

            // 设置总库存数量和仓库库存列表
            productStock.setTotalQuantity(totalQuantity);
            productStock.setWarehouseStocks(warehouseStocks);

            productStockList.add(productStock);
        }

        return productStockList;
    }

    /**
     * 获取当前库存数量
     *
     * @param productId 物品ID
     * @param warehouseId 仓库ID
     * @return 当前库存数量
     */
    @Override
    public BigDecimal getCurrentStock(Long productId, Long warehouseId)
    {
        try {
            WmsInventory inventory = wmsInventoryMapper.selectWmsInventoryByProductIdAndWarehouseId(productId, warehouseId);
            if (inventory != null && inventory.getQuantity() != null) {
                return inventory.getQuantity();
            }
        } catch (Exception e) {
            logger.error("查询库存时出现异常，产品ID: {}, 仓库ID: {}, 错误: {}", 
                productId, warehouseId, e.getMessage());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 检查库存是否充足
     *
     * @param productId 物品ID
     * @param warehouseId 仓库ID
     * @param quantity 需要的数量
     * @return true:库存充足 false:库存不足
     */
    @Override
    public boolean checkStockAvailable(Long productId, Long warehouseId, BigDecimal quantity)
    {
        BigDecimal currentStock = getCurrentStock(productId, warehouseId);
        return currentStock.compareTo(quantity) >= 0;
    }
    
    /**
     * 检查库存预警
     * 
     * @param warehouseId 仓库ID
     * @param productId 商品ID
     * @param currentQuantity 当前库存数量
     */
    private void checkInventoryAlert(Long warehouseId, Long productId, BigDecimal currentQuantity) {
        try {
            // 查询该商品在该仓库的预警规则
            WhInventoryAlertRule rule = whInventoryAlertRuleService.selectWhInventoryAlertRuleByWarehouseAndGoods(warehouseId, productId);
            
            // 如果没有预警规则或规则已停用，则不进行预警检查
            if (rule == null || "1".equals(rule.getStatus())) {
                return;
            }
            
            java.util.Date now = com.wanyu.common.utils.DateUtils.getNowDate();
            String username = SecurityUtils.getUsername();
            
            // 检查是否低于最小库存
            if (currentQuantity.compareTo(rule.getMinQuantity()) < 0) {
                // 检查是否已存在未处理的库存不足预警
                boolean existingAlert = whInventoryAlertLogService.checkExistingAlert(warehouseId, productId, "1");
                if (!existingAlert) {
                    // 创建库存不足预警记录
                    WhInventoryAlertLog alertLog = new WhInventoryAlertLog();
                    alertLog.setRuleId(rule.getRuleId());
                    alertLog.setWarehouseId(warehouseId);
                    alertLog.setGoodsId(productId);
                    alertLog.setCurrentQuantity(currentQuantity);
                    alertLog.setThresholdValue(rule.getMinQuantity());
                    alertLog.setAlertType("1"); // 1-库存不足
                    alertLog.setAlertStatus("0"); // 0-未处理
                    alertLog.setNotifyTime(now);
                    alertLog.setCreateBy(username);
                    alertLog.setCreateTime(now);
                    whInventoryAlertLogService.insertWhInventoryAlertLog(alertLog);
                }
            }
            // 检查是否超过最大库存
            else if (currentQuantity.compareTo(rule.getMaxQuantity()) > 0) {
                // 检查是否已存在未处理的库存积压预警
                boolean existingAlert = whInventoryAlertLogService.checkExistingAlert(warehouseId, productId, "2");
                if (!existingAlert) {
                    // 创建库存积压预警记录
                    WhInventoryAlertLog alertLog = new WhInventoryAlertLog();
                    alertLog.setRuleId(rule.getRuleId());
                    alertLog.setWarehouseId(warehouseId);
                    alertLog.setGoodsId(productId);
                    alertLog.setCurrentQuantity(currentQuantity);
                    alertLog.setThresholdValue(rule.getMaxQuantity());
                    alertLog.setAlertType("2"); // 2-库存积压
                    alertLog.setAlertStatus("0"); // 0-未处理
                    alertLog.setNotifyTime(now);
                    alertLog.setCreateBy(username);
                    alertLog.setCreateTime(now);
                    whInventoryAlertLogService.insertWhInventoryAlertLog(alertLog);
                }
            }
        } catch (Exception e) {
            // 预警检查出错不应影响正常的库存操作，记录错误日志
            logger.error("库存预警检查失败: {}", e.getMessage());
        }
    }
}