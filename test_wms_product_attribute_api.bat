@echo off
echo 测试WMS物品属性API功能...
echo.

echo 1. 测试获取物品属性列表
curl -X GET "http://localhost:8080/product/attribute/list" -H "Content-Type: application/json"
echo.
echo.

echo 2. 测试获取特定属性详情（ID=1）
curl -X GET "http://localhost:8080/product/attribute/1" -H "Content-Type: application/json"
echo.
echo.

echo 3. 测试获取颜色属性选项（ID=1）
curl -X GET "http://localhost:8080/product/attribute/options/1" -H "Content-Type: application/json"
echo.
echo.

echo 4. 测试获取尺寸属性选项（ID=2）
curl -X GET "http://localhost:8080/product/attribute/options/2" -H "Content-Type: application/json"
echo.
echo.

echo 5. 测试按属性类型过滤（选项型）
curl -X GET "http://localhost:8080/product/attribute/list?attributeType=1" -H "Content-Type: application/json"
echo.
echo.

echo 6. 测试按是否必填过滤
curl -X GET "http://localhost:8080/product/attribute/list?isRequired=Y" -H "Content-Type: application/json"
echo.
echo.

echo 7. 测试按是否可搜索过滤
curl -X GET "http://localhost:8080/product/attribute/list?isSearchable=Y" -H "Content-Type: application/json"
echo.
echo.

echo WMS物品属性API测试完成！
pause