@echo off
echo ========================================
echo 字段标准化基础设施 - 数据备份和验证
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_PASS=123456
set DB_NAME=warehouse_system

echo [1/4] 开始执行数据库备份...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% < ..\sql\backup_database.sql
if %errorlevel% neq 0 (
    echo 错误: 数据库备份失败
    pause
    exit /b 1
)
echo 数据库备份完成

echo.
echo [2/4] 验证备份完整性...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% < ..\sql\verify_backup.sql
if %errorlevel% neq 0 (
    echo 错误: 备份验证失败
    pause
    exit /b 1
)
echo 备份验证完成

echo.
echo [3/4] 检查字段标准合规性...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% < ..\sql\check_field_standards.sql
if %errorlevel% neq 0 (
    echo 错误: 字段标准检查失败
    pause
    exit /b 1
)
echo 字段标准检查完成

echo.
echo [4/4] 生成字段合规报告...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% < ..\sql\generate_field_report.sql > ..\reports\field_compliance_report_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
if %errorlevel% neq 0 (
    echo 错误: 报告生成失败
    pause
    exit /b 1
)
echo 字段合规报告已生成

echo.
echo ========================================
echo 字段标准化基础设施创建完成！
echo ========================================
echo.
echo 已完成的工作:
echo - 创建了完整的数据库备份
echo - 验证了备份的完整性和正确性
echo - 检查了当前字段定义的标准合规性
echo - 生成了详细的合规性报告
echo.
echo 下一步操作:
echo 1. 查看生成的合规性报告
echo 2. 根据报告中的建议执行字段标准化修复
echo 3. 使用FieldStandardValidator工具类进行代码验证
echo.
pause