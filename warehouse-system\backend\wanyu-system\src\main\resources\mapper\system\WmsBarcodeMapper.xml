<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsBarcodeMapper">
    
    <resultMap type="WmsBarcode" id="WmsBarcodeResult">
        <result property="barcodeId"       column="barcode_id"       />
        <result property="productId"       column="product_id"       />
        <result property="productName"     column="product_name"     />
        <result property="barcodeContent"  column="barcode_content"  />
        <result property="barcodeType"     column="barcode_type"     />
        <result property="barcodeImage"    column="barcode_image"    />
        <result property="templateId"      column="template_id"      />
        <result property="isMain"          column="is_main"          />
        <result property="status"          column="status"           />
        <result property="remark"          column="remark"           />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
    </resultMap>

    <sql id="selectWmsBarcodeVo">
        select barcode_id, product_id, product_name, barcode_content, barcode_type, barcode_image, template_id, is_main, status, remark, create_by, create_time, update_by, update_time from wms_barcode
    </sql>

    <select id="selectWmsBarcodeList" parameterType="WmsBarcode" resultMap="WmsBarcodeResult">
        <include refid="selectWmsBarcodeVo"/>
        <where>  
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="barcodeContent != null  and barcodeContent != ''"> and barcode_content like concat('%', #{barcodeContent}, '%')</if>
            <if test="barcodeType != null  and barcodeType != ''"> and barcode_type = #{barcodeType}</if>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="isMain != null  and isMain != ''"> and is_main = #{isMain}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWmsBarcodeByBarcodeId" parameterType="Long" resultMap="WmsBarcodeResult">
        <include refid="selectWmsBarcodeVo"/>
        where barcode_id = #{barcodeId}
    </select>

    <select id="selectWmsBarcodeByContent" parameterType="String" resultMap="WmsBarcodeResult">
        <include refid="selectWmsBarcodeVo"/>
        where barcode_content = #{barcodeContent}
    </select>

    <select id="selectWmsBarcodeByProductId" parameterType="Long" resultMap="WmsBarcodeResult">
        <include refid="selectWmsBarcodeVo"/>
        where product_id = #{productId}
        order by is_main desc, create_time desc
    </select>
        
    <insert id="insertWmsBarcode" parameterType="WmsBarcode" useGeneratedKeys="true" keyProperty="barcodeId">
        insert into wms_barcode
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="barcodeContent != null and barcodeContent != ''">barcode_content,</if>
            <if test="barcodeType != null and barcodeType != ''">barcode_type,</if>
            <if test="barcodeImage != null">barcode_image,</if>
            <if test="templateId != null">template_id,</if>
            <if test="isMain != null">is_main,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="barcodeContent != null and barcodeContent != ''">#{barcodeContent},</if>
            <if test="barcodeType != null and barcodeType != ''">#{barcodeType},</if>
            <if test="barcodeImage != null">#{barcodeImage},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="isMain != null">#{isMain},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateWmsBarcode" parameterType="WmsBarcode">
        update wms_barcode
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="barcodeContent != null and barcodeContent != ''">barcode_content = #{barcodeContent},</if>
            <if test="barcodeType != null and barcodeType != ''">barcode_type = #{barcodeType},</if>
            <if test="barcodeImage != null">barcode_image = #{barcodeImage},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="isMain != null">is_main = #{isMain},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where barcode_id = #{barcodeId}
    </update>

    <delete id="deleteWmsBarcodeByBarcodeId" parameterType="Long">
        delete from wms_barcode where barcode_id = #{barcodeId}
    </delete>

    <delete id="deleteWmsBarcodeByBarcodeIds" parameterType="String">
        delete from wms_barcode where barcode_id in 
        <foreach item="barcodeId" collection="array" open="(" separator="," close=")">
            #{barcodeId}
        </foreach>
    </delete>

    <insert id="batchInsertWmsBarcode" parameterType="java.util.List">
        insert into wms_barcode (product_id, product_name, barcode_content, barcode_type, barcode_image, template_id, is_main, status, remark, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productId}, #{item.productName}, #{item.barcodeContent}, #{item.barcodeType}, #{item.barcodeImage}, #{item.templateId}, #{item.isMain}, #{item.status}, #{item.remark}, #{item.createBy}, sysdate())
        </foreach>
    </insert>

</mapper>