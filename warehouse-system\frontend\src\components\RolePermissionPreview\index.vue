<template>
  <div class="role-permission-preview">
    <el-card class="preview-card">
      <div slot="header" class="clearfix">
        <span>{{ title || '角色权限预览' }}</span>
        <el-tooltip :content="description || '显示角色拥有的权限'" placement="top">
          <i class="el-icon-question" style="margin-left: 5px;"></i>
        </el-tooltip>
      </div>
      <div class="permission-content" v-loading="loading">
        <template v-if="!loading && permissions.length > 0">
          <!-- 权限统计 -->
          <div class="permission-stats">
            <el-row :gutter="10">
              <el-col :span="8">
                <div class="stat-item" :style="{ backgroundColor: '#ecf5ff', color: '#409EFF' }">
                  <div class="stat-icon"><i class="el-icon-menu"></i></div>
                  <div class="stat-info">
                    <div class="stat-value">{{ countMenuPermissions }}</div>
                    <div class="stat-label">菜单权限</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item" :style="{ backgroundColor: '#f0f9eb', color: '#67C23A' }">
                  <div class="stat-icon"><i class="el-icon-s-operation"></i></div>
                  <div class="stat-info">
                    <div class="stat-value">{{ operationPermissions.length }}</div>
                    <div class="stat-label">操作权限</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item" :style="{ backgroundColor: '#fdf6ec', color: '#E6A23C' }">
                  <div class="stat-icon"><i class="el-icon-s-data"></i></div>
                  <div class="stat-info">
                    <div class="stat-value">{{ dataPermissions.length + apiPermissions.length + warehousePermissions.length }}</div>
                    <div class="stat-label">其他权限</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 权限分类展示 -->
          <el-tabs type="border-card" class="permission-tabs">
            <el-tab-pane label="菜单权限">
              <el-tree
                :data="menuPermissions"
                :props="defaultProps"
                default-expand-all
                node-key="id"
                highlight-current
                :expand-on-click-node="false"
              >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <span>{{ node.label }}</span>
                </span>
              </el-tree>
            </el-tab-pane>

            <el-tab-pane label="操作权限">
              <div class="permission-tags">
                <el-tag
                  v-for="perm in operationPermissions"
                  :key="perm.id"
                  :type="getPermissionTagType(perm.type)"
                  class="permission-tag"
                >
                  {{ perm.label }}
                </el-tag>
              </div>
            </el-tab-pane>

            <el-tab-pane label="数据权限">
              <div class="permission-tags">
                <el-tag
                  v-for="perm in dataPermissions"
                  :key="perm.id"
                  :type="getPermissionTagType(perm.type)"
                  class="permission-tag"
                >
                  {{ perm.label }}
                </el-tag>
                <div v-if="dataPermissions.length === 0" class="empty-tab-content">
                  <i class="el-icon-info"></i>
                  <p>没有数据权限</p>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="API权限">
              <div class="permission-tags">
                <el-tag
                  v-for="perm in apiPermissions"
                  :key="perm.id"
                  :type="getPermissionTagType(perm.type)"
                  class="permission-tag"
                >
                  {{ perm.label }}
                </el-tag>
                <div v-if="apiPermissions.length === 0" class="empty-tab-content">
                  <i class="el-icon-info"></i>
                  <p>没有API权限</p>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="仓库权限">
              <div class="permission-tags">
                <el-tag
                  v-for="perm in warehousePermissions"
                  :key="perm.id"
                  :type="getPermissionTagType(perm.type)"
                  class="permission-tag"
                >
                  {{ perm.label }}
                </el-tag>
                <div v-if="warehousePermissions.length === 0" class="empty-tab-content">
                  <i class="el-icon-info"></i>
                  <p>没有仓库权限</p>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </template>
        <div v-else-if="!loading && permissions.length === 0" class="empty-preview">
          <i class="el-icon-warning"></i>
          <p>该角色没有任何权限</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getRolePermissions } from "@/api/system/permission";

export default {
  name: "RolePermissionPreview",
  props: {
    roleId: {
      type: [Number, String],
      required: true
    },
    title: {
      type: String,
      default: ""
    },
    description: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      loading: false,
      permissions: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    };
  },
  computed: {
    menuPermissions() {
      return this.formatMenuPermissions(this.permissions.filter(p => p.startsWith('system:') || p.startsWith('monitor:') || p.startsWith('tool:')));
    },
    countMenuPermissions() {
      // 计算菜单权限的数量（包括子菜单）
      const countNodes = (nodes) => {
        if (!nodes) return 0;
        let count = nodes.length;
        nodes.forEach(node => {
          if (node.children) {
            count += countNodes(node.children);
          }
        });
        return count;
      };
      return countNodes(this.menuPermissions);
    },
    operationPermissions() {
      const operations = this.permissions
        .filter(p => p.includes(':') && (
          p.endsWith(':add') ||
          p.endsWith(':edit') ||
          p.endsWith(':remove') ||
          p.endsWith(':export') ||
          p.endsWith(':import') ||
          p.endsWith(':query') ||
          p.endsWith(':list')
        ))
        .map(p => {
          const parts = p.split(':');
          let type = 'info';
          if (p.endsWith(':add')) type = 'success';
          if (p.endsWith(':edit')) type = 'primary';
          if (p.endsWith(':remove')) type = 'danger';
          if (p.endsWith(':export')) type = 'warning';
          if (p.endsWith(':import')) type = 'warning';
          if (p.endsWith(':query')) type = 'info';
          if (p.endsWith(':list')) type = 'info';

          return {
            id: p,
            label: this.formatPermissionLabel(p),
            type: type
          };
        });
      return operations;
    },
    dataPermissions() {
      return this.permissions
        .filter(p => p.includes('data:') || p.includes(':data'))
        .map(p => ({
          id: p,
          label: this.formatPermissionLabel(p),
          type: 'info'
        }));
    },
    apiPermissions() {
      return this.permissions
        .filter(p => p.includes('api:') || p.includes(':api'))
        .map(p => ({
          id: p,
          label: this.formatPermissionLabel(p),
          type: 'success'
        }));
    },
    warehousePermissions() {
      return this.permissions
        .filter(p => p.includes('warehouse:') || p.includes(':warehouse'))
        .map(p => ({
          id: p,
          label: this.formatPermissionLabel(p),
          type: 'primary'
        }));
    }
  },
  watch: {
    roleId: {
      handler(val) {
        if (val) {
          this.getPermissions();
        }
      },
      immediate: true
    }
  },
  methods: {
    getPermissions() {
      this.loading = true;
      getRolePermissions(this.roleId)
        .then(response => {
          this.permissions = response.data || [];
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    formatMenuPermissions(permissions) {
      // 构建菜单树
      const menuMap = {};
      const result = [];

      // 第一步：创建所有节点
      permissions.forEach(p => {
        const parts = p.split(':');
        let currentLevel = result;
        let currentPath = '';

        parts.forEach((part, index) => {
          currentPath = currentPath ? `${currentPath}:${part}` : part;

          if (!menuMap[currentPath]) {
            const node = {
              id: currentPath,
              label: this.formatPartLabel(part),
              children: []
            };
            menuMap[currentPath] = node;

            if (index === 0) {
              result.push(node);
            } else {
              const parentPath = parts.slice(0, index).join(':');
              if (menuMap[parentPath]) {
                menuMap[parentPath].children.push(node);
              }
            }
          }
        });
      });

      // 第二步：清理空的children数组
      this.cleanupEmptyChildren(result);

      return result;
    },
    cleanupEmptyChildren(nodes) {
      if (!nodes) return;

      nodes.forEach(node => {
        if (node.children && node.children.length === 0) {
          delete node.children;
        } else if (node.children) {
          this.cleanupEmptyChildren(node.children);
        }
      });
    },
    formatPartLabel(part) {
      // 格式化权限标识的各部分
      const labelMap = {
        // 系统模块
        'system': '系统管理',
        'user': '用户管理',
        'role': '角色管理',
        'menu': '菜单管理',
        'dept': '部门管理',

        'dict': '字典管理',
        'config': '参数设置',
        'notice': '通知公告',
        'log': '日志管理',

        // 监控模块
        'monitor': '系统监控',
        'online': '在线用户',
        'job': '定时任务',
        'druid': '数据监控',
        'server': '服务监控',
        'logininfor': '登录日志',
        'operlog': '操作日志',

        // 工具模块
        'tool': '系统工具',
        'gen': '代码生成',
        'swagger': '系统接口',

        // 仓库模块
        'warehouse': '仓库管理',
        'inventory': '库存管理',
        'inbound': '入库管理',
        'outbound': '出库管理',
        'transfer': '库存调拨',
        'count': '库存盘点',
        'alert': '库存预警',
        'report': '报表统计',
        'qrcode': '二维码管理',

        // 权限模块
        'permission': '权限管理',
        'api': 'API权限',
        'data': '数据权限',
        'datascope': '数据范围',

        // 操作类型
        'list': '查询列表',
        'query': '查询详情',
        'add': '新增',
        'edit': '修改',
        'remove': '删除',
        'export': '导出',
        'import': '导入',
        'download': '下载',
        'upload': '上传',
        'print': '打印',
        'approve': '审批',
        'reject': '驳回',
        'assign': '分配',
        'cancel': '取消',
        'view': '查看'
      };

      return labelMap[part] || part;
    },
    formatPermissionLabel(permission) {
      const parts = permission.split(':');
      let label = '';

      parts.forEach(part => {
        label += this.formatPartLabel(part) + ':';
      });

      return label.slice(0, -1); // 移除最后一个冒号
    },
    getPermissionTagType(type) {
      return type || 'info';
    }
  }
};
</script>

<style scoped>
.role-permission-preview {
  width: 100%;
}

.preview-card {
  margin-bottom: 20px;
}

.permission-content {
  min-height: 300px;
}

.permission-section {
  margin-bottom: 20px;
}

.permission-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #606266;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
}

.permission-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

.empty-preview i {
  font-size: 48px;
  margin-bottom: 10px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.permission-stats {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 4px;
  height: 80px;
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
}

.permission-tabs {
  margin-bottom: 20px;
}

.empty-tab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #909399;
}

.empty-tab-content i {
  font-size: 32px;
  margin-bottom: 10px;
}
</style>
