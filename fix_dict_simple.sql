-- 简化的字典数据修复
USE warehouse_system;

-- 清理旧数据
DELETE FROM sys_dict_data WHERE dict_type = 'product_attribute_type';

-- 插入属性类型字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time) 
VALUES 
(1, '选项型', '1', 'product_attribute_type', '', 'primary', 'Y', '0', 'admin', NOW()),
(2, '输入型', '2', 'product_attribute_type', '', 'info', 'N', '0', 'admin', NOW());

-- 确保字典类型存在
INSERT IGNORE INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time) 
VALUES ('物品属性类型', 'product_attribute_type', '0', 'admin', NOW());

-- 验证结果
SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'product_attribute_type' ORDER BY dict_sort;

COMMIT;