<template>
  <div class="print-container">
    <!-- 打印预览界面 -->
    <div class="print-header" v-if="!isPrinting">
      <el-button type="primary" icon="el-icon-printer" size="small" @click="handlePrint">
        打印申购单
      </el-button>
      <el-button type="default" icon="el-icon-setting" size="small" @click="handlePrintSettings">
        打印设置
      </el-button>
      <el-button type="default" icon="el-icon-close" size="small" @click="handleClose">
        关闭
      </el-button>
    </div>
    
    <!-- 打印设置对话框 -->
    <el-dialog title="打印设置" :visible.sync="printSettingsVisible" width="500px" append-to-body>
      <el-form ref="printSettingsForm" :model="printSettings" label-width="100px">
        <el-form-item label="页面边距">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input v-model.number="printSettings.marginTop" size="small">
                <template slot="append">mm</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model.number="printSettings.marginRight" size="small">
                <template slot="append">mm</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model.number="printSettings.marginBottom" size="small">
                <template slot="append">mm</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model.number="printSettings.marginLeft" size="small">
                <template slot="append">mm</template>
              </el-input>
            </el-col>
          </el-row>
          <div class="margin-labels">
            <span>上</span>
            <span>右</span>
            <span>下</span>
            <span>左</span>
          </div>
        </el-form-item>
        
        <el-form-item label="字体大小">
          <el-slider v-model="printSettings.fontSize" :min="8" :max="20" show-input></el-slider>
        </el-form-item>
        
        <el-form-item label="页面方向">
          <el-radio-group v-model="printSettings.orientation">
            <el-radio label="portrait">纵向</el-radio>
            <el-radio label="landscape">横向</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="纸张大小">
          <el-select v-model="printSettings.paperSize" placeholder="请选择纸张大小">
            <el-option label="A4" value="A4"></el-option>
            <el-option label="A5" value="A5"></el-option>
            <el-option label="B5" value="B5"></el-option>
            <el-option label="Letter" value="letter"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="printSettingsVisible = false">取 消</el-button>
        <el-button type="primary" @click="savePrintSettings">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 打印内容 -->
    <div class="print-content" id="printContent">
      <div class="purchase-header">
        <h2 class="title">申购单</h2>
        <div class="header-info">
          <div class="header-item">
            <span class="label">申购单号：</span>
            <span class="value">{{ formData.requestNo }}</span>
          </div>
          <div class="header-item">
            <span class="label">申请日期：</span>
            <span class="value">{{ parseTime(formData.createTime) }}</span>
          </div>
          <div class="header-item">
            <span class="label">状态：</span>
            <span class="value">
              <dict-tag :options="dict.type.request_status" :value="formData.status" />
            </span>
          </div>
        </div>
      </div>
      
      <div class="purchase-info">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">申请人：</span>
              <span class="value">{{ formData.applicant }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">所属部门：</span>
              <span class="value">{{ formData.department }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">紧急程度：</span>
              <span class="value">
                <dict-tag :options="dict.type.urgency_level" :value="formData.urgencyLevel" />
              </span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">期望交付日期：</span>
              <span class="value">{{ parseTime(formData.expectedDeliveryDate) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <span class="label">用途说明：</span>
              <span class="value">{{ formData.purpose }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <div class="purchase-details">
        <h3>申购物品信息</h3>
        <el-table :data="detailData" class="detail-table" show-summary>
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column label="商品名称" prop="productName" />
          <el-table-column label="商品编码" prop="productCode" />
          <el-table-column label="规格型号" prop="specifications" />
          <el-table-column label="单位" prop="unit" />
          <el-table-column label="申购数量" prop="quantity" />
          <el-table-column label="已入库数量" prop="stockInCount" />
          <el-table-column label="单价" prop="unitPrice" :formatter="formatAmount" />
          <el-table-column label="总金额" prop="totalPrice" :formatter="formatAmount" />
        </el-table>
      </div>
      
      <div class="purchase-footer">
        <div style="display: flex; margin-bottom: 15px;">
          <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
            <span style="font-weight: bold; min-width: 100px;">申请人签字：</span>
            <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;"></div>
          </div>
          <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
            <span style="font-weight: bold; min-width: 100px;">审批人签字：</span>
            <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;"></div>
          </div>
          <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
            <span style="font-weight: bold; min-width: 100px;">日期：</span>
            <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 5px; position: relative; bottom: 3px;"></div>
          </div>
        </div>
      </div>
      
      <div class="approval-info" v-if="formData.status !== '0'">
        <h3>审批信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">审批人：</span>
              <span class="value">{{ approverRealName || formData.approver }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">审批时间：</span>
              <span class="value">{{ parseTime(formData.approvalTime) }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <span class="label">审批意见：</span>
              <span class="value">{{ formData.approvalComment }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { getPurchase } from "@/api/inventory/purchase";
import { getUserRealName } from "@/utils/userUtils";

export default {
  name: "PurchasePrint",
  dicts: ['urgency_level', 'request_status'],
  data() {
    return {
      loading: false,
      isPrinting: false,
      printSettingsVisible: false,
      formData: {},
      detailData: [],
      requestId: null,
      approverRealName: '', // 添加审批人真实姓名字段
      printSettings: {
        marginTop: 15,
        marginRight: 15,
        marginBottom: 15,
        marginLeft: 15,
        fontSize: 12,
        orientation: 'portrait',
        paperSize: 'A4'
      }
    };
  },
  created() {
    this.requestId = this.$route.params.requestId || this.$route.query.requestId;
    this.getRequestInfo();
  },
  methods: {
    /** 获取申购单信息 */
    getRequestInfo() {
      this.loading = true;
      getPurchase(this.requestId).then(response => {
        this.formData = response.data;
        // 构造明细数据数组
        this.detailData = [response.data];
        
        // 获取审批人真实姓名
        if (this.formData.approver) {
          getUserRealName(this.formData.approver).then(realName => {
            this.approverRealName = realName;
          });
        }
        
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.$message.error("获取申购单信息失败");
      });
    },
    
    /** 处理打印设置 */
    handlePrintSettings() {
      this.printSettingsVisible = true;
    },
    
    /** 保存打印设置 */
    savePrintSettings() {
      // 保存到本地存储
      localStorage.setItem('purchasePrintSettings', JSON.stringify(this.printSettings));
      this.printSettingsVisible = false;
      this.$message.success('打印设置已保存');
    },
    
    /** 加载打印设置 */
    loadPrintSettings() {
      const savedSettings = localStorage.getItem('purchasePrintSettings');
      if (savedSettings) {
        this.printSettings = JSON.parse(savedSettings);
      }
    },
    
    /** 格式化金额 */
    formatAmount(row, column, cellValue) {
      if (!cellValue) return '0.00';
      return parseFloat(cellValue).toFixed(2);
    },
    
    /** 打印 */
    handlePrint() {
      this.isPrinting = true;
      
      // 确保数据已加载
      if (!this.formData.requestNo) {
        this.$message.warning('数据还在加载中，请稍后再试');
        this.isPrinting = false;
        return;
      }
      
      this.$nextTick(() => {
        // 创建新窗口进行打印
        const printWindow = window.open('', '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');
        
        // 生成打印内容
        const printContent = this.generatePrintHTML();
        
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        // 等待内容加载完成后打印
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            // 打印完成后不自动关闭窗口，让用户手动关闭
            this.isPrinting = false;
          }, 500);
        };
      });
    },
    
    /** 生成打印页面HTML */
    generatePrintHTML() {
      const details = [this.formData] || [];
      let detailsHTML = '';
      
      details.forEach((item, index) => {
        detailsHTML += `
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${index + 1}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.productName || ''}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.productCode || ''}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.specifications || ''}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.unit || ''}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.quantity || ''}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${item.stockInCount || '0'}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${this.formatAmount(null, null, item.unitPrice)}</td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center;">${this.formatAmount(null, null, item.totalPrice)}</td>
          </tr>
        `;
      });
      
      // 根据设置确定页面方向和纸张大小
      let pageSizeStyle = '';
      if (this.printSettings.orientation === 'landscape') {
        pageSizeStyle = `size: ${this.printSettings.paperSize} landscape;`;
      } else {
        pageSizeStyle = `size: ${this.printSettings.paperSize} portrait;`;
      }
      
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>申购单打印</title>
          <style>
            @page { 
              ${pageSizeStyle}
              margin: ${this.printSettings.marginTop}mm ${this.printSettings.marginRight}mm ${this.printSettings.marginBottom}mm ${this.printSettings.marginLeft}mm; 
            }
            
            * {
              box-sizing: border-box;
            }
            
            html, body { 
              font-family: "Microsoft YaHei", SimSun, sans-serif; 
              font-size: ${this.printSettings.fontSize}pt; 
              color: #000; 
              background: #fff; 
              margin: 0; 
              padding: ${this.printSettings.marginTop/3}mm;
              width: 100%;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
            
            .container {
              width: 100%;
              max-width: 1000px;
              margin: 0 auto;
            }
            
            .print-container {
              padding: 20px;
              background-color: #fff;
              color: #000;
            }
            
            .print-content {
              max-width: 1000px;
              margin: 0 auto;
              font-family: "SimSun", "宋体", serif;
              color: #000;
            }
            
            .purchase-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #000;
              padding-bottom: 10px;
            }
            
            .purchase-header .title {
              font-size: ${this.printSettings.fontSize + 8}pt;
              font-weight: bold;
              margin: 0 0 20px 0;
            }
            
            .header-info {
              display: flex;
              justify-content: space-between;
            }
            
            .header-item {
              display: flex;
              align-items: center;
            }
            
            .header-item .label {
              font-weight: bold;
              margin-right: 5px;
            }
            
            .purchase-info {
              margin-bottom: 30px;
            }
            
            .info-item {
              margin-bottom: 15px;
              display: flex;
              align-items: flex-start;
            }
            
            .info-item .label {
              font-weight: bold;
              min-width: 100px;
              flex-shrink: 0;
            }
            
            .purchase-details h3,
            .approval-info h3 {
              font-size: ${this.printSettings.fontSize + 4}pt;
              margin: 0 0 15px 0;
              border-left: 4px solid #409EFF;
              padding-left: 10px;
            }
            
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
              table-layout: fixed;
            }
            
            th {
              background-color: #f5f5f5;
              color: #000;
              font-weight: bold;
              border: 1px solid #000;
              padding: 8px;
              text-align: center;
            }
            
            td {
              border: 1px solid #000;
              padding: 8px;
              text-align: center;
              word-wrap: break-word;
              word-break: break-all;
            }
            
            .purchase-footer {
              margin: 30px 0;
            }
            
            .footer-item {
              margin-bottom: 15px;
            }
            
            .signature-line {
              display: inline-block;
              width: 80px;
              height: 1px;
              background-color: #000;
              margin-left: 10px;
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            <div class="print-content">
              <div class="purchase-header">
                <h2 class="title">申购单</h2>
                <div class="header-info">
                  <div class="header-item">
                    <span class="label">申购单号：</span>
                    <span class="value">${this.formData.requestNo || ''}</span>
                  </div>
                  <div class="header-item">
                    <span class="label">申请日期：</span>
                    <span class="value">${this.parseTime(this.formData.createTime) || ''}</span>
                  </div>
                  <div class="header-item">
                    <span class="label">状态：</span>
                    <span class="value">${this.getStatusName(this.formData.status) || ''}</span>
                  </div>
                </div>
              </div>
              
              <div class="purchase-info">
                <div style="display: flex; margin-bottom: 15px;">
                  <div style="width: 50%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">申请人：</span>
                    <span>${this.formData.applicant || ''}</span>
                  </div>
                  <div style="width: 50%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">所属部门：</span>
                    <span>${this.formData.department || ''}</span>
                  </div>
                </div>
                <div style="display: flex; margin-bottom: 15px;">
                  <div style="width: 50%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">紧急程度：</span>
                    <span>${this.getUrgencyLevelName(this.formData.urgencyLevel) || ''}</span>
                  </div>
                  <div style="width: 50%; display: flex;">
                    <span style="font-weight: bold; min-width: 120px;">期望交付日期：</span>
                    <span>${this.parseTime(this.formData.expectedDeliveryDate) || ''}</span>
                  </div>
                </div>
                
                <div style="display: flex; margin-bottom: 15px;">
                  <div style="width: 100%; display: flex;">
                    <span style="font-weight: bold; min-width: 100px;">用途说明：</span>
                    <span>${this.formData.purpose || ''}</span>
                  </div>
                </div>
              </div>
              
              <div class="purchase-details">
                <h3>申购物品信息</h3>
                <table>
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>商品名称</th>
                      <th>商品编码</th>
                      <th>规格型号</th>
                      <th>单位</th>
                      <th>申购数量</th>
                      <th>已入库数量</th>
                      <th>单价</th>
                      <th>总金额</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${detailsHTML}
                  </tbody>
                </table>
              </div>
              
              <div class="purchase-footer">
                <div style="display: flex; margin-bottom: 15px;">
                  <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
                    <span style="font-weight: bold; min-width: 100px;">申请人签字：</span>
                    <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;"></div>
                  </div>
                  <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
                    <span style="font-weight: bold; min-width: 100px;">审批人签字：</span>
                    <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;"></div>
                  </div>
                  <div style="width: 33.33%; display: flex; align-items: flex-end; height: 30px;">
                    <span style="font-weight: bold; min-width: 100px;">日期：</span>
                    <div style="display: inline-block; width: 80px; height: 1px; background-color: #000; margin-left: 10px; position: relative; bottom: 3px;"></div>
                  </div>
                </div>
              </div>
              
              ${
                this.formData.status !== '0' ?
                `<div class="approval-info">
                  <h3>审批信息</h3>
                  <div style="display: flex; margin-bottom: 15px;">
                    <div style="width: 50%; display: flex;">
                      <span style="font-weight: bold; min-width: 100px;">审批人：</span>
                      <span>${this.approverRealName || this.formData.approver || ''}</span>
                    </div>
                    <div style="width: 50%; display: flex;">
                      <span style="font-weight: bold; min-width: 100px;">审批时间：</span>
                      <span>${this.parseTime(this.formData.approvalTime) || ''}</span>
                    </div>
                  </div>
                  <div style="display: flex; margin-bottom: 15px;">
                    <div style="width: 100%; display: flex;">
                      <span style="font-weight: bold; min-width: 100px;">审批意见：</span>
                      <span>${this.formData.approvalComment || ''}</span>
                    </div>
                  </div>
                </div>` : ''
              }
            </div>
          </div>
        </body>
        </html>
      `;
    },
    
    /** 获取状态名称 */
    getStatusName(status) {
      // 使用与屏幕预览相同的字典映射方式
      const statusDict = this.dict.type.request_status || [];
      const statusItem = statusDict.find(item => item.value === status);
      return statusItem ? statusItem.label : '';
    },
    
    /** 获取紧急程度名称 */
    getUrgencyLevelName(level) {
      // 使用与屏幕预览相同的字典映射方式
      const levelDict = this.dict.type.urgency_level || [];
      const levelItem = levelDict.find(item => item.value === level);
      return levelItem ? levelItem.label : '';
    },
    
    /** 关闭 */
    handleClose() {
      this.$router.go(-1);
    }
  },
  mounted() {
    // 加载打印设置
    this.loadPrintSettings();
  }
};
</script>

<style scoped>
.print-container {
  padding: 20px;
  background-color: #fff;
  color: #000;
}

.print-header {
  margin-bottom: 20px;
  text-align: center;
}

.print-content {
  max-width: 1000px;
  margin: 0 auto;
  font-family: "SimSun", "宋体", serif;
  color: #000;
}

.purchase-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #000;
  padding-bottom: 10px;
}

.purchase-header .title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 20px 0;
}

.header-info {
  display: flex;
  justify-content: space-between;
}

.header-item {
  display: flex;
  align-items: center;
}

.header-item .label {
  font-weight: bold;
}

.purchase-info {
  margin-bottom: 30px;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.info-item .label {
  font-weight: bold;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-table {
  margin-bottom: 30px;
}

.detail-table ::v-deep .el-table__header th {
  background-color: #f5f5f5;
  color: #000;
}

.detail-table ::v-deep .el-table__row td {
  color: #000;
}

.purchase-details h3,
.approval-info h3 {
  font-size: 18px;
  margin: 0 0 15px 0;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.purchase-footer {
  margin: 30px 0;
}

.footer-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-end;
  height: 30px;
}

.footer-item .label {
  font-weight: bold;
  min-width: 100px;
  flex-shrink: 0;
}

.signature-line {
  display: inline-block;
  width: 80px;
  height: 1px;
  background-color: #000;
  margin-left: 5px;
  position: relative;
  bottom: 3px;
}

.margin-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}
</style>