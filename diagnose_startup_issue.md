# 启动问题诊断指南

## 当前状态
- 所有新增的控制器都已被禁用
- 应用仍然启动失败
- 需要获取详细的错误信息进行诊断

## 获取详细错误信息

### 方法1：使用 -e 参数获取完整堆栈跟踪
```bash
cd C:\CKGLXT\warehouse-system\backend
mvn spring-boot:run -e
```

### 方法2：使用 -X 参数获取调试日志
```bash
cd C:\CKGLXT\warehouse-system\backend
mvn spring-boot:run -X
```

### 方法3：检查应用日志
```bash
# 如果有日志文件，查看最新的错误日志
# 通常在 logs/ 目录下
```

## 可能的问题原因

### 1. 数据库连接问题
- 数据库服务未启动
- 连接配置错误
- 权限问题

### 2. 端口占用问题
- 8080端口被其他应用占用
- 检查命令：`netstat -ano | findstr :8080`

### 3. 配置文件问题
- application.yml 或 application.properties 配置错误
- 环境变量问题

### 4. 依赖冲突
- Maven依赖版本冲突
- 类路径问题

### 5. JVM内存问题
- 内存不足
- JVM参数配置问题

## 诊断步骤

### 步骤1：检查基础环境
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查端口占用
netstat -ano | findstr :8080
```

### 步骤2：检查数据库连接
```bash
# 尝试连接数据库
mysql -h localhost -u root -p123456 -D warehouse_system -e "SELECT 1;"
```

### 步骤3：简化启动
```bash
# 尝试只编译不运行
mvn clean compile

# 检查编译是否成功
echo $?
```

### 步骤4：检查配置文件
查看以下配置文件是否有问题：
- `application.yml`
- `application-dev.yml`
- `application-prod.yml`

## 临时解决方案

### 方案1：回滚到工作版本
如果之前有工作的版本，可以：
1. 备份当前更改
2. 回滚到最后一个工作版本
3. 逐步应用更改

### 方案2：最小化配置
1. 临时禁用数据库连接
2. 禁用安全配置
3. 使用内存数据库

### 方案3：分模块启动
1. 只启动核心模块
2. 逐步添加其他模块

## 常见错误及解决方案

### 错误1：端口占用
```
Port 8080 was already in use
```
**解决**：更改端口或停止占用端口的进程

### 错误2：数据库连接失败
```
Could not connect to database
```
**解决**：检查数据库服务和连接配置

### 错误3：Bean创建失败
```
Error creating bean with name 'xxx'
```
**解决**：检查依赖注入和配置

### 错误4：类找不到
```
ClassNotFoundException
```
**解决**：检查依赖和类路径

## 下一步行动

1. **获取详细错误信息**：运行 `mvn spring-boot:run -e`
2. **分析错误日志**：找出具体的失败原因
3. **针对性解决**：根据错误信息采取相应措施
4. **逐步验证**：确保每个修复都有效

## 紧急恢复方案

如果问题无法快速解决：
1. 删除所有新增的控制器文件
2. 恢复到添加控制器之前的状态
3. 重新启动应用验证基础功能
4. 重新规划控制器添加策略