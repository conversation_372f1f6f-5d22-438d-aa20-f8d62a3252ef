@echo off
chcp 65001 >nul
echo ========================================
echo 物品相关表名规范化修复脚本
echo 修复表名规范: 
echo   wms_product_category → wms_category
echo   wms_product_specification → wms_specification  
echo   wms_product_unit → wms_unit
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo 🔧 开始修复物品相关表名...
echo.

echo 📋 执行表名规范化SQL脚本...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < fix_wms_table_names_complete.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 数据库修复完成！
    echo.
    echo 📊 验证修复结果...
    echo.
    
    echo 查询wms_category表记录数:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as category_count FROM wms_category WHERE del_flag = '0';"
    
    echo.
    echo 查询wms_specification表记录数:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as spec_count FROM wms_specification;"
    
    echo.
    echo 查询wms_unit表记录数:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COUNT(*) as unit_count FROM wms_unit;"
    
    echo.
    echo 查看分类数据示例:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT category_id, category_name, category_code FROM wms_category WHERE del_flag = '0' LIMIT 3;"
    
    echo.
    echo 查看规格数据示例:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT spec_id, spec_name, spec_code FROM wms_specification LIMIT 3;"
    
    echo.
    echo 查看单位数据示例:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT unit_id, unit_name, unit_code FROM wms_unit LIMIT 3;"
    
    echo.
    echo ========================================
    echo ✅ 物品相关表名修复完成！
    echo ========================================
    echo.
    echo 📝 修复内容:
    echo   1. ✅ 创建/验证 wms_category 表
    echo   2. ✅ 创建/验证 wms_specification 表  
    echo   3. ✅ 创建/验证 wms_unit 表
    echo   4. ✅ 迁移旧数据（如果存在）
    echo   5. ✅ 插入默认数据
    echo   6. ✅ 更新外键引用
    echo.
    echo 🔄 下一步操作:
    echo   1. 重新编译后端项目
    echo   2. 重启后端服务
    echo   3. 测试物品分类、规格、单位功能
    echo.
    echo 💡 提示: 相关实体类和Mapper已使用正确表名:
    echo   - ProductCategory.java → wms_category
    echo   - ProductSpecification.java → wms_specification  
    echo   - ProductUnit.java → wms_unit
    echo   - 对应的Mapper XML文件已更新
    echo.
) else (
    echo.
    echo ❌ 数据库修复失败！
    echo 请检查数据库连接和权限设置
    echo.
)

echo 按任意键退出...
pause >nul