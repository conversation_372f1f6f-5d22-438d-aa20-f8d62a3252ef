package com.wanyu.web.controller.inventory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;

/**
 * 库存管理控制器
 * 
 * <AUTHOR>
 */
// @RestController
// @RequestMapping("/inventory/stock")
public class WmsStockController extends BaseController
{
    /**
     * 获取库存预警信息
     */
    @GetMapping("/alert")
    public AjaxResult getStockAlert()
    {
        List<Map<String, Object>> alertList = new ArrayList<>();
        
        // 模拟一些库存预警数据
        Map<String, Object> alert1 = new HashMap<>();
        alert1.put("productId", 1);
        alert1.put("productName", "产品A");
        alert1.put("productCode", "PA001");
        alert1.put("currentStock", 5);
        alert1.put("minStock", 10);
        alert1.put("warehouseName", "主仓库");
        alert1.put("alertType", "低库存预警");
        alertList.add(alert1);
        
        Map<String, Object> alert2 = new HashMap<>();
        alert2.put("productId", 2);
        alert2.put("productName", "产品B");
        alert2.put("productCode", "PB001");
        alert2.put("currentStock", 2);
        alert2.put("minStock", 15);
        alert2.put("warehouseName", "分仓库");
        alert2.put("alertType", "低库存预警");
        alertList.add(alert2);
        
        return AjaxResult.success(alertList);
    }
}