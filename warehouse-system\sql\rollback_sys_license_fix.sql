-- =====================================================
-- sys_license表字段标准化修复回滚脚本
-- 
-- 用途：回滚sys_license表status字段修复，恢复到修复前状态
-- 前提：必须存在备份表 sys_license_backup_before_fix
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 步骤1：检查备份表是否存在
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system' AND table_name = 'sys_license_backup_before_fix')
        THEN '✓ 备份表存在，可以执行回滚'
        ELSE '✗ 备份表不存在，无法执行回滚'
    END as backup_check,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system' AND table_name = 'sys_license_backup_before_fix')
        THEN (SELECT COUNT(*) FROM sys_license_backup_before_fix WHERE license_id != 999999)
        ELSE 0
    END as backup_record_count;

-- 步骤2：显示当前状态
SELECT 
    'sys_license回滚前状态' as check_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as status_0_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as status_1_count
FROM sys_license;

-- 步骤3：创建当前状态备份（以防回滚后需要再次修复）
DROP TABLE IF EXISTS `sys_license_backup_after_fix`;
CREATE TABLE `sys_license_backup_after_fix` AS SELECT * FROM `sys_license`;

-- 步骤4：清空当前表数据
DELETE FROM `sys_license`;

-- 步骤5：从备份表恢复数据（排除日志记录）
INSERT INTO `sys_license` (
    license_id, license_key, company_name, contact_info, license_type, 
    max_users, max_warehouses, start_date, end_date, hardware_fingerprint, 
    status, features, create_time, update_time, create_by, update_by, remark, current
)
SELECT 
    license_id, license_key, company_name, contact_info, license_type, 
    max_users, max_warehouses, start_date, end_date, hardware_fingerprint, 
    status, features, create_time, update_time, create_by, update_by, remark, current
FROM `sys_license_backup_before_fix`
WHERE license_id != 999999;  -- 排除日志记录

-- 步骤6：恢复原始字段定义
ALTER TABLE `sys_license` MODIFY COLUMN `status` CHAR(1) DEFAULT '1' COMMENT '状态(0=禁用,1=启用)';

-- 步骤7：验证回滚结果
SELECT 
    'sys_license回滚后状态' as check_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as status_0_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as status_1_count
FROM sys_license;

-- 步骤8：验证字段定义恢复
SELECT 
    COLUMN_NAME,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
    AND TABLE_NAME = 'sys_license' 
    AND COLUMN_NAME = 'status';

-- 步骤9：对比回滚前后数据
SELECT 
    '=== 回滚验证对比 ===' as section,
    'sys_license回滚后' as table_name,
    COUNT(*) as record_count,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as status_0_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as status_1_count
FROM sys_license

UNION ALL

SELECT 
    '=== 回滚验证对比 ===' as section,
    'sys_license_backup_before_fix原始备份' as table_name,
    COUNT(*) as record_count,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as status_0_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as status_1_count
FROM sys_license_backup_before_fix
WHERE license_id != 999999;

-- 提交事务
COMMIT;

-- 最终回滚完成提示
SELECT 
    '=== sys_license表字段标准化回滚完成 ===' as message,
    '恢复内容：status字段 0=禁用，1=启用' as rollback_content,
    '修复后备份：sys_license_backup_after_fix' as fix_backup_table,
    NOW() as completion_time;