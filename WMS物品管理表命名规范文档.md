# WMS物品管理表命名规范文档

## 概述

本文档基于项目的字段定义标准化规范和WMS表名规范化指南，制定了物品管理模块的统一数据库表命名规范。所有物品管理相关的表都应严格遵循此规范进行命名和设计。

## 命名规范原则

### 1. 表名命名规范

#### 基本规则
- **前缀规范**：所有WMS相关表必须使用 `wms_` 前缀
- **命名风格**：使用小写字母和下划线分隔（snake_case）
- **语义清晰**：表名应直接反映业务功能，避免冗余词汇
- **简洁明了**：在保证语义清晰的前提下，尽量简化表名

#### 具体规则
```sql
-- 正确示例
wms_product          -- 物品表
wms_category         -- 分类表
wms_specification    -- 规格表
wms_unit            -- 单位表
wms_barcode         -- 条码表

-- 错误示例（避免使用）
product_info         -- 缺少wms前缀
wms_product_info     -- 冗余的info后缀
WmsProduct          -- 使用驼峰命名
wms-product         -- 使用连字符
```

### 2. 字段命名规范

#### 主键字段
```sql
-- 格式：{业务名}_id
product_id          -- 物品ID
category_id         -- 分类ID
specification_id    -- 规格ID（可简化为spec_id）
unit_id            -- 单位ID
barcode_id          -- 条码ID
attribute_id        -- 属性ID
```

#### 状态字段
```sql
-- 通用状态字段
status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）'

-- 删除标记字段
del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）'

-- 布尔类型字段
is_main CHAR(1) DEFAULT '0' COMMENT '是否主要（0否 1是）'
is_default CHAR(1) DEFAULT '0' COMMENT '是否默认（0否 1是）'
is_required CHAR(1) DEFAULT '0' COMMENT '是否必填（0否 1是）'
is_searchable CHAR(1) DEFAULT '0' COMMENT '是否可搜索（0否 1是）'
```

#### 审计字段
```sql
-- 标准审计字段（所有表必须包含）
create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
```

## 物品管理模块表结构规范

### 1. wms_category（物品分类表）

```sql
CREATE TABLE wms_category (
    category_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    parent_id BIGINT(20) DEFAULT 0 COMMENT '父分类ID',
    ancestors VARCHAR(50) DEFAULT '' COMMENT '祖级列表',
    category_name VARCHAR(100) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(50) DEFAULT NULL COMMENT '分类编码',
    order_num INT(4) DEFAULT 0 COMMENT '显示顺序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (category_id),
    UNIQUE KEY uk_category_code (category_code),
    KEY idx_parent_id (parent_id),
    KEY idx_status (status),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS物品分类表';
```

### 2. wms_product（物品信息表）

```sql
CREATE TABLE wms_product (
    product_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '物品ID',
    product_name VARCHAR(200) NOT NULL COMMENT '物品名称',
    product_code VARCHAR(100) DEFAULT NULL COMMENT '物品编码',
    category_id BIGINT(20) DEFAULT NULL COMMENT '分类ID',
    spec_id BIGINT(20) DEFAULT NULL COMMENT '规格ID',
    unit_id BIGINT(20) DEFAULT NULL COMMENT '单位ID',
    product_image VARCHAR(500) DEFAULT NULL COMMENT '物品图片',
    product_desc TEXT COMMENT '物品描述',
    min_stock DECIMAL(10,2) DEFAULT 0.00 COMMENT '最小库存',
    max_stock DECIMAL(10,2) DEFAULT 0.00 COMMENT '最大库存',
    purchase_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '采购价格',
    sale_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '销售价格',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (product_id),
    UNIQUE KEY uk_product_code (product_code),
    KEY idx_category_id (category_id),
    KEY idx_spec_id (spec_id),
    KEY idx_unit_id (unit_id),
    KEY idx_product_name (product_name),
    KEY idx_status (status),
    KEY idx_create_time (create_time),
    CONSTRAINT fk_wms_product_category_id FOREIGN KEY (category_id) REFERENCES wms_category (category_id),
    CONSTRAINT fk_wms_product_spec_id FOREIGN KEY (spec_id) REFERENCES wms_specification (spec_id),
    CONSTRAINT fk_wms_product_unit_id FOREIGN KEY (unit_id) REFERENCES wms_unit (unit_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS物品信息表';
```

### 3. wms_specification（物品规格表）

```sql
CREATE TABLE wms_specification (
    spec_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
    spec_name VARCHAR(100) NOT NULL COMMENT '规格名称',
    spec_code VARCHAR(50) DEFAULT NULL COMMENT '规格编码',
    spec_desc VARCHAR(500) DEFAULT NULL COMMENT '规格描述',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (spec_id),
    UNIQUE KEY uk_spec_code (spec_code),
    KEY idx_spec_name (spec_name),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS物品规格表';
```

### 4. wms_unit（物品单位表）

```sql
CREATE TABLE wms_unit (
    unit_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '单位ID',
    unit_name VARCHAR(50) NOT NULL COMMENT '单位名称',
    unit_code VARCHAR(20) DEFAULT NULL COMMENT '单位编码',
    unit_symbol VARCHAR(10) DEFAULT NULL COMMENT '单位符号',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (unit_id),
    UNIQUE KEY uk_unit_code (unit_code),
    KEY idx_unit_name (unit_name),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS物品单位表';
```

### 5. wms_product_attribute（物品属性表）

```sql
CREATE TABLE wms_product_attribute (
    attribute_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '属性ID',
    attribute_name VARCHAR(100) NOT NULL COMMENT '属性名称',
    attribute_type VARCHAR(10) NOT NULL DEFAULT '1' COMMENT '属性类型（1选项型 2输入型）',
    is_required CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否必填（Y是 N否）',
    is_searchable CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否可搜索（Y是 N否）',
    order_num INT(4) DEFAULT 0 COMMENT '显示顺序',
    status CHAR(1) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (attribute_id),
    KEY idx_attribute_name (attribute_name),
    KEY idx_status (status),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS物品属性表';
```

### 6. wms_product_attribute_option（物品属性选项表）

```sql
CREATE TABLE wms_product_attribute_option (
    option_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '选项ID',
    attribute_id BIGINT(20) NOT NULL COMMENT '属性ID',
    option_value VARCHAR(200) NOT NULL COMMENT '选项值',
    option_label VARCHAR(200) NOT NULL COMMENT '选项标签',
    order_num INT(4) DEFAULT 0 COMMENT '显示顺序',
    status CHAR(1) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (option_id),
    KEY idx_attribute_id (attribute_id),
    KEY idx_option_value (option_value),
    KEY idx_status (status),
    CONSTRAINT fk_wms_product_attribute_option_attribute_id 
        FOREIGN KEY (attribute_id) REFERENCES wms_product_attribute (attribute_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS物品属性选项表';
```

### 7. wms_barcode（物品条码表）

```sql
CREATE TABLE wms_barcode (
    barcode_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '条码ID',
    product_id BIGINT(20) NOT NULL COMMENT '物品ID',
    product_name VARCHAR(100) DEFAULT NULL COMMENT '物品名称',
    barcode_content VARCHAR(200) NOT NULL COMMENT '条码内容',
    barcode_type VARCHAR(20) NOT NULL DEFAULT 'CODE128' COMMENT '条码类型',
    barcode_image VARCHAR(500) DEFAULT NULL COMMENT '条码图片路径',
    template_id BIGINT(20) DEFAULT NULL COMMENT '条码模板ID',
    is_main CHAR(1) DEFAULT '0' COMMENT '是否主条码（0否 1是）',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (barcode_id),
    UNIQUE KEY uk_barcode_content (barcode_content),
    KEY idx_product_id (product_id),
    KEY idx_barcode_type (barcode_type),
    KEY idx_create_time (create_time),
    CONSTRAINT fk_wms_barcode_product_id FOREIGN KEY (product_id) REFERENCES wms_product (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS物品条码表';
```

### 8. wms_barcode_template（物品条码模板表）

```sql
CREATE TABLE wms_barcode_template (
    template_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(20) NOT NULL COMMENT '模板类型',
    template_width INT(11) DEFAULT 100 COMMENT '模板宽度（mm）',
    template_height INT(11) DEFAULT 50 COMMENT '模板高度（mm）',
    barcode_width INT(11) DEFAULT 80 COMMENT '条码宽度（mm）',
    barcode_height INT(11) DEFAULT 30 COMMENT '条码高度（mm）',
    font_size INT(11) DEFAULT 12 COMMENT '字体大小',
    show_text CHAR(1) DEFAULT '1' COMMENT '是否显示文字（0否 1是）',
    show_product_name CHAR(1) DEFAULT '1' COMMENT '是否显示物品名称（0否 1是）',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (template_id),
    KEY idx_template_name (template_name),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS物品条码模板表';
```

## 表关系图

```
wms_category (分类表)
    ↓ (1:N)
wms_product (物品表) ← wms_specification (规格表)
    ↓ (1:N)        ← wms_unit (单位表)
wms_barcode (条码表)
    ↓ (N:1)
wms_barcode_template (条码模板表)

wms_product_attribute (属性表)
    ↓ (1:N)
wms_product_attribute_option (属性选项表)
```

## Java实体类命名规范

### 1. 实体类命名
```java
// 正确命名
WmsCategory.java          // 对应 wms_category 表
WmsProduct.java           // 对应 wms_product 表
WmsSpecification.java     // 对应 wms_specification 表
WmsUnit.java             // 对应 wms_unit 表
WmsProductAttribute.java  // 对应 wms_product_attribute 表
WmsBarcode.java          // 对应 wms_barcode 表

// 避免使用
ProductCategory.java      // 缺少Wms前缀
WmsProductInfo.java      // 冗余的Info后缀
```

### 2. 字段映射规范
```java
@TableName("wms_product")
public class WmsProduct extends BaseEntity {
    
    @TableId("product_id")
    private Long productId;
    
    @TableField("product_name")
    private String productName;
    
    @TableField("category_id")
    private Long categoryId;
    
    // 状态字段统一使用String类型
    @TableField("status")
    private String status;
    
    // 布尔字段统一使用String类型
    @TableField("is_main")
    private String isMain;
}
```

## Mapper接口命名规范

### 1. Mapper接口命名
```java
// 正确命名
WmsCategoryMapper.java
WmsProductMapper.java
WmsSpecificationMapper.java
WmsUnitMapper.java
WmsProductAttributeMapper.java
WmsBarcodeMapper.java
```

### 2. XML文件命名
```xml
<!-- 文件路径：src/main/resources/mapper/wms/ -->
WmsCategoryMapper.xml
WmsProductMapper.xml
WmsSpecificationMapper.xml
WmsUnitMapper.xml
WmsProductAttributeMapper.xml
WmsBarcodeMapper.xml
```

## Service层命名规范

### 1. Service接口命名
```java
// 正确命名
IWmsCategoryService.java
IWmsProductService.java
IWmsSpecificationService.java
IWmsUnitService.java
IWmsProductAttributeService.java
IWmsBarcodeService.java
```

### 2. Service实现类命名
```java
// 正确命名
WmsCategoryServiceImpl.java
WmsProductServiceImpl.java
WmsSpecificationServiceImpl.java
WmsUnitServiceImpl.java
WmsProductAttributeServiceImpl.java
WmsBarcodeServiceImpl.java
```

## Controller层命名规范

### 1. Controller类命名
```java
// 推荐命名（保持与菜单一致）
ProductCategoryController.java    // 物品分类
ProductInfoController.java        // 物品信息
ProductSpecificationController.java // 物品规格
ProductUnitController.java        // 物品单位
ProductAttributeController.java   // 物品属性
ProductBarcodeController.java     // 物品条码
```

### 2. 请求映射规范
```java
@RestController
@RequestMapping("/product/category")  // 与前端路由保持一致
public class ProductCategoryController {
    
    @Autowired
    private IWmsCategoryService wmsCategoryService; // 注入WMS服务
}
```

## 数据字典规范

### 1. 字典类型命名
```sql
-- 物品相关字典类型
wms_product_status        -- 物品状态
wms_barcode_type         -- 条码类型
wms_attribute_type       -- 属性类型
wms_unit_type           -- 单位类型
```

### 2. 字典数据示例
```sql
-- 物品状态字典
INSERT INTO sys_dict_data VALUES 
(1, 1, '正常', '0', 'wms_product_status', '', 'primary', 'Y', '0', 'admin', NOW(), '', NULL, '正常状态'),
(2, 2, '停用', '1', 'wms_product_status', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '停用状态');

-- 条码类型字典
INSERT INTO sys_dict_data VALUES 
(3, 1, 'CODE128', 'CODE128', 'wms_barcode_type', '', 'primary', 'Y', '0', 'admin', NOW(), '', NULL, 'CODE128条码'),
(4, 2, 'EAN13', 'EAN13', 'wms_barcode_type', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, 'EAN13条码'),
(5, 3, '二维码', 'QR_CODE', 'wms_barcode_type', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '二维码');
```

## 权限字符规范

### 1. 权限字符命名
```java
// 物品分类权限
product:category:list     // 查看分类列表
product:category:query    // 查看分类详情
product:category:add      // 新增分类
product:category:edit     // 修改分类
product:category:remove   // 删除分类

// 物品信息权限
product:info:list         // 查看物品列表
product:info:query        // 查看物品详情
product:info:add          // 新增物品
product:info:edit         // 修改物品
product:info:remove       // 删除物品

// 其他模块类似...
```

## 索引命名规范

### 1. 索引命名规则
```sql
-- 主键索引：PRIMARY KEY
-- 唯一索引：uk_{表名}_{字段名} 或 uk_{字段名}
-- 普通索引：idx_{字段名}
-- 外键索引：fk_{表名}_{字段名}

-- 示例
PRIMARY KEY (product_id)                    -- 主键
UNIQUE KEY uk_product_code (product_code)   -- 唯一索引
KEY idx_category_id (category_id)           -- 普通索引
KEY idx_create_time (create_time)           -- 时间索引
```

## 最佳实践建议

### 1. 表设计原则
- 每个表都必须有主键
- 每个表都必须包含标准的审计字段
- 状态字段统一使用CHAR(1)类型
- 外键关系要明确定义约束
- 适当添加索引提高查询性能

### 2. 字段设计原则
- 字段名要有明确的业务含义
- 避免使用保留字作为字段名
- 数值类型要考虑精度和范围
- 字符串类型要合理设置长度
- 时间字段统一使用DATETIME类型

### 3. 注释规范
- 每个表都要有清晰的注释说明
- 每个字段都要有详细的注释
- 状态字段要明确说明各个值的含义
- 外键字段要说明关联关系

### 4. 数据完整性
- 重要的业务字段要设置NOT NULL约束
- 唯一性字段要设置UNIQUE约束
- 外键关系要设置FOREIGN KEY约束
- 状态字段要设置合理的默认值

## 总结

本规范文档基于项目现有的字段定义标准化规范和WMS表名规范化指南，为物品管理模块制定了统一的命名标准。所有开发人员在进行物品管理相关功能开发时，都应严格遵循此规范，确保系统的一致性和可维护性。

### 核心要点
1. **表名统一使用wms_前缀**
2. **字段定义遵循标准化规范**
3. **状态字段统一使用0正常1停用**
4. **审计字段必须完整包含**
5. **索引命名要规范清晰**
6. **注释信息要详细准确**

遵循这些规范将有助于提高代码质量，降低维护成本，确保系统的长期稳定运行。