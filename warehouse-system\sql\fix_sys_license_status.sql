-- =====================================================
-- 修复sys_license表字段定义标准化脚本
-- 
-- 问题描述：
-- 当前sys_license表的status字段定义为：0=禁用，1=启用
-- 项目标准应该是：0=启用，1=禁用
-- 
-- 修复策略：
-- 1. 创建备份表
-- 2. 安全地转换数据（0↔1值互换）
-- 3. 更新字段定义和默认值
-- 4. 验证数据完整性
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 步骤1：创建备份表
DROP TABLE IF EXISTS `sys_license_backup_before_fix`;
CREATE TABLE `sys_license_backup_before_fix` AS SELECT * FROM `sys_license`;

-- 验证备份表创建成功
SELECT 
    'sys_license备份验证' as check_name,
    (SELECT COUNT(*) FROM sys_license) as original_count,
    (SELECT COUNT(*) FROM sys_license_backup_before_fix) as backup_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM sys_license) = (SELECT COUNT(*) FROM sys_license_backup_before_fix) 
        THEN '备份成功' 
        ELSE '备份失败' 
    END as backup_status;

-- 步骤2：显示修复前的数据状态
SELECT 
    'sys_license修复前状态' as check_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as status_0_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as status_1_count,
    SUM(CASE WHEN status NOT IN ('0', '1') THEN 1 ELSE 0 END) as invalid_status_count
FROM sys_license;

-- 步骤3：添加临时字段进行安全转换
ALTER TABLE `sys_license` ADD COLUMN `status_new` CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- 步骤4：执行数据转换（0↔1值互换）
UPDATE `sys_license` SET `status_new` = CASE 
    WHEN `status` = '0' THEN '1'  -- 原来禁用(0) -> 新标准禁用(1)
    WHEN `status` = '1' THEN '0'  -- 原来启用(1) -> 新标准启用(0)
    ELSE '0'  -- 默认为启用状态
END;

-- 步骤5：验证转换结果
SELECT 
    'sys_license数据转换验证' as check_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN status_new = '0' THEN 1 ELSE 0 END) as new_enabled_count,
    SUM(CASE WHEN status_new = '1' THEN 1 ELSE 0 END) as new_disabled_count,
    SUM(CASE WHEN status_new NOT IN ('0', '1') THEN 1 ELSE 0 END) as invalid_new_status_count
FROM sys_license;

-- 步骤6：删除原字段，重命名新字段
ALTER TABLE `sys_license` DROP COLUMN `status`;
ALTER TABLE `sys_license` CHANGE COLUMN `status_new` `status` CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- 步骤7：验证字段定义更新
SELECT 
    COLUMN_NAME,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
    AND TABLE_NAME = 'sys_license' 
    AND COLUMN_NAME = 'status';

-- 步骤8：最终数据完整性验证
SELECT 
    'sys_license修复后验证' as check_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as enabled_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as disabled_count,
    SUM(CASE WHEN status NOT IN ('0', '1') THEN 1 ELSE 0 END) as invalid_count
FROM sys_license;

-- 步骤9：验证数据逻辑正确性
-- 检查当前使用的许可证应该是启用状态(status='0')
SELECT 
    'current许可证状态检查' as check_name,
    license_id,
    company_name,
    status,
    current,
    CASE 
        WHEN current = 1 AND status = '0' THEN '正确：当前许可证已启用'
        WHEN current = 1 AND status = '1' THEN '错误：当前许可证被禁用'
        WHEN current = 0 THEN '非当前许可证'
        ELSE '状态异常'
    END as status_check
FROM sys_license;

-- 步骤10：记录修复操作日志
INSERT INTO sys_license_backup_before_fix (license_id, license_key, company_name, contact_info, license_type, max_users, max_warehouses, start_date, end_date, hardware_fingerprint, status, features, create_time, update_time, create_by, update_by, remark, current)
SELECT 
    999999 as license_id,
    'FIELD_STANDARDIZATION_LOG' as license_key,
    '字段标准化修复日志' as company_name,
    NULL as contact_info,
    'log' as license_type,
    0 as max_users,
    0 as max_warehouses,
    NOW() as start_date,
    NULL as end_date,
    NULL as hardware_fingerprint,
    '0' as status,
    'sys_license表status字段标准化修复完成' as features,
    NOW() as create_time,
    NOW() as update_time,
    'field_standardization_script' as create_by,
    'field_standardization_script' as update_by,
    CONCAT('修复时间: ', NOW(), ' - 将status字段从0=禁用,1=启用 改为 0=启用,1=禁用') as remark,
    0 as current;

-- 提交事务
COMMIT;

-- 最终成功提示
SELECT 
    '=== sys_license表字段标准化修复完成 ===' as message,
    '修复内容：status字段 0=启用，1=禁用' as fix_content,
    '备份表：sys_license_backup_before_fix' as backup_table,
    NOW() as completion_time;