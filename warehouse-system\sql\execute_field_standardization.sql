-- =====================================================
-- 字段定义标准化完整数据迁移脚本
-- 
-- 功能描述：
-- 统一执行所有字段定义标准化修复，确保系统中所有状态字段
-- 都遵循项目标准：0=正常/启用/成功，1=异常/禁用/失败
-- 
-- 修复范围：
-- 1. sys_license表的status字段
-- 2. sys_license_feature表的status字段  
-- 3. 操作日志相关字段验证
-- 4. 数据字典标准化
-- 
-- 执行策略：
-- - 分阶段执行，每个阶段都有验证检查点
-- - 完整的备份和回滚机制
-- - 详细的进度跟踪和错误处理
-- - 数据完整性验证
-- =====================================================

USE warehouse_system;

-- =====================================================
-- 阶段0：初始化和环境检查
-- =====================================================

-- 创建迁移日志表
DROP TABLE IF EXISTS field_standardization_log;
CREATE TABLE field_standardization_log (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    migration_id VARCHAR(50) NOT NULL,
    phase VARCHAR(50) NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    status ENUM('started', 'completed', 'failed', 'skipped') NOT NULL,
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    end_time DATETIME NULL,
    affected_records INT DEFAULT 0,
    error_message TEXT NULL,
    details JSON NULL,
    INDEX idx_migration_id (migration_id),
    INDEX idx_phase (phase),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字段标准化迁移日志表';

-- 生成唯一的迁移ID
SET @migration_id = CONCAT('FIELD_STD_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));

-- 记录迁移开始
INSERT INTO field_standardization_log (migration_id, phase, step_name, status, details) 
VALUES (@migration_id, 'INIT', '迁移初始化', 'started', JSON_OBJECT('start_time', NOW()));

-- 环境检查
SELECT 
    '=== 字段定义标准化迁移开始 ===' as message,
    @migration_id as migration_id,
    NOW() as start_time,
    USER() as executor,
    DATABASE() as target_database;

-- 检查必要的表是否存在
SELECT 
    '环境检查' as check_type,
    table_name,
    CASE 
        WHEN table_name IS NOT NULL THEN '✓ 存在'
        ELSE '✗ 不存在'
    END as status
FROM (
    SELECT 'sys_license' as expected_table
    UNION ALL SELECT 'sys_license_feature'
    UNION ALL SELECT 'sys_dict_data'
) expected
LEFT JOIN (
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'warehouse_system'
) existing ON expected.expected_table = existing.table_name;

-- 更新初始化状态
UPDATE field_standardization_log 
SET status = 'completed', end_time = NOW() 
WHERE migration_id = @migration_id AND phase = 'INIT' AND step_name = '迁移初始化';

-- =====================================================
-- 阶段1：数据备份
-- =====================================================

-- 记录备份阶段开始
INSERT INTO field_standardization_log (migration_id, phase, step_name, status) 
VALUES (@migration_id, 'BACKUP', '数据备份', 'started');

-- 开始备份事务
START TRANSACTION;

-- 创建备份数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS warehouse_system_field_std_backup;

-- 备份sys_license表
DROP TABLE IF EXISTS warehouse_system_field_std_backup.sys_license_backup;
CREATE TABLE warehouse_system_field_std_backup.sys_license_backup AS 
SELECT * FROM sys_license;

-- 备份sys_license_feature表
DROP TABLE IF EXISTS warehouse_system_field_std_backup.sys_license_feature_backup;
CREATE TABLE warehouse_system_field_std_backup.sys_license_feature_backup AS 
SELECT * FROM sys_license_feature;

-- 备份相关数据字典
DROP TABLE IF EXISTS warehouse_system_field_std_backup.sys_dict_data_backup;
CREATE TABLE warehouse_system_field_std_backup.sys_dict_data_backup AS 
SELECT * FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status');

-- 创建备份元信息表
DROP TABLE IF EXISTS warehouse_system_field_std_backup.backup_metadata;
CREATE TABLE warehouse_system_field_std_backup.backup_metadata (
    backup_id INT AUTO_INCREMENT PRIMARY KEY,
    migration_id VARCHAR(50) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    original_count INT NOT NULL,
    backup_count INT NOT NULL,
    backup_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    checksum VARCHAR(32) NULL,
    status ENUM('completed', 'failed') DEFAULT 'completed'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 记录备份信息
INSERT INTO warehouse_system_field_std_backup.backup_metadata 
(migration_id, table_name, original_count, backup_count) VALUES
(@migration_id, 'sys_license', 
 (SELECT COUNT(*) FROM sys_license), 
 (SELECT COUNT(*) FROM warehouse_system_field_std_backup.sys_license_backup)),
(@migration_id, 'sys_license_feature', 
 (SELECT COUNT(*) FROM sys_license_feature), 
 (SELECT COUNT(*) FROM warehouse_system_field_std_backup.sys_license_feature_backup)),
(@migration_id, 'sys_dict_data', 
 (SELECT COUNT(*) FROM sys_dict_data WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status')), 
 (SELECT COUNT(*) FROM warehouse_system_field_std_backup.sys_dict_data_backup));

-- 验证备份完整性
SELECT 
    '备份验证' as check_type,
    table_name,
    original_count,
    backup_count,
    CASE 
        WHEN original_count = backup_count THEN '✓ 备份完整'
        ELSE CONCAT('✗ 备份不完整，差异：', ABS(original_count - backup_count), '条')
    END as backup_status
FROM warehouse_system_field_std_backup.backup_metadata
WHERE migration_id = @migration_id;

-- 检查备份是否成功
SET @backup_failed = (
    SELECT COUNT(*) 
    FROM warehouse_system_field_std_backup.backup_metadata 
    WHERE migration_id = @migration_id AND original_count != backup_count
);

-- 如果备份失败，回滚并终止
IF @backup_failed > 0 THEN
    ROLLBACK;
    INSERT INTO field_standardization_log (migration_id, phase, step_name, status, error_message) 
    VALUES (@migration_id, 'BACKUP', '数据备份', 'failed', '备份验证失败，存在数据不一致');
    
    SELECT '❌ 备份失败，迁移终止' as error_message, @migration_id as migration_id;
    -- 这里应该终止脚本执行，但SQL不支持直接终止，需要在应用层处理
END IF;

-- 提交备份事务
COMMIT;

-- 更新备份状态
UPDATE field_standardization_log 
SET status = 'completed', end_time = NOW(), 
    affected_records = (SELECT SUM(backup_count) FROM warehouse_system_field_std_backup.backup_metadata WHERE migration_id = @migration_id)
WHERE migration_id = @migration_id AND phase = 'BACKUP' AND step_name = '数据备份';

SELECT 
    '✓ 备份阶段完成' as message,
    (SELECT SUM(backup_count) FROM warehouse_system_field_std_backup.backup_metadata WHERE migration_id = @migration_id) as total_backup_records,
    NOW() as completion_time;

-- =====================================================
-- 阶段2：sys_license表字段标准化
-- =====================================================

-- 记录sys_license修复开始
INSERT INTO field_standardization_log (migration_id, phase, step_name, status) 
VALUES (@migration_id, 'SYS_LICENSE', 'sys_license表字段修复', 'started');

-- 开始sys_license修复事务
START TRANSACTION;

-- 记录修复前状态
SELECT 
    'sys_license修复前状态' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '禁用(当前定义)'
        WHEN status = '1' THEN '启用(当前定义)'
        ELSE '未知状态'
    END as current_meaning,
    COUNT(*) as count
FROM sys_license 
GROUP BY status
ORDER BY status;

-- 添加临时字段
ALTER TABLE sys_license ADD COLUMN status_temp CHAR(1) DEFAULT '0' COMMENT '临时状态字段';

-- 执行数据转换（0↔1值互换）
UPDATE sys_license SET status_temp = CASE 
    WHEN status = '0' THEN '1'  -- 原来禁用(0) -> 新标准禁用(1)
    WHEN status = '1' THEN '0'  -- 原来启用(1) -> 新标准启用(0)
    ELSE '0'  -- 默认为启用状态
END;

-- 验证转换结果
SELECT 
    'sys_license数据转换验证' as step,
    status as old_status,
    status_temp as new_status,
    CASE 
        WHEN status = '0' AND status_temp = '1' THEN '✓ 禁用->禁用'
        WHEN status = '1' AND status_temp = '0' THEN '✓ 启用->启用'
        ELSE '✗ 转换异常'
    END as conversion_result,
    COUNT(*) as count
FROM sys_license 
GROUP BY status, status_temp
ORDER BY status;

-- 检查转换异常
SET @license_conversion_errors = (
    SELECT COUNT(*) FROM sys_license 
    WHERE (status = '0' AND status_temp != '1') OR (status = '1' AND status_temp != '0')
);

-- 如果转换有误，回滚
IF @license_conversion_errors > 0 THEN
    ROLLBACK;
    INSERT INTO field_standardization_log (migration_id, phase, step_name, status, error_message) 
    VALUES (@migration_id, 'SYS_LICENSE', 'sys_license表字段修复', 'failed', 
            CONCAT('数据转换异常，错误记录数：', @license_conversion_errors));
    
    SELECT '❌ sys_license数据转换失败' as error_message;
    -- 应该终止执行
END IF;

-- 删除原字段，重命名新字段
ALTER TABLE sys_license DROP COLUMN status;
ALTER TABLE sys_license CHANGE COLUMN status_temp status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- 验证字段定义
SELECT 
    'sys_license字段定义验证' as check_type,
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME = 'sys_license' 
  AND COLUMN_NAME = 'status';

-- 验证修复后数据分布
SELECT 
    'sys_license修复后状态分布' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '启用(新标准)'
        WHEN status = '1' THEN '禁用(新标准)'
        ELSE '未知状态'
    END as new_meaning,
    COUNT(*) as count
FROM sys_license 
GROUP BY status
ORDER BY status;

-- 提交sys_license修复
COMMIT;

-- 更新sys_license修复状态
UPDATE field_standardization_log 
SET status = 'completed', end_time = NOW(), 
    affected_records = (SELECT COUNT(*) FROM sys_license)
WHERE migration_id = @migration_id AND phase = 'SYS_LICENSE' AND step_name = 'sys_license表字段修复';

SELECT 
    '✓ sys_license表修复完成' as message,
    (SELECT COUNT(*) FROM sys_license) as affected_records,
    NOW() as completion_time;

-- =====================================================
-- 阶段3：sys_license_feature表字段标准化
-- =====================================================

-- 记录sys_license_feature修复开始
INSERT INTO field_standardization_log (migration_id, phase, step_name, status) 
VALUES (@migration_id, 'SYS_LICENSE_FEATURE', 'sys_license_feature表字段修复', 'started');

-- 开始sys_license_feature修复事务
START TRANSACTION;

-- 记录修复前状态
SELECT 
    'sys_license_feature修复前状态' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '禁用(当前定义)'
        WHEN status = '1' THEN '启用(当前定义)'
        ELSE '未知状态'
    END as current_meaning,
    COUNT(*) as count
FROM sys_license_feature 
GROUP BY status
ORDER BY status;

-- 添加临时字段
ALTER TABLE sys_license_feature ADD COLUMN status_temp CHAR(1) DEFAULT '0' COMMENT '临时状态字段';

-- 执行数据转换（0↔1值互换）
UPDATE sys_license_feature SET status_temp = CASE 
    WHEN status = '0' THEN '1'  -- 原来禁用(0) -> 新标准禁用(1)
    WHEN status = '1' THEN '0'  -- 原来启用(1) -> 新标准启用(0)
    ELSE '0'  -- 默认为启用状态
END;

-- 验证转换结果
SELECT 
    'sys_license_feature数据转换验证' as step,
    status as old_status,
    status_temp as new_status,
    CASE 
        WHEN status = '0' AND status_temp = '1' THEN '✓ 禁用->禁用'
        WHEN status = '1' AND status_temp = '0' THEN '✓ 启用->启用'
        ELSE '✗ 转换异常'
    END as conversion_result,
    COUNT(*) as count
FROM sys_license_feature 
GROUP BY status, status_temp
ORDER BY status;

-- 检查转换异常
SET @feature_conversion_errors = (
    SELECT COUNT(*) FROM sys_license_feature 
    WHERE (status = '0' AND status_temp != '1') OR (status = '1' AND status_temp != '0')
);

-- 如果转换有误，回滚
IF @feature_conversion_errors > 0 THEN
    ROLLBACK;
    INSERT INTO field_standardization_log (migration_id, phase, step_name, status, error_message) 
    VALUES (@migration_id, 'SYS_LICENSE_FEATURE', 'sys_license_feature表字段修复', 'failed', 
            CONCAT('数据转换异常，错误记录数：', @feature_conversion_errors));
    
    SELECT '❌ sys_license_feature数据转换失败' as error_message;
    -- 应该终止执行
END IF;

-- 删除原字段，重命名新字段
ALTER TABLE sys_license_feature DROP COLUMN status;
ALTER TABLE sys_license_feature CHANGE COLUMN status_temp status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- 验证字段定义
SELECT 
    'sys_license_feature字段定义验证' as check_type,
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME = 'sys_license_feature' 
  AND COLUMN_NAME = 'status';

-- 验证核心功能状态
SELECT 
    'sys_license_feature核心功能检查' as check_type,
    feature_code,
    feature_name,
    is_core,
    status,
    CASE 
        WHEN is_core = '1' AND status = '1' THEN '⚠️ 核心功能被禁用'
        WHEN is_core = '1' AND status = '0' THEN '✓ 核心功能正常'
        WHEN is_core = '0' AND status = '0' THEN '✓ 可选功能启用'
        WHEN is_core = '0' AND status = '1' THEN '- 可选功能禁用'
        ELSE '? 状态未知'
    END as availability_status
FROM sys_license_feature 
ORDER BY is_core DESC, sort_order;

-- 提交sys_license_feature修复
COMMIT;

-- 更新sys_license_feature修复状态
UPDATE field_standardization_log 
SET status = 'completed', end_time = NOW(), 
    affected_records = (SELECT COUNT(*) FROM sys_license_feature)
WHERE migration_id = @migration_id AND phase = 'SYS_LICENSE_FEATURE' AND step_name = 'sys_license_feature表字段修复';

SELECT 
    '✓ sys_license_feature表修复完成' as message,
    (SELECT COUNT(*) FROM sys_license_feature) as affected_records,
    NOW() as completion_time;

-- =====================================================
-- 阶段4：操作日志字段验证
-- =====================================================

-- 记录操作日志验证开始
INSERT INTO field_standardization_log (migration_id, phase, step_name, status) 
VALUES (@migration_id, 'OPERATION_LOG', '操作日志字段验证', 'started');

-- 检查操作日志表是否存在
SET @operation_log_exists = (
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = 'warehouse_system' 
      AND table_name = 'wms_operation_log'
);

IF @operation_log_exists > 0 THEN
    -- 验证操作日志字段定义
    SELECT 
        '操作日志字段检查' as check_type,
        COLUMN_NAME,
        COLUMN_TYPE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT,
        CASE 
            WHEN COLUMN_NAME = 'operation_status' AND COLUMN_COMMENT LIKE '%0%成功%1%失败%' THEN '✓ 符合标准'
            WHEN COLUMN_NAME = 'operation_status' AND COLUMN_COMMENT LIKE '%0%正常%1%异常%' THEN '✓ 符合标准'
            WHEN COLUMN_NAME = 'operation_status' THEN '⚠️ 需要检查注释'
            ELSE '- 其他字段'
        END as standard_compliance
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'warehouse_system' 
      AND TABLE_NAME = 'wms_operation_log' 
      AND COLUMN_NAME LIKE '%status%';
    
    -- 检查操作日志数据分布
    SELECT 
        '操作日志状态分布' as analysis_type,
        operation_status,
        CASE 
            WHEN operation_status = '0' THEN '成功/正常'
            WHEN operation_status = '1' THEN '失败/异常'
            ELSE '未知状态'
        END as status_meaning,
        COUNT(*) as count
    FROM wms_operation_log 
    GROUP BY operation_status
    ORDER BY operation_status;
    
    UPDATE field_standardization_log 
    SET status = 'completed', end_time = NOW(), 
        affected_records = (SELECT COUNT(*) FROM wms_operation_log),
        details = JSON_OBJECT('note', '操作日志表存在，已验证字段定义')
    WHERE migration_id = @migration_id AND phase = 'OPERATION_LOG' AND step_name = '操作日志字段验证';
ELSE
    UPDATE field_standardization_log 
    SET status = 'skipped', end_time = NOW(), 
        details = JSON_OBJECT('note', '操作日志表不存在，跳过验证')
    WHERE migration_id = @migration_id AND phase = 'OPERATION_LOG' AND step_name = '操作日志字段验证';
    
    SELECT '- 操作日志表不存在，跳过验证' as message;
END IF;

-- =====================================================
-- 阶段5：数据字典标准化
-- =====================================================

-- 记录数据字典标准化开始
INSERT INTO field_standardization_log (migration_id, phase, step_name, status) 
VALUES (@migration_id, 'DICT_DATA', '数据字典标准化', 'started');

-- 开始数据字典标准化事务
START TRANSACTION;

-- 更新sys_normal_disable字典数据
UPDATE sys_dict_data 
SET dict_label = '正常', list_class = 'primary'
WHERE dict_type = 'sys_normal_disable' AND dict_value = '0';

UPDATE sys_dict_data 
SET dict_label = '停用', list_class = 'danger'
WHERE dict_type = 'sys_normal_disable' AND dict_value = '1';

-- 确保操作状态字典存在
INSERT IGNORE INTO sys_dict_data 
(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(1, '成功', '0', 'operation_status', '', 'success', 'Y', '0', 'field_standardization', NOW(), '操作成功'),
(2, '失败', '1', 'operation_status', '', 'danger', 'N', '0', 'field_standardization', NOW(), '操作失败');

-- 验证字典数据
SELECT 
    '数据字典验证' as check_type,
    dict_type,
    dict_value,
    dict_label,
    list_class,
    CASE 
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '0' AND dict_label = '正常' THEN '✓ 正确'
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '1' AND dict_label = '停用' THEN '✓ 正确'
        WHEN dict_type = 'operation_status' AND dict_value = '0' AND dict_label = '成功' THEN '✓ 正确'
        WHEN dict_type = 'operation_status' AND dict_value = '1' AND dict_label = '失败' THEN '✓ 正确'
        ELSE '⚠️ 需要检查'
    END as validation_result
FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status')
ORDER BY dict_type, dict_sort;

-- 提交数据字典标准化
COMMIT;

-- 更新数据字典标准化状态
UPDATE field_standardization_log 
SET status = 'completed', end_time = NOW(), 
    affected_records = (SELECT COUNT(*) FROM sys_dict_data WHERE dict_type IN ('sys_normal_disable', 'operation_status'))
WHERE migration_id = @migration_id AND phase = 'DICT_DATA' AND step_name = '数据字典标准化';

SELECT 
    '✓ 数据字典标准化完成' as message,
    NOW() as completion_time;

-- =====================================================
-- 阶段6：最终验证和报告
-- =====================================================

-- 记录最终验证开始
INSERT INTO field_standardization_log (migration_id, phase, step_name, status) 
VALUES (@migration_id, 'VALIDATION', '最终验证', 'started');

-- 综合数据完整性验证
SELECT 
    '=== 字段标准化迁移完成验证报告 ===' as report_title,
    @migration_id as migration_id,
    NOW() as completion_time;

-- 验证所有表的记录数是否一致
SELECT 
    '数据完整性验证' as check_type,
    table_name,
    original_count,
    current_count,
    CASE 
        WHEN original_count = current_count THEN '✓ 数据完整'
        ELSE CONCAT('✗ 数据不一致，差异：', ABS(original_count - current_count))
    END as integrity_status
FROM (
    SELECT 
        'sys_license' as table_name,
        (SELECT backup_count FROM warehouse_system_field_std_backup.backup_metadata 
         WHERE migration_id = @migration_id AND table_name = 'sys_license') as original_count,
        (SELECT COUNT(*) FROM sys_license) as current_count
    UNION ALL
    SELECT 
        'sys_license_feature' as table_name,
        (SELECT backup_count FROM warehouse_system_field_std_backup.backup_metadata 
         WHERE migration_id = @migration_id AND table_name = 'sys_license_feature') as original_count,
        (SELECT COUNT(*) FROM sys_license_feature) as current_count
) as integrity_check;

-- 验证字段定义标准
SELECT 
    '字段定义标准验证' as check_type,
    table_name,
    column_name,
    column_type,
    column_default,
    column_comment,
    CASE 
        WHEN column_name = 'status' AND column_default = '0' AND column_comment LIKE '%0%正常%1%停用%' THEN '✓ 符合标准'
        WHEN column_name = 'status' AND column_default = '0' AND column_comment LIKE '%0%启用%1%禁用%' THEN '✓ 符合标准'
        ELSE '⚠️ 需要检查'
    END as standard_compliance
FROM information_schema.columns 
WHERE table_schema = 'warehouse_system' 
  AND table_name IN ('sys_license', 'sys_license_feature') 
  AND column_name = 'status';

-- 验证业务逻辑正确性
SELECT 
    '业务逻辑验证' as check_type,
    'sys_license当前许可证状态' as check_item,
    COUNT(*) as total_current_licenses,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as enabled_current_licenses,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as disabled_current_licenses,
    CASE 
        WHEN SUM(CASE WHEN current = 1 AND status = '1' THEN 1 ELSE 0 END) > 0 THEN '⚠️ 存在被禁用的当前许可证'
        WHEN SUM(CASE WHEN current = 1 AND status = '0' THEN 1 ELSE 0 END) > 0 THEN '✓ 当前许可证状态正常'
        ELSE '- 无当前许可证'
    END as logic_validation
FROM sys_license 
WHERE current = 1;

-- 生成迁移统计报告
SELECT 
    '迁移统计报告' as report_section,
    phase,
    step_name,
    status,
    affected_records,
    TIMESTAMPDIFF(SECOND, start_time, COALESCE(end_time, NOW())) as duration_seconds,
    CASE 
        WHEN status = 'completed' THEN '✓'
        WHEN status = 'failed' THEN '✗'
        WHEN status = 'skipped' THEN '-'
        ELSE '?'
    END as status_icon
FROM field_standardization_log 
WHERE migration_id = @migration_id
ORDER BY log_id;

-- 检查是否有失败的步骤
SET @failed_steps = (
    SELECT COUNT(*) 
    FROM field_standardization_log 
    WHERE migration_id = @migration_id AND status = 'failed'
);

-- 更新最终验证状态
IF @failed_steps = 0 THEN
    UPDATE field_standardization_log 
    SET status = 'completed', end_time = NOW(),
        details = JSON_OBJECT('result', '所有步骤执行成功', 'total_phases', 
                             (SELECT COUNT(DISTINCT phase) FROM field_standardization_log WHERE migration_id = @migration_id))
    WHERE migration_id = @migration_id AND phase = 'VALIDATION' AND step_name = '最终验证';
    
    SELECT 
        '🎉 字段定义标准化迁移成功完成！' as final_result,
        @migration_id as migration_id,
        (SELECT COUNT(DISTINCT phase) FROM field_standardization_log WHERE migration_id = @migration_id) as completed_phases,
        (SELECT SUM(affected_records) FROM field_standardization_log WHERE migration_id = @migration_id AND affected_records > 0) as total_affected_records,
        NOW() as completion_time;
ELSE
    UPDATE field_standardization_log 
    SET status = 'failed', end_time = NOW(),
        error_message = CONCAT('存在', @failed_steps, '个失败步骤'),
        details = JSON_OBJECT('failed_steps', @failed_steps)
    WHERE migration_id = @migration_id AND phase = 'VALIDATION' AND step_name = '最终验证';
    
    SELECT 
        '❌ 字段定义标准化迁移部分失败' as final_result,
        @migration_id as migration_id,
        @failed_steps as failed_steps,
        '请检查迁移日志并执行回滚' as recommendation;
END IF;

-- =====================================================
-- 使用说明和后续步骤
-- =====================================================

SELECT 
    '=== 迁移完成后的使用说明 ===' as section_title;

SELECT 
    '修复后的标准用法' as usage_type,
    '查询启用的许可证' as operation,
    'SELECT * FROM sys_license WHERE status = ''0''' as sql_example
UNION ALL
SELECT 
    '修复后的标准用法',
    '查询禁用的许可证',
    'SELECT * FROM sys_license WHERE status = ''1'''
UNION ALL
SELECT 
    '修复后的标准用法',
    '启用许可证',
    'UPDATE sys_license SET status = ''0'' WHERE license_id = ?'
UNION ALL
SELECT 
    '修复后的标准用法',
    '禁用许可证',
    'UPDATE sys_license SET status = ''1'' WHERE license_id = ?'
UNION ALL
SELECT 
    '修复后的标准用法',
    '查询可用功能',
    'SELECT * FROM sys_license_feature WHERE status = ''0'''
UNION ALL
SELECT 
    '修复后的标准用法',
    '记录成功操作',
    'INSERT INTO wms_operation_log (..., operation_status) VALUES (..., ''0'')'
UNION ALL
SELECT 
    '修复后的标准用法',
    '记录失败操作',
    'INSERT INTO wms_operation_log (..., operation_status) VALUES (..., ''1'')';

SELECT 
    '后续必要步骤' as step_type,
    '1. 更新Java业务代码中的状态判断逻辑' as step_description
UNION ALL
SELECT 
    '后续必要步骤',
    '2. 更新前端页面中的状态显示和操作逻辑'
UNION ALL
SELECT 
    '后续必要步骤',
    '3. 执行完整的功能回归测试'
UNION ALL
SELECT 
    '后续必要步骤',
    '4. 更新相关的API文档和开发规范'
UNION ALL
SELECT 
    '后续必要步骤',
    '5. 培训团队成员新的字段定义标准';

SELECT 
    '备份信息' as info_type,
    CONCAT('备份数据库：warehouse_system_field_std_backup') as backup_location,
    CONCAT('迁移ID：', @migration_id) as migration_identifier,
    '如需回滚，请使用rollback_field_standardization.sql脚本' as rollback_instruction;

-- 记录迁移完成
INSERT INTO field_standardization_log (migration_id, phase, step_name, status, details) 
VALUES (@migration_id, 'COMPLETED', '迁移完成', 'completed', 
        JSON_OBJECT('completion_time', NOW(), 'total_duration_minutes', 
                   TIMESTAMPDIFF(MINUTE, 
                                (SELECT MIN(start_time) FROM field_standardization_log WHERE migration_id = @migration_id), 
                                NOW())));