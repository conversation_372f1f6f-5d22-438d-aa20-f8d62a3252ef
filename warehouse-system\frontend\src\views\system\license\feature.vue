<template>
  <div class="app-container">
    <!-- 功能权限管理页面 -->
    <el-card shadow="hover" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold;">功能权限管理</span>
        <div style="float: right;">
          <el-tag type="success" size="mini">字段标准化已修复</el-tag>
        </div>
      </div>
      
      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="功能代码" prop="featureCode">
          <el-input
            v-model="queryParams.featureCode"
            placeholder="请输入功能代码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="功能名称" prop="featureName">
          <el-input
            v-model="queryParams.featureName"
            placeholder="请输入功能名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="功能状态" clearable>
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="核心功能" prop="isCore">
          <el-select v-model="queryParams.isCore" placeholder="是否核心功能" clearable>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:feature:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-check"
            size="mini"
            :disabled="multiple"
            @click="handleEnableBatch"
            v-hasPermi="['system:feature:edit']"
          >批量启用</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-close"
            size="mini"
            :disabled="multiple"
            @click="handleDisableBatch"
            v-hasPermi="['system:feature:edit']"
          >批量禁用</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:feature:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:feature:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="featureList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="功能ID" align="center" prop="featureId" width="80" />
        <el-table-column label="功能代码" align="center" prop="featureCode" :show-overflow-tooltip="true" />
        <el-table-column label="功能名称" align="center" prop="featureName" :show-overflow-tooltip="true" />
        <el-table-column label="功能描述" align="center" prop="featureDesc" :show-overflow-tooltip="true" />
        <el-table-column label="支持授权类型" align="center" prop="licenseTypes" width="150">
          <template slot-scope="scope">
            <el-tag 
              v-for="type in parseLicenseTypes(scope.row.licenseTypes)" 
              :key="type" 
              size="mini" 
              :type="getLicenseTypeTagType(type)"
              style="margin-right: 5px;"
            >
              {{ getLicenseTypeName(type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="核心功能" align="center" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isCore === '1' ? 'danger' : 'info'" size="mini">
              {{ scope.row.isCore === '1' ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
        <el-table-column label="状态" align="center" width="80">
          <template slot-scope="scope">
            <!-- 修复后的状态显示：0=正常，1=停用 -->
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'" size="mini">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:feature:edit']"
            >修改</el-button>
            <!-- 修复后的启用/禁用按钮：基于新标准 0=正常，1=停用 -->
            <el-button
              v-if="scope.row.status === '1'"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleEnable(scope.row)"
              v-hasPermi="['system:feature:edit']"
            >启用</el-button>
            <el-button
              v-if="scope.row.status === '0' && scope.row.isCore !== '1'"
              size="mini"
              type="text"
              icon="el-icon-close"
              @click="handleDisable(scope.row)"
              v-hasPermi="['system:feature:edit']"
            >禁用</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:feature:remove']"
              :disabled="scope.row.isCore === '1'"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改功能权限对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="功能代码" prop="featureCode">
          <el-input v-model="form.featureCode" placeholder="请输入功能代码" :disabled="form.featureId != null" />
        </el-form-item>
        <el-form-item label="功能名称" prop="featureName">
          <el-input v-model="form.featureName" placeholder="请输入功能名称" />
        </el-form-item>
        <el-form-item label="功能描述" prop="featureDesc">
          <el-input v-model="form.featureDesc" type="textarea" placeholder="请输入功能描述" />
        </el-form-item>
        <el-form-item label="支持授权类型" prop="licenseTypes">
          <el-checkbox-group v-model="selectedLicenseTypes">
            <el-checkbox label="trial">试用版</el-checkbox>
            <el-checkbox label="standard">标准版</el-checkbox>
            <el-checkbox label="enterprise">企业版</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="是否核心功能" prop="isCore">
          <el-radio-group v-model="form.isCore">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <!-- 修复后的状态选项：0=正常，1=停用 -->
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listLicenseFeature, 
  getLicenseFeature, 
  delLicenseFeature, 
  addLicenseFeature, 
  updateLicenseFeature,
  enableFeature,
  disableFeature,
  enableFeatures,
  disableFeatures
} from "@/api/system/licenseFeature";

export default {
  name: "LicenseFeature",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 功能权限表格数据
      featureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        featureCode: null,
        featureName: null,
        featureDesc: null,
        licenseTypes: null,
        isCore: null,
        status: null
      },
      // 表单参数
      form: {},
      // 选中的授权类型
      selectedLicenseTypes: [],
      // 表单校验
      rules: {
        featureCode: [
          { required: true, message: "功能代码不能为空", trigger: "blur" },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: "功能代码只能包含字母、数字和下划线，且以字母开头", trigger: "blur" }
        ],
        featureName: [
          { required: true, message: "功能名称不能为空", trigger: "blur" }
        ],
        isCore: [
          { required: true, message: "请选择是否核心功能", trigger: "change" }
        ],
        status: [
          { required: true, message: "请选择状态", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询功能权限列表 */
    getList() {
      this.loading = true;
      listLicenseFeature(this.queryParams).then(response => {
        this.featureList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        featureId: null,
        featureCode: null,
        featureName: null,
        featureDesc: null,
        licenseTypes: null,
        isCore: "0",
        sortOrder: 0,
        status: "0" // 修复：默认为正常状态
      };
      this.selectedLicenseTypes = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.featureId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加功能权限";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const featureId = row.featureId || this.ids
      getLicenseFeature(featureId).then(response => {
        this.form = response.data;
        // 解析授权类型
        if (this.form.licenseTypes) {
          this.selectedLicenseTypes = this.form.licenseTypes.split(',');
        }
        this.open = true;
        this.title = "修改功能权限";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 组合授权类型
          this.form.licenseTypes = this.selectedLicenseTypes.join(',');
          
          if (this.form.featureId != null) {
            updateLicenseFeature(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLicenseFeature(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const featureIds = row.featureId || this.ids;
      this.$modal.confirm('是否确认删除功能权限编号为"' + featureIds + '"的数据项？').then(function() {
        return delLicenseFeature(featureIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 启用功能 */
    handleEnable(row) {
      const featureId = row.featureId;
      const featureName = row.featureName;
      this.$modal.confirm('是否确认启用功能"' + featureName + '"？').then(function() {
        return enableFeature(featureId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("启用成功");
      }).catch(() => {});
    },
    /** 禁用功能 */
    handleDisable(row) {
      const featureId = row.featureId;
      const featureName = row.featureName;
      
      // 检查是否为核心功能
      if (row.isCore === '1') {
        this.$modal.msgError("核心功能不能禁用");
        return;
      }
      
      this.$modal.confirm('是否确认禁用功能"' + featureName + '"？').then(function() {
        return disableFeature(featureId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("禁用成功");
      }).catch(() => {});
    },
    /** 批量启用功能 */
    handleEnableBatch() {
      const featureIds = this.ids;
      this.$modal.confirm('是否确认启用选中的功能？').then(function() {
        return enableFeatures(featureIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("启用成功");
      }).catch(() => {});
    },
    /** 批量禁用功能 */
    handleDisableBatch() {
      const featureIds = this.ids;
      
      // 检查是否包含核心功能
      const coreFeatures = this.featureList.filter(item => 
        featureIds.includes(item.featureId) && item.isCore === '1'
      );
      
      if (coreFeatures.length > 0) {
        this.$modal.msgError("选中的功能中包含核心功能，不能禁用");
        return;
      }
      
      this.$modal.confirm('是否确认禁用选中的功能？').then(function() {
        return disableFeatures(featureIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("禁用成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/feature/export', {
        ...this.queryParams
      }, `feature_${new Date().getTime()}.xlsx`)
    },
    /** 解析授权类型 */
    parseLicenseTypes(licenseTypes) {
      if (!licenseTypes) return [];
      return licenseTypes.split(',').filter(type => type.trim());
    },
    /** 获取授权类型标签类型 */
    getLicenseTypeTagType(type) {
      const typeMap = {
        'trial': 'info',
        'standard': 'warning', 
        'enterprise': 'success'
      };
      return typeMap[type] || 'info';
    },
    /** 获取授权类型名称 */
    getLicenseTypeName(type) {
      const nameMap = {
        'trial': '试用版',
        'standard': '标准版',
        'enterprise': '企业版'
      };
      return nameMap[type] || type;
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>