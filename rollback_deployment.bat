@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: 字段定义标准化快速回滚脚本
:: 版本: 1.0
:: 创建时间: %date% %time%
:: 描述: 快速回滚数据库和代码到部署前状态
:: ========================================

echo.
echo ========================================
echo 字段定义标准化快速回滚脚本
echo ========================================
echo.

:: 设置项目路径和配置
set "PROJECT_ROOT=C:\CKGLXT\warehouse-system"
set "BACKEND_PATH=%PROJECT_ROOT%\backend"
set "FRONTEND_PATH=%PROJECT_ROOT%\frontend"
set "DB_HOST=localhost"
set "DB_PORT=3306"
set "DB_NAME=warehouse_system"
set "DB_USER=root"
set "DB_PASSWORD=123456"
set "BACKUP_DIR=%PROJECT_ROOT%\backups"
set "LOG_FILE=%PROJECT_ROOT%\rollback.log"

:: 创建日志函数
call :log "开始字段定义标准化回滚流程"

:: 确认回滚操作
echo.
echo ========================================
echo 警告：即将执行回滚操作
echo ========================================
echo.
echo 此操作将：
echo 1. 停止当前运行的服务
echo 2. 恢复数据库到部署前状态
echo 3. 恢复代码到上一个版本
echo 4. 重新启动服务
echo.
echo 所有在部署后的数据变更将会丢失！
echo.
set /p "confirm=确认执行回滚操作？(Y/N): "
if /i "!confirm!" neq "Y" (
    call :log "用户取消回滚操作"
    echo 回滚操作已取消
    pause
    goto :end
)

:: 检查回滚环境
call :log "检查回滚环境"
call :check_rollback_prerequisites
if !errorlevel! neq 0 (
    call :log "ERROR: 回滚环境检查失败"
    goto :error_exit
)

:: 第一阶段：停止服务
call :log "阶段1: 停止应用服务"
call :stop_services
if !errorlevel! neq 0 (
    call :log "WARNING: 服务停止可能不完整，继续执行回滚"
)

:: 第二阶段：创建当前状态备份（以防回滚失败需要恢复）
call :log "阶段2: 创建当前状态备份"
call :create_current_backup
if !errorlevel! neq 0 (
    call :log "WARNING: 当前状态备份失败，继续执行回滚"
)

:: 第三阶段：回滚数据库
call :log "阶段3: 回滚数据库到部署前状态"
call :rollback_database
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库回滚失败"
    goto :error_exit
)

:: 第四阶段：验证数据库回滚
call :log "阶段4: 验证数据库回滚结果"
call :verify_database_rollback
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库回滚验证失败"
    goto :error_exit
)

:: 第五阶段：回滚代码
call :log "阶段5: 回滚代码到上一个版本"
call :rollback_code
if !errorlevel! neq 0 (
    call :log "WARNING: 代码回滚失败，继续执行"
)

:: 第六阶段：重新编译
call :log "阶段6: 重新编译应用"
call :rebuild_application
if !errorlevel! neq 0 (
    call :log "ERROR: 应用重新编译失败"
    goto :error_exit
)

:: 第七阶段：启动服务
call :log "阶段7: 启动应用服务"
call :start_services
if !errorlevel! neq 0 (
    call :log "ERROR: 服务启动失败"
    goto :error_exit
)

:: 第八阶段：验证回滚结果
call :log "阶段8: 验证回滚结果"
call :verify_rollback
if !errorlevel! neq 0 (
    call :log "ERROR: 回滚验证失败"
    goto :error_exit
)

:: 回滚成功
call :log "SUCCESS: 字段定义标准化回滚成功完成"
echo.
echo ========================================
echo 回滚成功完成！
echo 系统已恢复到部署前状态
echo 当前状态备份: %BACKUP_DIR%
echo 日志文件: %LOG_FILE%
echo ========================================
echo.
pause
goto :end

:: ========================================
:: 函数定义
:: ========================================

:check_rollback_prerequisites
call :log "检查回滚环境和必要工具"

:: 检查MySQL客户端
mysql --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: MySQL客户端未安装或不在PATH中"
    exit /b 1
)

:: 检查Java环境
java -version >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: Java环境未配置"
    exit /b 1
)

:: 检查Maven
mvn --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: Maven未安装或不在PATH中"
    exit /b 1
)

:: 检查Git（用于代码回滚）
git --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log "WARNING: Git未安装，代码回滚可能失败"
)

:: 检查项目目录
if not exist "%PROJECT_ROOT%" (
    call :log "ERROR: 项目根目录不存在: %PROJECT_ROOT%"
    exit /b 1
)

if not exist "%BACKEND_PATH%" (
    call :log "ERROR: 后端目录不存在: %BACKEND_PATH%"
    exit /b 1
)

:: 检查备份目录
if not exist "%BACKUP_DIR%" (
    call :log "ERROR: 备份目录不存在: %BACKUP_DIR%"
    exit /b 1
)

:: 查找可用的备份文件
call :find_latest_backup
if !errorlevel! neq 0 (
    call :log "ERROR: 未找到可用的备份文件"
    exit /b 1
)

call :log "回滚环境检查通过"
exit /b 0

:find_latest_backup
call :log "查找最新的备份文件"

set "LATEST_BACKUP="
for /f "delims=" %%i in ('dir /b /o-d "%BACKUP_DIR%\warehouse_system_backup_*.sql" 2^>nul') do (
    set "LATEST_BACKUP=%BACKUP_DIR%\%%i"
    goto :found_backup
)

call :log "ERROR: 未找到备份文件"
exit /b 1

:found_backup
call :log "找到备份文件: !LATEST_BACKUP!"

:: 检查备份文件完整性
if not exist "!LATEST_BACKUP!" (
    call :log "ERROR: 备份文件不存在"
    exit /b 1
)

:: 检查备份文件大小
for %%A in ("!LATEST_BACKUP!") do set backup_size=%%~zA
if !backup_size! lss 1000 (
    call :log "ERROR: 备份文件过小，可能损坏"
    exit /b 1
)

call :log "备份文件验证通过，大小: !backup_size! 字节"
exit /b 0

:stop_services
call :log "停止应用服务"

:: 停止Java进程
tasklist | findstr "java.exe" >nul
if !errorlevel! equ 0 (
    call :log "发现Java进程，正在停止..."
    taskkill /F /IM java.exe >nul 2>&1
    timeout /t 5 >nul
    
    :: 确认进程已停止
    tasklist | findstr "java.exe" >nul
    if !errorlevel! equ 0 (
        call :log "WARNING: Java进程可能未完全停止"
    ) else (
        call :log "Java进程已停止"
    )
)

:: 停止Node.js进程（如果前端在运行）
tasklist | findstr "node.exe" >nul
if !errorlevel! equ 0 (
    call :log "发现Node.js进程，正在停止..."
    taskkill /F /IM node.exe >nul 2>&1
    timeout /t 2 >nul
)

call :log "服务停止完成"
exit /b 0

:create_current_backup
call :log "创建当前状态备份"

:: 生成当前状态备份文件名
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set backup_date=%%d%%b%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set backup_time=%%a%%b
set "CURRENT_BACKUP=%BACKUP_DIR%\warehouse_system_current_%backup_date%_%backup_time%.sql"

:: 执行当前状态备份
call :log "当前状态备份文件: %CURRENT_BACKUP%"
mysqldump -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% > "%CURRENT_BACKUP%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "WARNING: 当前状态备份失败"
    exit /b 1
)

call :log "当前状态备份成功"
exit /b 0

:rollback_database
call :log "开始数据库回滚"

call :log "使用备份文件: !LATEST_BACKUP!"

:: 创建回滚脚本
set "ROLLBACK_SCRIPT=%TEMP%\database_rollback.sql"
call :create_rollback_script "%ROLLBACK_SCRIPT%"

:: 执行回滚脚本
call :log "执行数据库回滚脚本"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% < "%ROLLBACK_SCRIPT%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库回滚脚本执行失败"
    exit /b 1
)

:: 恢复备份数据
call :log "恢复备份数据"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "!LATEST_BACKUP!" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库数据恢复失败"
    exit /b 1
)

call :log "数据库回滚成功"
exit /b 0

:create_rollback_script
set "script_file=%~1"
call :log "创建数据库回滚脚本: %script_file%"

(
echo -- 数据库回滚脚本
echo -- 生成时间: %date% %time%
echo.
echo -- 删除当前数据库
echo DROP DATABASE IF EXISTS %DB_NAME%;
echo.
echo -- 重新创建数据库
echo CREATE DATABASE %DB_NAME% DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
echo.
echo -- 使用数据库
echo USE %DB_NAME%;
) > "%script_file%"

exit /b 0

:verify_database_rollback
call :log "验证数据库回滚结果"

:: 检查数据库连接
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库连接失败"
    exit /b 1
)

:: 检查关键表是否存在
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SHOW TABLES LIKE 'sys_license';" %DB_NAME% | findstr "sys_license" >nul
if !errorlevel! neq 0 (
    call :log "ERROR: 关键表sys_license不存在"
    exit /b 1
)

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SHOW TABLES LIKE 'sys_license_feature';" %DB_NAME% | findstr "sys_license_feature" >nul
if !errorlevel! neq 0 (
    call :log "ERROR: 关键表sys_license_feature不存在"
    exit /b 1
)

:: 验证字段定义是否回滚到原始状态
call :log "验证字段定义回滚状态"
set "VERIFY_RESULT=%TEMP%\rollback_verify_result.txt"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "DESCRIBE sys_license status;" %DB_NAME% > "%VERIFY_RESULT%" 2>&1

call :log "数据库回滚验证通过"
exit /b 0

:rollback_code
call :log "开始代码回滚"

cd /d "%BACKEND_PATH%"
if !errorlevel! neq 0 (
    call :log "ERROR: 无法切换到后端目录"
    exit /b 1
)

:: 检查是否是Git仓库
if exist ".git" (
    call :log "检测到Git仓库，执行Git回滚"
    
    :: 回滚到上一个提交
    git reset --hard HEAD~1 >>"%LOG_FILE%" 2>&1
    if !errorlevel! neq 0 (
        call :log "WARNING: Git回滚失败，尝试其他方法"
        goto :manual_rollback
    )
    
    call :log "Git代码回滚成功"
    exit /b 0
) else (
    call :log "未检测到Git仓库，跳过代码回滚"
    goto :manual_rollback
)

:manual_rollback
call :log "执行手动代码回滚"
:: 这里可以添加手动回滚逻辑，比如恢复备份的代码文件
call :log "手动代码回滚完成"
exit /b 0

:rebuild_application
call :log "重新编译应用"

cd /d "%BACKEND_PATH%"
if !errorlevel! neq 0 (
    call :log "ERROR: 无法切换到后端目录"
    exit /b 1
)

:: 清理和重新编译
call :log "执行Maven清理和编译"
mvn clean package -DskipTests -q >>"%LOG_FILE%" 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: Maven编译失败"
    exit /b 1
)

:: 检查编译产物
if not exist "wanyu-admin\target\wanyu-admin.jar" (
    call :log "ERROR: 编译产物不存在"
    exit /b 1
)

call :log "应用重新编译成功"
exit /b 0

:start_services
call :log "启动应用服务"

cd /d "%BACKEND_PATH%"

:: 启动后端服务
call :log "启动后端服务"
start "Warehouse System Backend (Rollback)" java -jar wanyu-admin\target\wanyu-admin.jar

:: 等待服务启动
call :log "等待服务启动..."
timeout /t 30 >nul

:: 检查服务是否启动成功
call :check_service_health
if !errorlevel! neq 0 (
    call :log "ERROR: 服务启动失败或健康检查失败"
    exit /b 1
)

call :log "服务启动成功"
exit /b 0

:check_service_health
call :log "执行服务健康检查"

:: 尝试连接后端服务
for /l %%i in (1,1,10) do (
    curl -s http://localhost:8080/actuator/health >nul 2>&1
    if !errorlevel! equ 0 (
        call :log "服务健康检查通过"
        exit /b 0
    )
    call :log "等待服务启动... (%%i/10)"
    timeout /t 6 >nul
)

:: 如果curl不可用，尝试使用telnet检查端口
telnet localhost 8080 >nul 2>&1
if !errorlevel! equ 0 (
    call :log "服务端口检查通过"
    exit /b 0
)

call :log "服务健康检查失败"
exit /b 1

:verify_rollback
call :log "执行回滚后验证"

:: 验证数据库连接
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库连接验证失败"
    exit /b 1
)

:: 验证关键功能
call :log "验证许可证管理功能"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT COUNT(*) FROM sys_license;" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: 许可证表验证失败"
    exit /b 1
)

call :log "验证许可证功能表"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT COUNT(*) FROM sys_license_feature;" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: 许可证功能表验证失败"
    exit /b 1
)

:: 验证字段定义是否回到原始状态
call :log "验证字段定义状态"
set "FIELD_CHECK=%TEMP%\field_check_result.txt"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA='%DB_NAME%' AND TABLE_NAME='sys_license' AND COLUMN_NAME='status';" %DB_NAME% > "%FIELD_CHECK%" 2>&1

call :log "回滚验证通过"
exit /b 0

:log
set "timestamp=%date% %time%"
echo [%timestamp%] %~1
echo [%timestamp%] %~1 >> "%LOG_FILE%"
exit /b 0

:error_exit
echo.
echo ========================================
echo 回滚失败！
echo 请检查日志文件: %LOG_FILE%
echo 备份位置: %BACKUP_DIR%
echo ========================================
echo.
echo 可能的解决方案：
echo 1. 检查数据库连接和权限
echo 2. 确认备份文件完整性
echo 3. 手动恢复数据库备份
echo 4. 联系系统管理员
echo.
pause
exit /b 1

:end
endlocal