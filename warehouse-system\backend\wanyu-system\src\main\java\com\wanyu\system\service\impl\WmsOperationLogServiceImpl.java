package com.wanyu.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.system.mapper.WmsOperationLogMapper;
import com.wanyu.system.domain.WmsOperationLog;
import com.wanyu.system.service.IWmsOperationLogService;

/**
 * 操作日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Service
public class WmsOperationLogServiceImpl implements IWmsOperationLogService 
{
    @Autowired
    private WmsOperationLogMapper wmsOperationLogMapper;

    /**
     * 查询操作日志
     * 
     * @param logId 操作日志主键
     * @return 操作日志
     */
    @Override
    public WmsOperationLog selectWmsOperationLogByLogId(Long logId)
    {
        return wmsOperationLogMapper.selectWmsOperationLogByLogId(logId);
    }

    /**
     * 查询操作日志列表
     * 
     * @param wmsOperationLog 操作日志
     * @return 操作日志
     */
    @Override
    public List<WmsOperationLog> selectWmsOperationLogList(WmsOperationLog wmsOperationLog)
    {
        return wmsOperationLogMapper.selectWmsOperationLogList(wmsOperationLog);
    }

    /**
     * 查询成功操作日志
     * 
     * @param wmsOperationLog 查询条件
     * @return 成功操作日志集合
     */
    @Override
    public List<WmsOperationLog> selectSuccessOperations(WmsOperationLog wmsOperationLog)
    {
        return wmsOperationLogMapper.selectSuccessOperations(wmsOperationLog);
    }

    /**
     * 查询失败操作日志
     * 
     * @param wmsOperationLog 查询条件
     * @return 失败操作日志集合
     */
    @Override
    public List<WmsOperationLog> selectFailedOperations(WmsOperationLog wmsOperationLog)
    {
        return wmsOperationLogMapper.selectFailedOperations(wmsOperationLog);
    }

    /**
     * 统计操作状态
     * 
     * @param operationType 操作类型
     * @param operatorId 操作人ID
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> countByOperationStatus(String operationType, Long operatorId, String beginTime, String endTime)
    {
        return wmsOperationLogMapper.countByOperationStatus(operationType, operatorId, beginTime, endTime);
    }

    /**
     * 新增操作日志
     * 
     * @param wmsOperationLog 操作日志
     * @return 结果
     */
    @Override
    public int insertWmsOperationLog(WmsOperationLog wmsOperationLog)
    {
        if (wmsOperationLog.getOperationTime() == null) {
            wmsOperationLog.setOperationTime(new Date());
        }
        if (wmsOperationLog.getCreateTime() == null) {
            wmsOperationLog.setCreateTime(new Date());
        }
        return wmsOperationLogMapper.insertWmsOperationLog(wmsOperationLog);
    }

    /**
     * 修改操作日志
     * 
     * @param wmsOperationLog 操作日志
     * @return 结果
     */
    @Override
    public int updateWmsOperationLog(WmsOperationLog wmsOperationLog)
    {
        return wmsOperationLogMapper.updateWmsOperationLog(wmsOperationLog);
    }

    /**
     * 批量删除操作日志
     * 
     * @param logIds 需要删除的操作日志主键
     * @return 结果
     */
    @Override
    public int deleteWmsOperationLogByLogIds(Long[] logIds)
    {
        return wmsOperationLogMapper.deleteWmsOperationLogByLogIds(logIds);
    }

    /**
     * 删除操作日志信息
     * 
     * @param logId 操作日志主键
     * @return 结果
     */
    @Override
    public int deleteWmsOperationLogByLogId(Long logId)
    {
        return wmsOperationLogMapper.deleteWmsOperationLogByLogId(logId);
    }

    /**
     * 记录成功操作
     * 
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param ipAddress IP地址
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param responseData 响应数据
     * @param executionTime 执行时间
     * @return 结果
     */
    @Override
    public int logSuccessOperation(String operationType, String operationDesc, Long operatorId, 
                                 String operatorName, String ipAddress, String requestUrl, 
                                 String requestMethod, String requestParams, String responseData, 
                                 Long executionTime)
    {
        WmsOperationLog log = new WmsOperationLog();
        log.setOperationType(operationType);
        log.setOperationDesc(operationDesc);
        log.setOperationStatus("0"); // 0=成功（符合字段定义标准）
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setIpAddress(ipAddress);
        log.setRequestUrl(requestUrl);
        log.setRequestMethod(requestMethod);
        log.setRequestParams(requestParams);
        log.setResponseData(responseData);
        log.setExecutionTime(executionTime);
        log.setOperationTime(new Date());
        log.setCreateTime(new Date());
        
        return insertWmsOperationLog(log);
    }

    /**
     * 记录失败操作
     * 
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     * @param errorMessage 错误信息
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param ipAddress IP地址
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param executionTime 执行时间
     * @return 结果
     */
    @Override
    public int logFailedOperation(String operationType, String operationDesc, String errorMessage, 
                                Long operatorId, String operatorName, String ipAddress, 
                                String requestUrl, String requestMethod, String requestParams, 
                                Long executionTime)
    {
        WmsOperationLog log = new WmsOperationLog();
        log.setOperationType(operationType);
        log.setOperationDesc(operationDesc);
        log.setOperationStatus("1"); // 1=失败（符合字段定义标准）
        log.setErrorMessage(errorMessage);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setIpAddress(ipAddress);
        log.setRequestUrl(requestUrl);
        log.setRequestMethod(requestMethod);
        log.setRequestParams(requestParams);
        log.setExecutionTime(executionTime);
        log.setOperationTime(new Date());
        log.setCreateTime(new Date());
        
        return insertWmsOperationLog(log);
    }

    /**
     * 清理过期日志
     * 
     * @param days 保留天数
     * @return 结果
     */
    @Override
    public int cleanExpiredLogs(int days)
    {
        return wmsOperationLogMapper.cleanExpiredLogs(days);
    }
}