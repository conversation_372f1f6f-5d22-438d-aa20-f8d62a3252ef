package com.wanyu.common.core.cache;

import org.springframework.stereotype.Component;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 验证码缓存服务
 * 使用内存缓存存储验证码
 *
 * <AUTHOR>
 */
@Component
public class CaptchaCacheService {
    
    private static final Logger log = LoggerFactory.getLogger(CaptchaCacheService.class);
    
    // 内存缓存
    private final ConcurrentHashMap<String, String> memoryCache = new ConcurrentHashMap<>();
    
    /**
     * 设置验证码缓存
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间（分钟）
     */
    public void setCaptcha(String key, String value, long timeout) {
        memoryCache.put(key, value);
        // 启动一个定时任务在指定时间后删除缓存
        Thread cleaner = new Thread(() -> {
            try {
                Thread.sleep(timeout * 60 * 1000); // 转换为毫秒
                memoryCache.remove(key);
                log.debug("内存缓存验证码已过期并删除: {}", key);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        cleaner.setDaemon(true);
        cleaner.start();
        log.debug("验证码已存储到内存缓存: {}", key);
    }
    
    /**
     * 获取验证码缓存
     * 
     * @param key 缓存键
     * @return 缓存值，如果不存在返回null
     */
    public String getCaptcha(String key) {
        // 从内存获取
        String value = memoryCache.get(key);
        log.debug("从内存缓存获取验证码: {}, 结果: {}", key, value);
        return value;
    }
    
    /**
     * 删除验证码缓存
     * 
     * @param key 缓存键
     */
    public void deleteCaptcha(String key) {
        // 从内存删除
        memoryCache.remove(key);
        log.debug("从内存缓存删除验证码: {}", key);
    }
}



