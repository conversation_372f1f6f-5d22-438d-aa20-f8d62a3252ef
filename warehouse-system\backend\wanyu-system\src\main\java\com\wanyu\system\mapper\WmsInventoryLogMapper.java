package com.wanyu.system.mapper;

import java.util.List;
import java.util.Map;
import com.wanyu.system.domain.WmsInventoryLog;

/**
 * 出入库日志Mapper接口
 * 
 * <AUTHOR>
 */
public interface WmsInventoryLogMapper 
{
    /**
     * 查询出入库日志
     * 
     * @param logId 出入库日志ID
     * @return 出入库日志
     */
    public WmsInventoryLog selectWmsInventoryLogByLogId(Long logId);

    /**
     * 查询出入库日志列表
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 出入库日志集合
     */
    public List<WmsInventoryLog> selectWmsInventoryLogList(WmsInventoryLog wmsInventoryLog);

    /**
     * 根据用户仓库权限查询出入库日志列表
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 出入库日志集合
     */
    public List<WmsInventoryLog> selectWmsInventoryLogListWithAuth(WmsInventoryLog wmsInventoryLog);

    /**
     * 新增出入库日志
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 结果
     */
    public int insertWmsInventoryLog(WmsInventoryLog wmsInventoryLog);

    /**
     * 修改出入库日志
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 结果
     */
    public int updateWmsInventoryLog(WmsInventoryLog wmsInventoryLog);

    /**
     * 删除出入库日志
     * 
     * @param logId 出入库日志ID
     * @return 结果
     */
    public int deleteWmsInventoryLogByLogId(Long logId);

    /**
     * 批量删除出入库日志
     *
     * @param logIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteWmsInventoryLogByLogIds(Long[] logIds);

    /**
     * 清空出入库日志
     *
     * @return 结果
     */
    public int cleanWmsInventoryLog();

    /**
     * 获取库存统计信息
     * 
     * @param params 查询参数
     * @return 统计信息
     */
    public Map<String, Object> getInventoryStatistics(Map<String, Object> params);

    /**
     * 获取库存操作趋势数据
     * 
     * @param params 查询参数
     * @return 趋势数据
     */
    public List<Map<String, Object>> getInventoryTrend(Map<String, Object> params);

    /**
     * 根据用户ID获取可访问的仓库ID列表
     * 
     * @param userId 用户ID
     * @return 仓库ID列表
     */
    public List<Long> getUserWarehouseIds(Long userId);

    /**
     * 根据库存ID查询出入库日志列表
     * @param inventoryId 库存ID
     * @return 日志集合
     */
    public List<WmsInventoryLog> selectWmsInventoryLogListByInventoryId(Long inventoryId);
    
    /**
     * 按物品类别统计库存操作
     * 
     * @param params 查询参数
     * @return 统计数据
     */
    public List<Map<String, Object>> getInventoryStatsByCategory(Map<String, Object> params);


    /**
     * 获取仓库信息
     * 
     * @param warehouseId 仓库ID
     * @return 仓库信息
     */
    public Map<String, Object> getWarehouseInfo(Long warehouseId);
    
    /**
     * 获取物品信息
     *
     * @param productId 物品ID
     * @return 物品信息
     */
    public Map<String, Object> getProductInfo(Long productId);

    /**
     * 根据日志ID更新仓库名称
     *
     * @param logId 日志ID
     * @return 更新结果
     */
    public int updateWarehouseNameByLogId(Long logId);
}