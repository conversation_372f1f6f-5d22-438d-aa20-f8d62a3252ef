@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: 字段定义标准化简化部署脚本
:: 版本: 1.0
:: 描述: 使用预写SQL文件执行数据库字段标准化
:: ========================================

echo.
echo ========================================
echo 字段定义标准化简化部署脚本
echo ========================================
echo.

:: 设置配置
set "DB_HOST=localhost"
set "DB_PORT=3306"
set "DB_NAME=warehouse_system"
set "DB_USER=root"
set "DB_PASSWORD=123456"
set "PROJECT_ROOT=C:\CKGLXT\warehouse-system"
set "BACKUP_DIR=%PROJECT_ROOT%\backups"
set "LOG_FILE=%PROJECT_ROOT%\simple_deployment.log"

:: 创建日志
call :log "开始字段定义标准化简化部署"

:: 检查环境
call :log "检查MySQL连接"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    echo 错误：无法连接到数据库
    echo 请检查MySQL服务和连接参数
    pause
    exit /b 1
)
call :log "数据库连接成功"

:: 创建备份目录
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

:: 创建备份
call :log "创建数据库备份"
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set backup_date=%%d%%b%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set backup_time=%%a%%b
set "BACKUP_FILE=%BACKUP_DIR%\warehouse_system_backup_%backup_date%_%backup_time%.sql"

mysqldump -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% > "%BACKUP_FILE%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    echo 错误：数据库备份失败
    pause
    exit /b 1
)
call :log "数据库备份成功: %BACKUP_FILE%"

:: 执行修复脚本
call :log "执行字段标准化修复"
if not exist "field_standardization_fix.sql" (
    echo 错误：修复脚本文件不存在: field_standardization_fix.sql
    echo 请确保该文件在当前目录中
    pause
    exit /b 1
)

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < field_standardization_fix.sql 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    echo 错误：修复脚本执行失败
    call :log "修复脚本执行失败，开始回滚"
    goto :rollback
)
call :log "修复脚本执行成功"

:: 验证修复结果
call :log "验证修复结果"
if not exist "field_standardization_verify.sql" (
    echo 警告：验证脚本文件不存在: field_standardization_verify.sql
    echo 跳过验证步骤
    goto :success
)

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < field_standardization_verify.sql > verification_result.txt 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    echo 警告：验证脚本执行失败，但修复可能已成功
    goto :success
)

call :log "验证脚本执行成功"
echo.
echo 验证结果：
type verification_result.txt
echo.

:success
call :log "字段定义标准化部署成功完成"
echo.
echo ========================================
echo 部署成功完成！
echo ========================================
echo.
echo 完成的操作：
echo ✓ 数据库备份已创建
echo ✓ sys_license表status字段已标准化 (0=启用, 1=禁用)
echo ✓ sys_license_feature表status字段已标准化 (0=启用, 1=禁用)
echo ✓ 数据字典已更新
echo.
echo 备份文件: %BACKUP_FILE%
echo 日志文件: %LOG_FILE%
echo.
echo 现在可以重启应用服务以使用新的字段定义标准
echo.
pause
goto :end

:rollback
call :log "开始数据库回滚"
echo.
echo 正在回滚数据库...

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "DROP DATABASE IF EXISTS %DB_NAME%;" 2>>"%LOG_FILE%"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE %DB_NAME%;" 2>>"%LOG_FILE%"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%BACKUP_FILE%" 2>>"%LOG_FILE%"

if !errorlevel! neq 0 (
    echo 错误：数据库回滚失败
    echo 请手动恢复备份文件: %BACKUP_FILE%
) else (
    echo 数据库回滚成功
    call :log "数据库回滚成功"
)

echo.
echo ========================================
echo 部署失败，已回滚到原始状态
echo ========================================
echo.
pause
exit /b 1

:log
set "timestamp=%date% %time%"
echo [%timestamp%] %~1
echo [%timestamp%] %~1 >> "%LOG_FILE%"
exit /b 0

:end
endlocal