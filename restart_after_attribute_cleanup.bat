@echo off
chcp 65001 >nul
echo ========================================
echo 物品属性模块删除后重启服务
echo ========================================

echo 1. 最终验证删除结果...
mysql -u root -p123456 -D warehouse_system -e "
SELECT '菜单验证' as check_item, COUNT(*) as count FROM sys_menu WHERE menu_name LIKE '%%物品属性%%' OR menu_name LIKE '%%属性%%';
SELECT '字典验证' as check_item, COUNT(*) as count FROM sys_dict_type WHERE dict_type LIKE '%%attribute%%';
SELECT '表验证' as check_item, COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'warehouse_system' AND table_name LIKE '%%attribute%%';
"

echo.
echo 2. 停止后端服务...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8080') do (
    echo 正在停止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo 3. 停止前端服务...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8081') do (
    echo 正在停止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo 4. 启动后端服务...
cd /d "warehouse-system\backend"
start "后端服务" cmd /c "mvn spring-boot:run -Dspring-boot.run.profiles=dev"

echo 等待后端服务启动...
timeout /t 10 /nobreak >nul

echo.
echo 5. 启动前端服务...
cd /d "..\frontend"
start "前端服务" cmd /c "npm run dev"

echo.
echo ========================================
echo ✅ 服务重启完成！
echo ========================================
echo.
echo 🌐 访问地址：
echo 前端: http://localhost:8081
echo 后端: http://localhost:8080
echo.
echo 📋 删除验证：
echo - 物品属性菜单已完全删除
echo - 相关数据表已完全删除  
echo - 前后端代码文件已完全删除
echo - 路由配置已更新
echo.
echo 💡 建议清除浏览器缓存后重新登录系统
echo.
pause