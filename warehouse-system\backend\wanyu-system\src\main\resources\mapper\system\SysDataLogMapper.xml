<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.SysDataLogMapper">
    
    <resultMap type="SysDataLog" id="SysDataLogResult">
        <result property="logId"        column="log_id"        />
        <result property="tableName"    column="table_name"    />
        <result property="operType"     column="operation_type" />
        <result property="primaryKey"   column="record_id"     />
        <result property="oldData"      column="old_data"      />
        <result property="newData"      column="new_data"      />
        <result property="changedFields" column="changed_fields" />
        <result property="operUser"     column="user_name"     />
        <result property="operTime"     column="operation_time" />
        <result property="clientIp"     column="client_ip"     />
        <result property="createBy"     column="create_by"     />
        <result property="createTime"   column="create_time"   />
        <result property="updateBy"     column="update_by"     />
        <result property="updateTime"   column="update_time"   />
        <result property="remark"       column="remark"        />
    </resultMap>

    <sql id="selectDataLogVo">
        select log_id, table_name, operation_type, record_id, old_data, new_data, changed_fields, user_name, operation_time, client_ip, create_by, create_time, update_by, update_time, remark from sys_data_log
    </sql>

    <select id="selectDataLogList" parameterType="SysDataLog" resultMap="SysDataLogResult">
        <include refid="selectDataLogVo"/>
        <where>  
            <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="operType != null  and operType != ''"> and operation_type = #{operType}</if>
            <if test="operUser != null  and operUser != ''"> and user_name like concat('%', #{operUser}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(operation_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(operation_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by operation_time desc
    </select>
    
    <select id="selectDataLogById" parameterType="Long" resultMap="SysDataLogResult">
        <include refid="selectDataLogVo"/>
        where log_id = #{logId}
    </select>
        
    <insert id="insertDataLog" parameterType="SysDataLog" useGeneratedKeys="true" keyProperty="logId">
        insert into sys_data_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tableName != null">table_name,</if>
            <if test="operType != null">operation_type,</if>
            <if test="primaryKey != null">record_id,</if>
            <if test="oldData != null">old_data,</if>
            <if test="newData != null">new_data,</if>
            <if test="changedFields != null">changed_fields,</if>
            <if test="operUser != null">user_name,</if>
            <if test="operTime != null">operation_time,</if>
            <if test="clientIp != null">client_ip,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tableName != null">#{tableName},</if>
            <if test="operType != null">#{operType},</if>
            <if test="primaryKey != null">#{primaryKey},</if>
            <if test="oldData != null">#{oldData},</if>
            <if test="newData != null">#{newData},</if>
            <if test="changedFields != null">#{changedFields},</if>
            <if test="operUser != null">#{operUser},</if>
            <if test="operTime != null">#{operTime},</if>
            <if test="clientIp != null">#{clientIp},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDataLog" parameterType="SysDataLog">
        update sys_data_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="operType != null">operation_type = #{operType},</if>
            <if test="primaryKey != null">record_id = #{primaryKey},</if>
            <if test="oldData != null">old_data = #{oldData},</if>
            <if test="newData != null">new_data = #{newData},</if>
            <if test="changedFields != null">changed_fields = #{changedFields},</if>
            <if test="operUser != null">user_name = #{operUser},</if>
            <if test="operTime != null">operation_time = #{operTime},</if>
            <if test="clientIp != null">client_ip = #{clientIp},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteDataLogById" parameterType="Long">
        delete from sys_data_log where log_id = #{logId}
    </delete>

    <delete id="deleteDataLogByIds" parameterType="String">
        delete from sys_data_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
    
    <delete id="cleanDataLog">
        truncate table sys_data_log
    </delete>

</mapper>