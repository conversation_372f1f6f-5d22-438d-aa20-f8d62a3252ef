@echo off
chcp 65001
echo ========================================
echo 测试物品管理API
echo ========================================

echo 正在测试API端点...
echo.

echo 测试1: 检查后端服务是否运行...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8080/product/info/list?pageNum=1^&pageSize=10
echo.

echo.
echo 测试2: 查看详细响应...
curl -X GET "http://localhost:8080/product/info/list?pageNum=1&pageSize=10" -H "Content-Type: application/json"
echo.

echo.
echo 测试3: 检查数据库数据...
mysql -h localhost -P 3306 -u root -p123456 -D warehouse_system -e "SELECT product_id, product_name, product_code, status FROM wms_product LIMIT 3;"

echo.
echo ========================================
echo 测试完成
echo ========================================
pause