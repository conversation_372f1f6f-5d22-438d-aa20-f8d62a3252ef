package com.wanyu.web.controller.monitor;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.framework.web.service.SysPasswordService;
import com.wanyu.system.domain.SysLogininfor;
import com.wanyu.system.service.ISysLogPermissionService;
import com.wanyu.system.service.ISysLogininforService;

/**
 * 系统访问记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController extends BaseController
{
    @Autowired
    private ISysLogininforService logininforService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private ISysLogPermissionService logPermissionService;

    @PreAuthorize("@ss.hasPermi('monitor:logininfor:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysLogininfor logininfor)
    {
        // 检查用户是否有权限查看登录日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "login"))
        {
            return getDataTable(new ArrayList<>());
        }

        startPage();
        List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
        return getDataTable(list);
    }

    @Log(title = "登录日志", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:export')")
    @GetMapping("/export")
    public void export(HttpServletResponse response, SysLogininfor logininfor)
    {
        // 检查用户是否有权限查看登录日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "login"))
        {
            return;
        }

        List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
        ExcelUtil<SysLogininfor> util = new ExcelUtil<SysLogininfor>(SysLogininfor.class);
        util.exportExcel(response, list, "登录日志");
    }

    @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    public AjaxResult remove(@PathVariable Long[] infoIds)
    {
        // 检查用户是否有权限管理登录日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "login"))
        {
            return error("您没有权限删除登录日志");
        }

        return toAjax(logininforService.deleteLogininforByIds(infoIds));
    }

    @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        // 检查用户是否有权限管理登录日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "login"))
        {
            return error("您没有权限清空登录日志");
        }

        logininforService.cleanLogininfor();
        return success();
    }

    @PreAuthorize("@ss.hasPermi('monitor:logininfor:unlock')")
    @Log(title = "账户解锁", businessType = BusinessType.OTHER)
    @GetMapping("/unlock/{userName}")
    public AjaxResult unlock(@PathVariable("userName") String userName)
    {
        // 检查用户是否有权限管理登录日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "login"))
        {
            return error("您没有权限解锁账户");
        }

        passwordService.clearLoginRecordCache(userName);
        return success();
    }
}
