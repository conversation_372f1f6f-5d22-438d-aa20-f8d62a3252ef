-- 删除库区和货架相关的数据库表和数据
-- 执行日期: 2025-08-31
-- 说明: 清理不需要的库区和货架功能

-- 1. 删除相关数据表（按依赖关系顺序删除）

-- 删除货架表（依赖库区表）
DROP TABLE IF EXISTS `wms_warehouse_rack`;

-- 删除库区表
DROP TABLE IF EXISTS `wms_warehouse_area`;

-- 2. 删除相关的菜单项
DELETE FROM sys_menu WHERE menu_name LIKE '%库区%' OR menu_name LIKE '%货架%' OR menu_name LIKE '%区域%' OR menu_name LIKE '%库位%';
DELETE FROM sys_menu WHERE component LIKE '%area%' OR component LIKE '%location%';
DELETE FROM sys_menu WHERE path LIKE '%area%' OR path LIKE '%location%';

-- 3. 删除相关的权限字符
DELETE FROM sys_menu WHERE perms LIKE '%area%' OR perms LIKE '%location%';

-- 4. 删除相关的角色菜单关联
DELETE FROM sys_role_menu WHERE menu_id NOT IN (SELECT menu_id FROM sys_menu);

-- 5. 删除相关的字典数据（如果有）
DELETE FROM sys_dict_data WHERE dict_type LIKE '%area%' OR dict_type LIKE '%location%';
DELETE FROM sys_dict_type WHERE dict_type LIKE '%area%' OR dict_type LIKE '%location%';

-- 6. 验证删除结果
SELECT '=== 清理完成验证 ===' as result;

SELECT '检查相关表是否已删除:' as check_tables;
SELECT table_name FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND (table_name LIKE '%area%' OR table_name LIKE '%location%' OR table_name LIKE '%rack%');

SELECT '检查相关菜单是否已删除:' as check_menus;
SELECT menu_id, menu_name, component, path FROM sys_menu 
WHERE menu_name LIKE '%库区%' OR menu_name LIKE '%货架%' OR menu_name LIKE '%区域%' OR menu_name LIKE '%库位%'
OR component LIKE '%area%' OR component LIKE '%location%'
OR path LIKE '%area%' OR path LIKE '%location%';

SELECT '清理完成！' as completion;