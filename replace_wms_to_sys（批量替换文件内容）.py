import os
import re
from pathlib import Path

def is_binary_file(filepath):
    """检查文件是否为二进制文件"""
    try:
        with open(filepath, 'rb') as f:
            chunk = f.read(1024)
            if b'\x00' in chunk:  # 二进制文件通常包含null字节
                return True
    except IOError:
        return True
    return False

def replace_in_file(filepath, old_str, new_str):
    """在文件中替换字符串"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        new_content = content.replace(old_str, new_str)
        
        if new_content != content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"Updated: {filepath}")
            return True
        return False
    except UnicodeDecodeError:
        print(f"Skipped binary file: {filepath}")
        return False
    except Exception as e:
        print(f"Error processing {filepath}: {str(e)}")
        return False

def main():
    # 配置参数
    directory = os.path.dirname(os.path.abspath(__file__))
    old_str = "wms_warehouse"
    new_str = "sys_warehouse"
    
    print(f"Starting replacement from '{old_str}' to '{new_str}' in {directory}")
    
    total_files = 0
    updated_files = 0
    
    for root, _, files in os.walk(directory):
        for filename in files:
            filepath = os.path.join(root, filename)
            
            # 跳过二进制文件和自身
            if (filename == os.path.basename(__file__) or 
                is_binary_file(filepath)):
                continue
                
            total_files += 1
            if replace_in_file(filepath, old_str, new_str):
                updated_files += 1
    
    print(f"\nProcess completed. Scanned {total_files} files, updated {updated_files} files.")

if __name__ == "__main__":
    main()