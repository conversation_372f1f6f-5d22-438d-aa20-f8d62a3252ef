package com.wanyu.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 功能权限对象 sys_license_feature
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
public class SysLicenseFeature extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 功能ID */
    private Long featureId;

    /** 功能代码 */
    @Excel(name = "功能代码")
    private String featureCode;

    /** 功能名称 */
    @Excel(name = "功能名称")
    private String featureName;

    /** 功能描述 */
    @Excel(name = "功能描述")
    private String featureDesc;

    /** 支持的授权类型 */
    @Excel(name = "支持的授权类型")
    private String licenseTypes;

    /** 是否核心功能 */
    @Excel(name = "是否核心功能", readConverterExp = "0=否,1=是")
    private String isCore;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 是否启用（计算字段） */
    private Boolean enabled;

    public void setFeatureId(Long featureId) 
    {
        this.featureId = featureId;
    }

    public Long getFeatureId() 
    {
        return featureId;
    }

    public void setFeatureCode(String featureCode) 
    {
        this.featureCode = featureCode;
    }

    public String getFeatureCode() 
    {
        return featureCode;
    }

    public void setFeatureName(String featureName) 
    {
        this.featureName = featureName;
    }

    public String getFeatureName() 
    {
        return featureName;
    }

    public void setFeatureDesc(String featureDesc) 
    {
        this.featureDesc = featureDesc;
    }

    public String getFeatureDesc() 
    {
        return featureDesc;
    }

    public void setLicenseTypes(String licenseTypes) 
    {
        this.licenseTypes = licenseTypes;
    }

    public String getLicenseTypes() 
    {
        return licenseTypes;
    }

    public void setIsCore(String isCore) 
    {
        this.isCore = isCore;
    }

    public String getIsCore() 
    {
        return isCore;
    }

    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setEnabled(Boolean enabled) 
    {
        this.enabled = enabled;
    }

    public Boolean getEnabled() 
    {
        return enabled;
    }

    @Override
    public String toString() {
        return "SysLicenseFeature{" +
                "featureId=" + featureId +
                ", featureCode='" + featureCode + '\'' +
                ", featureName='" + featureName + '\'' +
                ", featureDesc='" + featureDesc + '\'' +
                ", licenseTypes='" + licenseTypes + '\'' +
                ", isCore='" + isCore + '\'' +
                ", sortOrder=" + sortOrder +
                ", status='" + status + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}