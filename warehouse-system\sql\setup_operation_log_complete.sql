-- 完整的操作日志功能设置脚本
-- 包含表创建、字典数据、菜单权限等
-- 符合字段定义标准化要求

-- 1. 创建操作日志表
SOURCE create_wms_operation_log_table.sql;

-- 2. 添加菜单和权限
SOURCE add_operation_log_menu.sql;

-- 3. 验证设置结果
SELECT '=== 表结构验证 ===' as info;
DESCRIBE wms_operation_log;

SELECT '=== 字典数据验证 ===' as info;
SELECT dict_type, dict_label, dict_value, status 
FROM sys_dict_data 
WHERE dict_type IN ('operation_status', 'operation_type')
ORDER BY dict_type, dict_sort;

SELECT '=== 菜单权限验证 ===' as info;
SELECT menu_name, menu_type, visible, status, perms 
FROM sys_menu 
WHERE menu_name LIKE '%操作日志%' OR perms LIKE 'system:operationLog:%'
ORDER BY parent_id, order_num;

SELECT '=== 操作日志功能设置完成 ===' as info;