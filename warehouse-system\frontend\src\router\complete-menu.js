// 完整的菜单配置，根据 README.md 文档的要求
export const completeMenuRoutes = [
  {
    path: '/system',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'System',
    meta: {
      title: '系统管理',
      icon: 'system'
    },
    children: [
      {
        path: 'user',
        component: 'system/user/index',
        name: 'CompleteUser',
        meta: {
          title: '用户管理',
          icon: 'user'
        }
      },
      {
        path: 'role',
        component: 'system/role/index',
        name: 'CompleteRole',
        meta: {
          title: '角色管理',
          icon: 'peoples'
        }
      },
      {
        path: 'menu',
        component: 'system/menu/index',
        name: 'CompleteMenu',
        meta: {
          title: '菜单管理',
          icon: 'tree-table'
        }
      },
      {
        path: 'dept',
        component: 'system/dept/index',
        name: 'CompleteDept',
        meta: {
          title: '部门管理',
          icon: 'tree'
        }
      },

      {
        path: 'dict',
        component: 'system/dict/index',
        name: 'CompleteDict',
        meta: {
          title: '字典管理',
          icon: 'dict'
        }
      },
      {
        path: 'config',
        component: 'system/config/index',
        name: 'CompleteConfig',
        meta: {
          title: '参数设置',
          icon: 'config'
        }
      },
      {
        path: 'notice',
        component: 'system/notice/index',
        name: 'CompleteNotice',
        meta: {
          title: '通知公告',
          icon: 'message'
        }
      },
      {
        path: 'log',
        component: 'system/log/index',
        name: 'CompleteLog',
        meta: {
          title: '日志管理',
          icon: 'log'
        },
        children: [
          {
            path: 'operlog',
            component: 'system/log/operlog/index',
            name: 'CompleteOperLog',
            meta: {
              title: '操作日志',
              icon: 'form'
            }
          },
          {
            path: 'logininfor',
            component: 'system/log/logininfor/index',
            name: 'CompleteLoginLog',
            meta: {
              title: '登录日志',
              icon: 'logininfor'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/monitor',
    component: 'Layout',
    redirect: 'noredirect',
    name: 'CompleteMonitor',
    meta: {
      title: '系统监控',
      icon: 'monitor'
    },
    children: [
      {
        path: 'online',
        component: 'monitor/online/index',
        name: 'CompleteOnline',
        meta: {
          title: '在线用户',
          icon: 'online'
        }
      },
      {
        path: 'job',
        component: 'monitor/job/index',
        name: 'CompleteJob',
        meta: {
          title: '定时任务',
          icon: 'job'
        }
      },
      {
        path: 'server',
        component: 'monitor/server/index',
        name: 'CompleteServer',
        meta: {
          title: '服务监控',
          icon: 'server'
        }
      },
      {
        path: 'cache',
        component: 'monitor/cache/index',
        name: 'CompleteCache',
        meta: {
          title: '缓存监控',
          icon: 'redis'
        }
      }
    ]
  },
  {
    path: '/auth',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'CompleteAuth',
    meta: {
      title: '权限管理',
      icon: 'lock'
    },
    children: [
      {
        path: 'permission',
        component: 'system/permission/index',
        name: 'CompletePermission',
        meta: {
          title: '权限定义',
          icon: 'validCode'
        }
      },

      {
        path: 'permission-template',
        component: 'system/permission/template/index',
        name: 'CompletePermissionTemplate',
        meta: {
          title: '权限模板',
          icon: 'form'
        }
      },
      {
        path: 'data-permission',
        component: 'system/permission/dataPermission',
        name: 'CompleteDataPermission',
        meta: {
          title: '数据权限',
          icon: 'table'
        }
      },
      {
        path: 'api-permission',
        component: 'system/permission/apiPermission',
        name: 'CompleteApiPermission',
        meta: {
          title: 'API权限',
          icon: 'swagger'
        }
      },
      {
        path: 'permission-list',
        component: 'system/permission/permissionList',
        name: 'CompletePermissionList',
        meta: {
          title: '权限字符列表',
          icon: 'list'
        }
      }
    ]
  },
  {
    path: '/warehouse',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'CompleteWarehouseManage',
    meta: {
      title: '仓库管理',
      icon: 'build'
    },
    children: [
      {
        path: 'info',
        component: 'warehouse/info/index',
        name: 'CompleteWarehouseInfo',
        meta: {
          title: '仓库信息',
          icon: 'build'
        }
      }
    ]
  },
  {
    path: '/product',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'CompleteProductManage',
    meta: {
      title: '物品管理',
      icon: 'shopping'
    },
    children: [
      {
        path: 'category',
        component: 'product/category/index',
        name: 'CompleteProductCategory',
        meta: {
          title: '物品分类',
          icon: 'tree'
        }
      },
      {
        path: 'info',
        component: 'product/info/index',
        name: 'CompleteProductInfo',
        meta: {
          title: '物品信息',
          icon: 'shopping'
        }
      },
      {
        path: 'spec',
        component: 'product/spec/index',
        name: 'CompleteProductSpec',
        meta: {
          title: '物品规格',
          icon: 'list'
        }
      },
      {
        path: 'unit',
        component: 'product/unit/index',
        name: 'CompleteProductUnit',
        meta: {
          title: '物品单位',
          icon: 'example'
        }
      }
    ]
  },
  {
    path: '/qrcode',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'QrCodeManage',
    meta: {
      title: '二维码管理',
      icon: 'code'
    },
    children: [
      {
        path: 'generate',
        component: 'qrcode/generate/index',
        name: 'QrCodeGenerate',
        meta: {
          title: '二维码生成',
          icon: 'code'
        }
      },
      {
        path: 'batch',
        component: 'system/qrcode/index',
        name: 'QrCodeBatch',
        meta: {
          title: '批量生成',
          icon: 'job'
        }
      },

      {
        path: 'scan',
        component: 'qrcode/scan/index',
        name: 'QrCodeScan',
        meta: {
          title: '二维码扫描',
          icon: 'search'
        }
      },
      {
        path: 'batch-scan',
        component: 'qrcode/batchScan/index',
        name: 'QrCodeBatchScan',
        meta: {
          title: '批量扫描',
          icon: 'eye-open'
        }
      }
    ]
  },
  {
    path: '/inventory',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'InventoryManage',
    meta: {
      title: '库存管理',
      icon: 'documentation'
    },
    children: [
      {
        path: 'stock',
        component: 'inventory/stock/index',
        name: 'InventoryStock',
        meta: {
          title: '库存查询',
          icon: 'search'
        }
      },
      {
        path: 'in',
        component: 'inventory/in/index',
        name: 'InventoryIn',
        meta: {
          title: '入库管理',
          icon: 'upload'
        }
      },
      {
        path: 'out',
        component: 'inventory/out/index',
        name: 'InventoryOut',
        meta: {
          title: '出库管理',
          icon: 'download'
        }
      },
      {
        path: 'transfer',
        component: 'inventory/transfer/index',
        name: 'InventoryTransfer',
        meta: {
          title: '库存调拨',
          icon: 'drag'
        }
      },
      {
        path: 'check',
        component: 'inventory/check/index',
        name: 'InventoryCheck',
        meta: {
          title: '库存盘点',
          icon: 'checkbox'
        }
      },
      {
        path: 'alert',
        component: 'inventory/alert/index',
        name: 'InventoryAlert',
        meta: {
          title: '库存预警',
          icon: 'warning'
        },
        children: [
          {
            path: 'rules',
            component: 'inventory/alert/rules',
            name: 'InventoryAlertRules',
            meta: {
              title: '预警规则',
              icon: 'documentation'
            }
          },
          {
            path: 'log',
            component: 'inventory/alert/log',
            name: 'InventoryAlertLog',
            meta: {
              title: '预警记录',
              icon: 'list'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/log',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'LogManage',
    meta: {
      title: '日志管理',
      icon: 'log'
    },
    children: [
      {
        path: 'operation',
        component: 'log/operation/index',
        name: 'OperationLog',
        meta: {
          title: '操作日志',
          icon: 'edit'
        }
      },
      {
        path: 'inventory',
        component: 'log/inventory/index',
        name: 'InventoryLog',
        meta: {
          title: '出入库日志',
          icon: 'documentation'
        }
      },

      {
        path: 'permission',
        component: 'log/permission/index',
        name: 'LogPermission',
        meta: {
          title: '日志权限',
          icon: 'lock'
        }
      }
    ]
  },
  {
    path: '/report',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'ReportManage',
    meta: {
      title: '报表统计',
      icon: 'chart'
    },
    children: [
      {
        path: 'stock',
        component: 'report/stock/index',
        name: 'StockReportMenu',
        meta: {
          title: '库存报表',
          icon: 'documentation'
        }
      },
      {
        path: 'in',
        component: 'report/in/index',
        name: 'InReportMenu',
        meta: {
          title: '入库报表',
          icon: 'upload'
        }
      },
      {
        path: 'out',
        component: 'report/out/index',
        name: 'OutReport',
        meta: {
          title: '出库报表',
          icon: 'download'
        }
      }
    ]
  }
]