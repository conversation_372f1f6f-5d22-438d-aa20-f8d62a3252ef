-- 完整清理库位、货架、岗位相关的所有数据库内容
-- 执行前请先备份数据库

USE warehouse_system;

-- 1. 删除库区相关表
DROP TABLE IF EXISTS `wms_warehouse_area`;
DROP TABLE IF EXISTS `sys_warehouse_area`;

-- 2. 删除货架相关表
DROP TABLE IF EXISTS `wms_warehouse_rack`;
DROP TABLE IF EXISTS `sys_warehouse_rack`;

-- 3. 删除库位相关表
DROP TABLE IF EXISTS `wms_warehouse_location`;
DROP TABLE IF EXISTS `sys_warehouse_location`;

-- 4. 删除岗位相关表
DROP TABLE IF EXISTS `sys_post`;
DROP TABLE IF EXISTS `sys_user_post`;

-- 5. 删除所有相关菜单项
DELETE FROM sys_menu WHERE menu_name LIKE '%库区%' OR menu_name LIKE '%货架%' OR menu_name LIKE '%库位%' OR menu_name LIKE '%岗位%';
DELETE FROM sys_menu WHERE perms LIKE '%area%' OR perms LIKE '%rack%' OR perms LIKE '%location%' OR perms LIKE '%post%';
DELETE FROM sys_menu WHERE path LIKE '%area%' OR path LIKE '%rack%' OR path LIKE '%location%' OR path LIKE '%post%';

-- 6. 删除相关权限
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE 
    menu_name LIKE '%库区%' OR menu_name LIKE '%货架%' OR menu_name LIKE '%库位%' OR menu_name LIKE '%岗位%'
    OR perms LIKE '%area%' OR perms LIKE '%rack%' OR perms LIKE '%location%' OR perms LIKE '%post%'
);

-- 7. 检查并删除其他表中的相关字段
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND (COLUMN_NAME LIKE '%area_id%' OR COLUMN_NAME LIKE '%rack_id%' OR COLUMN_NAME LIKE '%location_id%' OR COLUMN_NAME LIKE '%post_id%')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 8. 删除相关的数据字典项
DELETE FROM sys_dict_type WHERE dict_type LIKE '%area%' OR dict_type LIKE '%rack%' OR dict_type LIKE '%location%' OR dict_type LIKE '%post%';
DELETE FROM sys_dict_data WHERE dict_type LIKE '%area%' OR dict_type LIKE '%rack%' OR dict_type LIKE '%location%' OR dict_type LIKE '%post%';

-- 9. 清理库存日志中的相关统计查询
-- 注意：这里只是标记，实际的XML文件需要手动清理

COMMIT;