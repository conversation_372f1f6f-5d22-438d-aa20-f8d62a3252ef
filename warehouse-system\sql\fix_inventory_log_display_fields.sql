-- ===================================================================
-- 修复出入库日志页面字段显示问题
-- 解决操作类型、物品名称、物品编码、物品规格、物品单位字段无法正确获取的问题
-- ===================================================================

-- 1. 检查并添加缺失的字段
-- ===================================================================

-- 添加仓库名称字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'warehouse_name') > 0,
    'SELECT "Column warehouse_name already exists"',
    'ALTER TABLE wms_inventory_log ADD COLUMN warehouse_name varchar(200) DEFAULT \'\' COMMENT \'仓库名称\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加物品名称字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'product_name') > 0,
    'SELECT "Column product_name already exists"',
    'ALTER TABLE wms_inventory_log ADD COLUMN product_name varchar(200) DEFAULT \'\' COMMENT \'物品名称\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加物品编码字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'product_code') > 0,
    'SELECT "Column product_code already exists"',
    'ALTER TABLE wms_inventory_log ADD COLUMN product_code varchar(100) DEFAULT \'\' COMMENT \'物品编码\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加物品规格字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'product_spec') > 0,
    'SELECT "Column product_spec already exists"',
    'ALTER TABLE wms_inventory_log ADD COLUMN product_spec varchar(200) DEFAULT \'\' COMMENT \'物品规格\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加物品单位字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'product_unit') > 0,
    'SELECT "Column product_unit already exists"',
    'ALTER TABLE wms_inventory_log ADD COLUMN product_unit varchar(50) DEFAULT \'\' COMMENT \'物品单位\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 修改操作类型字段以支持更多类型
-- ===================================================================

-- 修改操作类型字段，从char(1)改为varchar(20)以支持IN/OUT等字符串类型
ALTER TABLE wms_inventory_log 
MODIFY COLUMN operation_type varchar(20) NOT NULL DEFAULT 'IN' 
COMMENT '操作类型(IN-入库,OUT-出库,TRANSFER-调拨,ADJUST-调整)';

-- 3. 更新现有数据的操作类型
-- ===================================================================

-- 将旧的数字类型操作类型转换为新的字符串类型
UPDATE wms_inventory_log 
SET operation_type = CASE 
    WHEN operation_type = '1' THEN 'IN'
    WHEN operation_type = '2' THEN 'OUT'
    ELSE operation_type
END
WHERE operation_type IN ('1', '2');

-- 4. 填充缺失的仓库和物品信息
-- ===================================================================

-- 更新仓库名称（从sys_warehouse表获取）
UPDATE wms_inventory_log l 
LEFT JOIN sys_warehouse w ON l.warehouse_id = w.warehouse_id 
SET l.warehouse_name = COALESCE(w.warehouse_name, CONCAT('仓库-', l.warehouse_id))
WHERE l.warehouse_name IS NULL OR l.warehouse_name = '';

-- 更新物品信息（从wms_product表获取）
UPDATE wms_inventory_log l 
LEFT JOIN wms_product p ON l.product_id = p.product_id 
LEFT JOIN wms_specification ps ON p.spec_id = ps.spec_id
LEFT JOIN wms_unit pu ON p.unit_id = pu.unit_id
SET 
  l.product_name = COALESCE(p.product_name, CONCAT('物品-', l.product_id)),
  l.product_code = COALESCE(p.product_code, ''),
  l.product_spec = COALESCE(ps.spec_name, ps.specification, ''),
  l.product_unit = COALESCE(pu.unit_name, pu.unit_code, '')
WHERE l.product_name IS NULL OR l.product_name = '';

-- 5. 创建必要的索引以提高查询性能
-- ===================================================================

-- 操作类型索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_operation_type') > 0,
    'SELECT "Index idx_operation_type already exists"',
    'CREATE INDEX idx_operation_type ON wms_inventory_log(operation_type)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 仓库ID索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_warehouse_id') > 0,
    'SELECT "Index idx_warehouse_id already exists"',
    'CREATE INDEX idx_warehouse_id ON wms_inventory_log(warehouse_id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 物品ID索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_product_id') > 0,
    'SELECT "Index idx_product_id already exists"',
    'CREATE INDEX idx_product_id ON wms_inventory_log(product_id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 操作时间索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_operation_time') > 0,
    'SELECT "Index idx_operation_time already exists"',
    'CREATE INDEX idx_operation_time ON wms_inventory_log(operation_time)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 验证修复结果
-- ===================================================================

-- 检查表结构
SELECT 
  COLUMN_NAME as '字段名',
  DATA_TYPE as '数据类型',
  IS_NULLABLE as '允许空值',
  COLUMN_DEFAULT as '默认值',
  COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'wms_inventory_log'
  AND COLUMN_NAME IN ('operation_type', 'warehouse_name', 'product_name', 'product_code', 'product_spec', 'product_unit')
ORDER BY ORDINAL_POSITION;

-- 检查数据完整性
SELECT 
  '总记录数' as 检查项,
  COUNT(*) as 数量
FROM wms_inventory_log
UNION ALL
SELECT 
  '有仓库名称的记录数' as 检查项,
  COUNT(*) as 数量
FROM wms_inventory_log 
WHERE warehouse_name IS NOT NULL AND warehouse_name != ''
UNION ALL
SELECT 
  '有物品名称的记录数' as 检查项,
  COUNT(*) as 数量
FROM wms_inventory_log 
WHERE product_name IS NOT NULL AND product_name != ''
UNION ALL
SELECT 
  '有物品编码的记录数' as 检查项,
  COUNT(*) as 数量
FROM wms_inventory_log 
WHERE product_code IS NOT NULL AND product_code != ''
UNION ALL
SELECT 
  '操作类型为IN的记录数' as 检查项,
  COUNT(*) as 数量
FROM wms_inventory_log 
WHERE operation_type = 'IN'
UNION ALL
SELECT 
  '操作类型为OUT的记录数' as 检查项,
  COUNT(*) as 数量
FROM wms_inventory_log 
WHERE operation_type = 'OUT';

-- 显示前5条记录作为样例
SELECT 
  log_id,
  operation_type,
  warehouse_name,
  product_name,
  product_code,
  product_spec,
  product_unit,
  quantity,
  operator,
  operation_time
FROM wms_inventory_log 
ORDER BY log_id DESC 
LIMIT 5;

SELECT '出入库日志字段修复完成！' as message;