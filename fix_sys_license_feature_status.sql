-- ============================================================================
-- 修复sys_license_feature表字段定义脚本
-- 
-- 问题描述：
-- 当前sys_license_feature表的status字段定义为：0=禁用，1=启用（与项目标准相反）
-- 项目标准应为：0=启用/正常，1=禁用/停用
--
-- 修复策略：
-- 1. 创建数据备份
-- 2. 添加临时字段进行数据转换
-- 3. 颠倒现有状态值（0↔1互换）
-- 4. 更新字段定义和注释
-- 5. 验证数据完整性
-- ============================================================================

USE warehouse_system;

-- 开始事务
START TRANSACTION;

-- ============================================================================
-- 第一步：创建备份表
-- ============================================================================
DROP TABLE IF EXISTS sys_license_feature_backup;
CREATE TABLE sys_license_feature_backup AS SELECT * FROM sys_license_feature;

SELECT 
    '备份完成' as step,
    COUNT(*) as backup_records,
    NOW() as backup_time
FROM sys_license_feature_backup;

-- ============================================================================
-- 第二步：记录修复前的状态分布
-- ============================================================================
SELECT 
    '修复前状态分布' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '禁用(当前定义)'
        WHEN status = '1' THEN '启用(当前定义)'
        ELSE '未知状态'
    END as current_meaning,
    COUNT(*) as count
FROM sys_license_feature 
GROUP BY status
ORDER BY status;

-- ============================================================================
-- 第三步：添加临时字段进行数据转换
-- ============================================================================
ALTER TABLE sys_license_feature 
ADD COLUMN status_new CHAR(1) DEFAULT '0' COMMENT '新状态字段（0正常 1停用）';

-- ============================================================================
-- 第四步：执行数据转换（颠倒状态值）
-- ============================================================================
UPDATE sys_license_feature 
SET status_new = CASE 
    WHEN status = '0' THEN '1'  -- 原来禁用(0) -> 新标准禁用(1)
    WHEN status = '1' THEN '0'  -- 原来启用(1) -> 新标准启用(0)
    ELSE '0'  -- 默认为启用状态
END;

-- 验证转换结果
SELECT 
    '数据转换验证' as step,
    status as old_status,
    status_new as new_status,
    CASE 
        WHEN status = '0' AND status_new = '1' THEN '✓ 禁用->禁用'
        WHEN status = '1' AND status_new = '0' THEN '✓ 启用->启用'
        ELSE '✗ 转换异常'
    END as conversion_result,
    COUNT(*) as count
FROM sys_license_feature 
GROUP BY status, status_new
ORDER BY status;

-- ============================================================================
-- 第五步：删除原字段，重命名新字段
-- ============================================================================
ALTER TABLE sys_license_feature DROP COLUMN status;
ALTER TABLE sys_license_feature 
CHANGE COLUMN status_new status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）';

-- ============================================================================
-- 第六步：验证修复结果
-- ============================================================================
-- 检查表结构
SELECT 
    '字段定义验证' as check_type,
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
  AND TABLE_NAME = 'sys_license_feature' 
  AND COLUMN_NAME = 'status';

-- 检查数据分布
SELECT 
    '修复后状态分布' as analysis_type,
    status,
    CASE 
        WHEN status = '0' THEN '启用(新标准)'
        WHEN status = '1' THEN '禁用(新标准)'
        ELSE '未知状态'
    END as new_meaning,
    COUNT(*) as count
FROM sys_license_feature 
GROUP BY status
ORDER BY status;

-- ============================================================================
-- 第七步：功能可用性验证检查
-- ============================================================================
-- 验证核心功能状态
SELECT 
    '核心功能可用性检查' as check_type,
    feature_code,
    feature_name,
    is_core,
    status,
    CASE 
        WHEN is_core = '1' AND status = '1' THEN '⚠️ 核心功能被禁用'
        WHEN is_core = '1' AND status = '0' THEN '✓ 核心功能正常'
        WHEN is_core = '0' AND status = '0' THEN '✓ 可选功能启用'
        WHEN is_core = '0' AND status = '1' THEN '- 可选功能禁用'
        ELSE '? 状态未知'
    END as availability_status
FROM sys_license_feature 
ORDER BY is_core DESC, sort_order;

-- 验证数据完整性
SELECT 
    '数据完整性验证' as check_type,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) as enabled_count,
    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as disabled_count,
    SUM(CASE WHEN status NOT IN ('0', '1') THEN 1 ELSE 0 END) as invalid_status_count,
    SUM(CASE WHEN feature_code IS NULL OR feature_code = '' THEN 1 ELSE 0 END) as invalid_code_count
FROM sys_license_feature;

-- ============================================================================
-- 第八步：生成修复报告
-- ============================================================================
SELECT 
    '=== sys_license_feature表字段标准化修复报告 ===' as report_title,
    NOW() as completion_time;

SELECT 
    '修复项目' as item,
    '修复前定义' as before_fix,
    '修复后定义' as after_fix,
    '影响记录数' as affected_records
UNION ALL
SELECT 
    'status字段定义',
    '0=禁用,1=启用',
    '0=正常,1=停用',
    CAST(COUNT(*) AS CHAR)
FROM sys_license_feature
UNION ALL
SELECT 
    '默认值',
    '1(启用)',
    '0(正常)',
    '新记录适用'
UNION ALL
SELECT 
    '字段注释',
    '状态(0=禁用,1=启用)',
    '状态（0正常 1停用）',
    '表结构更新';

-- 提交事务
COMMIT;

-- ============================================================================
-- 使用说明和注意事项
-- ============================================================================
/*
修复完成后的使用说明：

1. 查询启用的功能：
   SELECT * FROM sys_license_feature WHERE status = '0';

2. 查询禁用的功能：
   SELECT * FROM sys_license_feature WHERE status = '1';

3. 启用功能：
   UPDATE sys_license_feature SET status = '0' WHERE feature_id = ?;

4. 禁用功能：
   UPDATE sys_license_feature SET status = '1' WHERE feature_id = ?;

注意事项：
- 修复后所有相关的Java代码和前端代码都需要相应更新
- 备份表sys_license_feature_backup保留了原始数据，可用于回滚
- 核心功能(is_core='1')建议保持启用状态(status='0')
- 新增功能记录时，status字段默认值为'0'(启用状态)

回滚方法（如需要）：
DROP TABLE sys_license_feature;
CREATE TABLE sys_license_feature AS SELECT * FROM sys_license_feature_backup;
ALTER TABLE sys_license_feature ADD PRIMARY KEY (feature_id);
ALTER TABLE sys_license_feature ADD UNIQUE INDEX feature_code(feature_code);
*/