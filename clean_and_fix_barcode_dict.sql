-- 清理并重新插入条码字典数据

-- 1. 删除重复的条码类型字典数据
DELETE FROM sys_dict_data WHERE dict_type = 'wms_barcode_type';

-- 2. 删除条码类型字典类型（如果存在）
DELETE FROM sys_dict_type WHERE dict_type = 'wms_barcode_type';

-- 3. 重新插入条码类型字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES 
('物品条码类型', 'wms_barcode_type', '0', 'admin', NOW(), '物品条码类型列表');

-- 4. 重新插入条码类型字典数据项
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, 'CODE128', 'CODE128', 'wms_barcode_type', '', 'primary', 'Y', '0', 'admin', NOW(), 'CODE128条码'),
(2, 'EAN13', 'EAN13', 'wms_barcode_type', '', 'success', 'N', '0', 'admin', NOW(), 'EAN13条码'),
(3, 'EAN8', 'EAN8', 'wms_barcode_type', '', 'info', 'N', '0', 'admin', NOW(), 'EAN8条码'),
(4, 'UPC_A', 'UPC_A', 'wms_barcode_type', '', 'warning', 'N', '0', 'admin', NOW(), 'UPC-A条码'),
(5, '二维码', 'QR_CODE', 'wms_barcode_type', '', 'danger', 'N', '0', 'admin', NOW(), 'QR二维码');

-- 5. 验证字典数据
SELECT '清理并重新插入完成，验证结果:' as result;
SELECT dict_sort, dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'wms_barcode_type' ORDER BY dict_sort;