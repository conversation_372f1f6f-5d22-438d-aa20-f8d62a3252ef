# 字段定义标准化部署指南

## 概述

本指南详细说明了如何使用自动化脚本执行字段定义标准化的部署和回滚操作。

## 部署前准备

### 环境要求

1. **操作系统**: Windows 10/11 或 Windows Server
2. **数据库**: MySQL 5.7+ 或 MySQL 8.0+
3. **Java**: JDK 8 或 JDK 11
4. **Maven**: 3.6+
5. **Node.js**: 14+ (可选，用于前端)
6. **Git**: 2.0+ (可选，用于代码回滚)

### 工具检查

确保以下工具在系统PATH中可用：
```cmd
mysql --version
java -version
mvn --version
node --version  # 可选
git --version   # 可选
curl --version  # 可选，用于健康检查
```

### 项目配置

确认项目路径和数据库配置：
- 项目根目录: `C:\CKGLXT\warehouse-system`
- 后端目录: `C:\CKGLXT\warehouse-system\backend`
- 前端目录: `C:\CKGLXT\warehouse-system\frontend`
- 数据库: `warehouse_system`
- 数据库用户: `root`
- 数据库密码: `123456`

## 部署流程

### 1. 执行部署

运行部署脚本：
```cmd
deploy_field_standardization.bat
```

### 2. 部署阶段说明

#### 阶段1: 环境检查
- 验证必要工具是否安装
- 检查项目目录结构
- 创建备份目录

#### 阶段2: 数据库备份
- 创建完整数据库备份
- 验证备份文件完整性
- 备份文件命名格式: `warehouse_system_backup_YYYYMMDD_HHMM.sql`

#### 阶段3: 停止服务
- 停止Java进程 (后端服务)
- 停止Node.js进程 (前端服务，如果运行)

#### 阶段4: 数据库修复
- 执行字段标准化SQL脚本
- 修复sys_license表status字段
- 修复sys_license_feature表status字段
- 更新数据字典

#### 阶段5: 验证数据库修复
- 验证字段定义正确性
- 检查数据完整性
- 确认状态值转换正确

#### 阶段6: 后端代码部署
- Maven清理和编译
- 生成新的JAR文件
- 验证编译产物

#### 阶段7: 前端代码部署 (可选)
- 安装依赖 (如需要)
- 构建生产版本

#### 阶段8: 启动服务
- 启动后端服务
- 等待服务就绪
- 执行健康检查

#### 阶段9: 部署验证
- 验证数据库连接
- 测试关键功能
- 确认字段标准化生效

### 3. 部署成功标志

部署成功时会显示：
```
========================================
部署成功完成！
备份位置: C:\CKGLXT\warehouse-system\backups
日志文件: C:\CKGLXT\warehouse-system\deployment.log
========================================
```

## 回滚流程

### 1. 执行回滚

如果部署出现问题，运行回滚脚本：
```cmd
rollback_deployment.bat
```

### 2. 回滚确认

脚本会要求确认回滚操作：
```
警告：即将执行回滚操作
此操作将：
1. 停止当前运行的服务
2. 恢复数据库到部署前状态
3. 恢复代码到上一个版本
4. 重新启动服务

所有在部署后的数据变更将会丢失！

确认执行回滚操作？(Y/N):
```

### 3. 回滚阶段说明

#### 阶段1: 停止服务
- 停止所有相关进程

#### 阶段2: 创建当前状态备份
- 备份当前数据库状态 (以防回滚失败)

#### 阶段3: 数据库回滚
- 使用最新备份恢复数据库
- 验证恢复完整性

#### 阶段4: 代码回滚
- Git回滚 (如果可用)
- 手动回滚 (备选方案)

#### 阶段5: 重新编译
- 编译回滚后的代码

#### 阶段6: 启动服务
- 重新启动应用

#### 阶段7: 验证回滚
- 确认系统恢复正常

## 故障排除

### 常见问题

#### 0. 部署脚本在环境检查阶段停止
**症状**: 脚本显示"检查部署环境和必要工具"后停止，按任意键退出
**解决方案**:
1. 运行环境诊断脚本: `check_environment.bat`
2. 根据诊断结果安装缺失的工具:
   - MySQL客户端: 下载MySQL安装包或单独安装MySQL客户端
   - Java JDK: 安装JDK 8或11，配置JAVA_HOME
   - Apache Maven: 下载并配置MAVEN_HOME
3. 确保所有工具的bin目录都在系统PATH中
4. 重新打开命令提示符后再次运行部署脚本

#### 1. 数据库连接失败
**症状**: 无法连接到MySQL数据库
**解决方案**:
- 检查MySQL服务是否运行
- 验证数据库连接参数
- 确认用户权限

#### 2. 备份失败
**症状**: 数据库备份创建失败
**解决方案**:
- 检查磁盘空间
- 验证数据库权限
- 确认备份目录可写

#### 3. 编译失败
**症状**: Maven编译报错
**解决方案**:
- 检查Java环境
- 清理Maven缓存: `mvn clean`
- 检查网络连接 (下载依赖)

#### 4. 服务启动失败
**症状**: 应用服务无法启动
**解决方案**:
- 检查端口占用: `netstat -an | findstr 8080`
- 查看应用日志
- 验证配置文件

#### 5. 健康检查失败
**症状**: 服务启动但健康检查失败
**解决方案**:
- 等待更长时间 (服务可能需要更多启动时间)
- 检查应用日志
- 手动访问健康检查端点

### 日志文件

#### 部署日志
位置: `C:\CKGLXT\warehouse-system\deployment.log`
包含: 完整的部署过程记录

#### 回滚日志
位置: `C:\CKGLXT\warehouse-system\rollback.log`
包含: 完整的回滚过程记录

#### 应用日志
位置: 应用启动目录下的logs文件夹
包含: 应用运行时日志

### 手动操作

#### 手动数据库备份
```cmd
mysqldump -h localhost -P 3306 -u root -p123456 warehouse_system > backup.sql
```

#### 手动数据库恢复
```cmd
mysql -h localhost -P 3306 -u root -p123456 warehouse_system < backup.sql
```

#### 手动服务停止
```cmd
taskkill /F /IM java.exe
```

#### 手动服务启动
```cmd
cd C:\CKGLXT\warehouse-system\backend
java -jar wanyu-admin\target\wanyu-admin.jar
```

## 验证清单

### 部署后验证

- [ ] 数据库连接正常
- [ ] sys_license表status字段定义正确 (0=启用, 1=禁用)
- [ ] sys_license_feature表status字段定义正确 (0=启用, 1=禁用)
- [ ] 数据字典更新正确
- [ ] 应用服务正常启动
- [ ] 健康检查通过
- [ ] 许可证管理功能正常
- [ ] 操作日志记录正常

### 回滚后验证

- [ ] 数据库恢复到原始状态
- [ ] 字段定义回到部署前状态
- [ ] 应用功能正常
- [ ] 数据完整性保持
- [ ] 服务正常运行

## 联系信息

如遇到无法解决的问题，请联系：
- 系统管理员
- 开发团队
- 数据库管理员

## 附录

### 脚本文件说明

#### check_environment.bat
- 环境诊断脚本
- 检查所有必需的工具和环境
- 提供详细的错误信息和解决方案
- 建议在部署前先运行此脚本

#### deploy_field_standardization.bat
- 主要部署脚本
- 包含完整的部署流程
- 自动化程度高
- 包含错误处理和回滚机制

#### rollback_deployment.bat
- 快速回滚脚本
- 恢复到部署前状态
- 包含安全确认机制
- 支持多种回滚方式

### 配置文件位置

- 数据库配置: `backend/src/main/resources/application.yml`
- 前端配置: `frontend/src/settings.js`
- Maven配置: `backend/pom.xml`

### 重要目录

- 备份目录: `C:\CKGLXT\warehouse-system\backups`
- 日志目录: `C:\CKGLXT\warehouse-system\logs`
- 临时文件: `%TEMP%\field_standardization_*`