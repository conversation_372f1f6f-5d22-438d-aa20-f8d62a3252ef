<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="操作类型" prop="operationType">
        <el-select v-model="queryParams.operationType" placeholder="请选择操作类型" clearable>
          <el-option
            v-for="dict in dict.type.operation_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作状态" prop="operationStatus">
        <el-select v-model="queryParams.operationStatus" placeholder="请选择操作状态" clearable>
          <el-option
            v-for="dict in dict.type.operation_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作人" prop="operatorName">
        <el-input
          v-model="queryParams.operatorName"
          placeholder="请输入操作人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IP地址" prop="ipAddress">
        <el-input
          v-model="queryParams.ipAddress"
          placeholder="请输入IP地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="daterangeOperationTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          @click="handleSuccess"
        >成功日志</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-close"
          size="mini"
          @click="handleFailed"
        >失败日志</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-pie-chart"
          size="mini"
          @click="handleStatistics"
        >统计分析</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:operationLog:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:operationLog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleClean"
          v-hasPermi="['system:operationLog:clean']"
        >清理日志</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="operationLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="日志ID" align="center" prop="logId" />
      <el-table-column label="操作类型" align="center" prop="operationType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.operation_type" :value="scope.row.operationType"/>
        </template>
      </el-table-column>
      <el-table-column label="操作描述" align="center" prop="operationDesc" :show-overflow-tooltip="true" />
      <el-table-column label="操作状态" align="center" prop="operationStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.operation_status" :value="scope.row.operationStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作人" align="center" prop="operatorName" />
      <el-table-column label="IP地址" align="center" prop="ipAddress" />
      <el-table-column label="执行时间" align="center" prop="executionTime">
        <template slot-scope="scope">
          <span v-if="scope.row.executionTime">{{ scope.row.executionTime }}ms</span>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" align="center" prop="operationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.operationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:operationLog:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:operationLog:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 操作日志详情对话框 -->
    <el-dialog title="操作日志详情" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="日志ID：">{{ form.logId }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作类型：">
              <dict-tag :options="dict.type.operation_type" :value="form.operationType"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作状态：">
              <dict-tag :options="dict.type.operation_status" :value="form.operationStatus"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行时间：">{{ form.executionTime }}ms</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作人：">{{ form.operatorName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="IP地址：">{{ form.ipAddress }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求URL：">{{ form.requestUrl }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="请求方法：">{{ form.requestMethod }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作时间：">{{ parseTime(form.operationTime) }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="操作描述：">{{ form.operationDesc }}</el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.errorMessage">
          <el-col :span="24">
            <el-form-item label="错误信息：">
              <el-input v-model="form.errorMessage" type="textarea" :rows="3" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.requestParams">
          <el-col :span="24">
            <el-form-item label="请求参数：">
              <el-input v-model="form.requestParams" type="textarea" :rows="3" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.responseData">
          <el-col :span="24">
            <el-form-item label="响应数据：">
              <el-input v-model="form.responseData" type="textarea" :rows="3" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 清理日志对话框 -->
    <el-dialog title="清理过期日志" :visible.sync="cleanOpen" width="400px" append-to-body>
      <el-form ref="cleanForm" :model="cleanForm" label-width="100px">
        <el-form-item label="保留天数" prop="days">
          <el-input-number v-model="cleanForm.days" :min="1" :max="365" />
          <div style="margin-top: 5px; color: #909399; font-size: 12px;">
            将删除 {{ cleanForm.days }} 天前的所有日志记录
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cleanOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmClean">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOperationLog, listSuccessOperations, listFailedOperations, getOperationLog, delOperationLog, cleanExpiredLogs, getOperationStatistics } from "@/api/system/operationLog";

export default {
  name: "OperationLog",
  dicts: ['operation_type', 'operation_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 操作日志表格数据
      operationLogList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 清理日志对话框
      cleanOpen: false,
      // 操作时间范围
      daterangeOperationTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        operationType: null,
        operationDesc: null,
        operationStatus: null,
        operatorName: null,
        ipAddress: null,
      },
      // 表单参数
      form: {},
      // 清理表单
      cleanForm: {
        days: 30
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询操作日志列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeOperationTime && '' != this.daterangeOperationTime) {
        this.queryParams.params["beginOperationTime"] = this.daterangeOperationTime[0];
        this.queryParams.params["endOperationTime"] = this.daterangeOperationTime[1];
      }
      listOperationLog(this.queryParams).then(response => {
        this.operationLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeOperationTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.logId)
      this.multiple = !selection.length
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = Object.assign({}, row);
    },
    /** 成功日志按钮操作 */
    handleSuccess() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeOperationTime && '' != this.daterangeOperationTime) {
        this.queryParams.params["beginOperationTime"] = this.daterangeOperationTime[0];
        this.queryParams.params["endOperationTime"] = this.daterangeOperationTime[1];
      }
      listSuccessOperations(this.queryParams).then(response => {
        this.operationLogList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$modal.msgSuccess("已筛选成功操作日志");
      });
    },
    /** 失败日志按钮操作 */
    handleFailed() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeOperationTime && '' != this.daterangeOperationTime) {
        this.queryParams.params["beginOperationTime"] = this.daterangeOperationTime[0];
        this.queryParams.params["endOperationTime"] = this.daterangeOperationTime[1];
      }
      listFailedOperations(this.queryParams).then(response => {
        this.operationLogList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$modal.msgSuccess("已筛选失败操作日志");
      });
    },
    /** 统计分析按钮操作 */
    handleStatistics() {
      const params = {
        operationType: this.queryParams.operationType,
        operatorId: this.queryParams.operatorId,
        beginTime: this.daterangeOperationTime && this.daterangeOperationTime[0] ? this.daterangeOperationTime[0] : null,
        endTime: this.daterangeOperationTime && this.daterangeOperationTime[1] ? this.daterangeOperationTime[1] : null
      };
      
      getOperationStatistics(params).then(response => {
        const data = response.data;
        let message = "操作统计结果：\n";
        let totalCount = 0;
        let successCount = 0;
        let failedCount = 0;
        
        data.forEach(item => {
          totalCount += parseInt(item.count);
          if (item.operation_status === '0') {
            successCount = parseInt(item.count);
            message += `成功操作：${item.count} 次\n`;
          } else if (item.operation_status === '1') {
            failedCount = parseInt(item.count);
            message += `失败操作：${item.count} 次\n`;
          }
        });
        
        message += `总计：${totalCount} 次\n`;
        if (totalCount > 0) {
          const successRate = ((successCount / totalCount) * 100).toFixed(2);
          message += `成功率：${successRate}%`;
        }
        
        this.$alert(message, "统计分析", {
          confirmButtonText: "确定",
          type: "info"
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const logIds = row.logId || this.ids;
      this.$modal.confirm('是否确认删除操作日志编号为"' + logIds + '"的数据项？').then(function() {
        return delOperationLog(logIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 清理日志按钮操作 */
    handleClean() {
      this.cleanOpen = true;
    },
    /** 确认清理日志 */
    confirmClean() {
      this.$modal.confirm(`确认清理 ${this.cleanForm.days} 天前的所有日志记录吗？此操作不可恢复！`).then(() => {
        return cleanExpiredLogs(this.cleanForm.days);
      }).then((response) => {
        this.cleanOpen = false;
        this.getList();
        this.$modal.msgSuccess(response.msg);
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/operationLog/export', {
        ...this.queryParams
      }, `operation_log_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>