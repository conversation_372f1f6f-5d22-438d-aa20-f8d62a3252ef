-- 选择数据库
USE warehouse_system;

-- 修改数据库字符集
ALTER DATABASE warehouse_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改表字符集
ALTER TABLE wms_barcode CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wms_barcode_template CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复wms_barcode表注释
ALTER TABLE `wms_barcode` COMMENT='物品条码表';

-- 修复wms_barcode表字段注释
ALTER TABLE `wms_barcode` 
MODIFY COLUMN `barcode_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '条码ID',
MODIFY COLUMN `product_id` bigint(20) NOT NULL COMMENT '物品ID',
MODIFY COLUMN `product_name` varchar(100) DEFAULT NULL COMMENT '物品名称',
MODIFY COLUMN `barcode_content` varchar(200) NOT NULL COMMENT '条码内容',
MODIFY COLUMN `barcode_type` varchar(20) NOT NULL DEFAULT 'CODE128' COMMENT '条码类型(CODE128,EAN13,QR_CODE等)',
MODIFY COLUMN `barcode_image` varchar(500) DEFAULT NULL COMMENT '条码图片路径',
MODIFY COLUMN `template_id` bigint(20) DEFAULT NULL COMMENT '条码模板ID',
MODIFY COLUMN `is_main` char(1) DEFAULT '0' COMMENT '是否主条码(0否 1是)',
MODIFY COLUMN `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
MODIFY COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注',
MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
MODIFY COLUMN `create_time` datetime DEFAULT NULL COMMENT '创建时间',
MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
MODIFY COLUMN `update_time` datetime DEFAULT NULL COMMENT '更新时间';

-- 修复wms_barcode_template表注释
ALTER TABLE `wms_barcode_template` COMMENT='物品条码模板表';

-- 修复wms_barcode_template表字段注释
ALTER TABLE `wms_barcode_template`
MODIFY COLUMN `template_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
MODIFY COLUMN `template_name` varchar(100) NOT NULL COMMENT '模板名称',
MODIFY COLUMN `template_type` varchar(20) NOT NULL COMMENT '模板类型',
MODIFY COLUMN `template_width` int(11) DEFAULT 100 COMMENT '模板宽度(mm)',
MODIFY COLUMN `template_height` int(11) DEFAULT 50 COMMENT '模板高度(mm)',
MODIFY COLUMN `barcode_width` int(11) DEFAULT 80 COMMENT '条码宽度(mm)',
MODIFY COLUMN `barcode_height` int(11) DEFAULT 30 COMMENT '条码高度(mm)',
MODIFY COLUMN `font_size` int(11) DEFAULT 12 COMMENT '字体大小',
MODIFY COLUMN `show_text` char(1) DEFAULT '1' COMMENT '是否显示文字(0否 1是)',
MODIFY COLUMN `show_product_name` char(1) DEFAULT '1' COMMENT '是否显示物品名称(0否 1是)',
MODIFY COLUMN `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
MODIFY COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注',
MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
MODIFY COLUMN `create_time` datetime DEFAULT NULL COMMENT '创建时间',
MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
MODIFY COLUMN `update_time` datetime DEFAULT NULL COMMENT '更新时间';