import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* 模块路由 */
import productRouter from './modules/product'
import warehouseRouter from './modules/warehouse'
import inventoryRouter from './modules/inventory'
import systemRouter from './modules/system'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/help',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/help/index'),
        name: 'Help',
        meta: { title: '帮助中心', icon: 'question' }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  // 仓库管理路由
  warehouseRouter,
  // 库存管理路由
  inventoryRouter,
  // 系统管理路由
  systemRouter,
  // 物品管理路由
  productRouter,

  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'SystemUserAuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/user-warehouse',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: ':userId(\\d+)/:userName',
        component: () => import('@/views/system/user/authWarehouse'),
        name: 'SystemUserAuthWarehouse',
        meta: { title: '分配仓库', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/permission/auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: ':userId(\\d+)',
        component: () => import('@/views/system/permission/authUser'),
        name: 'SystemUserAuthPermission',
        meta: { title: '分配权限', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/permission/authLog',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: ':userId(\\d+)',
        component: () => import('@/views/system/permission/authLog'),
        name: 'SystemUserAuthLogPermission',
        meta: { title: '分配日志权限', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/log/auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: ':userId(\\d+)',
        component: () => import('@/views/system/log/auth'),
        name: 'SystemUserAuthLog',
        meta: { title: '分配日志', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/user/menu',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: ':userId(\\d+)',
        component: () => import('@/views/system/user/authMenu'),
        name: 'SystemUserAuthMenu',
        meta: { title: '分配菜单', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'SystemRoleAuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/role-template',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/system/role/template'),
        name: 'SystemRoleTemplate',
        meta: { title: '角色权限模板', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/permission/datascope',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: '',
        component: () => import('@/views/system/permission/datascope'),
        name: 'DataScope',
        meta: { title: '数据权限配置', activeMenu: '/system/role' }
      },
      {
        path: 'index',
        component: () => import('@/views/system/permission/datascope'),
        name: 'DataScopeIndex',
        meta: { title: '数据权限配置', activeMenu: '/system/role' }
      },

    ]
  },
  {
    path: '/system/permission/advancedDatascope',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: '',
        component: () => import('@/views/system/permission/advancedDatascope'),
        name: 'AdvancedDataScope',
        meta: { title: '复合数据权限配置', activeMenu: '/system/role' }
      },
      {
        path: 'index',
        component: () => import('@/views/system/permission/advancedDatascope'),
        name: 'AdvancedDataScopeIndex',
        meta: { title: '复合数据权限配置', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/permission/visualization',
    component: Layout,
    hidden: true,
    permissions: ['system:permission:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/system/permission/visualization'),
        name: 'PermissionVisualization',
        meta: { title: '权限可视化', activeMenu: '/system/permission' }
      }
    ]
  },
  {
    path: '/system/permission/template',
    component: Layout,
    hidden: true,
    permissions: ['system:template:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/system/permission/template/index'),
        name: 'SystemPermissionTemplate',
        meta: { title: '权限模板管理', activeMenu: '/system/permission' }
      },
      {
        path: 'batchApply',
        component: () => import('@/views/system/permission/template/batchApply'),
        name: 'SystemPermissionTemplateBatchApply',
        meta: { title: '批量应用模板', activeMenu: '/system/permission/template' }
      }
    ]
  },
  {
    path: '/system/permission/advancedDataPermission',
    component: Layout,
    hidden: false,
    permissions: ['system:permission:advanced:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/system/permission/advancedDataPermission/index'),
        name: 'AdvancedDataPermission',
        meta: { title: '高级数据权限', icon: 'table' }
      }
    ]
  },

  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
  {
    path: '/inventory/in-scan',
    component: Layout,
    hidden: true,
    permissions: ['inventory:in:add'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/inventory/in/scan'),
        name: 'InventoryInScan',
        meta: { title: '扫码入库', activeMenu: '/inventory/in' }
      }
    ]
  },
  {
    path: '/inventory/in/batch',
    component: Layout,
    hidden: true,
    permissions: ['inventory:in:add'],
    children: [
      {
        path: '',
        component: () => import('@/views/inventory/in/batchIn'),
        name: 'BatchInventoryIn',
        meta: { title: '批量入库', activeMenu: '/inventory/in' }
      }
    ]
  },
  {
    path: '/inventory/out-scan',
    component: Layout,
    hidden: true,
    permissions: ['inventory:out:add'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/inventory/out/scan'),
        name: 'InventoryOutScan',
        meta: { title: '扫码出库', activeMenu: '/inventory/out' }
      }
    ]
  },
  {
    path: '/inventory/check-scan',
    component: Layout,
    hidden: true,
    permissions: ['inventory:check:add'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/inventory/check/scan'),
        name: 'InventoryCheckScan',
        meta: { title: '扫码盘点', activeMenu: '/inventory/check' }
      }
    ]
  },
  {
    path: '/report',
    component: Layout,
    hidden: false,
    alwaysShow: true,
    name: 'ReportStatistics',
    meta: { title: '报表统计', icon: 'chart' },
    permissions: ['inventory:stock:list', 'inventory:in:list', 'inventory:out:list'],
    children: [
      {
        path: 'stock',
        component: () => import('@/views/report/stock/index'),
        name: 'ReportStockView',
        meta: { title: '库存报表', icon: 'documentation' }
      },
      {
        path: 'in',
        component: () => import('@/views/report/in/index'),
        name: 'ReportInView',
        meta: { title: '入库报表', icon: 'upload' }
      },
      {
        path: 'out',
        component: () => import('@/views/report/out/index'),
        name: 'ReportOutView',
        meta: { title: '出库报表', icon: 'download' }
      },
      {
        path: 'alert',
        component: () => import('@/views/report/alert/index'),
        name: 'ReportAlertView',
        meta: { title: '库存预警报表', icon: 'warning' }
      },
      {
        path: 'analysis/turnover',
        component: () => import('@/views/report/analysis/turnover'),
        name: 'ReportTurnoverAnalysis',
        meta: { title: '物品周转率分析', icon: 'chart' }
      },
      {
        path: 'analysis/value',
        component: () => import('@/views/report/analysis/value'),
        name: 'ReportValueAnalysis',
        meta: { title: '库存价值分析', icon: 'money' }
      }
    ]
  },
  {
    path: '/inventory/batch',
    component: Layout,
    hidden: true,
    permissions: ['inventory:stock:edit'],
    children: [
      {
        path: 'threshold',
        component: () => import('@/views/inventory/batch/threshold'),
        name: 'BatchThreshold',
        meta: { title: '库存阈值设置', activeMenu: '/inventory/stock' }
      }
    ]
  },
  {
    path: '/inventory/stock/product',
    component: Layout,
    hidden: true,
    permissions: ['inventory:stock:list'],
    children: [
      {
        path: '',
        component: () => import('@/views/inventory/stock/productStock'),
        name: 'ProductStock',
        meta: { title: '物品库存', activeMenu: '/inventory/stock' }
      }
    ]
  },

  {
    path: '/inventory/transfer/print',
    component: Layout,
    hidden: true,
    permissions: ['inventory:transfer:print'],
    children: [
      {
        path: ':id(\\d+)',
        component: () => import('@/views/inventory/transfer/print'),
        name: 'TransferPrint',
        meta: { title: '调拨单打印', activeMenu: '/inventory/transfer' }
      }
    ]
  },
  {
    path: '/inventory/transfer/batch',
    component: Layout,
    hidden: true,
    permissions: ['inventory:transfer:add'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/inventory/transfer/batch'),
        name: 'BatchTransfer',
        meta: { title: '批量调拨', activeMenu: '/inventory/transfer' }
      }
    ]
  },
  {
    path: '/inventory/check/print',
    component: Layout,
    hidden: true,
    permissions: ['inventory:check:print'],
    children: [
      {
        path: ':id(\\d+)',
        component: () => import('@/views/inventory/check/print'),
        name: 'CheckPrint',
        meta: { title: '盘点单打印', activeMenu: '/inventory/check' }
      }
    ]
  },
  {
    path: '/inventory/purchase/print',
    component: Layout,
    hidden: true,
    permissions: ['inventory:purchase:print'],
    children: [
      {
        path: ':requestId(\\d+)',
        component: () => import('@/views/inventory/purchase/print'),
        name: 'PurchasePrint',
        meta: { title: '申购单打印', activeMenu: '/inventory/purchase' }
      }
    ]
  },
  {
    path: '/inventory/in/print',
    component: Layout,
    hidden: true,
    permissions: ['inventory:in:print'],
    children: [
      {
        path: ':id(\\d+)',
        component: () => import('@/views/inventory/in/print'),
        name: 'InventoryInPrint',
        meta: { title: '入库单打印', activeMenu: '/inventory/in' }
      }
    ]
  },
  {
    path: '/inventory/out/print',
    component: Layout,
    hidden: true,
    permissions: ['inventory:out:print'],
    children: [
      {
        path: ':id(\\d+)',
        component: () => import('@/views/inventory/out/print'),
        name: 'InventoryOutPrint',
        meta: { title: '出库单打印', activeMenu: '/inventory/out' }
      }
    ]
  },
  {
    path: '/inventory/check/batch',
    component: Layout,
    hidden: true,
    permissions: ['inventory:check:add'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/inventory/check/batch'),
        name: 'BatchCheck',
        meta: { title: '批量盘点', activeMenu: '/inventory/check' }
      }
    ]
  },



  {
    path: '/system/role-warehouse',
    component: Layout,
    hidden: true,
    permissions: ['system:role:authwarehouse'],
    children: [
      {
        path: ':roleId(\\d+)/:roleName',
        component: () => import('@/views/system/role/role-warehouse.vue'),
        name: 'SystemRoleAuthWarehouse',
        meta: { title: '分配仓库', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/role/advancedDataScope',
    component: Layout,
    hidden: true,
    permissions: ['system:role:advancedscope'],
    children: [
      {
        path: ':roleId(\\d+)/:roleName',
        component: () => import('@/views/system/role/advancedDataScope'),
        name: 'SystemRoleAdvancedDataScope',
        meta: { title: '高级数据权限配置', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/role/complexDataScope',
    component: Layout,
    hidden: true,
    permissions: ['system:role:complexscope'],
    children: [
      {
        path: ':roleId(\\d+)/:roleName',
        component: () => import('@/views/system/role/complexDataScope'),
        name: 'SystemRoleComplexDataScope',
        meta: { title: '复合数据权限配置', activeMenu: '/system/role' }
      }
    ]
  },


  {
    path: '/log',
    component: Layout,
    hidden: false,
    name: 'LogManagement',
    meta: { title: '日志管理', icon: 'log' },
    children: [
      {
        path: 'stock',
        component: () => import('@/views/log/stock/index'),
        name: 'InventoryLog',
        meta: { title: '出入库日志', icon: 'documentation' }
      },
      {
        path: 'error',
        component: () => import('@/views/log/error/index'),
        name: 'ErrorLog',
        meta: { title: '错误日志', icon: 'bug' }
      },
      {
        path: 'security',
        component: () => import('@/views/log/security/index'),
        name: 'SecurityLog',
        meta: { title: '安全日志', icon: 'lock' }
      },
      {
        path: 'system',
        component: () => import('@/views/log/system/index'),
        name: 'SystemLog',
        meta: { title: '系统日志', icon: 'system' }
      }
    ]
  }
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})