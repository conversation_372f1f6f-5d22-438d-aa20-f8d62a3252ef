<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.warehouse.product.mapper.ProductMapper">
    
    <resultMap type="com.warehouse.product.domain.Product" id="ProductResult">
        <id     property="productId"      column="product_id"      />
        <result property="productCode"    column="product_code"    />
        <result property="productName"    column="product_name"    />
        <result property="specification"  column="specification"   />
        <result property="unit"           column="unit"            />
        <result property="category"       column="category"        />
        <result property="price"          column="price"           />
        <result property="stock"          column="stock"           />
        <result property="supplierId"     column="supplier_id"     />
        <result property="supplierName"   column="supplier_name"   />
        <result property="remark"         column="remark"          />
        <result property="imageUrl"       column="image_url"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateTime"     column="update_time"     />
    </resultMap>

    <sql id="selectProductVo">
        select product_id, product_code, product_name, product_spec as specification, 
               product_unit as unit, product_category as category, price, status, remark, 
               create_time, update_time, image_url
        from wms_product
    </sql>

    <select id="selectProductList" parameterType="com.warehouse.product.domain.Product" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        <where>
            <if test="productCode != null and productCode != ''">
                AND product_code like concat('%', #{productCode}, '%')
            </if>
            <if test="productName != null and productName != ''">
                AND product_name like concat('%', #{productName}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="supplierId != null">
                AND supplier_id = #{supplierId}
            </if>
            <if test="supplierName != null and supplierName != ''">
                AND supplier_name like concat('%', #{supplierName}, '%')
            </if>
        </where>
    </select>
    
    <select id="selectProductById" parameterType="Long" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        where product_id = #{productId}
    </select>
    
    <select id="selectProductByCode" parameterType="String" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        where product_code = #{productCode}
    </select>
    
    <insert id="insertProduct" parameterType="com.warehouse.product.domain.Product" useGeneratedKeys="true" keyProperty="productId">
        insert into wms_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productCode != null">product_code,</if>
            <if test="productName != null">product_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unit != null">unit,</if>
            <if test="category != null">category,</if>
            <if test="price != null">price,</if>
            <if test="stock != null">stock,</if>
            <if test="supplierId != null">supplier_id,</if>
            <if test="supplierName != null">supplier_name,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productCode != null">#{productCode},</if>
            <if test="productName != null">#{productName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unit != null">#{unit},</if>
            <if test="category != null">#{category},</if>
            <if test="price != null">#{price},</if>
            <if test="stock != null">#{stock},</if>
            <if test="supplierId != null">#{supplierId},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateProduct" parameterType="com.warehouse.product.domain.Product">
        update wms_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="productCode != null">product_code = #{productCode},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="category != null">category = #{category},</if>
            <if test="price != null">price = #{price},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="supplierId != null">supplier_id = #{supplierId},</if>
            <if test="supplierName != null">supplier_name = #{supplierName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where product_id = #{productId}
    </update>

    <update id="updateProductStock" parameterType="com.warehouse.product.domain.Product">
        update wms_product
        set stock = #{stock},
            update_time = #{updateTime}
        where product_id = #{productId}
    </update>

    <delete id="deleteProductById" parameterType="Long">
        delete from wms_product where product_id = #{productId}
    </delete>

    <delete id="deleteProductByIds" parameterType="Long">
        delete from wms_product where product_id in
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>
</mapper>