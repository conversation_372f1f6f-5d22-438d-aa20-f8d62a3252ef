<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.SysLicenseFeatureMapper">
    
    <resultMap type="SysLicenseFeature" id="SysLicenseFeatureResult">
        <result property="featureId"    column="feature_id"    />
        <result property="featureCode"    column="feature_code"    />
        <result property="featureName"    column="feature_name"    />
        <result property="featureDesc"    column="feature_desc"    />
        <result property="licenseTypes"    column="license_types"    />
        <result property="isCore"    column="is_core"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSysLicenseFeatureVo">
        select feature_id, feature_code, feature_name, feature_desc, license_types, is_core, sort_order, status, create_time from sys_license_feature
    </sql>

    <select id="selectSysLicenseFeatureList" parameterType="SysLicenseFeature" resultMap="SysLicenseFeatureResult">
        <include refid="selectSysLicenseFeatureVo"/>
        <where>  
            <if test="featureCode != null  and featureCode != ''"> and feature_code like concat('%', #{featureCode}, '%')</if>
            <if test="featureName != null  and featureName != ''"> and feature_name like concat('%', #{featureName}, '%')</if>
            <if test="featureDesc != null  and featureDesc != ''"> and feature_desc like concat('%', #{featureDesc}, '%')</if>
            <if test="licenseTypes != null  and licenseTypes != ''"> and license_types like concat('%', #{licenseTypes}, '%')</if>
            <if test="isCore != null  and isCore != ''"> and is_core = #{isCore}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, feature_id asc
    </select>
    
    <select id="selectSysLicenseFeatureByFeatureId" parameterType="Long" resultMap="SysLicenseFeatureResult">
        <include refid="selectSysLicenseFeatureVo"/>
        where feature_id = #{featureId}
    </select>

    <select id="selectSysLicenseFeatureByStatus" parameterType="String" resultMap="SysLicenseFeatureResult">
        <include refid="selectSysLicenseFeatureVo"/>
        where status = #{status}
        order by sort_order asc, feature_id asc
    </select>

    <select id="selectSysLicenseFeatureByCode" parameterType="String" resultMap="SysLicenseFeatureResult">
        <include refid="selectSysLicenseFeatureVo"/>
        where feature_code = #{featureCode}
    </select>

    <select id="selectSysLicenseFeatureByCore" parameterType="String" resultMap="SysLicenseFeatureResult">
        <include refid="selectSysLicenseFeatureVo"/>
        where is_core = #{isCore}
        order by sort_order asc, feature_id asc
    </select>

    <select id="selectFeaturesByLicenseType" parameterType="String" resultMap="SysLicenseFeatureResult">
        <include refid="selectSysLicenseFeatureVo"/>
        where (license_types like concat('%', #{licenseType}, '%') or license_types is null or license_types = '')
        and status = '0'
        order by sort_order asc, feature_id asc
    </select>
        
    <insert id="insertSysLicenseFeature" parameterType="SysLicenseFeature" useGeneratedKeys="true" keyProperty="featureId">
        insert into sys_license_feature
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="featureCode != null and featureCode != ''">feature_code,</if>
            <if test="featureName != null and featureName != ''">feature_name,</if>
            <if test="featureDesc != null">feature_desc,</if>
            <if test="licenseTypes != null">license_types,</if>
            <if test="isCore != null">is_core,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="featureCode != null and featureCode != ''">#{featureCode},</if>
            <if test="featureName != null and featureName != ''">#{featureName},</if>
            <if test="featureDesc != null">#{featureDesc},</if>
            <if test="licenseTypes != null">#{licenseTypes},</if>
            <if test="isCore != null">#{isCore},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateSysLicenseFeature" parameterType="SysLicenseFeature">
        update sys_license_feature
        <trim prefix="SET" suffixOverrides=",">
            <if test="featureCode != null and featureCode != ''">feature_code = #{featureCode},</if>
            <if test="featureName != null and featureName != ''">feature_name = #{featureName},</if>
            <if test="featureDesc != null">feature_desc = #{featureDesc},</if>
            <if test="licenseTypes != null">license_types = #{licenseTypes},</if>
            <if test="isCore != null">is_core = #{isCore},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where feature_id = #{featureId}
    </update>

    <update id="updateFeatureStatus">
        update sys_license_feature 
        set status = #{status}, update_time = now()
        where feature_id = #{featureId}
    </update>

    <update id="updateFeaturesStatus">
        update sys_license_feature 
        set status = #{status}, update_time = now()
        where feature_id in
        <foreach item="featureId" collection="featureIds" open="(" separator="," close=")">
            #{featureId}
        </foreach>
    </update>

    <delete id="deleteSysLicenseFeatureByFeatureId" parameterType="Long">
        delete from sys_license_feature where feature_id = #{featureId}
    </delete>

    <delete id="deleteSysLicenseFeatureByFeatureIds" parameterType="String">
        delete from sys_license_feature where feature_id in 
        <foreach item="featureId" collection="array" open="(" separator="," close=")">
            #{featureId}
        </foreach>
    </delete>
</mapper>