package com.wanyu.system.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.wanyu.common.core.domain.entity.SysRole;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.system.domain.SysPermissionTemplate;
import com.wanyu.system.domain.SysPermissionTemplateCategory;
import com.wanyu.system.domain.SysRoleMenu;
import com.wanyu.system.domain.SysRoleWarehouse;
import com.wanyu.system.mapper.SysPermissionTemplateMapper;
import com.wanyu.system.mapper.SysPermissionTemplateCategoryMapper;
import com.wanyu.system.mapper.SysRoleMenuMapper;
import com.wanyu.system.mapper.SysRoleWarehouseMapper;
import com.wanyu.system.service.ISysPermissionLogService;
import com.wanyu.system.service.ISysPermissionTemplateService;
import com.wanyu.system.service.ISysUserMenuService;
import com.wanyu.system.service.ISysUserWarehouseService;

/**
 * 权限模板Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysPermissionTemplateServiceImpl implements ISysPermissionTemplateService {
    
    @Autowired
    private SysPermissionTemplateMapper permissionTemplateMapper;
    
    @Autowired
    private SysRoleMenuMapper roleMenuMapper;
    
    @Autowired
    private SysRoleWarehouseMapper roleWarehouseMapper;
    
    @Autowired
    private SysPermissionTemplateCategoryMapper categoryMapper;
    
    @Autowired
    private ISysPermissionLogService permissionLogService;
    
    @Autowired
    private ISysUserMenuService userMenuService;
    
    @Autowired
    private ISysUserWarehouseService userWarehouseService;
    
    /**
     * 查询权限模板
     *
     * @param templateId 权限模板ID
     * @return 权限模板
     */
    @Override
    public SysPermissionTemplate selectSysPermissionTemplateByTemplateId(Long templateId) {
        return permissionTemplateMapper.selectSysPermissionTemplateByTemplateId(templateId);
    }

    /**
     * 查询权限模板列表
     *
     * @param permissionTemplate 权限模板
     * @return 权限模板集合
     */
    @Override
    public List<SysPermissionTemplate> selectSysPermissionTemplateList(SysPermissionTemplate permissionTemplate) {
        List<SysPermissionTemplate> list = permissionTemplateMapper.selectSysPermissionTemplateList(permissionTemplate);
        for (SysPermissionTemplate template : list) {
            if (template.getCategoryId() != null) {
                SysPermissionTemplateCategory category = categoryMapper.selectSysPermissionTemplateCategoryById(template.getCategoryId());
                if (category != null) {
                    template.setCategoryName(category.getCategoryName());
                }
            }
        }
        return list;
    }

    /**
     * 查询权限模板权限
     *
     * @param templateId 权限模板ID
     * @return 权限列表
     */
    @Override
    public List<String> selectPermissionsByTemplateId(Long templateId) {
        return permissionTemplateMapper.selectPermissionsByTemplateId(templateId);
    }
    
    /**
     * 查询权限模板菜单ID列表
     *
     * @param templateId 权限模板ID
     * @return 菜单ID列表
     */
    @Override
    public List<Long> selectTemplateMenuIds(Long templateId) {
        return permissionTemplateMapper.selectTemplateMenuIds(templateId);
    }

    /**
     * 新增权限模板
     *
     * @param permissionTemplate 权限模板
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSysPermissionTemplate(SysPermissionTemplate permissionTemplate) {
        // 校验模板名称不能为空
        if (StringUtils.isEmpty(permissionTemplate.getTemplateName())) {
            throw new RuntimeException("模板名称不能为空");
        }
        
        // 插入权限模板
        int rows = permissionTemplateMapper.insertSysPermissionTemplate(permissionTemplate);
        
        // 如果插入成功且模板ID已生成，再处理关联关系
        if (rows > 0 && permissionTemplate.getTemplateId() != null) {
            // 新增权限模板菜单关联
            insertTemplateMenu(permissionTemplate);
            
            // 新增权限模板仓库关联
            insertTemplateWarehouse(permissionTemplate);
            
            // 注意：category_id 已经在主表中设置，不需要单独的关联表
        }
        
        // 记录权限变更日志(注释掉不存在表的操作)
        /*
        if (rows > 0 && permissionTemplate.getTemplateId() != null) {
            permissionLogService.insertTemplateLog(
                    permissionTemplate.getTemplateId(),
                    permissionTemplate.getTemplateName(),
                    "TEMPLATE",
                    permissionTemplate.getTemplateId(),
                    permissionTemplate.getTemplateName(),
                    "CREATE",
                    "0",
                    SecurityUtils.getUsername(),
                    "创建权限模板");
        }
        */
        
        return rows;
    }

    /**
     * 修改权限模板
     *
     * @param permissionTemplate 权限模板
     * @return 结果
     */
    @Override
    @Transactional
    public int updateSysPermissionTemplate(SysPermissionTemplate permissionTemplate) {
        // 获取修改前的权限模板
        SysPermissionTemplate oldTemplate = selectSysPermissionTemplateByTemplateId(permissionTemplate.getTemplateId());
        
        // 删除权限模板与菜单关联
        permissionTemplateMapper.deleteTemplateMenuByTemplateId(permissionTemplate.getTemplateId());

        // 删除权限模板与仓库关联
        permissionTemplateMapper.deleteTemplateWarehouseByTemplateId(permissionTemplate.getTemplateId());
        
        // 新增权限模板菜单关联
        insertTemplateMenu(permissionTemplate);
        
        // 新增权限模板仓库关联
        insertTemplateWarehouse(permissionTemplate);
        
        // 更新权限模板分类关联
        // 注意：category_id 已经在主表中更新，不需要单独的关联表操作
        
        // 更新权限模板
        int rows = permissionTemplateMapper.updateSysPermissionTemplate(permissionTemplate);
        
        // 记录权限变更日志
        permissionLogService.insertTemplateLog(
                permissionTemplate.getTemplateId(),
                permissionTemplate.getTemplateName(),
                "TEMPLATE",
                permissionTemplate.getTemplateId(),
                permissionTemplate.getTemplateName(),
                "UPDATE",
                "0",
                SecurityUtils.getUsername(),
                "修改权限模板");
        
        return rows;
    }

    /**
     * 批量删除权限模板
     *
     * @param templateIds 需要删除的权限模板ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSysPermissionTemplateByTemplateIds(Long[] templateIds) {
        // 记录权限变更日志
        for (Long templateId : templateIds) {
            SysPermissionTemplate template = selectSysPermissionTemplateByTemplateId(templateId);
            permissionLogService.insertTemplateLog(
                templateId,
                template.getTemplateName(),
                "TEMPLATE",
                templateId,
                template.getTemplateName(),
                "DELETE",
                "0",
                SecurityUtils.getUsername(),
                "删除权限模板");
        }
        
        // 删除权限模板与菜单关联
        for (Long templateId : templateIds) {
            permissionTemplateMapper.deleteTemplateMenuByTemplateId(templateId);
        }

        // 删除权限模板与仓库关联
        permissionTemplateMapper.deleteTemplateWarehouseByTemplateIds(templateIds);
        
        // 删除权限模板
        return permissionTemplateMapper.deleteSysPermissionTemplateByTemplateIds(templateIds);
    }

    /**
     * 删除权限模板信息
     *
     * @param templateId 权限模板ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSysPermissionTemplateByTemplateId(Long templateId) {
        // 记录权限变更日志
        SysPermissionTemplate template = selectSysPermissionTemplateByTemplateId(templateId);
        permissionLogService.insertTemplateLog(
                templateId,
                template.getTemplateName(),
                "TEMPLATE",
                templateId,
                template.getTemplateName(),
                "DELETE",
                "0",
                SecurityUtils.getUsername(),
                "删除权限模板");
        
        // 删除权限模板与菜单关联
        permissionTemplateMapper.deleteTemplateMenuByTemplateId(templateId);
        
        // 删除权限模板与仓库关联
        permissionTemplateMapper.deleteTemplateWarehouseByTemplateId(templateId);
        
        // 删除权限模板
        return permissionTemplateMapper.deleteSysPermissionTemplateByTemplateId(templateId);
    }

    /**
     * 新增权限模板菜单关联
     * 
     * @param permissionTemplate 权限模板对象
     */
    private void insertTemplateMenu(SysPermissionTemplate permissionTemplate) {
        Long[] menuIds = permissionTemplate.getMenuIds();
        if (StringUtils.isNotNull(menuIds) && menuIds.length > 0 && permissionTemplate.getTemplateId() != null) {
            // 新增模板与菜单关联
            List<Map<String, Object>> list = new ArrayList<>();
            for (Long menuId : menuIds) {
                Map<String, Object> item = new HashMap<>();
                item.put("templateId", permissionTemplate.getTemplateId());
                item.put("menuId", menuId);
                list.add(item);
            }
            if (!list.isEmpty()) {
                permissionTemplateMapper.batchInsertTemplateMenu(list);
            }
        }
    }

    /**
     * 新增权限模板仓库关联
     * 
     * @param permissionTemplate 权限模板对象
     */
    private void insertTemplateWarehouse(SysPermissionTemplate permissionTemplate) {
        Long[] warehouseIds = permissionTemplate.getWarehouseIds();
        if (StringUtils.isNotNull(warehouseIds) && warehouseIds.length > 0 && permissionTemplate.getTemplateId() != null) {
            // 新增模板与仓库关联
            List<Map<String, Object>> list = new ArrayList<>();
            for (Long warehouseId : warehouseIds) {
                Map<String, Object> item = new HashMap<>();
                item.put("templateId", permissionTemplate.getTemplateId());
                item.put("warehouseId", warehouseId);
                list.add(item);
            }
            if (!list.isEmpty()) {
                permissionTemplateMapper.batchInsertTemplateWarehouse(list);
            }
        }
    }

    /**
     * 将权限模板应用到角色
     *
     * @param templateId 权限模板ID
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional
    public int applyTemplateToRole(Long templateId, Long roleId) {
        // 获取权限模板
        SysPermissionTemplate template = selectSysPermissionTemplateByTemplateId(templateId);
        if (template == null) {
            return 0;
        }
        
        // 获取模板的菜单ID列表
        List<Long> menuIdsList = permissionTemplateMapper.selectTemplateMenuIds(templateId);
        Long[] menuIds = menuIdsList != null ? menuIdsList.toArray(new Long[0]) : null;
        
        // 获取模板的仓库ID列表
        List<Long> warehouseIdsList = permissionTemplateMapper.selectTemplateWarehouseIds(templateId);
        Long[] warehouseIds = warehouseIdsList != null ? warehouseIdsList.toArray(new Long[0]) : null;
        
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        
        // 批量新增角色菜单关联
        if (menuIds != null && menuIds.length > 0) {
            List<SysRoleMenu> list = Arrays.stream(menuIds)
                    .map(menuId -> {
                        SysRoleMenu rm = new SysRoleMenu();
                        rm.setRoleId(roleId);
                        rm.setMenuId(menuId);
                        return rm;
                    })
                    .collect(Collectors.toList());
            roleMenuMapper.batchRoleMenu(list);
        }
        
        // 删除角色与仓库关联
        roleWarehouseMapper.deleteRoleWarehouseByRoleId(roleId);
        
        // 批量新增角色仓库关联
        if (warehouseIds != null && warehouseIds.length > 0) {
            List<SysRoleWarehouse> list = Arrays.stream(warehouseIds)
                    .map(warehouseId -> {
                        SysRoleWarehouse rw = new SysRoleWarehouse();
                        rw.setRoleId(roleId);
                        rw.setWarehouseId(warehouseId);
                        return rw;
                    })
                    .collect(Collectors.toList());
            roleWarehouseMapper.batchInsertRoleWarehouse(list);
        }
        
        // 记录日志
        permissionLogService.insertTemplateLog(
            templateId,
            template.getTemplateName(),
            "ROLE",
            roleId,
            "",
            "APPLY",
            "0",
            SecurityUtils.getUsername(),
            "应用权限模板到角色"
        );
        
        return 1;
    }

    /**
     * 将权限模板应用到用户
     *
     * @param templateId 权限模板ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int applyTemplateToUser(Long templateId, Long userId) {
        // 获取权限模板
        SysPermissionTemplate template = selectSysPermissionTemplateByTemplateId(templateId);
        if (template == null) {
            return 0;
        }
        
        try {
            // 获取模板的菜单ID列表
            List<Long> menuIdsList = permissionTemplateMapper.selectTemplateMenuIds(templateId);
            Long[] menuIds = menuIdsList != null ? menuIdsList.toArray(new Long[0]) : null;
            
            // 获取模板的仓库ID列表
            List<Long> warehouseIdsList = permissionTemplateMapper.selectTemplateWarehouseIds(templateId);
            Long[] warehouseIds = warehouseIdsList != null ? warehouseIdsList.toArray(new Long[0]) : null;
            
            // 删除用户原有的菜单权限
            userMenuService.deleteUserMenuByUserId(userId);
            
            // 批量新增用户菜单权限
            if (menuIds != null && menuIds.length > 0) {
                userMenuService.insertUserMenu(userId, menuIds);
            }
            
            // 删除用户原有的仓库权限
            userWarehouseService.deleteUserWarehouseByUserId(userId);
            
            // 批量新增用户仓库权限
            if (warehouseIds != null && warehouseIds.length > 0) {
                userWarehouseService.insertUserWarehouse(userId, warehouseIds);
            }
            
            // 记录日志
            permissionLogService.insertTemplateLog(
                templateId,
                template.getTemplateName(),
                "USER",
                userId,
                "",
                "APPLY",
                "0", // 成功
                SecurityUtils.getUsername(),
                "应用权限模板到用户"
            );
            
            return 1;
        } catch (Exception e) {
            // 记录失败日志
            permissionLogService.insertTemplateLog(
                templateId,
                template.getTemplateName(),
                "USER",
                userId,
                "",
                "APPLY",
                "1", // 失败
                SecurityUtils.getUsername(),
                "应用权限模板到用户失败: " + e.getMessage()
            );
            throw e;
        }
    }

    @Override
    public List<Map<String, Object>> getTemplateCategoryTree() {
        List<SysPermissionTemplateCategory> categories = categoryMapper.selectSysPermissionTemplateCategoryList(new SysPermissionTemplateCategory());
        List<Map<String, Object>> treeList = new ArrayList<>();
        
        // 构建分类树并转换为前端需要的格式
        for (SysPermissionTemplateCategory category : categories) {
            if (category.getParentId() == 0) {
                Map<String, Object> node = new HashMap<>();
                node.put("id", category.getCategoryId());
                node.put("label", category.getCategoryName());
                
                // 获取子分类列表
                List<Map<String, Object>> children = getCategoryChildren(categories, category.getCategoryId());
                // 只有当存在子分类时才添加children属性，避免显示"No sub-options"
                if (children != null && !children.isEmpty()) {
                    node.put("children", children);
                }
                
                treeList.add(node);
            }
        }
        
        return treeList;
    }
    
    /**
     * 递归获取子分类
     */
    private List<Map<String, Object>> getCategoryChildren(List<SysPermissionTemplateCategory> categories, Long parentId) {
        List<Map<String, Object>> children = new ArrayList<>();
        for (SysPermissionTemplateCategory category : categories) {
            if (parentId.equals(category.getParentId())) {
                Map<String, Object> node = new HashMap<>();
                node.put("id", category.getCategoryId());
                node.put("label", category.getCategoryName());
                
                // 递归获取子分类的子分类
                List<Map<String, Object>> subChildren = getCategoryChildren(categories, category.getCategoryId());
                // 只有当存在子分类时才添加children属性
                if (subChildren != null && !subChildren.isEmpty()) {
                    node.put("children", subChildren);
                }
                
                children.add(node);
            }
        }
        return children;
    }

}