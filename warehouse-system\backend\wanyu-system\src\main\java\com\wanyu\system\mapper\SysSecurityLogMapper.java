package com.wanyu.system.mapper;

import java.util.List;
import com.wanyu.system.domain.SysSecurityLog;

/**
 * 安全日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface SysSecurityLogMapper 
{
    /**
     * 查询安全日志
     * 
     * @param logId 安全日志主键
     * @return 安全日志
     */
    public SysSecurityLog selectSysSecurityLogByLogId(Long logId);

    /**
     * 查询安全日志列表
     * 
     * @param sysSecurityLog 安全日志
     * @return 安全日志集合
     */
    public List<SysSecurityLog> selectSysSecurityLogList(SysSecurityLog sysSecurityLog);

    /**
     * 新增安全日志
     * 
     * @param sysSecurityLog 安全日志
     * @return 结果
     */
    public int insertSysSecurityLog(SysSecurityLog sysSecurityLog);

    /**
     * 修改安全日志
     * 
     * @param sysSecurityLog 安全日志
     * @return 结果
     */
    public int updateSysSecurityLog(SysSecurityLog sysSecurityLog);

    /**
     * 删除安全日志
     * 
     * @param logId 安全日志主键
     * @return 结果
     */
    public int deleteSysSecurityLogByLogId(Long logId);

    /**
     * 批量删除安全日志
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysSecurityLogByLogIds(Long[] logIds);
}
