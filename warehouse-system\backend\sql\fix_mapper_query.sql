-- 这个脚本用于查看当前的XML映射文件内容
-- 实际修改需要在XML文件中进行

-- 查询wms_specification表的列名
DESCRIBE wms_specification;

-- 示例查询（仅用于参考）
SELECT 
  l.log_id, 
  l.warehouse_id, 
  w.warehouse_name, 
  l.product_id, 
  p.product_name, 
  p.product_code, 
  l.operation_type, 
  l.quantity, 
  l.before_quantity, 
  l.after_quantity, 
  l.related_order_id, 
  l.related_order_type, 
  l.operator, 
  l.operation_time, 
  ps.spec_name as specification, -- 修改这里，使用spec_name而不是specification
  pu.unit_name as unit, 
  l.remark, 
  l.create_by, 
  l.create_time, 
  l.update_by, 
  l.update_time 
FROM 
  wms_inventory_log l 
  LEFT JOIN sys_warehouse w ON l.warehouse_id = w.warehouse_id 
  LEFT JOIN wms_product p ON l.product_id = p.product_id 
  LEFT JOIN wms_specification ps ON p.spec_id = ps.spec_id 
  LEFT JOIN wms_unit pu ON p.unit_id = pu.unit_id 
ORDER BY 
  l.operation_time DESC;