@echo off
chcp 65001 >nul
echo ========================================
echo 最终系统重启 - 库位货架岗位清理完成
echo ========================================

echo.
echo 1. 构建后端JAR文件...
cd /d "C:\CKGLXT\warehouse-system\backend"
call mvn clean package -Dmaven.test.skip=true
if %errorlevel% neq 0 (
    echo 后端构建失败，请检查代码
    pause
    exit /b 1
)

echo.
echo 2. 清理前端缓存并重新构建...
cd /d "C:\CKGLXT\warehouse-system\frontend"
if exist "dist" (
    rmdir /s /q "dist"
    echo 已清理前端构建文件
)

if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache"
    echo 已清理前端缓存
)

call npm run build:prod
if %errorlevel% neq 0 (
    echo 前端构建失败，请检查代码
    pause
    exit /b 1
)

echo.
echo 3. 启动后端服务...
cd /d "C:\CKGLXT\warehouse-system\backend\wanyu-admin\target"
start "仓库系统后端" java -jar wanyu-admin-3.8.9.jar --server.port=8080

echo 等待后端服务启动...
timeout /t 15 /nobreak >nul

echo.
echo 4. 启动前端服务...
cd /d "C:\CKGLXT\warehouse-system\frontend"
start "仓库系统前端" npm run dev

echo.
echo ========================================
echo 系统启动完成！
echo ========================================
echo.
echo 服务地址：
echo 后端服务: http://localhost:8080
echo 前端服务: http://localhost:8081
echo.
echo 已完成清理的功能：
echo - 库区管理（已删除）
echo - 货架管理（已删除）
echo - 库位管理（已删除）
echo - 岗位管理（已删除）
echo.
echo 保留的核心功能：
echo - 仓库信息管理
echo - 物品管理
echo - 库存管理
echo - 用户权限管理
echo - 入库出库管理
echo - 系统监控
echo.
echo 请在浏览器中访问 http://localhost:8081 验证系统功能
echo.
pause