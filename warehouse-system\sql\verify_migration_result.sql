-- =====================================================
-- 字段定义标准化迁移结果验证脚本
-- 
-- 功能描述：
-- 全面验证字段定义标准化迁移的结果，确保：
-- 1. 数据完整性和一致性
-- 2. 字段定义符合项目标准
-- 3. 业务逻辑正确性
-- 4. 系统功能正常运行
-- 
-- 验证范围：
-- - 表结构验证
-- - 数据完整性验证
-- - 业务逻辑验证
-- - 性能影响评估
-- - 回滚准备状态检查
-- =====================================================

USE warehouse_system;

-- =====================================================
-- 验证参数设置
-- =====================================================

-- 获取最新的迁移ID（如果未指定）
SET @target_migration_id = COALESCE(
    @migration_id,
    (SELECT migration_id FROM field_standardization_log 
     WHERE phase = 'COMPLETED' AND step_name = '迁移完成' 
     ORDER BY log_id DESC LIMIT 1)
);

-- 创建验证结果表
DROP TABLE IF EXISTS migration_verification_result;
CREATE TABLE migration_verification_result (
    verification_id INT AUTO_INCREMENT PRIMARY KEY,
    migration_id VARCHAR(50) NOT NULL,
    verification_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    check_category VARCHAR(50) NOT NULL,
    check_name VARCHAR(100) NOT NULL,
    check_status ENUM('PASS', 'FAIL', 'WARNING', 'INFO') NOT NULL,
    expected_value VARCHAR(500),
    actual_value VARCHAR(500),
    check_details TEXT,
    severity ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO') DEFAULT 'MEDIUM',
    INDEX idx_migration_id (migration_id),
    INDEX idx_category (check_category),
    INDEX idx_status (check_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='迁移验证结果表';

-- =====================================================
-- 1. 基础环境验证
-- =====================================================

SELECT 
    '=== 字段定义标准化迁移验证开始 ===' as message,
    @target_migration_id as migration_id,
    NOW() as verification_start_time;

-- 验证迁移是否存在
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'ENVIRONMENT',
    '迁移记录存在性检查',
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END,
    '存在迁移记录',
    CASE WHEN COUNT(*) > 0 THEN CONCAT('找到', COUNT(*), '条迁移记录') ELSE '未找到迁移记录' END,
    CASE WHEN COUNT(*) > 0 THEN '迁移记录验证通过' ELSE '未找到指定的迁移记录，请检查migration_id' END,
    CASE WHEN COUNT(*) > 0 THEN 'INFO' ELSE 'CRITICAL' END
FROM field_standardization_log 
WHERE migration_id = @target_migration_id;

-- 验证备份数据库存在性
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'ENVIRONMENT',
    '备份数据库存在性检查',
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END,
    '备份数据库存在',
    CASE WHEN COUNT(*) > 0 THEN '备份数据库存在' ELSE '备份数据库不存在' END,
    CASE WHEN COUNT(*) > 0 THEN '备份数据库验证通过，支持回滚操作' ELSE '备份数据库不存在，无法执行回滚' END,
    CASE WHEN COUNT(*) > 0 THEN 'INFO' ELSE 'HIGH' END
FROM information_schema.schemata 
WHERE schema_name = 'warehouse_system_field_std_backup';

-- =====================================================
-- 2. 表结构验证
-- =====================================================

-- 验证sys_license表status字段定义
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'TABLE_STRUCTURE',
    'sys_license.status字段定义',
    CASE 
        WHEN COLUMN_DEFAULT = '0' AND COLUMN_COMMENT LIKE '%0%正常%1%停用%' THEN 'PASS'
        WHEN COLUMN_DEFAULT = '0' AND COLUMN_COMMENT LIKE '%0%启用%1%禁用%' THEN 'PASS'
        WHEN COLUMN_DEFAULT = '0' THEN 'WARNING'
        ELSE 'FAIL'
    END,
    'DEFAULT ''0'', COMMENT包含标准定义',
    CONCAT('DEFAULT ''', COALESCE(COLUMN_DEFAULT, 'NULL'), ''', COMMENT: ', COALESCE(COLUMN_COMMENT, 'NULL')),
    CASE 
        WHEN COLUMN_DEFAULT = '0' AND (COLUMN_COMMENT LIKE '%0%正常%1%停用%' OR COLUMN_COMMENT LIKE '%0%启用%1%禁用%') 
        THEN 'sys_license.status字段定义符合标准'
        WHEN COLUMN_DEFAULT = '0' THEN 'sys_license.status字段默认值正确，但注释可能需要完善'
        ELSE 'sys_license.status字段定义不符合标准，需要修复'
    END,
    CASE 
        WHEN COLUMN_DEFAULT = '0' AND (COLUMN_COMMENT LIKE '%0%正常%1%停用%' OR COLUMN_COMMENT LIKE '%0%启用%1%禁用%') 
        THEN 'INFO'
        WHEN COLUMN_DEFAULT = '0' THEN 'LOW'
        ELSE 'HIGH'
    END
FROM information_schema.columns 
WHERE table_schema = 'warehouse_system' 
  AND table_name = 'sys_license' 
  AND column_name = 'status';

-- 验证sys_license_feature表status字段定义
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'TABLE_STRUCTURE',
    'sys_license_feature.status字段定义',
    CASE 
        WHEN COLUMN_DEFAULT = '0' AND COLUMN_COMMENT LIKE '%0%正常%1%停用%' THEN 'PASS'
        WHEN COLUMN_DEFAULT = '0' AND COLUMN_COMMENT LIKE '%0%启用%1%禁用%' THEN 'PASS'
        WHEN COLUMN_DEFAULT = '0' THEN 'WARNING'
        ELSE 'FAIL'
    END,
    'DEFAULT ''0'', COMMENT包含标准定义',
    CONCAT('DEFAULT ''', COALESCE(COLUMN_DEFAULT, 'NULL'), ''', COMMENT: ', COALESCE(COLUMN_COMMENT, 'NULL')),
    CASE 
        WHEN COLUMN_DEFAULT = '0' AND (COLUMN_COMMENT LIKE '%0%正常%1%停用%' OR COLUMN_COMMENT LIKE '%0%启用%1%禁用%') 
        THEN 'sys_license_feature.status字段定义符合标准'
        WHEN COLUMN_DEFAULT = '0' THEN 'sys_license_feature.status字段默认值正确，但注释可能需要完善'
        ELSE 'sys_license_feature.status字段定义不符合标准，需要修复'
    END,
    CASE 
        WHEN COLUMN_DEFAULT = '0' AND (COLUMN_COMMENT LIKE '%0%正常%1%停用%' OR COLUMN_COMMENT LIKE '%0%启用%1%禁用%') 
        THEN 'INFO'
        WHEN COLUMN_DEFAULT = '0' THEN 'LOW'
        ELSE 'HIGH'
    END
FROM information_schema.columns 
WHERE table_schema = 'warehouse_system' 
  AND table_name = 'sys_license_feature' 
  AND column_name = 'status';

-- =====================================================
-- 3. 数据完整性验证
-- =====================================================

-- 验证sys_license表数据完整性
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'DATA_INTEGRITY',
    'sys_license表记录数一致性',
    CASE WHEN current_count = backup_count THEN 'PASS' ELSE 'FAIL' END,
    CAST(backup_count AS CHAR),
    CAST(current_count AS CHAR),
    CASE 
        WHEN current_count = backup_count THEN 'sys_license表记录数与备份一致'
        ELSE CONCAT('sys_license表记录数不一致，差异：', ABS(current_count - backup_count), '条')
    END,
    CASE WHEN current_count = backup_count THEN 'INFO' ELSE 'CRITICAL' END
FROM (
    SELECT 
        (SELECT COUNT(*) FROM sys_license) as current_count,
        COALESCE((SELECT backup_count FROM warehouse_system_field_std_backup.backup_metadata 
                  WHERE migration_id = @target_migration_id AND table_name = 'sys_license'), 0) as backup_count
) as count_comparison;

-- 验证sys_license_feature表数据完整性
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'DATA_INTEGRITY',
    'sys_license_feature表记录数一致性',
    CASE WHEN current_count = backup_count THEN 'PASS' ELSE 'FAIL' END,
    CAST(backup_count AS CHAR),
    CAST(current_count AS CHAR),
    CASE 
        WHEN current_count = backup_count THEN 'sys_license_feature表记录数与备份一致'
        ELSE CONCAT('sys_license_feature表记录数不一致，差异：', ABS(current_count - backup_count), '条')
    END,
    CASE WHEN current_count = backup_count THEN 'INFO' ELSE 'CRITICAL' END
FROM (
    SELECT 
        (SELECT COUNT(*) FROM sys_license_feature) as current_count,
        COALESCE((SELECT backup_count FROM warehouse_system_field_std_backup.backup_metadata 
                  WHERE migration_id = @target_migration_id AND table_name = 'sys_license_feature'), 0) as backup_count
) as count_comparison;

-- 验证状态字段值的有效性
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'DATA_INTEGRITY',
    'sys_license状态值有效性',
    CASE WHEN invalid_count = 0 THEN 'PASS' ELSE 'FAIL' END,
    '0',
    CAST(invalid_count AS CHAR),
    CASE 
        WHEN invalid_count = 0 THEN 'sys_license表所有status值都有效（0或1）'
        ELSE CONCAT('sys_license表存在', invalid_count, '条无效status值')
    END,
    CASE WHEN invalid_count = 0 THEN 'INFO' ELSE 'HIGH' END
FROM (
    SELECT COUNT(*) as invalid_count
    FROM sys_license 
    WHERE status NOT IN ('0', '1') OR status IS NULL
) as validity_check;

INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'DATA_INTEGRITY',
    'sys_license_feature状态值有效性',
    CASE WHEN invalid_count = 0 THEN 'PASS' ELSE 'FAIL' END,
    '0',
    CAST(invalid_count AS CHAR),
    CASE 
        WHEN invalid_count = 0 THEN 'sys_license_feature表所有status值都有效（0或1）'
        ELSE CONCAT('sys_license_feature表存在', invalid_count, '条无效status值')
    END,
    CASE WHEN invalid_count = 0 THEN 'INFO' ELSE 'HIGH' END
FROM (
    SELECT COUNT(*) as invalid_count
    FROM sys_license_feature 
    WHERE status NOT IN ('0', '1') OR status IS NULL
) as validity_check;

-- =====================================================
-- 4. 业务逻辑验证
-- =====================================================

-- 验证当前许可证状态逻辑
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'BUSINESS_LOGIC',
    '当前许可证状态逻辑',
    CASE 
        WHEN disabled_current_count = 0 THEN 'PASS'
        WHEN disabled_current_count > 0 AND enabled_current_count > 0 THEN 'WARNING'
        ELSE 'FAIL'
    END,
    '当前许可证应为启用状态(status=0)',
    CONCAT('启用:', enabled_current_count, ', 禁用:', disabled_current_count),
    CASE 
        WHEN disabled_current_count = 0 THEN '所有当前许可证都处于启用状态，逻辑正确'
        WHEN disabled_current_count > 0 AND enabled_current_count > 0 THEN '存在被禁用的当前许可证，可能影响系统功能'
        ELSE '当前许可证状态异常，需要检查'
    END,
    CASE 
        WHEN disabled_current_count = 0 THEN 'INFO'
        WHEN disabled_current_count > 0 AND enabled_current_count > 0 THEN 'MEDIUM'
        ELSE 'HIGH'
    END
FROM (
    SELECT 
        SUM(CASE WHEN current = 1 AND status = '0' THEN 1 ELSE 0 END) as enabled_current_count,
        SUM(CASE WHEN current = 1 AND status = '1' THEN 1 ELSE 0 END) as disabled_current_count
    FROM sys_license
) as current_license_check;

-- 验证核心功能状态
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'BUSINESS_LOGIC',
    '核心功能可用性',
    CASE 
        WHEN disabled_core_count = 0 THEN 'PASS'
        WHEN disabled_core_count > 0 AND enabled_core_count > 0 THEN 'WARNING'
        ELSE 'FAIL'
    END,
    '核心功能应为启用状态(status=0)',
    CONCAT('启用:', enabled_core_count, ', 禁用:', disabled_core_count),
    CASE 
        WHEN disabled_core_count = 0 THEN '所有核心功能都处于启用状态'
        WHEN disabled_core_count > 0 AND enabled_core_count > 0 THEN CONCAT('存在', disabled_core_count, '个被禁用的核心功能')
        ELSE '核心功能状态异常'
    END,
    CASE 
        WHEN disabled_core_count = 0 THEN 'INFO'
        WHEN disabled_core_count > 0 AND enabled_core_count > 0 THEN 'MEDIUM'
        ELSE 'HIGH'
    END
FROM (
    SELECT 
        SUM(CASE WHEN is_core = '1' AND status = '0' THEN 1 ELSE 0 END) as enabled_core_count,
        SUM(CASE WHEN is_core = '1' AND status = '1' THEN 1 ELSE 0 END) as disabled_core_count
    FROM sys_license_feature
) as core_feature_check;

-- =====================================================
-- 5. 数据字典验证
-- =====================================================

-- 验证sys_normal_disable字典
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'DICT_DATA',
    'sys_normal_disable字典标准化',
    CASE 
        WHEN normal_label = '正常' AND disable_label = '停用' THEN 'PASS'
        WHEN normal_label IS NOT NULL AND disable_label IS NOT NULL THEN 'WARNING'
        ELSE 'FAIL'
    END,
    '0=正常, 1=停用',
    CONCAT('0=', COALESCE(normal_label, 'NULL'), ', 1=', COALESCE(disable_label, 'NULL')),
    CASE 
        WHEN normal_label = '正常' AND disable_label = '停用' THEN 'sys_normal_disable字典标准化正确'
        WHEN normal_label IS NOT NULL AND disable_label IS NOT NULL THEN 'sys_normal_disable字典存在但标签可能需要调整'
        ELSE 'sys_normal_disable字典缺失或配置错误'
    END,
    CASE 
        WHEN normal_label = '正常' AND disable_label = '停用' THEN 'INFO'
        WHEN normal_label IS NOT NULL AND disable_label IS NOT NULL THEN 'LOW'
        ELSE 'MEDIUM'
    END
FROM (
    SELECT 
        (SELECT dict_label FROM sys_dict_data WHERE dict_type = 'sys_normal_disable' AND dict_value = '0' LIMIT 1) as normal_label,
        (SELECT dict_label FROM sys_dict_data WHERE dict_type = 'sys_normal_disable' AND dict_value = '1' LIMIT 1) as disable_label
) as dict_check;

-- 验证operation_status字典
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'DICT_DATA',
    'operation_status字典配置',
    CASE 
        WHEN success_label = '成功' AND fail_label = '失败' THEN 'PASS'
        WHEN success_label IS NOT NULL AND fail_label IS NOT NULL THEN 'WARNING'
        ELSE 'INFO'
    END,
    '0=成功, 1=失败',
    CONCAT('0=', COALESCE(success_label, 'NULL'), ', 1=', COALESCE(fail_label, 'NULL')),
    CASE 
        WHEN success_label = '成功' AND fail_label = '失败' THEN 'operation_status字典配置正确'
        WHEN success_label IS NOT NULL AND fail_label IS NOT NULL THEN 'operation_status字典存在但标签可能需要调整'
        ELSE 'operation_status字典不存在，如有操作日志功能需要配置'
    END,
    CASE 
        WHEN success_label = '成功' AND fail_label = '失败' THEN 'INFO'
        WHEN success_label IS NOT NULL AND fail_label IS NOT NULL THEN 'LOW'
        ELSE 'INFO'
    END
FROM (
    SELECT 
        (SELECT dict_label FROM sys_dict_data WHERE dict_type = 'operation_status' AND dict_value = '0' LIMIT 1) as success_label,
        (SELECT dict_label FROM sys_dict_data WHERE dict_type = 'operation_status' AND dict_value = '1' LIMIT 1) as fail_label
) as operation_dict_check;

-- =====================================================
-- 6. 迁移过程验证
-- =====================================================

-- 验证迁移步骤完成情况
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'MIGRATION_PROCESS',
    '迁移步骤完成情况',
    CASE 
        WHEN failed_steps = 0 AND completed_steps > 0 THEN 'PASS'
        WHEN failed_steps = 0 AND completed_steps = 0 THEN 'WARNING'
        ELSE 'FAIL'
    END,
    '所有步骤成功完成',
    CONCAT('完成:', completed_steps, ', 失败:', failed_steps, ', 跳过:', skipped_steps),
    CASE 
        WHEN failed_steps = 0 AND completed_steps > 0 THEN '所有迁移步骤成功完成'
        WHEN failed_steps = 0 AND completed_steps = 0 THEN '未找到已完成的迁移步骤'
        ELSE CONCAT('存在', failed_steps, '个失败的迁移步骤')
    END,
    CASE 
        WHEN failed_steps = 0 AND completed_steps > 0 THEN 'INFO'
        WHEN failed_steps = 0 AND completed_steps = 0 THEN 'MEDIUM'
        ELSE 'HIGH'
    END
FROM (
    SELECT 
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_steps,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_steps,
        SUM(CASE WHEN status = 'skipped' THEN 1 ELSE 0 END) as skipped_steps
    FROM field_standardization_log 
    WHERE migration_id = @target_migration_id
) as migration_status_check;

-- 验证迁移耗时
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'MIGRATION_PROCESS',
    '迁移执行时间',
    CASE 
        WHEN duration_minutes <= 30 THEN 'PASS'
        WHEN duration_minutes <= 60 THEN 'WARNING'
        ELSE 'INFO'
    END,
    '≤30分钟',
    CONCAT(duration_minutes, '分钟'),
    CASE 
        WHEN duration_minutes <= 30 THEN '迁移执行时间正常'
        WHEN duration_minutes <= 60 THEN '迁移执行时间较长，但在可接受范围内'
        ELSE '迁移执行时间超过预期，需要关注性能'
    END,
    CASE 
        WHEN duration_minutes <= 30 THEN 'INFO'
        WHEN duration_minutes <= 60 THEN 'LOW'
        ELSE 'MEDIUM'
    END
FROM (
    SELECT 
        COALESCE(TIMESTAMPDIFF(MINUTE, 
                              (SELECT MIN(start_time) FROM field_standardization_log WHERE migration_id = @target_migration_id), 
                              (SELECT MAX(end_time) FROM field_standardization_log WHERE migration_id = @target_migration_id AND end_time IS NOT NULL)), 0) as duration_minutes
) as duration_check;

-- =====================================================
-- 7. 性能影响评估
-- =====================================================

-- 检查表大小变化（如果有性能监控表）
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'PERFORMANCE',
    '表大小影响评估',
    'INFO',
    '无显著变化',
    CONCAT('sys_license: ', license_size, 'MB, sys_license_feature: ', feature_size, 'MB'),
    '字段标准化不应显著影响表大小，仅结构调整',
    'INFO'
FROM (
    SELECT 
        ROUND(((data_length + index_length) / 1024 / 1024), 2) as license_size
    FROM information_schema.tables 
    WHERE table_schema = 'warehouse_system' AND table_name = 'sys_license'
) license_info,
(
    SELECT 
        ROUND(((data_length + index_length) / 1024 / 1024), 2) as feature_size
    FROM information_schema.tables 
    WHERE table_schema = 'warehouse_system' AND table_name = 'sys_license_feature'
) feature_info;

-- =====================================================
-- 8. 回滚准备状态检查
-- =====================================================

-- 验证回滚脚本可用性
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'ROLLBACK_READINESS',
    '回滚脚本可用性',
    CASE WHEN script_exists THEN 'PASS' ELSE 'WARNING' END,
    '回滚脚本存在',
    CASE WHEN script_exists THEN '回滚脚本存在' ELSE '回滚脚本不存在' END,
    CASE WHEN script_exists THEN '回滚脚本可用，支持快速回滚' ELSE '建议准备回滚脚本以备不时之需' END,
    CASE WHEN script_exists THEN 'INFO' ELSE 'LOW' END
FROM (
    SELECT EXISTS(
        SELECT 1 FROM information_schema.routines 
        WHERE routine_schema = 'warehouse_system' 
          AND routine_name LIKE '%rollback%'
        UNION
        SELECT 1 WHERE EXISTS(
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'warehouse_system_field_std_backup'
        )
    ) as script_exists
) as rollback_check;

-- 验证备份数据可用性
INSERT INTO migration_verification_result 
(migration_id, check_category, check_name, check_status, expected_value, actual_value, check_details, severity)
SELECT 
    @target_migration_id,
    'ROLLBACK_READINESS',
    '备份数据可用性',
    CASE 
        WHEN backup_tables >= 3 THEN 'PASS'
        WHEN backup_tables > 0 THEN 'WARNING'
        ELSE 'FAIL'
    END,
    '≥3个备份表',
    CONCAT(backup_tables, '个备份表'),
    CASE 
        WHEN backup_tables >= 3 THEN '备份数据完整，支持完全回滚'
        WHEN backup_tables > 0 THEN '部分备份数据可用'
        ELSE '备份数据不可用，无法回滚'
    END,
    CASE 
        WHEN backup_tables >= 3 THEN 'INFO'
        WHEN backup_tables > 0 THEN 'MEDIUM'
        ELSE 'HIGH'
    END
FROM (
    SELECT COUNT(*) as backup_tables
    FROM information_schema.tables 
    WHERE table_schema = 'warehouse_system_field_std_backup'
      AND table_name LIKE '%backup%'
) as backup_availability_check;

-- =====================================================
-- 9. 生成验证报告
-- =====================================================

-- 统计验证结果
SELECT 
    '=== 字段定义标准化迁移验证报告 ===' as report_title,
    @target_migration_id as migration_id,
    NOW() as verification_completion_time;

-- 按类别统计验证结果
SELECT 
    '验证结果统计' as section,
    check_category as category,
    COUNT(*) as total_checks,
    SUM(CASE WHEN check_status = 'PASS' THEN 1 ELSE 0 END) as passed,
    SUM(CASE WHEN check_status = 'FAIL' THEN 1 ELSE 0 END) as failed,
    SUM(CASE WHEN check_status = 'WARNING' THEN 1 ELSE 0 END) as warnings,
    SUM(CASE WHEN check_status = 'INFO' THEN 1 ELSE 0 END) as info,
    ROUND(SUM(CASE WHEN check_status = 'PASS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as pass_rate
FROM migration_verification_result 
WHERE migration_id = @target_migration_id
GROUP BY check_category
ORDER BY check_category;

-- 显示失败和警告的检查项
SELECT 
    '需要关注的问题' as section,
    check_category,
    check_name,
    check_status,
    severity,
    check_details
FROM migration_verification_result 
WHERE migration_id = @target_migration_id
  AND check_status IN ('FAIL', 'WARNING')
ORDER BY 
    CASE severity 
        WHEN 'CRITICAL' THEN 1 
        WHEN 'HIGH' THEN 2 
        WHEN 'MEDIUM' THEN 3 
        WHEN 'LOW' THEN 4 
        ELSE 5 
    END,
    check_category;

-- 生成总体验证结论
SELECT 
    '验证结论' as conclusion_type,
    CASE 
        WHEN critical_issues > 0 THEN '❌ 验证失败 - 存在严重问题，建议回滚'
        WHEN high_issues > 0 THEN '⚠️ 验证部分通过 - 存在重要问题，需要修复'
        WHEN medium_issues > 0 THEN '✅ 验证基本通过 - 存在一般问题，建议关注'
        WHEN low_issues > 0 THEN '✅ 验证通过 - 存在轻微问题，可以接受'
        ELSE '🎉 验证完全通过 - 迁移成功，无问题'
    END as conclusion,
    CONCAT('严重:', critical_issues, ', 重要:', high_issues, ', 一般:', medium_issues, ', 轻微:', low_issues) as issue_summary,
    CASE 
        WHEN critical_issues > 0 THEN '立即执行回滚操作'
        WHEN high_issues > 0 THEN '修复重要问题后再继续'
        WHEN medium_issues > 0 THEN '关注一般问题，可继续使用'
        WHEN low_issues > 0 THEN '关注轻微问题，正常使用'
        ELSE '可以正常使用系统'
    END as recommendation
FROM (
    SELECT 
        SUM(CASE WHEN check_status = 'FAIL' AND severity = 'CRITICAL' THEN 1 ELSE 0 END) as critical_issues,
        SUM(CASE WHEN check_status IN ('FAIL', 'WARNING') AND severity = 'HIGH' THEN 1 ELSE 0 END) as high_issues,
        SUM(CASE WHEN check_status IN ('FAIL', 'WARNING') AND severity = 'MEDIUM' THEN 1 ELSE 0 END) as medium_issues,
        SUM(CASE WHEN check_status IN ('FAIL', 'WARNING') AND severity = 'LOW' THEN 1 ELSE 0 END) as low_issues
    FROM migration_verification_result 
    WHERE migration_id = @target_migration_id
) as issue_summary;

-- 显示详细的验证数据
SELECT 
    '详细验证数据' as section,
    check_category,
    check_name,
    check_status,
    expected_value,
    actual_value,
    severity,
    check_details
FROM migration_verification_result 
WHERE migration_id = @target_migration_id
ORDER BY 
    check_category,
    CASE check_status 
        WHEN 'FAIL' THEN 1 
        WHEN 'WARNING' THEN 2 
        WHEN 'PASS' THEN 3 
        ELSE 4 
    END,
    check_name;

-- 生成验证完成记录
INSERT INTO field_standardization_log (migration_id, phase, step_name, status, details) 
VALUES (@target_migration_id, 'VERIFICATION', '迁移结果验证', 'completed', 
        JSON_OBJECT(
            'verification_time', NOW(),
            'total_checks', (SELECT COUNT(*) FROM migration_verification_result WHERE migration_id = @target_migration_id),
            'passed_checks', (SELECT COUNT(*) FROM migration_verification_result WHERE migration_id = @target_migration_id AND check_status = 'PASS'),
            'failed_checks', (SELECT COUNT(*) FROM migration_verification_result WHERE migration_id = @target_migration_id AND check_status = 'FAIL'),
            'warning_checks', (SELECT COUNT(*) FROM migration_verification_result WHERE migration_id = @target_migration_id AND check_status = 'WARNING')
        ));

SELECT 
    '验证完成' as status,
    @target_migration_id as migration_id,
    (SELECT COUNT(*) FROM migration_verification_result WHERE migration_id = @target_migration_id) as total_checks_performed,
    NOW() as verification_end_time;