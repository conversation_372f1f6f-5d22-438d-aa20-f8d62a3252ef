package com.wanyu.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 物品条码对象 wms_barcode
 *
 * <AUTHOR>
 */
public class WmsBarcode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 条码ID */
    private Long barcodeId;

    /** 物品ID */
    @Excel(name = "物品ID")
    private Long productId;

    /** 物品名称 */
    @Excel(name = "物品名称")
    private String productName;

    /** 条码内容 */
    @Excel(name = "条码内容")
    private String barcodeContent;

    /** 条码类型 */
    @Excel(name = "条码类型", dictType = "wms_barcode_type")
    private String barcodeType;

    /** 条码图片路径 */
    @Excel(name = "条码图片路径")
    private String barcodeImage;

    /** 条码模板ID */
    @Excel(name = "条码模板ID")
    private Long templateId;

    /** 是否主条码 */
    @Excel(name = "是否主条码", readConverterExp = "0=否,1=是")
    private String isMain;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    // 批量生成相关字段（不映射到数据库）
    /** 前缀 */
    private String prefix;

    /** 起始数字 */
    private Integer startNum;

    /** 数字位数 */
    private Integer numDigits;

    /** 生成数量 */
    private Integer count;

    public void setBarcodeId(Long barcodeId)
    {
        this.barcodeId = barcodeId;
    }

    public Long getBarcodeId()
    {
        return barcodeId;
    }

    public void setProductId(Long productId)
    {
        this.productId = productId;
    }

    public Long getProductId()
    {
        return productId;
    }

    public void setProductName(String productName)
    {
        this.productName = productName;
    }

    public String getProductName()
    {
        return productName;
    }

    public void setBarcodeContent(String barcodeContent)
    {
        this.barcodeContent = barcodeContent;
    }

    public String getBarcodeContent()
    {
        return barcodeContent;
    }

    public void setBarcodeType(String barcodeType)
    {
        this.barcodeType = barcodeType;
    }

    public String getBarcodeType()
    {
        return barcodeType;
    }

    public void setBarcodeImage(String barcodeImage)
    {
        this.barcodeImage = barcodeImage;
    }

    public String getBarcodeImage()
    {
        return barcodeImage;
    }

    public void setTemplateId(Long templateId)
    {
        this.templateId = templateId;
    }

    public Long getTemplateId()
    {
        return templateId;
    }

    public void setIsMain(String isMain)
    {
        this.isMain = isMain;
    }

    public String getIsMain()
    {
        return isMain;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setPrefix(String prefix)
    {
        this.prefix = prefix;
    }

    public String getPrefix()
    {
        return prefix;
    }

    public void setStartNum(Integer startNum)
    {
        this.startNum = startNum;
    }

    public Integer getStartNum()
    {
        return startNum;
    }

    public void setNumDigits(Integer numDigits)
    {
        this.numDigits = numDigits;
    }

    public Integer getNumDigits()
    {
        return numDigits;
    }

    public void setCount(Integer count)
    {
        this.count = count;
    }

    public Integer getCount()
    {
        return count;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("barcodeId", getBarcodeId())
            .append("productId", getProductId())
            .append("productName", getProductName())
            .append("barcodeContent", getBarcodeContent())
            .append("barcodeType", getBarcodeType())
            .append("barcodeImage", getBarcodeImage())
            .append("templateId", getTemplateId())
            .append("isMain", getIsMain())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}