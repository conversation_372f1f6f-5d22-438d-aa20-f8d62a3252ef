@echo off
chcp 65001 >nul
echo ========================================
echo 完整删除岗位相关组件
echo ========================================

echo.
echo 1. 删除前端岗位相关页面...
if exist "warehouse-system\frontend\src\views\system\post" (
    rmdir /s /q "warehouse-system\frontend\src\views\system\post"
    echo    已删除: warehouse-system\frontend\src\views\system\post
)

echo.
echo 2. 删除后端岗位相关Java文件...
if exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\system\SysPostController.java" (
    del "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\system\SysPostController.java"
    echo    已删除: SysPostController.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\ISysPostService.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\ISysPostService.java"
    echo    已删除: ISysPostService.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\impl\SysPostServiceImpl.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\impl\SysPostServiceImpl.java"
    echo    已删除: SysPostServiceImpl.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\mapper\SysPostMapper.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\mapper\SysPostMapper.java"
    echo    已删除: SysPostMapper.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\domain\SysPost.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\domain\SysPost.java"
    echo    已删除: SysPost.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\resources\mapper\system\SysPostMapper.xml" (
    del "warehouse-system\backend\wanyu-system\src\main\resources\mapper\system\SysPostMapper.xml"
    echo    已删除: SysPostMapper.xml
)

echo.
echo 3. 删除前端API文件...
if exist "warehouse-system\frontend\src\api\system\post.js" (
    del "warehouse-system\frontend\src\api\system\post.js"
    echo    已删除: post.js API文件
)

echo.
echo 4. 执行数据库清理...
mysql -h localhost -P 3306 -u root -p123456 warehouse_system < complete_remove_post_components.sql
if %errorlevel% equ 0 (
    echo    数据库清理完成
) else (
    echo    数据库清理失败，请检查连接
)

echo.
echo ========================================
echo 岗位组件删除完成！
echo ========================================
echo.
echo 接下来需要手动执行以下步骤：
echo 1. 检查并更新用户管理相关代码
echo 2. 重新编译后端项目
echo 3. 重新启动前端项目  
echo 4. 检查系统功能是否正常
echo.
pause