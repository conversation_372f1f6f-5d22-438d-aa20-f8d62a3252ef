package com.wanyu.web.controller.product;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.system.domain.ProductSpecification;
import com.wanyu.system.service.IProductSpecificationService;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;

/**
 * 物品规格Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@RestController
@RequestMapping("/product/spec")
public class ProductSpecController extends BaseController
{
    @Autowired
    private IProductSpecificationService productSpecificationService;

    /**
     * 查询物品规格列表
     */
    @PreAuthorize("@ss.hasPermi('product:spec:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductSpecification productSpecification)
    {
        startPage();
        List<ProductSpecification> list = productSpecificationService.selectProductSpecificationList(productSpecification);
        return getDataTable(list);
    }

    /**
     * 导出物品规格列表
     */
    @PreAuthorize("@ss.hasPermi('product:spec:export')")
    @Log(title = "物品规格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductSpecification productSpecification)
    {
        List<ProductSpecification> list = productSpecificationService.selectProductSpecificationList(productSpecification);
        ExcelUtil<ProductSpecification> util = new ExcelUtil<ProductSpecification>(ProductSpecification.class);
        util.exportExcel(response, list, "物品规格数据");
    }

    /**
     * 获取物品规格详细信息
     */
    @PreAuthorize("@ss.hasPermi('product:spec:query')")
    @GetMapping(value = "/{specId}")
    public AjaxResult getInfo(@PathVariable("specId") Long specId)
    {
        return success(productSpecificationService.selectProductSpecificationBySpecId(specId));
    }

    /**
     * 新增物品规格
     */
    @PreAuthorize("@ss.hasPermi('product:spec:add')")
    @Log(title = "物品规格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProductSpecification productSpecification)
    {
        return toAjax(productSpecificationService.insertProductSpecification(productSpecification));
    }

    /**
     * 修改物品规格
     */
    @PreAuthorize("@ss.hasPermi('product:spec:edit')")
    @Log(title = "物品规格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductSpecification productSpecification)
    {
        return toAjax(productSpecificationService.updateProductSpecification(productSpecification));
    }

    /**
     * 删除物品规格
     */
    @PreAuthorize("@ss.hasPermi('product:spec:remove')")
    @Log(title = "物品规格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{specIds}")
    public AjaxResult remove(@PathVariable Long[] specIds)
    {
        return toAjax(productSpecificationService.deleteProductSpecificationBySpecIds(specIds));
    }
}