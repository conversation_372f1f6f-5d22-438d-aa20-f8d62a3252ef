<template>
  <div class="app-container">
    <!-- 移动端结构 -->
    <div v-if="$store.getters.device === 'mobile'" class="mobile-inventory-container">
      <el-collapse v-model="mobileSearchVisible" class="mobile-search">
        <el-collapse-item title="搜索条件" name="search">
          <el-form :model="queryParams" ref="queryForm" size="small" label-width="80px">
            <el-form-item label="调拨单号" prop="transferCode">
              <el-input v-model="queryParams.transferCode" placeholder="请输入调拨单号" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="调出仓库" prop="fromWarehouseId">
              <el-select v-model="queryParams.fromWarehouseId" placeholder="请选择调出仓库" clearable style="width: 100%">
                <el-option v-for="item in warehouseOptions" :key="item.warehouseId" :label="item.warehouseName" :value="item.warehouseId" />
              </el-select>
            </el-form-item>
            <el-form-item label="调入仓库" prop="toWarehouseId">
              <el-select v-model="queryParams.toWarehouseId" placeholder="请选择调入仓库" clearable style="width: 100%">
                <el-option v-for="item in warehouseOptions" :key="item.warehouseId" :label="item.warehouseName" :value="item.warehouseId" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option v-for="dict in dict.type.inventory_transfer_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="调拨时间">
              <el-date-picker v-model="dateRange" style="width: 100%" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <!-- 移动端操作按钮区 -->
      <div class="mobile-actions">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['inventory:transfer:add']">新增</el-button>
        <el-button type="success" icon="el-icon-edit" size="small" :disabled="single" @click="handleUpdate" v-hasPermi="['inventory:transfer:edit']">修改</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple" @click="handleDelete" v-hasPermi="['inventory:transfer:remove']">删除</el-button>
        <el-dropdown size="small" style="margin-left: 10px;">
          <el-button size="small">更多<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="export" icon="el-icon-download" v-hasPermi="['inventory:transfer:export']">导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <!-- 移动端调拨卡片列表 -->
      <div v-loading="loading" class="mobile-transfer-list">
        <div v-for="item in transferList" :key="item.transferId" class="transfer-card">
          <div class="transfer-card-header">
            <div class="transfer-title">{{ item.transferCode }}</div>
            <el-tag size="mini" :type="item.status === '0' ? 'success' : 'info'" class="transfer-status">{{ getTransferStatusLabel(item.status) }}</el-tag>
          </div>
          <div class="transfer-card-body">
            <div class="transfer-detail"><span class="label">调出仓库:</span><span class="value">{{ item.fromWarehouseName }}</span></div>
            <div class="transfer-detail"><span class="label">调入仓库:</span><span class="value">{{ item.toWarehouseName }}</span></div>
            <div class="transfer-detail"><span class="label">调拨时间:</span><span class="value">{{ item.transferTime }}</span></div>
          </div>
          <div class="transfer-card-actions" @click.stop>
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(item)" v-hasPermi="['inventory:transfer:query']">查看</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(item)" v-hasPermi="['inventory:transfer:edit']" v-if="item.status === '0'">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(item)" v-hasPermi="['inventory:transfer:remove']" v-if="item.status === '0'">删除</el-button>
            <el-button size="mini" type="text" icon="el-icon-check" @click="handleAudit(item)" v-hasPermi="['inventory:transfer:audit']" v-if="item.status === '0'">审核</el-button>
            <el-button size="mini" type="text" icon="el-icon-printer" @click="handlePrint(item)" v-hasPermi="['inventory:transfer:print']">打印</el-button>
          </div>
        </div>
      </div>
      <!-- 移动端分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      <!-- 移动端弹窗（新增/编辑） -->
      <el-dialog
        v-if="$store.getters.device === 'mobile'"
        :title="title"
        :visible.sync="open"
        width="100vw"
        top="0"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="调出仓库" prop="fromWarehouseId">
                <el-select v-model="form.fromWarehouseId" placeholder="请选择调出仓库">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="调入仓库" prop="toWarehouseId">
                <el-select v-model="form.toWarehouseId" placeholder="请选择调入仓库">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="center">调拨明细</el-divider>
          <el-row>
            <el-col :span="24">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddDetail">添加明细</el-button>
              <el-table :data="form.details" style="margin-top: 10px;">
                <el-table-column label="序号" type="index" width="55" align="center" />
                <el-table-column label="物品" prop="productName" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" placeholder="请选择物品" @change="(val) => handleProductChange(val, scope.$index)">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.productId"
                        :label="item.productName"
                        :value="item.productId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" width="150">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.quantity" :min="1" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteDetail(scope.$index)"
                      v-hasPermi="['inventory:transfer:edit']"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer" style="display:flex;gap:4%;justify-content:space-between">
          <el-button type="primary" @click="submitForm" style="width:48%">确 定</el-button>
          <el-button @click="cancel" style="width:48%">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看调拨单对话框 -->
      <el-dialog title="查看调拨单" :visible.sync="viewOpen" width="780px" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="调拨单号">{{ viewForm.transferCode }}</el-descriptions-item>
          <el-descriptions-item label="调出仓库">{{ viewForm.fromWarehouseName }}</el-descriptions-item>
          <el-descriptions-item label="调入仓库">{{ viewForm.toWarehouseName }}</el-descriptions-item>
          <el-descriptions-item label="调拨时间">{{ parseTime(viewForm.transferTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.inventory_transfer_status" :value="viewForm.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ viewForm.createBy }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="center">调拨明细</el-divider>
        <el-table :data="viewForm.details" style="margin-top: 10px;">
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物品名称" prop="productName" min-width="180" />
          <el-table-column label="物品编码" prop="productCode" width="120" />
          <el-table-column label="数量" prop="quantity" width="100" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 审核调拨单对话框 -->
      <el-dialog title="审核调拨单" :visible.sync="auditOpen" width="500px" append-to-body>
        <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
          <el-form-item label="调拨单号">{{ auditForm.transferCode }}</el-form-item>
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="auditForm.status">
              <el-radio label="1">通过</el-radio>
              <el-radio label="2">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见" prop="remark">
            <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入审核意见" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAudit">确 定</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>

    <!-- 桌面端结构 -->
    <div v-else class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="调拨单号" prop="transferCode">
          <el-input
            v-model="queryParams.transferCode"
            placeholder="请输入调拨单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="调出仓库" prop="fromWarehouseId">
          <el-select v-model="queryParams.fromWarehouseId" placeholder="请选择调出仓库" clearable>
            <el-option
              v-for="item in warehouseOptions"
              :key="item.warehouseId"
              :label="item.warehouseName"
              :value="item.warehouseId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="调入仓库" prop="toWarehouseId">
          <el-select v-model="queryParams.toWarehouseId" placeholder="请选择调入仓库" clearable>
            <el-option
              v-for="item in warehouseOptions"
              :key="item.warehouseId"
              :label="item.warehouseName"
              :value="item.warehouseId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in dict.type.inventory_transfer_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="调拨时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['inventory:transfer:add']"
          >新增</el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inventory:transfer:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport('excel')"
            v-hasPermi="['inventory:transfer:export']"
          >导出Excel</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-sort"
            size="mini"
            @click="handleBatchTransfer"
            v-hasPermi="['inventory:transfer:add']"
          >批量调拨</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="transferList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="调拨ID" align="center" prop="transferId" v-if="false" />
        <el-table-column label="调拨单号" align="center" prop="transferCode" />
        <el-table-column label="调出仓库" align="center" prop="fromWarehouseName" :show-overflow-tooltip="true" />
        <el-table-column label="调入仓库" align="center" prop="toWarehouseName" :show-overflow-tooltip="true" />
        <el-table-column label="调拨时间" align="center" prop="transferTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.transferTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.inventory_transfer_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="审核人" align="center" prop="auditBy" />
        <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.auditTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['inventory:transfer:query']"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['inventory:transfer:edit']"
              v-if="scope.row.status === '0'"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['inventory:transfer:remove']"
              v-if="scope.row.status === '0'"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleAudit(scope.row)"
              v-hasPermi="['inventory:transfer:audit']"
              v-if="scope.row.status === '0'"
            >审核</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-printer"
              @click="handlePrint(scope.row)"
              v-hasPermi="['inventory:transfer:print']"
            >打印</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改调拨单对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="调出仓库" prop="fromWarehouseId">
                <el-select v-model="form.fromWarehouseId" placeholder="请选择调出仓库">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="调入仓库" prop="toWarehouseId">
                <el-select v-model="form.toWarehouseId" placeholder="请选择调入仓库">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="center">调拨明细</el-divider>
          <el-row>
            <el-col :span="24">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddDetail">添加明细</el-button>
              <el-table :data="form.details" style="margin-top: 10px;">
                <el-table-column label="序号" type="index" width="55" align="center" />
                <el-table-column label="物品" prop="productName" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" placeholder="请选择物品" @change="(val) => handleProductChange(val, scope.$index)">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.productId"
                        :label="item.productName"
                        :value="item.productId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" width="150">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.quantity" :min="1" :precision="2" :step="1" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteDetail(scope.$index)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看调拨单对话框 -->
      <el-dialog title="查看调拨单" :visible.sync="viewOpen" width="780px" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="调拨单号">{{ viewForm.transferCode }}</el-descriptions-item>
          <el-descriptions-item label="调出仓库">{{ viewForm.fromWarehouseName }}</el-descriptions-item>
          <el-descriptions-item label="调入仓库">{{ viewForm.toWarehouseName }}</el-descriptions-item>
          <el-descriptions-item label="调拨时间">{{ parseTime(viewForm.transferTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.inventory_transfer_status" :value="viewForm.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ viewForm.createBy }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="center">调拨明细</el-divider>
        <el-table :data="viewForm.details" style="margin-top: 10px;">
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物品名称" prop="productName" min-width="180" />
          <el-table-column label="物品编码" prop="productCode" width="120" />
          <el-table-column label="数量" prop="quantity" width="100" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 审核调拨单对话框 -->
      <el-dialog title="审核调拨单" :visible.sync="auditOpen" width="500px" append-to-body>
        <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
          <el-form-item label="调拨单号">{{ auditForm.transferCode }}</el-form-item>
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="auditForm.status">
              <el-radio label="1">通过</el-radio>
              <el-radio label="2">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见" prop="remark">
            <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入审核意见" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAudit">确 定</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 导出字段选择弹窗 -->
      <el-dialog title="选择导出字段" :visible.sync="exportFieldDialogVisible" width="400px">
        <el-checkbox-group v-model="selectedExportFields">
          <el-checkbox v-for="col in exportFieldOptions" :key="col.value" :label="col.value">{{ col.label }}</el-checkbox>
        </el-checkbox-group>
        <div slot="footer" class="dialog-footer">
          <el-button @click="exportFieldDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDoExport">导出</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listInventoryTransfer, getInventoryTransfer, delInventoryTransfer, addInventoryTransfer, updateInventoryTransfer, auditInventoryTransfer } from "@/api/inventory/transfer";
import { optionselect } from "@/api/system/warehouse";
import { listProduct } from "@/api/product/info";
import { getToken } from "@/utils/auth";
import axios from "axios";

export default {
  name: "InventoryTransfer",
  dicts: ['inventory_transfer_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调拨表格数据
      transferList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 日期范围
      dateRange: [],
      // 仓库选项
      warehouseOptions: [],
      // 物品选项
      productOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        transferCode: null,
        fromWarehouseId: null,
        toWarehouseId: null,
        status: null
      },
      // 表单参数
      form: {
        details: []
      },
      // 表单校验
      rules: {
        fromWarehouseId: [
          { required: true, message: "调出仓库不能为空", trigger: "change" }
        ],
        toWarehouseId: [
          { required: true, message: "调入仓库不能为空", trigger: "change" }
        ]
      },
      // 查看表单参数
      viewForm: {
        details: []
      },
      // 审核表单参数
      auditForm: {},
      // 审核表单校验
      auditRules: {
        status: [
          { required: true, message: "审核结果不能为空", trigger: "change" }
        ]
      },
      // 移动端搜索折叠面板
      mobileSearchVisible: ['search'],
      // 导出字段选择
      exportFieldDialogVisible: false,
      selectedExportFields: [],
      exportFieldOptions: [
        { label: '调拨单号', value: 'transferCode' },
        { label: '调出仓库', value: 'fromWarehouseName' },
        { label: '调入仓库', value: 'toWarehouseName' },
        { label: '物品名称', value: 'productName' },
        { label: '物品编码', value: 'productCode' },
        { label: '数量', value: 'quantity' },
        { label: '调拨时间', value: 'transferTime' },
        { label: '操作人员', value: 'operateBy' },
        { label: '状态', value: 'status' },
        { label: '审核人', value: 'auditBy' },
        { label: '审核时间', value: 'auditTime' }
      ]
    };
  },
  created() {
    this.getList();
    this.getWarehouseOptions();
    this.getProductOptions();
  },
  methods: {
    /** 查询调拨列表 */
    getList() {
      this.loading = true;
      listInventoryTransfer(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.transferList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取仓库选项 */
    getWarehouseOptions() {
      optionselect().then(response => {
        this.warehouseOptions = response.data || response.rows;
      });
    },
    /** 获取物品选项 */
    getProductOptions() {
      listProduct().then(response => {
        this.productOptions = response.rows;
      });
    },
    // 物品选择事件
    handleProductChange(value, index) {
      const product = this.productOptions.find(item => item.productId === value);
      if (product) {
        this.form.details[index].productName = product.productName;
        this.form.details[index].productCode = product.productCode;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        transferId: null,
        transferCode: null,
        fromWarehouseId: null,
        toWarehouseId: null,
        transferTime: null,
        status: "0",
        remark: null,
        details: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.transferId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加调拨单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const transferId = row.transferId || this.ids[0];
      const selectedRow = row || (this.selectedRows && this.selectedRows[0]);
      
      if (!selectedRow) {
        this.$modal.msgError("请选择要修改的调拨单");
        return;
      }

      console.log('Selected row status:', selectedRow.status);
      
      // 只有未审核状态(status === '0')才允许修改
      if (selectedRow.status && selectedRow.status.toString() === '0') {
        this.reset();
        getInventoryTransfer(transferId).then(response => {
          if (response.data.status.toString() === '0') {
            this.form = response.data;
            this.open = true;
            this.title = "修改调拨单";
          } else {
            this.$modal.msgError(`调拨单 ${response.data.transferCode} 状态已变更，不能修改`);
          }
        }).catch(error => {
          this.$modal.msgError("获取调拨单详情失败");
          console.error(error);
        });
      } else {
        this.$modal.msgError(`调拨单 ${selectedRow.transferCode} 已审核，不能修改`);
      }
    },
    /** 查看按钮操作 */
    handleView(row) {
      getInventoryTransfer(row.transferId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm = {
        transferId: row.transferId,
        transferCode: row.transferCode,
        status: "1",
        remark: ""
      };
      this.auditOpen = true;
    },
    /** 打印按钮操作 */
    handlePrint(row) {
      this.$router.push({ path: `/inventory/transfer/print/${row.transferId}` });
    },
    /** 批量调拨按钮操作 */
    handleBatchTransfer() {
      this.$router.push({ path: "/inventory/transfer/batch/index" });
    },
    /** 添加明细 */
    handleAddDetail() {
      this.form.details.push({
        productId: null,
        productName: null,
        productCode: null,
        quantity: 1
      });
    },
    /** 删除明细 */
    handleDeleteDetail(index) {
      this.form.details.splice(index, 1);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查明细是否为空
          if (this.form.details.length === 0) {
            this.$modal.msgError("请添加调拨明细");
            return;
          }

          // 检查明细是否填写完整
          for (let i = 0; i < this.form.details.length; i++) {
            const detail = this.form.details[i];
            if (!detail.productId) {
              this.$modal.msgError("请选择物品");
              return;
            }
            if (!detail.quantity || detail.quantity <= 0) {
              this.$modal.msgError("请输入正确的数量");
              return;
            }
          }

          // 检查调出仓库和调入仓库是否相同
          if (this.form.fromWarehouseId === this.form.toWarehouseId) {
            this.$modal.msgError("调出仓库和调入仓库不能相同");
            return;
          }

          if (this.form.transferId != null) {
            updateInventoryTransfer(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventoryTransfer(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交审核 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditInventoryTransfer(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const transferIds = row.transferId || this.ids;
      this.$modal.confirm('是否确认删除调拨单编号为"' + transferIds + '"的数据项？').then(() => {
        return delInventoryTransfer(transferIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport(command) {
      this.exportType = command;
      this.exportFieldDialogVisible = true;
      // 默认全选
      if (this.selectedExportFields.length === 0) {
        this.selectedExportFields = this.exportFieldOptions.map(opt => opt.value);
      }
    },
    handleDoExport() {
      if (!this.selectedExportFields.length) {
        this.$modal.msgError('请至少选择一个导出字段');
        return;
      }
      const params = { ...this.queryParams, columns: this.selectedExportFields.join(',') };
      let url = '';
      let fileName = '';
      if (this.exportType === 'excel') {
        url = '/api/v1/reports/export/transfer/excel';
        fileName = `调拨明细报表_${new Date().getTime()}.xlsx`;
      } else if (this.exportType === 'pdf') {
        url = '/api/v1/reports/export/transfer/pdf';
        fileName = `调拨明细报表_${new Date().getTime()}.pdf`;
      }
      this.exportReport(url, params, fileName);
      this.exportFieldDialogVisible = false;
    },
    /**
     * 导出报表
     * @param {string} url - 导出URL
     * @param {object} params - 查询参数
     * @param {string} fileName - 文件名
     */
    exportReport(url, params, fileName) {
      this.$modal.loading("正在导出数据，请稍候...");
      const baseUrl = process.env.VUE_APP_BASE_API;
      const fullUrl = baseUrl + url;
      axios({
        method: 'get',
        url: fullUrl,
        params: params,
        responseType: 'blob',
        headers: { 'Authorization': 'Bearer ' + getToken() }
      }).then(response => {
        const blob = new Blob([response.data]);
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$modal.closeLoading();
        this.$modal.msgSuccess("导出成功");
      }).catch(() => {
        this.$modal.closeLoading();
        this.$modal.msgError("导出失败，请重试");
      });
    },
    getTransferStatusLabel: function(status) {
      var arr = this.dict.type.inventory_transfer_status;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].value == status) return arr[i].label;
      }
      return status;
    }
  }
};
</script>

<style scoped>
.mobile-inventory-container {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.mobile-search {
  margin-bottom: 10px;
}

.mobile-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.mobile-transfer-list {
  margin-top: 12px;
}
.transfer-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  margin-bottom: 12px;
  padding: 12px;
}
.transfer-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}
.transfer-title {
  font-weight: bold;
  font-size: 16px;
  flex: 1;
}
.transfer-status {
  margin-left: 8px;
}
.transfer-card-body {
  margin-bottom: 8px;
}
.transfer-detail {
  font-size: 13px;
  margin-bottom: 2px;
}
.transfer-detail .label {
  color: #888;
  margin-right: 4px;
}
.transfer-detail .value {
  color: #333;
}
.transfer-card-actions {
  display: flex;
  gap: 8px;
}
</style>