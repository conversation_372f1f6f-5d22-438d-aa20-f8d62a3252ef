package com.wanyu.web.controller.product;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.system.domain.ProductCategory;
import com.wanyu.system.service.IProductCategoryService;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 物品分类Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@RestController
@RequestMapping("/product/category")
public class ProductCategoryController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(ProductCategoryController.class);
    
    @Autowired
    private IProductCategoryService productCategoryService;

    /**
     * 查询物品分类列表
     */
    @PreAuthorize("@ss.hasPermi('product:category:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductCategory productCategory)
    {
        startPage();
        List<ProductCategory> list = productCategoryService.selectProductCategoryList(productCategory);
        TableDataInfo dataTable = getDataTable(list);
        logger.info("物品分类列表查询结果: {}", dataTable);
        return dataTable;
    }

    /**
     * 导出物品分类列表
     */
    @PreAuthorize("@ss.hasPermi('product:category:export')")
    @Log(title = "物品分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductCategory productCategory)
    {
        List<ProductCategory> list = productCategoryService.selectProductCategoryList(productCategory);
        ExcelUtil<ProductCategory> util = new ExcelUtil<ProductCategory>(ProductCategory.class);
        util.exportExcel(response, list, "物品分类数据");
    }

    /**
     * 获取物品分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('product:category:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        return success(productCategoryService.selectProductCategoryByCategoryId(categoryId));
    }

    /**
     * 新增物品分类
     */
    @PreAuthorize("@ss.hasPermi('product:category:add')")
    @Log(title = "物品分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProductCategory productCategory)
    {
        return toAjax(productCategoryService.insertProductCategory(productCategory));
    }

    /**
     * 修改物品分类
     */
    @PreAuthorize("@ss.hasPermi('product:category:edit')")
    @Log(title = "物品分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductCategory productCategory)
    {
        return toAjax(productCategoryService.updateProductCategory(productCategory));
    }

    /**
     * 删除物品分类
     */
    @PreAuthorize("@ss.hasPermi('product:category:remove')")
    @Log(title = "物品分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds)
    {
        return toAjax(productCategoryService.deleteProductCategoryByCategoryIds(categoryIds));
    }
    
    /**
     * 查询物品分类下拉树结构
     */
    @PreAuthorize("@ss.hasPermi('product:category:list')")
    @GetMapping("/treeselect")
    public AjaxResult treeselect()
    {
        List<ProductCategory> list = productCategoryService.selectProductCategoryTreeList();
        List<ProductCategory> treeList = productCategoryService.buildCategoryTreeSelect(list);
        return success(treeList);
    }
}