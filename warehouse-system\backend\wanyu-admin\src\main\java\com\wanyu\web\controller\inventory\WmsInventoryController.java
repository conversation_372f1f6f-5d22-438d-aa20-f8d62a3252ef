package com.wanyu.web.controller.inventory;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.ApiPermission;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.system.domain.WmsInventory;
import com.wanyu.system.service.IWmsInventoryService;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;
import java.util.HashMap;
import org.springframework.web.multipart.MultipartFile;
import com.wanyu.system.service.IWmsInventoryLogService;
import com.wanyu.system.domain.WmsInventoryLog;

/**
 * 库存信息Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/inventory/stock")
public class WmsInventoryController extends BaseController
{
    @Autowired
    private IWmsInventoryService wmsInventoryService;

    @Autowired
    private IWmsInventoryLogService wmsInventoryLogService;

    /**
     * 查询库存信息列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:list')")
    @ApiPermission("inventory:stock:list")
    @GetMapping("/list")
    public TableDataInfo list(WmsInventory wmsInventory)
    {
        startPage();
        List<WmsInventory> list = wmsInventoryService.selectWmsInventoryList(wmsInventory);
        return getDataTable(list);
    }

    /**
     * 查询库存预警信息列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:list')")
    @ApiPermission("inventory:stock:list")
    @GetMapping("/alert")
    public TableDataInfo alert(WmsInventory wmsInventory)
    {
        startPage();
        List<WmsInventory> list = wmsInventoryService.selectWmsInventoryAlertList(wmsInventory);
        return getDataTable(list);
    }

    /**
     * 导出库存信息列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:export')")
    @ApiPermission("inventory:stock:export")
    @Log(title = "库存信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, WmsInventory wmsInventory)
    {
        List<WmsInventory> list = wmsInventoryService.selectWmsInventoryList(wmsInventory);
        ExcelUtil<WmsInventory> util = new ExcelUtil<WmsInventory>(WmsInventory.class);
        util.exportExcel(response, list, "库存信息数据");
    }

    /**
     * 获取库存信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:query')")
    @ApiPermission("inventory:stock:query")
    @GetMapping(value = "/{inventoryId}")
    public AjaxResult getInfo(@PathVariable("inventoryId") Long inventoryId)
    {
        return success(wmsInventoryService.selectWmsInventoryByInventoryId(inventoryId));
    }

    /**
     * 新增库存信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:add')")
    @ApiPermission("inventory:stock:add")
    @Log(title = "库存信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmsInventory wmsInventory)
    {
        return toAjax(wmsInventoryService.insertWmsInventory(wmsInventory));
    }

    /**
     * 修改库存信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:edit')")
    @ApiPermission("inventory:stock:edit")
    @Log(title = "库存信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmsInventory wmsInventory)
    {
        return toAjax(wmsInventoryService.updateWmsInventory(wmsInventory));
    }

    /**
     * 删除库存信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:remove')")
    @ApiPermission("inventory:stock:remove")
    @Log(title = "库存信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{inventoryIds}")
    public AjaxResult remove(@PathVariable Long[] inventoryIds)
    {
        return toAjax(wmsInventoryService.deleteWmsInventoryByInventoryIds(inventoryIds));
    }

    /**
     * 获取库存统计数据
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:list')")
    @ApiPermission("inventory:stock:list")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        return success(wmsInventoryService.getInventoryStatistics());
    }

    /**
     * 查询物品库存列表（按物品分组）
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:list')")
    @ApiPermission("inventory:stock:list")
    @GetMapping("/product")
    public TableDataInfo listProductStock(WmsInventory wmsInventory)
    {
        startPage();
        List<com.wanyu.system.domain.vo.ProductStockVO> list = wmsInventoryService.selectProductStockList(wmsInventory);
        return getDataTable(list);
    }

    /**
     * 发送库存预警通知
     */
    @PreAuthorize("@ss.hasPermi('inventory:alert:send')")
    @ApiPermission("inventory:alert:send")
    @Log(title = "库存预警通知", businessType = BusinessType.OTHER)
    @PostMapping("/alert/notify/send/{inventoryId}")
    public AjaxResult sendAlert(@PathVariable("inventoryId") Long inventoryId) {
        // 获取库存信息
        WmsInventory stock = wmsInventoryService.selectWmsInventoryByInventoryId(inventoryId);
        if (stock == null) {
            return error("库存信息不存在");
        }

        // 判断库存状态
        if ("0".equals(stock.getStatus())) {
            return error("库存正常，无需发送预警");
        }

        // 模拟发送预警通知
        String alertType = "1".equals(stock.getStatus()) ? "库存不足" : "库存过多";
        return success("已成功发送" + alertType + "预警通知：" + stock.getProductName());
    }

    /**
     * 批量发送库存预警通知
     */
    @PreAuthorize("@ss.hasPermi('inventory:alert:send')")
    @ApiPermission("inventory:alert:send")
    @Log(title = "库存预警通知", businessType = BusinessType.OTHER)
    @PostMapping("/alert/notify/batch")
    public AjaxResult batchSendAlert() {
        // 获取所有预警库存
        WmsInventory query = new WmsInventory();
        int count = wmsInventoryService.selectWmsInventoryAlertList(query).size();

        // 模拟批量发送预警通知
        return success("已成功发送" + count + "条库存预警通知");
    }

    /**
     * 获取邮件配置状态
     */
    @PreAuthorize("@ss.hasPermi('inventory:alert:query')")
    @ApiPermission("inventory:alert:query")
    @GetMapping("/alert/notify/config/status")
    public AjaxResult getEmailConfigStatus() {
        // 临时返回模拟数据
        Map<String, Object> data = new HashMap<>();
        data.put("configured", true);
        data.put("server", "smtp.example.com");
        data.put("port", 25);
        data.put("username", "<EMAIL>");
        data.put("recipients", "<EMAIL>,<EMAIL>");

        return success(data);
    }

    /**
     * 批量导入库存信息（Excel）
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:import')")
    @Log(title = "库存信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        // 这里假设有ExcelUtil工具类和批量导入逻辑
        ExcelUtil<WmsInventory> util = new ExcelUtil<>(WmsInventory.class);
        List<WmsInventory> list = util.importExcel(file.getInputStream());
        int success = 0, fail = 0;
        StringBuilder msg = new StringBuilder();
        for (WmsInventory inv : list) {
            try {
                wmsInventoryService.insertWmsInventory(inv);
                success++;
            } catch (Exception e) {
                fail++;
                msg.append("[" + inv.getProductName() + "]导入失败: " + e.getMessage() + "\n");
            }
        }
        return AjaxResult.success("成功导入" + success + "条，失败" + fail + "条。" + msg.toString());
    }

    /**
     * 获取库存信息导入模板
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:import')")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<WmsInventory> util = new ExcelUtil<>(WmsInventory.class);
        util.importTemplateExcel(response, "库存信息数据");
    }

    /**
     * 查询指定库存的变动日志
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:log')")
    @GetMapping("/log/{inventoryId}")
    public AjaxResult getStockLog(@PathVariable Long inventoryId) {
        List<WmsInventoryLog> logs = wmsInventoryLogService.selectWmsInventoryLogListByInventoryId(inventoryId);
        return AjaxResult.success(logs);
    }

    /**
     * 查询所有低于预警线的库存
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:list')")
    @GetMapping("/warning")
    public AjaxResult getStockWarning() {
        List<WmsInventory> warningList = wmsInventoryService.selectWmsInventoryAlertList(new WmsInventory());
        return AjaxResult.success(warningList);
    }

    /**
     * 批量更新库存阈值
     */
    @PreAuthorize("@ss.hasPermi('inventory:stock:edit')")
    @ApiPermission("inventory:stock:edit")
    @Log(title = "批量更新库存阈值", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/threshold")
    public AjaxResult batchUpdateThreshold(@RequestBody Map<String, Object> requestData)
    {
        try {
            // 解析请求数据 - 修复类型转换问题
            @SuppressWarnings("unchecked")
            List<Object> inventoryIdsRaw = (List<Object>) requestData.get("inventoryIds");
            List<Long> inventoryIds = new java.util.ArrayList<>();
            
            // 安全地转换ID列表，处理Integer到Long的转换
            if (inventoryIdsRaw != null) {
                for (Object idObj : inventoryIdsRaw) {
                    if (idObj instanceof Integer) {
                        inventoryIds.add(((Integer) idObj).longValue());
                    } else if (idObj instanceof Long) {
                        inventoryIds.add((Long) idObj);
                    } else if (idObj instanceof String) {
                        inventoryIds.add(Long.parseLong((String) idObj));
                    }
                }
            }
            
            Object minQuantityObj = requestData.get("minQuantity");
            Object maxQuantityObj = requestData.get("maxQuantity");
            String remark = (String) requestData.get("remark");
            
            if (inventoryIds == null || inventoryIds.isEmpty()) {
                return error("请选择要更新的库存记录");
            }
            
            int updateCount = 0;
            for (Long inventoryId : inventoryIds) {
                // 只更新阈值相关字段
                WmsInventory updateInventory = new WmsInventory();
                updateInventory.setInventoryId(inventoryId);
                
                // 处理最小库存量
                if (minQuantityObj != null) {
                    BigDecimal minQuantity = null;
                    if (minQuantityObj instanceof String) {
                        minQuantity = new BigDecimal((String) minQuantityObj);
                    } else if (minQuantityObj instanceof Number) {
                        minQuantity = new BigDecimal(minQuantityObj.toString());
                    }
                    updateInventory.setMinQuantity(minQuantity);
                }
                
                // 处理最大库存量
                if (maxQuantityObj != null) {
                    BigDecimal maxQuantity = null;
                    if (maxQuantityObj instanceof String) {
                        maxQuantity = new BigDecimal((String) maxQuantityObj);
                    } else if (maxQuantityObj instanceof Number) {
                        maxQuantity = new BigDecimal(maxQuantityObj.toString());
                    }
                    updateInventory.setMaxQuantity(maxQuantity);
                }
                
                updateInventory.setRemark(remark);
                
                int result = wmsInventoryService.updateWmsInventory(updateInventory);
                if (result > 0) {
                    updateCount++;
                }
            }
            return success("成功更新 " + updateCount + " 条库存阈值");
        } catch (Exception e) {
            logger.error("批量更新库存阈值失败", e);
            return error("批量更新库存阈值失败：" + e.getMessage());
        }
    }
}
