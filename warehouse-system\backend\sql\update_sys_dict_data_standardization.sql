-- ================================================================
-- Field Definition Standardization - System Dictionary Data Update Script
-- Function: Fix status dictionary data in sys_dict_data table
-- Standard: 0=normal/enabled/success, 1=abnormal/disabled/failed
-- Created: 2025-01-30
-- ================================================================

-- 开始事务
START TRANSACTION;

-- 1. Backup current dictionary data
CREATE TABLE IF NOT EXISTS sys_dict_data_backup_standardization AS 
SELECT * FROM sys_dict_data WHERE dict_type IN (
    'sys_normal_disable', 'operation_status', 'sys_common_status',
    'inventory_status', 'inventory_in_status', 'inventory_out_status',
    'inventory_transfer_status', 'inventory_check_status', 'request_status'
);

-- 2. Update sys_normal_disable dictionary (common status dictionary)
-- Ensure 0=normal, 1=disabled
UPDATE sys_dict_data SET 
    dict_label = '正常',
    dict_value = '0',
    css_class = '',
    list_class = 'primary',
    is_default = 'Y',
    remark = '正常状态'
WHERE dict_type = 'sys_normal_disable' AND dict_value = '0';

UPDATE sys_dict_data SET 
    dict_label = '停用',
    dict_value = '1',
    css_class = '',
    list_class = 'danger',
    is_default = 'N',
    remark = '停用状态'
WHERE dict_type = 'sys_normal_disable' AND dict_value = '1';

-- 3. 创建或更新操作状态字典
-- 首先检查operation_status字典类型是否存在
INSERT IGNORE INTO sys_dict_type (
    dict_name, dict_type, status, create_by, create_time, remark
) VALUES (
    '操作状态', 'operation_status', '0', 'admin', NOW(), '操作执行状态字典'
);

-- 删除可能存在的旧操作状态字典数据
DELETE FROM sys_dict_data WHERE dict_type = 'operation_status';

-- 插入标准的操作状态字典数据
INSERT INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '成功', '0', 'operation_status', '', 'success', 'Y', '0', 'admin', NOW(), '操作执行成功'),
(2, '失败', '1', 'operation_status', '', 'danger', 'N', '0', 'admin', NOW(), '操作执行失败');

-- 4. 更新库存相关状态字典，确保符合标准
-- 库存状态字典
UPDATE sys_dict_data SET 
    dict_label = '正常',
    dict_value = '0',
    list_class = 'success',
    remark = '库存正常'
WHERE dict_type = 'inventory_status' AND dict_value = '0';

-- 如果存在库存状态为1的记录，更新为停用状态
UPDATE sys_dict_data SET 
    dict_label = '停用',
    dict_value = '1',
    list_class = 'danger',
    remark = '库存停用'
WHERE dict_type = 'inventory_status' AND dict_value = '1';

-- 5. 更新入库状态字典
-- 确保入库状态遵循标准：0=正常，1=异常
INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '待入库', '0', 'inventory_in_status', '', 'info', 'Y', '0', 'admin', NOW(), '待入库状态'),
(2, '已入库', '1', 'inventory_in_status', '', 'success', 'N', '0', 'admin', NOW(), '已入库状态'),
(3, '入库异常', '2', 'inventory_in_status', '', 'danger', 'N', '0', 'admin', NOW(), '入库异常状态');

-- 6. 更新出库状态字典
INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '待出库', '0', 'inventory_out_status', '', 'info', 'Y', '0', 'admin', NOW(), '待出库状态'),
(2, '已出库', '1', 'inventory_out_status', '', 'success', 'N', '0', 'admin', NOW(), '已出库状态'),
(3, '出库异常', '2', 'inventory_out_status', '', 'danger', 'N', '0', 'admin', NOW(), '出库异常状态');

-- 7. 更新调拨状态字典
INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '待调拨', '0', 'inventory_transfer_status', '', 'info', 'Y', '0', 'admin', NOW(), '待调拨状态'),
(2, '已调拨', '1', 'inventory_transfer_status', '', 'success', 'N', '0', 'admin', NOW(), '已调拨状态'),
(3, '调拨异常', '2', 'inventory_transfer_status', '', 'danger', 'N', '0', 'admin', NOW(), '调拨异常状态');

-- 8. 更新盘点状态字典
INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '待盘点', '0', 'inventory_check_status', '', 'info', 'Y', '0', 'admin', NOW(), '待盘点状态'),
(2, '已盘点', '1', 'inventory_check_status', '', 'success', 'N', '0', 'admin', NOW(), '已盘点状态'),
(3, '盘点异常', '2', 'inventory_check_status', '', 'danger', 'N', '0', 'admin', NOW(), '盘点异常状态');

-- 9. 更新申购单状态字典
INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '待审核', '0', 'request_status', '', 'info', 'Y', '0', 'admin', NOW(), '申购单待审核'),
(2, '已审核', '1', 'request_status', '', 'success', 'N', '0', 'admin', NOW(), '申购单已审核'),
(3, '已拒绝', '2', 'request_status', '', 'danger', 'N', '0', 'admin', NOW(), '申购单已拒绝');

-- 10. 创建审核状态字典（如果不存在）
INSERT IGNORE INTO sys_dict_type (
    dict_name, dict_type, status, create_by, create_time, remark
) VALUES (
    '审核状态', 'audit_status', '0', 'admin', NOW(), '通用审核状态字典'
);

INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '待审核', '0', 'audit_status', '', 'info', 'Y', '0', 'admin', NOW(), '待审核状态'),
(2, '已审核', '1', 'audit_status', '', 'success', 'N', '0', 'admin', NOW(), '已审核状态'),
(3, '已拒绝', '2', 'audit_status', '', 'danger', 'N', '0', 'admin', NOW(), '已拒绝状态');

-- 11. 创建布尔值字典（是否类型）
INSERT IGNORE INTO sys_dict_type (
    dict_name, dict_type, status, create_by, create_time, remark
) VALUES (
    '是否状态', 'sys_yes_no', '0', 'admin', NOW(), '通用是否状态字典'
);

INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '否', '0', 'sys_yes_no', '', 'info', 'Y', '0', 'admin', NOW(), '否'),
(2, '是', '1', 'sys_yes_no', '', 'success', 'N', '0', 'admin', NOW(), '是');

-- 12. 验证更新结果
SELECT 
    '字典数据验证' as check_type,
    dict_type,
    dict_label,
    dict_value,
    CASE 
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '0' AND dict_label = '正常' THEN '✓ 符合标准'
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '1' AND dict_label = '停用' THEN '✓ 符合标准'
        WHEN dict_type = 'operation_status' AND dict_value = '0' AND dict_label = '成功' THEN '✓ 符合标准'
        WHEN dict_type = 'operation_status' AND dict_value = '1' AND dict_label = '失败' THEN '✓ 符合标准'
        WHEN dict_type = 'audit_status' AND dict_value = '0' AND dict_label = '待审核' THEN '✓ 符合标准'
        WHEN dict_type = 'audit_status' AND dict_value = '1' AND dict_label = '已审核' THEN '✓ 符合标准'
        WHEN dict_type = 'audit_status' AND dict_value = '2' AND dict_label = '已拒绝' THEN '✓ 符合标准'
        WHEN dict_type = 'sys_yes_no' AND dict_value = '0' AND dict_label = '否' THEN '✓ 符合标准'
        WHEN dict_type = 'sys_yes_no' AND dict_value = '1' AND dict_label = '是' THEN '✓ 符合标准'
        ELSE '⚠️ 需要检查'
    END as validation_result,
    remark
FROM sys_dict_data 
WHERE dict_type IN (
    'sys_normal_disable', 'operation_status', 'audit_status', 'sys_yes_no',
    'inventory_status', 'inventory_in_status', 'inventory_out_status',
    'inventory_transfer_status', 'inventory_check_status', 'request_status'
)
ORDER BY dict_type, dict_sort;

-- 13. 统计更新结果
SELECT 
    '更新统计' as summary_type,
    COUNT(*) as total_dict_entries,
    COUNT(CASE WHEN dict_type = 'sys_normal_disable' THEN 1 END) as normal_disable_count,
    COUNT(CASE WHEN dict_type = 'operation_status' THEN 1 END) as operation_status_count,
    COUNT(CASE WHEN dict_type = 'audit_status' THEN 1 END) as audit_status_count,
    COUNT(CASE WHEN dict_type = 'sys_yes_no' THEN 1 END) as yes_no_count
FROM sys_dict_data 
WHERE dict_type IN (
    'sys_normal_disable', 'operation_status', 'audit_status', 'sys_yes_no'
);

-- 提交事务
COMMIT;

-- 输出完成信息
SELECT 
    '字段定义标准化' as task_name,
    '系统数据字典配置更新' as subtask_name,
    '已完成' as status,
    NOW() as completion_time,
    '所有状态字典已更新为标准定义：0=正常/启用/成功，1=异常/禁用/失败' as description;