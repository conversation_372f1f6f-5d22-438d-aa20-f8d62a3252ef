package com.wanyu.system.service;

import java.util.List;
import java.util.Map;
import com.wanyu.system.domain.WmsOperationLog;

/**
 * 操作日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface IWmsOperationLogService 
{
    /**
     * 查询操作日志
     * 
     * @param logId 操作日志主键
     * @return 操作日志
     */
    public WmsOperationLog selectWmsOperationLogByLogId(Long logId);

    /**
     * 查询操作日志列表
     * 
     * @param wmsOperationLog 操作日志
     * @return 操作日志集合
     */
    public List<WmsOperationLog> selectWmsOperationLogList(WmsOperationLog wmsOperationLog);

    /**
     * 查询成功操作日志
     * 
     * @param wmsOperationLog 查询条件
     * @return 成功操作日志集合
     */
    public List<WmsOperationLog> selectSuccessOperations(WmsOperationLog wmsOperationLog);

    /**
     * 查询失败操作日志
     * 
     * @param wmsOperationLog 查询条件
     * @return 失败操作日志集合
     */
    public List<WmsOperationLog> selectFailedOperations(WmsOperationLog wmsOperationLog);

    /**
     * 统计操作状态
     * 
     * @param operationType 操作类型
     * @param operatorId 操作人ID
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public List<Map<String, Object>> countByOperationStatus(String operationType, Long operatorId, String beginTime, String endTime);

    /**
     * 新增操作日志
     * 
     * @param wmsOperationLog 操作日志
     * @return 结果
     */
    public int insertWmsOperationLog(WmsOperationLog wmsOperationLog);

    /**
     * 修改操作日志
     * 
     * @param wmsOperationLog 操作日志
     * @return 结果
     */
    public int updateWmsOperationLog(WmsOperationLog wmsOperationLog);

    /**
     * 批量删除操作日志
     * 
     * @param logIds 需要删除的操作日志主键集合
     * @return 结果
     */
    public int deleteWmsOperationLogByLogIds(Long[] logIds);

    /**
     * 删除操作日志信息
     * 
     * @param logId 操作日志主键
     * @return 结果
     */
    public int deleteWmsOperationLogByLogId(Long logId);

    /**
     * 记录成功操作
     * 
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param ipAddress IP地址
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param responseData 响应数据
     * @param executionTime 执行时间
     * @return 结果
     */
    public int logSuccessOperation(String operationType, String operationDesc, Long operatorId, 
                                 String operatorName, String ipAddress, String requestUrl, 
                                 String requestMethod, String requestParams, String responseData, 
                                 Long executionTime);

    /**
     * 记录失败操作
     * 
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     * @param errorMessage 错误信息
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param ipAddress IP地址
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param executionTime 执行时间
     * @return 结果
     */
    public int logFailedOperation(String operationType, String operationDesc, String errorMessage, 
                                Long operatorId, String operatorName, String ipAddress, 
                                String requestUrl, String requestMethod, String requestParams, 
                                Long executionTime);

    /**
     * 清理过期日志
     * 
     * @param days 保留天数
     * @return 结果
     */
    public int cleanExpiredLogs(int days);
}