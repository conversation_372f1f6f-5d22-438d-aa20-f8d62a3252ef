package com.wanyu.web.controller.system;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.annotation.Anonymous;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.SysLicense;
import com.wanyu.system.service.ISysLicenseService;

/**
 * 授权管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@RequestMapping("/system/license")
public class SysLicenseController extends BaseController
{
    @Autowired
    private ISysLicenseService sysLicenseService;

    /**
     * 查询授权管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:license:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysLicense sysLicense)
    {
        startPage();
        List<SysLicense> list = sysLicenseService.selectSysLicenseList(sysLicense);
        return getDataTable(list);
    }

    /**
     * 导出授权管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:license:export')")
    @Log(title = "授权管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLicense sysLicense)
    {
        List<SysLicense> list = sysLicenseService.selectSysLicenseList(sysLicense);
        ExcelUtil<SysLicense> util = new ExcelUtil<SysLicense>(SysLicense.class);
        util.exportExcel(response, list, "授权管理数据");
    }

    /**
     * 获取授权管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:license:query')")
    @GetMapping(value = "/{licenseId}")
    public AjaxResult getInfo(@PathVariable("licenseId") Long licenseId)
    {
        return success(sysLicenseService.selectSysLicenseByLicenseId(licenseId));
    }

    /**
     * 新增授权管理
     */
    @PreAuthorize("@ss.hasPermi('system:license:add')")
    @Log(title = "授权管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysLicense sysLicense)
    {
        return toAjax(sysLicenseService.insertSysLicense(sysLicense));
    }

    /**
     * 修改授权管理（安全版本）
     * 只允许修改非关键字段
     */
    @PreAuthorize("@ss.hasPermi('system:license:edit')")
    @Log(title = "授权管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysLicense sysLicense)
    {
        try {
            // 安全检查：只允许修改特定字段
            if (sysLicense.getLicenseId() == null) {
                return error("授权ID不能为空");
            }
            
            // 检查是否尝试修改关键字段（不允许）
            if (sysLicense.getLicenseType() != null || 
                sysLicense.getMaxUsers() != null || 
                sysLicense.getMaxWarehouses() != null ||
                sysLicense.getFeatures() != null ||
                sysLicense.getStartDate() != null ||
                sysLicense.getEndDate() != null ||
                sysLicense.getLicenseKey() != null) {
                
                return error("安全限制：不允许修改授权类型、用户数、仓库数、功能列表等关键字段。这些信息由授权密钥决定。");
            }
            
            // 使用安全的更新方法
            return toAjax(sysLicenseService.updateLicenseInfo(sysLicense));
            
        } catch (Exception e) {
            return error("授权信息更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除授权管理
     */
    @PreAuthorize("@ss.hasPermi('system:license:remove')")
    @Log(title = "授权管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{licenseIds}")
    public AjaxResult remove(@PathVariable Long[] licenseIds)
    {
        return toAjax(sysLicenseService.deleteSysLicenseByLicenseIds(licenseIds));
    }

    /**
     * 验证授权密钥
     */
    @Anonymous
    @PostMapping("/verify")
    public AjaxResult verifyLicense(@RequestBody SysLicense sysLicense)
    {
        try {
            boolean isValid = sysLicenseService.verifyLicense(sysLicense.getLicenseKey());
            if (isValid) {
                return success("授权验证成功");
            } else {
                return error("授权验证失败");
            }
        } catch (Exception e) {
            return error("授权验证异常: " + e.getMessage());
        }
    }

    /**
     * 获取当前授权状态
     */
    @Anonymous
    @GetMapping("/status")
    public AjaxResult getLicenseStatus()
    {
        try {
            return success(sysLicenseService.getCurrentLicenseStatus());
        } catch (Exception e) {
            return error("获取授权状态失败: " + e.getMessage());
        }
    }

    /**
     * 激活授权密钥
     */
    @Anonymous
    @PostMapping("/activate")
    public AjaxResult activateLicense(@RequestBody SysLicense sysLicense)
    {
        try {
            // 添加调试日志
            System.out.println("=== 激活授权调试信息 ===");
            System.out.println("licenseKey: " + sysLicense.getLicenseKey());
            System.out.println("companyName: " + sysLicense.getCompanyName());
            System.out.println("================================");
            
            boolean result;
            // 如果提供了公司名称，使用支持公司名称的激活方法
            if (sysLicense.getCompanyName() != null && !sysLicense.getCompanyName().trim().isEmpty()) {
                System.out.println("使用带公司名称的激活方法: " + sysLicense.getCompanyName());
                // 直接调用带公司名称的激活方法
                result = sysLicenseService.activateLicenseWithCompany(sysLicense.getLicenseKey(), sysLicense.getCompanyName());
            } else {
                System.out.println("使用默认激活方法");
                result = sysLicenseService.activateLicense(sysLicense.getLicenseKey());
            }
            
            if (result) {
                return success("授权激活成功");
            } else {
                return error("授权激活失败");
            }
        } catch (Exception e) {
            return error("授权激活异常: " + e.getMessage());
        }
    }

    /**
     * 更新授权状态
     */
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody SysLicense sysLicense)
    {
        try {
            return toAjax(sysLicenseService.updateLicenseStatus(sysLicense.getLicenseId(), sysLicense.getStatus()));
        } catch (Exception e) {
            return error("状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新授权信息
     */
    @PutMapping("/info")
    public AjaxResult updateInfo(@RequestBody SysLicense sysLicense)
    {
        try {
            return toAjax(sysLicenseService.updateLicenseInfo(sysLicense));
        } catch (Exception e) {
            return error("信息更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 重新同步授权密钥信息
     * 确保数据库中的授权信息与密钥内容一致
     */
    @PreAuthorize("@ss.hasPermi('system:license:edit')")
    @PostMapping("/sync/{licenseId}")
    public AjaxResult syncLicenseInfo(@PathVariable Long licenseId)
    {
        try {
            boolean result = sysLicenseService.revalidateAndSyncLicense(licenseId);
            if (result) {
                return success("授权信息同步成功，关键字段已从密钥重新解析");
            } else {
                return error("授权信息同步失败");
            }
        } catch (Exception e) {
            return error("同步操作失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成授权密钥
     */
    @PostMapping("/regenerate")
    public AjaxResult regenerateLicense(@RequestBody SysLicense sysLicense)
    {
        try {
            SysLicense result = sysLicenseService.regenerateLicense(sysLicense);
            return success(result);
        } catch (Exception e) {
            return error("密钥重新生成失败: " + e.getMessage());
        }
    }

    /**
     * 检查授权功能权限
     */
    @GetMapping("/feature/{featureCode}")
    public AjaxResult checkFeaturePermission(@PathVariable String featureCode)
    {
        try {
            boolean hasPermission = sysLicenseService.hasFeaturePermission(featureCode);
            return success(hasPermission);
        } catch (Exception e) {
            return error("功能权限检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取授权功能列表
     */
    @GetMapping("/features")
    public AjaxResult getLicenseFeatures()
    {
        try {
            return success(sysLicenseService.getLicenseFeatures());
        } catch (Exception e) {
            return error("获取功能列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取试用期剩余天数
     */
    @GetMapping("/trial/remaining")
    public AjaxResult getTrialRemainingDays()
    {
        try {
            int remainingDays = sysLicenseService.getTrialRemainingDays();
            return success(remainingDays);
        } catch (Exception e) {
            return error("获取试用期信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否为试用版
     */
    @GetMapping("/trial/check")
    public AjaxResult checkTrialVersion()
    {
        try {
            boolean isTrial = sysLicenseService.isTrialVersion();
            return success(isTrial);
        } catch (Exception e) {
            return error("试用版检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取授权详细验证结果（支持在线验证）
     */
    @PostMapping("/verify/details")
    public AjaxResult getLicenseVerificationDetails(@RequestBody SysLicense sysLicense)
    {
        try {
            // 调用服务层的详细验证方法
            Object result = sysLicenseService.getLicenseVerificationDetails(sysLicense.getLicenseKey());
            return success(result);
        } catch (Exception e) {
            return error("获取授权验证详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 远程撤销授权
     */
    @PostMapping("/revoke")
    @PreAuthorize("@ss.hasPermi('system:license:revoke')")
    @Log(title = "授权撤销", businessType = BusinessType.UPDATE)
    public AjaxResult revokeLicense(@RequestBody Map<String, String> params)
    {
        try {
            String licenseKey = params.get("licenseKey");
            String reason = params.get("reason");
            
            if (licenseKey == null || licenseKey.trim().isEmpty()) {
                return error("授权密钥不能为空");
            }
            
            if (reason == null || reason.trim().isEmpty()) {
                return error("撤销原因不能为空");
            }
            
            boolean result = sysLicenseService.revokeLicense(licenseKey, reason);
            if (result) {
                return success("授权撤销成功");
            } else {
                return error("授权撤销失败");
            }
        } catch (Exception e) {
            return error("授权撤销异常: " + e.getMessage());
        }
    }
    
    /**
     * 强制刷新授权状态（触发在线验证）
     */
    @PostMapping("/refresh")
    public AjaxResult refreshLicenseStatus(@RequestBody(required = false) Map<String, String> params)
    {
        try {
            String licenseKey = params != null ? params.get("licenseKey") : null;
            
            if (licenseKey != null && !licenseKey.trim().isEmpty()) {
                // 验证指定的授权密钥
                boolean isValid = sysLicenseService.verifyLicense(licenseKey);
                Map<String, Object> result = new HashMap<>();
                result.put("valid", isValid);
                result.put("licenseKey", licenseKey);
                return success(result);
            } else {
                // 刷新当前授权状态
                Map<String, Object> status = sysLicenseService.getCurrentLicenseStatus();
                return success(status);
            }
        } catch (Exception e) {
            return error("刷新授权状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量验证授权密钥
     */
    @PostMapping("/batch/verify")
    public AjaxResult batchVerifyLicense(@RequestBody Map<String, Object> params)
    {
        try {
            @SuppressWarnings("unchecked")
            List<String> licenseKeys = (List<String>) params.get("licenseKeys");
            
            if (licenseKeys == null || licenseKeys.isEmpty()) {
                return error("授权密钥列表不能为空");
            }
            
            Map<String, Object> results = new HashMap<>();
            for (String licenseKey : licenseKeys) {
                try {
                    boolean isValid = sysLicenseService.verifyLicense(licenseKey);
                    Map<String, Object> details = new HashMap<>();
                    details.put("valid", isValid);
                    details.put("timestamp", System.currentTimeMillis());
                    results.put(licenseKey, details);
                } catch (Exception e) {
                    Map<String, Object> details = new HashMap<>();
                    details.put("valid", false);
                    details.put("error", e.getMessage());
                    details.put("timestamp", System.currentTimeMillis());
                    results.put(licenseKey, details);
                }
            }
            
            return success(results);
        } catch (Exception e) {
            return error("批量验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取授权系统健康状态
     */
    @GetMapping("/health")
    public AjaxResult getLicenseSystemHealth()
    {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 检查当前授权状态
            Map<String, Object> currentStatus = sysLicenseService.getCurrentLicenseStatus();
            health.put("currentLicense", currentStatus);
            
            // 检查服务状态
            health.put("serviceStatus", "running");
            health.put("timestamp", System.currentTimeMillis());
            health.put("version", "2.0");
            
            // 检查功能权限
            List<Map<String, Object>> features = sysLicenseService.getLicenseFeatures();
            health.put("features", features);
            
            // 统计信息
            List<SysLicense> allLicenses = sysLicenseService.selectSysLicenseList(new SysLicense());
            health.put("totalLicenses", allLicenses.size());
            
            long activeLicenses = allLicenses.stream()
                .filter(license -> "0".equals(license.getStatus())) // 修复：0=启用状态
                .count();
            health.put("activeLicenses", activeLicenses);
            
            return success(health);
        } catch (Exception e) {
            return error("获取系统健康状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取授权统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getLicenseStatistics()
    {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            List<SysLicense> allLicenses = sysLicenseService.selectSysLicenseList(new SysLicense());
            
            // 按类型统计
            Map<String, Long> typeStats = allLicenses.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    license -> license.getLicenseType() != null ? license.getLicenseType() : "unknown",
                    java.util.stream.Collectors.counting()
                ));
            statistics.put("byType", typeStats);
            
            // 按状态统计
            Map<String, Long> statusStats = allLicenses.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    license -> "0".equals(license.getStatus()) ? "active" : "inactive", // 修复：0=启用状态
                    java.util.stream.Collectors.counting()
                ));
            statistics.put("byStatus", statusStats);
            
            // 过期统计
            Date now = new Date();
            long expiredCount = allLicenses.stream()
                .filter(license -> license.getEndDate() != null && license.getEndDate().before(now))
                .count();
            statistics.put("expired", expiredCount);
            
            // 即将过期（30天内）
            Date thirtyDaysLater = new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000);
            long soonExpireCount = allLicenses.stream()
                .filter(license -> license.getEndDate() != null && 
                        license.getEndDate().after(now) && 
                        license.getEndDate().before(thirtyDaysLater))
                .count();
            statistics.put("soonExpire", soonExpireCount);
            
            statistics.put("total", allLicenses.size());
            statistics.put("timestamp", System.currentTimeMillis());
            
            return success(statistics);
        } catch (Exception e) {
            return error("获取统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试授权连通性
     */
    @PostMapping("/test/connectivity")
    public AjaxResult testLicenseConnectivity(@RequestBody(required = false) Map<String, String> params)
    {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试本地验证
            result.put("localVerification", "available");
            
            // 测试在线验证（如果配置了验证服务器）
            try {
                // 这里可以添加实际的在线验证测试
                result.put("onlineVerification", "available");
                result.put("serverReachable", true);
            } catch (Exception e) {
                result.put("onlineVerification", "unavailable");
                result.put("serverReachable", false);
                result.put("serverError", e.getMessage());
            }
            
            // 硬件指纹测试
            try {
                String fingerprint = sysLicenseService.getHardwareFingerprint();
                result.put("hardwareFingerprint", fingerprint);
                result.put("fingerprintGeneration", "success");
            } catch (Exception e) {
                result.put("fingerprintGeneration", "failed");
                result.put("fingerprintError", e.getMessage());
            }
            
            result.put("timestamp", System.currentTimeMillis());
            return success(result);
        } catch (Exception e) {
            return error("连通性测试失败: " + e.getMessage());
        }
    }

}