<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.SysLogErrorMapper">
    
    <resultMap type="SysLogError" id="SysLogErrorResult">
        <result property="id"          column="id"          />
        <result property="userName"    column="user_name"   />
        <result property="action"      column="action"      />
        <result property="ip"          column="ip"          />
        <result property="status"      column="status"      />
        <result property="remark"      column="remark"      />
        <result property="createTime"  column="create_time" />
    </resultMap>

    <sql id="selectLogErrorVo">
        select id, user_name, action, ip, status, remark, create_time from sys_log_error
    </sql>

    <select id="selectLogErrorList" parameterType="SysLogError" resultMap="SysLogErrorResult">
        <include refid="selectLogErrorVo"/>
        <where>  
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="action != null  and action != ''"> and action like concat('%', #{action}, '%')</if>
            <if test="ip != null  and ip != ''"> and ip like concat('%', #{ip}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectLogErrorById" parameterType="Long" resultMap="SysLogErrorResult">
        <include refid="selectLogErrorVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLogError" parameterType="SysLogError" useGeneratedKeys="true" keyProperty="id">
        insert into sys_log_error
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">user_name,</if>
            <if test="action != null">action,</if>
            <if test="ip != null">ip,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="action != null">#{action},</if>
            <if test="ip != null">#{ip},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateLogError" parameterType="SysLogError">
        update sys_log_error
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="action != null">action = #{action},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLogErrorById" parameterType="Long">
        delete from sys_log_error where id = #{id}
    </delete>

    <delete id="deleteLogErrorByIds" parameterType="String">
        delete from sys_log_error where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="cleanLogError">
        truncate table sys_log_error
    </delete>

</mapper>