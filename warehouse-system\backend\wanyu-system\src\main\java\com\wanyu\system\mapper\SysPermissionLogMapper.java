package com.wanyu.system.mapper;

import java.util.List;
import java.util.Date;
import com.wanyu.system.domain.SysPermissionLog;
import com.wanyu.system.domain.SysPermissionTemplateLog;

/**
 * 权限日志Mapper接口
 *
 * <AUTHOR>
 */
public interface SysPermissionLogMapper
{
    /**
     * 查询权限日志
     *
     * @param logId 权限日志主键
     * @return 权限日志
     */
    public SysPermissionLog selectSysPermissionLogByLogId(Long logId);

    /**
     * 查询权限日志列表
     *
     * @param sysPermissionLog 权限日志
     * @return 权限日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogList(SysPermissionLog sysPermissionLog);

    /**
     * 新增权限日志
     *
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    public int insertSysPermissionLog(SysPermissionLog sysPermissionLog);

    /**
     * 修改权限日志
     *
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    public int updateSysPermissionLog(SysPermissionLog sysPermissionLog);

    /**
     * 删除权限日志
     *
     * @param logId 权限日志主键
     * @return 结果
     */
    public int deleteSysPermissionLogByLogId(Long logId);

    /**
     * 批量删除权限日志
     *
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysPermissionLogByLogIds(Long[] logIds);

    /**
     * 清空权限日志
     *
     * @return 结果
     */
    public int cleanSysPermissionLog();

    /**
     * 新增权限模板应用日志
     *
     * @param templateLog 权限模板应用日志
     * @return 结果
     */
    public int insertTemplateLog(SysPermissionTemplateLog templateLog);

    /**
     * 查询权限模板应用日志列表
     *
     * @param templateLog 权限模板应用日志
     * @return 权限模板应用日志集合
     */
    public List<SysPermissionTemplateLog> selectTemplateLogList(SysPermissionTemplateLog templateLog);

    /**
     * 查询指定时间范围内的权限日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 权限日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogByTimeRange(Date startTime, Date endTime);

    /**
     * 查询指定操作人的权限日志
     *
     * @param operatorId 操作人ID
     * @return 权限日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogByOperator(Long operatorId);

    /**
     * 查询指定目标的权限日志
     *
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 权限日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogByTarget(String targetType, Long targetId);
}
