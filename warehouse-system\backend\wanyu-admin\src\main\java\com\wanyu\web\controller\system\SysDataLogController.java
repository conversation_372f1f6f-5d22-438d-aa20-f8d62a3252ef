package com.wanyu.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.system.domain.SysDataLog;
import com.wanyu.system.service.ISysDataLogService;

/**
 * 数据日志Controller
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/system/data")
public class SysDataLogController extends BaseController
{
    @Autowired
    private ISysDataLogService dataLogService;

    /**
     * 查询数据日志列表
     */
    @PreAuthorize("@ss.hasPermi('log:data:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDataLog dataLog)
    {
        startPage();
        List<SysDataLog> list = dataLogService.selectDataLogList(dataLog);
        return getDataTable(list);
    }

    /**
     * 导出数据日志列表
     */
    @PreAuthorize("@ss.hasPermi('log:data:export')")
    @Log(title = "数据日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDataLog dataLog)
    {
        List<SysDataLog> list = dataLogService.selectDataLogList(dataLog);
        ExcelUtil<SysDataLog> util = new ExcelUtil<SysDataLog>(SysDataLog.class);
        util.exportExcel(response, list, "数据日志数据");
    }

    /**
     * 获取数据日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:data:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(dataLogService.selectDataLogById(logId));
    }

    /**
     * 删除数据日志
     */
    @PreAuthorize("@ss.hasPermi('log:data:remove')")
    @Log(title = "数据日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(dataLogService.deleteDataLogByIds(logIds));
    }

    /**
     * 清空数据日志
     */
    @PreAuthorize("@ss.hasPermi('log:data:remove')")
    @Log(title = "数据日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        dataLogService.cleanDataLog();
        return success();
    }
}