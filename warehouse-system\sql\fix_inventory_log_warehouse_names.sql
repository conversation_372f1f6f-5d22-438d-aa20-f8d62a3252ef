-- ===================================================================
-- 修复出入库日志仓库名称显示问题
-- ===================================================================

-- 1. 更新仓库名称（从sys_warehouse表获取）
-- ===================================================================
UPDATE wms_inventory_log l 
LEFT JOIN sys_warehouse w ON l.warehouse_id = w.warehouse_id 
SET l.warehouse_name = COALESCE(w.warehouse_name, CONCAT('仓库-', l.warehouse_id))
WHERE l.warehouse_name IS NULL OR l.warehouse_name = '';

-- 2. 更新物品信息（从wms_product表获取）
-- ===================================================================
UPDATE wms_inventory_log l 
LEFT JOIN wms_product p ON l.product_id = p.product_id 
LEFT JOIN wms_specification ps ON p.spec_id = ps.spec_id
LEFT JOIN wms_unit pu ON p.unit_id = pu.unit_id
SET 
  l.product_name = COALESCE(p.product_name, CONCAT('物品-', l.product_id)),
  l.product_code = COALESCE(p.product_code, ''),
  l.product_spec = COALESCE(ps.spec_name, ''),
  l.product_unit = COALESCE(pu.unit_name, pu.unit_code, '')
WHERE l.product_name IS NULL OR l.product_name = '';

-- 3. 验证修复结果
-- ===================================================================
SELECT 
  COUNT(*) as 总记录数,
  COUNT(CASE WHEN warehouse_name != '' AND warehouse_name IS NOT NULL THEN 1 END) as 有仓库名称,
  COUNT(CASE WHEN product_name != '' AND product_name IS NOT NULL THEN 1 END) as 有物品名称,
  COUNT(CASE WHEN operation_type = 'IN' THEN 1 END) as 入库记录,
  COUNT(CASE WHEN operation_type = 'OUT' THEN 1 END) as 出库记录
FROM wms_inventory_log;

-- 4. 显示前5条记录作为样例
-- ===================================================================
SELECT 
  log_id,
  operation_type,
  warehouse_name,
  product_name,
  product_code,
  product_spec,
  product_unit,
  quantity,
  operator,
  operation_time
FROM wms_inventory_log 
ORDER BY operation_time DESC 
LIMIT 5;

SELECT '出入库日志仓库名称修复完成！' as message;
