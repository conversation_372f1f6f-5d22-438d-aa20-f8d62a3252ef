@echo off
chcp 65001
echo ========================================
echo 创建测试图片文件
echo ========================================

echo 步骤1: 创建图片目录...
mkdir "C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13" 2>nul

echo 步骤2: 创建一个简单的测试图片...
echo 由于无法直接创建图片文件，请手动执行以下操作：
echo.
echo 1. 找一张测试图片（PNG/JPG格式）
echo 2. 重命名为：屏幕截图 2025-05-15 171140_20250813231048A002.png
echo 3. 复制到目录：C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13\
echo.
echo 或者执行以下PowerShell命令创建一个简单的测试文件：

echo 步骤3: 使用PowerShell创建测试文件...
powershell -Command "New-Item -Path 'C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13\屏幕截图 2025-05-15 171140_20250813231048A002.png' -ItemType File -Force"

if exist "C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13\屏幕截图 2025-05-15 171140_20250813231048A002.png" (
    echo ✅ 测试文件创建成功
) else (
    echo ❌ 测试文件创建失败
)

echo 步骤4: 测试图片访问...
echo 请启动后端服务后，在浏览器中访问：
echo http://localhost:8080/profile/upload/2025/08/13/屏幕截图 2025-05-15 171140_20250813231048A002.png
echo.

pause