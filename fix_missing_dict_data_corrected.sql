-- 修复缺失的字典数据（使用正确的ID）
USE warehouse_system;

-- 1. 添加产品属性类型字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (200, '产品属性类型', 'product_attribute_type', '0', 'admin', NOW(), '产品属性类型列表');

-- 2. 添加系统是否字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (201, '系统是否', 'sys_yes_no', '0', 'admin', NOW(), '系统是否列表');

-- 3. 获取最大的dict_code值
SELECT MAX(dict_code) as max_code FROM sys_dict_data;

-- 4. 添加字典数据项（使用更大的ID）
INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(500, 1, '基本属性', '1', 'product_attribute_type', '', 'primary', 'Y', '0', 'admin', NOW(), '产品基本属性'),
(501, 2, '扩展属性', '2', 'product_attribute_type', '', 'info', 'N', '0', 'admin', NOW(), '产品扩展属性'),
(502, 3, '技术参数', '3', 'product_attribute_type', '', 'success', 'N', '0', 'admin', NOW(), '产品技术参数'),
(503, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', NOW(), '系统默认是'),
(504, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', NOW(), '系统默认否');

COMMIT;