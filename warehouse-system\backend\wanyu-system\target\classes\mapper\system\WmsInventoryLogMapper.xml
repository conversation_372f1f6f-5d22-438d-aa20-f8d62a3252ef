<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsInventoryLogMapper">
    
    <resultMap type="WmsInventoryLog" id="WmsInventoryLogResult">
        <result property="logId"    column="log_id"    />
        <result property="operationType"    column="operation_type"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productCode"    column="product_code"    />
        <result property="productSpec"    column="product_spec"    />
        <result property="productUnit"    column="product_unit"    />
        <result property="quantity"    column="quantity"    />
        <result property="beforeQuantity"    column="before_quantity"    />
        <result property="afterQuantity"    column="after_quantity"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="relatedOrderId"    column="related_order_id"    />
        <result property="relatedOrderType"    column="related_order_type"    />
        <result property="operator"    column="operator"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="expireDate"    column="expire_date"    />
        <result property="supplierId"    column="supplier_id"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="reason"    column="reason"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <!-- 兼容旧字段 -->
        <result property="specification"    column="product_spec"    />
        <result property="unit"    column="product_unit"    />
    </resultMap>

    <sql id="selectWmsInventoryLogVo">
        select log_id, operation_type, warehouse_id, warehouse_name, product_id, product_name, product_code, product_spec, product_unit, quantity, before_quantity, after_quantity, unit_price, total_amount, related_order_id, related_order_type, operator, operator_id, operation_time, location_code, location_name, batch_no, expire_date, supplier_id, supplier_name, customer_id, customer_name, reason, remark, status, create_by, create_time, update_by, update_time from wms_inventory_log
    </sql>

    <select id="selectWmsInventoryLogList" parameterType="WmsInventoryLog" resultMap="WmsInventoryLogResult">
        <include refid="selectWmsInventoryLogVo"/>
        <where>  
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and product_code like concat('%', #{productCode}, '%')</if>
            <if test="relatedOrderId != null  and relatedOrderId != ''"> and related_order_id like concat('%', #{relatedOrderId}, '%')</if>
            <if test="relatedOrderType != null  and relatedOrderType != ''"> and related_order_type = #{relatedOrderType}</if>
            <if test="operator != null  and operator != ''"> and operator like concat('%', #{operator}, '%')</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code like concat('%', #{locationCode}, '%')</if>
            <if test="batchNo != null  and batchNo != ''"> and batch_no like concat('%', #{batchNo}, '%')</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <!-- 优化时间查询，避免使用函数 -->
            <if test="params.beginTime != null and params.beginTime != ''">
                and operation_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and operation_time &lt;= #{params.endTime}
            </if>
            <!-- 仓库权限过滤 -->
            <if test="params.warehouseScope != null and params.warehouseScope != ''">
                ${params.warehouseScope}
            </if>
        </where>
        <choose>
            <when test="params.orderBy != null and params.orderBy != ''">
                order by ${params.orderBy}
                <if test="params.isAsc != null and params.isAsc != ''">
                    <choose>
                        <when test="params.isAsc == 'asc'">asc</when>
                        <otherwise>desc</otherwise>
                    </choose>
                </if>
            </when>
            <otherwise>
                order by operation_time desc
            </otherwise>
        </choose>
    </select>

    <select id="selectWmsInventoryLogListWithAuth" parameterType="WmsInventoryLog" resultMap="WmsInventoryLogResult">
        <include refid="selectWmsInventoryLogVo"/>
        <where>  
            <!-- 仓库权限过滤 - 包含用户直接权限、角色权限和部门权限 -->
            <if test="params.userId != null">
                and warehouse_id in (
                    -- 用户直接分配的仓库权限
                    select warehouse_id from sys_user_warehouse 
                    where user_id = #{params.userId}
                    
                    union
                    
                    -- 角色仓库权限
                    select rw.warehouse_id from sys_role_warehouse rw
                    inner join sys_user_role ur on rw.role_id = ur.role_id
                    where ur.user_id = #{params.userId}
                    
                    union
                    
                    -- 部门仓库权限
                    select dw.warehouse_id from sys_dept_warehouse dw
                    inner join sys_user u on dw.dept_id = u.dept_id
                    where u.user_id = #{params.userId}
                )
            </if>
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and product_code like concat('%', #{productCode}, '%')</if>
            <if test="relatedOrderId != null  and relatedOrderId != ''"> and related_order_id like concat('%', #{relatedOrderId}, '%')</if>
            <if test="relatedOrderType != null  and relatedOrderType != ''"> and related_order_type = #{relatedOrderType}</if>
            <if test="operator != null  and operator != ''"> and operator like concat('%', #{operator}, '%')</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code like concat('%', #{locationCode}, '%')</if>
            <if test="batchNo != null  and batchNo != ''"> and batch_no like concat('%', #{batchNo}, '%')</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <!-- 优化时间查询，避免使用函数 -->
            <if test="params.beginTime != null and params.beginTime != ''">
                and operation_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and operation_time &lt;= #{params.endTime}
            </if>
        </where>
        <choose>
            <when test="params.orderBy != null and params.orderBy != ''">
                order by ${params.orderBy}
                <if test="params.isAsc != null and params.isAsc != ''">
                    <choose>
                        <when test="params.isAsc == 'asc'">asc</when>
                        <otherwise>desc</otherwise>
                    </choose>
                </if>
            </when>
            <otherwise>
                order by operation_time desc
            </otherwise>
        </choose>
    </select>
    
    <select id="selectWmsInventoryLogByLogId" parameterType="Long" resultMap="WmsInventoryLogResult">
        <include refid="selectWmsInventoryLogVo"/>
        where log_id = #{logId}
    </select>
        
    <insert id="insertWmsInventoryLog" parameterType="WmsInventoryLog" useGeneratedKeys="true" keyProperty="logId">
        insert into wms_inventory_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productCode != null and productCode != ''">product_code,</if>
            <if test="productSpec != null">product_spec,</if>
            <if test="productUnit != null">product_unit,</if>
            <if test="quantity != null">quantity,</if>
            <if test="beforeQuantity != null">before_quantity,</if>
            <if test="afterQuantity != null">after_quantity,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="relatedOrderId != null">related_order_id,</if>
            <if test="relatedOrderType != null">related_order_type,</if>
            <if test="operator != null and operator != ''">operator,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="batchNo != null">batch_no,</if>
            <if test="expireDate != null">expire_date,</if>
            <if test="supplierId != null">supplier_id,</if>
            <if test="supplierName != null">supplier_name,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="reason != null">reason,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productCode != null and productCode != ''">#{productCode},</if>
            <if test="productSpec != null">#{productSpec},</if>
            <if test="productUnit != null">#{productUnit},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="beforeQuantity != null">#{beforeQuantity},</if>
            <if test="afterQuantity != null">#{afterQuantity},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="relatedOrderId != null">#{relatedOrderId},</if>
            <if test="relatedOrderType != null">#{relatedOrderType},</if>
            <if test="operator != null and operator != ''">#{operator},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="expireDate != null">#{expireDate},</if>
            <if test="supplierId != null">#{supplierId},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="reason != null">#{reason},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmsInventoryLog" parameterType="WmsInventoryLog">
        update wms_inventory_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name = #{warehouseName},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productCode != null and productCode != ''">product_code = #{productCode},</if>
            <if test="productSpec != null">product_spec = #{productSpec},</if>
            <if test="productUnit != null">product_unit = #{productUnit},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="beforeQuantity != null">before_quantity = #{beforeQuantity},</if>
            <if test="afterQuantity != null">after_quantity = #{afterQuantity},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="relatedOrderId != null">related_order_id = #{relatedOrderId},</if>
            <if test="relatedOrderType != null">related_order_type = #{relatedOrderType},</if>
            <if test="operator != null and operator != ''">operator = #{operator},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="expireDate != null">expire_date = #{expireDate},</if>
            <if test="supplierId != null">supplier_id = #{supplierId},</if>
            <if test="supplierName != null">supplier_name = #{supplierName},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteWmsInventoryLogByLogId" parameterType="Long">
        delete from wms_inventory_log where log_id = #{logId}
    </delete>

    <delete id="deleteWmsInventoryLogByLogIds" parameterType="String">
        delete from wms_inventory_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

    <delete id="cleanWmsInventoryLog">
        delete from wms_inventory_log
    </delete>

    <!-- 获取库存统计信息 -->
    <select id="getInventoryStatistics" parameterType="map" resultType="map">
        SELECT 
            COUNT(*) as totalOperations,
            COUNT(CASE WHEN DATE(operation_time) = CURDATE() THEN 1 END) as todayOperations,
            COUNT(CASE WHEN operation_type = 'IN' THEN 1 END) as inOperations,
            COUNT(CASE WHEN operation_type = 'OUT' THEN 1 END) as outOperations
        FROM wms_inventory_log
        <where>
            <if test="userId != null">
                and warehouse_id in (
                    -- 用户直接分配的仓库权限
                    select warehouse_id from sys_user_warehouse 
                    where user_id = #{userId}
                    
                    union
                    
                    -- 角色仓库权限
                    select rw.warehouse_id from sys_role_warehouse rw
                    inner join sys_user_role ur on rw.role_id = ur.role_id
                    where ur.user_id = #{userId}
                    
                    union
                    
                    -- 部门仓库权限
                    select dw.warehouse_id from sys_dept_warehouse dw
                    inner join sys_user u on dw.dept_id = u.dept_id
                    where u.user_id = #{userId}
                )
            </if>
            <if test="beginTime != null and beginTime != ''">
                and operation_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and operation_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 获取库存操作趋势数据 -->
    <select id="getInventoryTrend" parameterType="map" resultType="map">
        SELECT 
            DATE(operation_time) as date,
            COUNT(CASE WHEN operation_type = 'IN' THEN 1 END) as inOperations,
            COUNT(CASE WHEN operation_type = 'OUT' THEN 1 END) as outOperations,
            COUNT(*) as totalOperations
        FROM wms_inventory_log
        <where>
            <if test="userId != null">
                and warehouse_id in (
                    -- 用户直接分配的仓库权限
                    select warehouse_id from sys_user_warehouse 
                    where user_id = #{userId}
                    
                    union
                    
                    -- 角色仓库权限
                    select rw.warehouse_id from sys_role_warehouse rw
                    inner join sys_user_role ur on rw.role_id = ur.role_id
                    where ur.user_id = #{userId}
                    
                    union
                    
                    -- 部门仓库权限
                    select dw.warehouse_id from sys_dept_warehouse dw
                    inner join sys_user u on dw.dept_id = u.dept_id
                    where u.user_id = #{userId}
                )
            </if>
            <if test="period == '7d'">
                and operation_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            </if>
            <if test="period == '30d'">
                and operation_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            </if>
            <if test="period == '90d'">
                and operation_time >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)
            </if>
        </where>
        GROUP BY DATE(operation_time)
        ORDER BY DATE(operation_time)
    </select>

    <!-- 获取用户可访问的仓库ID列表 -->
    <select id="getUserWarehouseIds" parameterType="Long" resultType="Long">
        -- 用户直接分配的仓库权限
        select warehouse_id from sys_user_warehouse 
        where user_id = #{userId}
        
        union
        
        -- 角色仓库权限
        select rw.warehouse_id from sys_role_warehouse rw
        inner join sys_user_role ur on rw.role_id = ur.role_id
        where ur.user_id = #{userId}
        
        union
        
        -- 部门仓库权限
        select dw.warehouse_id from sys_dept_warehouse dw
        inner join sys_user u on dw.dept_id = u.dept_id
        where u.user_id = #{userId}
    </select>

    <select id="selectWmsInventoryLogListByInventoryId" parameterType="Long" resultMap="WmsInventoryLogResult">
        <include refid="selectWmsInventoryLogVo"/>
        where inventory_id = #{inventoryId}
        order by operation_time desc
    </select>
    
    <!-- 按物品类别统计库存操作 -->
    <select id="getInventoryStatsByCategory" parameterType="map" resultType="map">
        SELECT 
            pc.category_name as categoryName,
            COUNT(*) as totalOperations,
            COUNT(CASE WHEN l.operation_type = 'IN' THEN 1 END) as inOperations,
            COUNT(CASE WHEN l.operation_type = 'OUT' THEN 1 END) as outOperations,
            SUM(l.quantity) as totalQuantity
        FROM wms_inventory_log l
        LEFT JOIN wms_product p ON l.product_id = p.product_id
        LEFT JOIN wms_category pc ON p.category_id = pc.category_id
        <where>
            <if test="userId != null">
                and l.warehouse_id in (
                    -- 用户直接分配的仓库权限
                    select warehouse_id from sys_user_warehouse 
                    where user_id = #{userId}
                    
                    union
                    
                    -- 角色仓库权限
                    select rw.warehouse_id from sys_role_warehouse rw
                    inner join sys_user_role ur on rw.role_id = ur.role_id
                    where ur.user_id = #{userId}
                    
                    union
                    
                    -- 部门仓库权限
                    select dw.warehouse_id from sys_dept_warehouse dw
                    inner join sys_user u on dw.dept_id = u.dept_id
                    where u.user_id = #{userId}
                )
            </if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(l.operation_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(l.operation_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <if test="operationType != null and operationType != ''">
                and l.operation_type = #{operationType}
            </if>
        </where>
        GROUP BY pc.category_id, pc.category_name
        ORDER BY totalOperations DESC
    </select>



    <!-- 优化的统计查询：按操作类型统计数量 -->
    <select id="countByOperationType" parameterType="map" resultType="map">
        select operation_type, count(1) as count
        from wms_inventory_log
        <where>
            <if test="warehouseId != null">and warehouse_id = #{warehouseId}</if>
            <if test="beginTime != null">and operation_time &gt;= #{beginTime}</if>
            <if test="endTime != null">and operation_time &lt;= #{endTime}</if>
        </where>
        group by operation_type
        order by operation_type
    </select>

    <!-- 优化的查询：获取最近的库存操作记录（限制结果集） -->
    <select id="selectRecentInventoryLogs" parameterType="map" resultMap="WmsInventoryLogResult">
        select log_id, operation_type, warehouse_id, warehouse_name, product_id, product_name, 
               product_code, quantity, before_quantity, after_quantity, operator, operation_time, status
        from wms_inventory_log
        <where>
            <if test="warehouseId != null">and warehouse_id = #{warehouseId}</if>
            <if test="productId != null">and product_id = #{productId}</if>
            <if test="operationType != null">and operation_type = #{operationType}</if>
        </where>
        order by operation_time desc
        limit #{limit}
    </select>

    <!-- 优化的查询：按仓库和产品统计库存变化 -->
    <select id="selectInventoryChangeSummary" parameterType="map" resultType="map">
        select warehouse_id, warehouse_name, product_id, product_name,
               sum(case when operation_type = 'IN' then quantity else 0 end) as in_quantity,
               sum(case when operation_type = 'OUT' then quantity else 0 end) as out_quantity,
               sum(case when operation_type = 'IN' then quantity else -quantity end) as net_change
        from wms_inventory_log
        <where>
            <if test="warehouseId != null">and warehouse_id = #{warehouseId}</if>
            <if test="productId != null">and product_id = #{productId}</if>
            <if test="beginTime != null">and operation_time &gt;= #{beginTime}</if>
            <if test="endTime != null">and operation_time &lt;= #{endTime}</if>
        </where>
        group by warehouse_id, warehouse_name, product_id, product_name
        having net_change != 0
        order by warehouse_id, product_id
    </select>

    <!-- 获取仓库信息 -->
    <select id="getWarehouseInfo" parameterType="Long" resultType="map">
        SELECT 
            warehouse_id,
            warehouse_name,
            warehouse_code,
            warehouse_address,
            status
        FROM sys_warehouse 
        WHERE warehouse_id = #{warehouseId}
        LIMIT 1
    </select>

    <!-- 获取物品信息 -->
    <select id="getProductInfo" parameterType="Long" resultType="map">
        SELECT 
            p.product_id,
            p.product_name,
            p.product_code,
            COALESCE(ps.spec_name, ps.specification, '') as product_spec,
            COALESCE(pu.unit_name, pu.unit_code, '') as product_unit,
            p.category_name,
            p.status
        FROM wms_product p
        LEFT JOIN wms_specification ps ON p.spec_id = ps.spec_id
        LEFT JOIN wms_unit pu ON p.unit_id = pu.unit_id
        WHERE p.product_id = #{productId}
        UNION ALL
        SELECT 
            product_id,
            product_name,
            product_code,
            COALESCE(product_spec, specification, '') as product_spec,
            COALESCE(product_unit, unit, '') as product_unit,
            product_category as category_name,
            status
        FROM wms_product 
        WHERE product_id = #{productId}
        LIMIT 1
    </select>

    <!-- 根据日志ID更新仓库名称 -->
    <update id="updateWarehouseNameByLogId" parameterType="Long">
        UPDATE wms_inventory_log l
        LEFT JOIN sys_warehouse w ON l.warehouse_id = w.warehouse_id
        SET l.warehouse_name = w.warehouse_name
        WHERE l.log_id = #{logId}
        AND w.warehouse_name IS NOT NULL
    </update>

</mapper>