-- ----------------------------
-- Alter table wms_product
-- ----------------------------
ALTER TABLE `wms_product` ADD COLUMN `unit_id` int(11) DEFAULT NULL COMMENT 'Unit ID' AFTER `product_unit`;
ALTER TABLE `wms_product` ADD COLUMN `spec_id` int(11) DEFAULT NULL COMMENT 'Specification ID' AFTER `product_spec`;
ALTER TABLE `wms_product` ADD COLUMN `category_id` int(11) DEFAULT NULL COMMENT 'Category ID' AFTER `product_category`;

-- Update existing records
UPDATE `wms_product` SET `unit_id` = 1 WHERE `product_unit` = 'PCS' OR `product_unit` = 'Piece';
UPDATE `wms_product` SET `unit_id` = 2 WHERE `product_unit` = 'BOX' OR `product_unit` = 'Box';
UPDATE `wms_product` SET `unit_id` = 3 WHERE `product_unit` = 'KG' OR `product_unit` = 'Kilogram';

UPDATE `wms_product` SET `spec_id` = 1 WHERE `product_spec` = 'STD' OR `product_spec` = 'Standard';
UPDATE `wms_product` SET `spec_id` = 2 WHERE `product_spec` = 'L' OR `product_spec` = 'Large';
UPDATE `wms_product` SET `spec_id` = 3 WHERE `product_spec` = 'S' OR `product_spec` = 'Small';
