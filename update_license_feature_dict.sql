-- ============================================================================
-- 更新授权功能相关数据字典脚本
-- 
-- 确保所有状态字典都使用标准定义：0=正常，1=停用
-- ============================================================================

USE warehouse_system;

-- 更新系统状态字典（确保标准化）
UPDATE sys_dict_data 
SET dict_label = '正常' 
WHERE dict_type = 'sys_normal_disable' AND dict_value = '0';

UPDATE sys_dict_data 
SET dict_label = '停用' 
WHERE dict_type = 'sys_normal_disable' AND dict_value = '1';

-- 如果不存在操作状态字典，则创建
INSERT IGNORE INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, '成功', '0', 'operation_status', '', 'success', 'Y', '0', 'admin', NOW(), '操作成功'),
(2, '失败', '1', 'operation_status', '', 'danger', 'N', '0', 'admin', NOW(), '操作失败');

-- 验证字典数据
SELECT 
    '字典数据验证' as check_type,
    dict_type,
    dict_label,
    dict_value,
    CASE 
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '0' AND dict_label = '正常' THEN '✓ 正确'
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '1' AND dict_label = '停用' THEN '✓ 正确'
        WHEN dict_type = 'operation_status' AND dict_value = '0' AND dict_label = '成功' THEN '✓ 正确'
        WHEN dict_type = 'operation_status' AND dict_value = '1' AND dict_label = '失败' THEN '✓ 正确'
        ELSE '⚠️ 需要检查'
    END as validation_result
FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status')
ORDER BY dict_type, dict_sort;