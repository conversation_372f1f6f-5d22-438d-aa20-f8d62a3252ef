package com.wanyu.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wanyu.common.core.domain.entity.SysRole;
import com.wanyu.common.core.domain.entity.SysUser;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.system.domain.SysUserRole;
import com.wanyu.system.mapper.SysRoleMapper;
import com.wanyu.system.mapper.SysUserRoleMapper;
import com.wanyu.system.service.ISysRoleWarehouseService;
import com.wanyu.system.service.IWarehouseDataScopeService;

/**
 * 仓库数据权限Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class WarehouseDataScopeServiceImpl implements IWarehouseDataScopeService {
    
    @Autowired
    private ISysRoleWarehouseService roleWarehouseService;
    
    @Autowired
    private SysRoleMapper roleMapper;
    
    @Autowired
    private SysUserRoleMapper userRoleMapper;
    
    /**
     * 获取用户可访问的仓库ID列表
     *
     * @param userId 用户ID
     * @return 仓库ID列表
     */
    @Override
    public List<Long> getWarehouseIdsByUserId(Long userId) {
        return getWarehouseAndChildIdsByUserId(userId);
    }

    /**
     * 获取用户可访问的仓库ID列表及其子仓库
     *
     * @param userId 用户ID
     * @return 仓库ID列表及其子仓库
     */
    @Override
    public List<Long> getWarehouseAndChildIdsByUserId(Long userId) {
        // 获取用户角色
        List<SysUserRole> userRoles = userRoleMapper.selectUserRoleByUserId(userId);
        if (userRoles.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取每个角色的仓库权限，并合并
        List<Long> warehouseIds = new ArrayList<>();
        for (SysUserRole userRole : userRoles) {
            List<Long> roleWarehouseIds = roleWarehouseService.selectRoleWarehouseAndChildrenByRoleId(userRole.getRoleId());
            warehouseIds.addAll(roleWarehouseIds);
        }
        
        // 去重
        return warehouseIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取角色可访问的仓库ID列表
     *
     * @param roleId 角色ID
     * @return 仓库ID列表
     */
    public List<Long> getRoleWarehouseScope(Long roleId) {
        return roleWarehouseService.selectRoleWarehouseAndChildrenByRoleId(roleId);
    }

    /**
     * 检查用户是否有指定仓库的访问权限
     *
     * @param userId 用户ID
     * @param warehouseId 仓库ID
     * @return 是否有权限
     */
    @Override
    public boolean checkUserWarehousePermission(Long userId, Long warehouseId) {
        // 获取用户角色
        List<SysUserRole> userRoles = userRoleMapper.selectUserRoleByUserId(userId);
        if (userRoles.isEmpty()) {
            return false;
        }
        
        // 检查每个角色是否有权限
        for (SysUserRole userRole : userRoles) {
            if (roleWarehouseService.checkRoleWarehousePermission(userRole.getRoleId(), warehouseId)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取当前用户可访问的仓库ID列表
     *
     * @return 仓库ID列表
     */
    public List<Long> getCurrentUserWarehouseScope() {
        // 获取当前用户
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        
        // 如果是管理员，返回所有仓库
        if (SecurityUtils.isAdmin(currentUser.getUserId())) {
            return getAllWarehouseIds();
        }
        
        // 否则获取用户的仓库权限
        return getWarehouseAndChildIdsByUserId(currentUser.getUserId());
    }

    /**
     * 获取所有仓库ID列表
     *
     * @return 所有仓库ID列表
     */
    @Override
    public List<Long> getAllWarehouseIds() {
        // 这里应该调用仓库服务获取所有仓库ID
        // 简化实现，返回空列表
        return new ArrayList<>();
    }

    /**
     * 获取用户可访问的仓库ID列表
     *
     * @param userId 用户ID
     * @return 仓库ID列表
     */
    @Override
    public List<Long> getUserWarehouseScope(Long userId) {
        return getWarehouseAndChildIdsByUserId(userId);
    }

    /**
     * 构建仓库数据权限SQL过滤条件
     *
     * @param userId 用户ID
     * @param warehouseAlias 仓库表别名
     * @return SQL过滤条件
     */
    public String getWarehouseScopeSQL(Long userId, String warehouseAlias) {
        // 如果是管理员，不需要过滤
        if (SecurityUtils.isAdmin(userId)) {
            return "";
        }
        
        // 获取用户可访问的仓库ID列表
        List<Long> warehouseIds = getWarehouseAndChildIdsByUserId(userId);
        if (warehouseIds.isEmpty()) {
            // 如果没有权限，返回一个不可能满足的条件
            return warehouseAlias + ".warehouse_id = -1";
        }
        
        // 构建IN条件
        String warehouseIdStr = StringUtils.join(warehouseIds, ",");
        return warehouseAlias + ".warehouse_id IN (" + warehouseIdStr + ")";
    }

    /**
     * 获取用户在指定仓库类型上的数据权限
     *
     * @param userId 用户ID
     * @param warehouseType 仓库类型
     * @return 仓库ID列表
     */
    public List<Long> getUserWarehouseScopeByType(Long userId, String warehouseType) {
        // 获取用户所有可访问的仓库
        List<Long> allWarehouseIds = getWarehouseAndChildIdsByUserId(userId);
        
        // 过滤出指定类型的仓库
        // 这里应该调用仓库服务根据类型过滤仓库
        // 简化实现，假设所有仓库都符合条件
        return new ArrayList<>(allWarehouseIds);
    }

    /**
     * 获取用户的仓库数据权限映射
     *
     * @param userId 用户ID
     * @return 仓库权限映射（仓库ID -> 权限级别）
     */
    public Map<Long, Integer> getUserWarehousePermissionMap(Long userId) {
        // 获取用户角色
        List<SysUserRole> userRoles = userRoleMapper.selectUserRoleByUserId(userId);
        if (userRoles.isEmpty()) {
            return new HashMap<>();
        }
        
        // 获取每个角色的仓库权限，并合并
        Map<Long, Integer> permissionMap = new HashMap<>();
        for (SysUserRole userRole : userRoles) {
            SysRole role = roleMapper.selectRoleById(userRole.getRoleId());
            if (role != null) {
                List<Long> roleWarehouseIds = roleWarehouseService.selectRoleWarehouseAndChildrenByRoleId(role.getRoleId());
                int permissionLevel = getPermissionLevelByDataScope(role.getDataScope());
                for (Long warehouseId : roleWarehouseIds) {
                    // 如果已存在权限，取最高级别
                    permissionMap.put(warehouseId, Math.max(permissionLevel, permissionMap.getOrDefault(warehouseId, 0)));
                }
            }
        }
        
        return permissionMap;
    }

    /**
     * 根据数据范围获取权限级别
     *
     * @param dataScope 数据范围
     * @return 权限级别
     */
    private int getPermissionLevelByDataScope(String dataScope) {
        if (StringUtils.isEmpty(dataScope)) {
            return 0;
        }
        switch (dataScope) {
            case "1": // 全部数据权限
                return 3;
            case "2": // 自定义数据权限
                return 2;
            case "3": // 本部门数据权限
                return 1;
            case "4": // 本部门及以下数据权限
                return 1;
            case "5": // 仅本人数据权限
                return 0;
            default:
                return 0;
        }
    }


    /**
     * 获取用户在指定仓库的权限级别
     *
     * @param userId 用户ID
     * @param warehouseId 仓库ID
     * @return 权限级别（0-无权限，1-查看，2-编辑，3-管理）
     */
    @Override
    public int getUserWarehousePermissionLevel(Long userId, Long warehouseId) {
        Map<Long, Integer> permissionMap = getUserWarehousePermissionMap(userId);
        return permissionMap.getOrDefault(warehouseId, 0);
    }
}