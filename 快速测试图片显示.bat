@echo off
chcp 65001
echo ========================================
echo 快速测试图片显示功能
echo ========================================

echo 步骤1: 检查图片目录...
if exist "C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13" (
    echo ✅ 图片目录存在
) else (
    echo ❌ 图片目录不存在，正在创建...
    mkdir "C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13"
)

echo 步骤2: 检查测试图片文件...
if exist "C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13\屏幕截图 2025-05-15 171140_20250813231048A002.png" (
    echo ✅ 测试图片文件存在
) else (
    echo ❌ 测试图片文件不存在，正在创建占位文件...
    echo. > "C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13\屏幕截图 2025-05-15 171140_20250813231048A002.png"
)

echo 步骤3: 测试后端服务状态...
curl -s -o nul -w "后端服务状态: %%{http_code}" http://localhost:8080/product/info/list
echo.

echo 步骤4: 测试图片资源访问...
curl -s -o nul -w "图片资源状态: %%{http_code}" "http://localhost:8080/profile/upload/2025/08/13/屏幕截图 2025-05-15 171140_20250813231048A002.png"
echo.

echo 步骤5: 获取物品信息数据...
echo 正在获取物品信息...
curl -X GET "http://localhost:8080/product/info/list?pageNum=1&pageSize=3" -H "Content-Type: application/json" -s | findstr "imageUrl"
echo.

echo ========================================
echo 测试完成
echo ========================================
echo.
echo 如果看到以下结果说明配置正确：
echo - 后端服务状态: 200
echo - 图片资源状态: 200 或 404（404表示文件不存在但路径正确）
echo - 能看到包含 imageUrl 的JSON数据
echo.
echo 接下来请：
echo 1. 在浏览器中访问 http://localhost:8081/product/info
echo 2. 检查物品列表中的图片是否显示
echo 3. 如果显示空白，请上传一张真实的图片文件
echo.
pause