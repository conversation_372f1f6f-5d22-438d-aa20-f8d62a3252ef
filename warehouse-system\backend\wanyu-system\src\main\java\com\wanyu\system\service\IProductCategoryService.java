package com.wanyu.system.service;

import java.util.List;
import com.wanyu.system.domain.ProductCategory;

/**
 * 物品分类Service接口
 * 
 * <AUTHOR>
 */
public interface IProductCategoryService 
{
    /**
     * 查询物品分类
     * 
     * @param categoryId 物品分类主键
     * @return 物品分类
     */
    public ProductCategory selectProductCategoryByCategoryId(Long categoryId);

    /**
     * 查询物品分类列表
     * 
     * @param productCategory 物品分类
     * @return 物品分类集合
     */
    public List<ProductCategory> selectProductCategoryList(ProductCategory productCategory);

    /**
     * 构建前端所需要树结构
     * 
     * @param list 物品分类列表
     * @return 树结构列表
     */
    public List<ProductCategory> buildCategoryTree(List<ProductCategory> list);

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param list 物品分类列表
     * @return 下拉树结构列表
     */
    public List<ProductCategory> buildCategoryTreeSelect(List<ProductCategory> list);

    /**
     * 查询物品分类树列表
     * 
     * @return 所有物品分类信息
     */
    public List<ProductCategory> selectProductCategoryTreeList();

    /**
     * 新增物品分类
     * 
     * @param productCategory 物品分类
     * @return 结果
     */
    public int insertProductCategory(ProductCategory productCategory);

    /**
     * 修改物品分类
     * 
     * @param productCategory 物品分类
     * @return 结果
     */
    public int updateProductCategory(ProductCategory productCategory);

    /**
     * 批量删除物品分类
     * 
     * @param categoryIds 需要删除的物品分类主键集合
     * @return 结果
     */
    public int deleteProductCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 删除物品分类信息
     * 
     * @param categoryId 物品分类主键
     * @return 结果
     */
    public int deleteProductCategoryByCategoryId(Long categoryId);
}
