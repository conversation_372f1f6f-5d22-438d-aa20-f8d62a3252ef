@echo off
echo 部署WMS物品属性表命名统一修复...
echo.

echo ========================================
echo 当前问题：
echo - wms_product_attribute 命名不一致
echo - wms_product_attribute_option 命名不一致
echo.
echo 修复方案：
echo - wms_product_attribute → wms_attribute
echo - wms_product_attribute_option → wms_attribute_option
echo ========================================
echo.

echo 1. 执行数据库表重命名...
mysql -h localhost -u root -p123456 warehouse_system < fix_wms_attribute_table_naming.sql
if %errorlevel% neq 0 (
    echo 数据库表重命名失败！
    pause
    exit /b 1
)

echo 2. 更新后端代码中的表名引用...
call update_attribute_backend_code.bat

echo 3. 验证修复结果...
echo 检查新表是否存在...
mysql -h localhost -u root -p123456 warehouse_system -e "SHOW TABLES LIKE 'wms_attribute%';"

echo 检查数据是否完整...
mysql -h localhost -u root -p123456 warehouse_system -e "SELECT COUNT(*) as attribute_count FROM wms_attribute;"
mysql -h localhost -u root -p123456 warehouse_system -e "SELECT COUNT(*) as option_count FROM wms_attribute_option;"

echo 4. 停止后端服务...
cd /d "C:\CKGLXT\warehouse-system\backend"
taskkill /f /im java.exe 2>nul
timeout /t 3 >nul

echo 5. 重新编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 项目编译失败！请检查代码更新是否正确
    pause
    exit /b 1
)

echo 6. 启动后端服务...
start "后端服务" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"

echo 7. 等待服务启动...
timeout /t 15 >nul

echo 8. 测试物品属性API...
cd /d "C:\CKGLXT"
echo 测试获取物品属性列表...
curl -X GET "http://localhost:8080/product/attribute/list" -H "Content-Type: application/json"
echo.
echo.

echo.
echo ========================================
echo WMS物品属性表命名统一修复完成！
echo.
echo 修复内容：
echo ✅ 表名统一：wms_product_attribute → wms_attribute
echo ✅ 选项表统一：wms_product_attribute_option → wms_attribute_option  
echo ✅ 后端代码已更新表名引用
echo ✅ 数据完整性已验证
echo ✅ 外键约束已重建
echo.
echo 命名规范现在完全一致：
echo - wms_product（物品表）
echo - wms_category（分类表）
echo - wms_specification（规格表）
echo - wms_unit（单位表）
echo - wms_barcode（条码表）
echo - wms_attribute（属性表）✨ 新命名
echo - wms_attribute_option（属性选项表）✨ 新命名
echo.
echo 所有表名现在都遵循简洁统一的命名规范！
echo ========================================
pause