-- ===================================================================
-- 修复库存日志页面数据显示问题的完整解决方案
-- 解决操作类型显示1和2、仓库名称、物品信息、关联单据等字段显示问题
-- ===================================================================

-- 1. 检查当前数据状态
-- ===================================================================

-- 查看当前表结构
SELECT 
  COLUMN_NAME as '字段名',
  DATA_TYPE as '数据类型',
  IS_NULLABLE as '允许空值',
  COLUMN_DEFAULT as '默认值',
  COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'wms_inventory_log'
ORDER BY ORDINAL_POSITION;

-- 查看当前数据样例
SELECT 
  log_id,
  operation_type,
  warehouse_id,
  warehouse_name,
  product_id,
  product_name,
  product_code,
  product_spec,
  product_unit,
  quantity,
  operator,
  operation_time,
  related_order_id
FROM wms_inventory_log 
ORDER BY log_id DESC 
LIMIT 5;

-- 2. 添加缺失字段（如果不存在）
-- ===================================================================

-- 添加仓库名称字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'warehouse_name') > 0,
    'SELECT "Column warehouse_name already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN warehouse_name varchar(200) DEFAULT \'\' COMMENT \'仓库名称\' AFTER warehouse_id'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加物品名称字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'product_name') > 0,
    'SELECT "Column product_name already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN product_name varchar(200) DEFAULT \'\' COMMENT \'物品名称\' AFTER product_id'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加物品编码字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'product_code') > 0,
    'SELECT "Column product_code already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN product_code varchar(100) DEFAULT \'\' COMMENT \'物品编码\' AFTER product_name'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加物品规格字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'product_spec') > 0,
    'SELECT "Column product_spec already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN product_spec varchar(200) DEFAULT \'\' COMMENT \'物品规格\' AFTER product_code'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加物品单位字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'product_unit') > 0,
    'SELECT "Column product_unit already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN product_unit varchar(50) DEFAULT \'\' COMMENT \'物品单位\' AFTER product_spec'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加其他可能缺失的字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'unit_price') > 0,
    'SELECT "Column unit_price already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN unit_price decimal(10,2) DEFAULT 0.00 COMMENT \'单价\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'total_amount') > 0,
    'SELECT "Column total_amount already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN total_amount decimal(10,2) DEFAULT 0.00 COMMENT \'总金额\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'location_code') > 0,
    'SELECT "Column location_code already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN location_code varchar(50) DEFAULT \'\' COMMENT \'库位编码\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'location_name') > 0,
    'SELECT "Column location_name already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN location_name varchar(100) DEFAULT \'\' COMMENT \'库位名称\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 修改操作类型字段
-- ===================================================================

-- 修改操作类型字段类型，支持字符串
ALTER TABLE wms_inventory_log 
MODIFY COLUMN operation_type varchar(20) NOT NULL DEFAULT 'IN' 
COMMENT '操作类型(IN-入库,OUT-出库,TRANSFER-调拨,ADJUST-调整,CHECK-盘点)';

-- 4. 更新现有数据的操作类型
-- ===================================================================

-- 将数字类型的操作类型转换为字符串类型
UPDATE wms_inventory_log 
SET operation_type = CASE 
    WHEN operation_type = '1' THEN 'IN'
    WHEN operation_type = '2' THEN 'OUT'
    WHEN operation_type = '3' THEN 'TRANSFER'
    WHEN operation_type = '4' THEN 'ADJUST'
    WHEN operation_type = '5' THEN 'CHECK'
    ELSE operation_type
END
WHERE operation_type IN ('1', '2', '3', '4', '5');

-- 5. 填充仓库信息
-- ===================================================================

-- 更新仓库名称（从sys_warehouse表获取）
UPDATE wms_inventory_log l 
LEFT JOIN sys_warehouse w ON l.warehouse_id = w.warehouse_id 
SET l.warehouse_name = COALESCE(w.warehouse_name, CONCAT('仓库-', l.warehouse_id))
WHERE l.warehouse_name IS NULL OR l.warehouse_name = '' OR l.warehouse_name = '未知仓库';

-- 6. 填充物品信息
-- ===================================================================

-- 检查物品表是否存在
SET @table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'wms_product');

-- 如果wms_product表存在，从该表更新物品信息
SET @sql = (SELECT IF(@table_exists > 0,
    'UPDATE wms_inventory_log l 
     LEFT JOIN wms_product p ON l.product_id = p.product_id 
     LEFT JOIN wms_specification ps ON p.spec_id = ps.spec_id
     LEFT JOIN wms_unit pu ON p.unit_id = pu.unit_id
     SET 
       l.product_name = COALESCE(p.product_name, CONCAT(\'物品-\', l.product_id)),
       l.product_code = COALESCE(p.product_code, \'\'),
       l.product_spec = COALESCE(ps.spec_name, ps.specification, \'\'),
       l.product_unit = COALESCE(pu.unit_name, pu.unit_code, \'\')
     WHERE l.product_name IS NULL OR l.product_name = \'\' OR l.product_name LIKE \'物品-%\'',
    'SELECT "wms_product table not found, skipping product info update" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果wms_product表不存在，检查是否有其他物品表
SET @alt_table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_SCHEMA = DATABASE() 
                        AND TABLE_NAME IN ('wms_product', 'product_info', 'product'));

-- 尝试从备用物品表更新信息
SET @sql = (SELECT IF(@alt_table_exists > 0 AND @table_exists = 0,
    CONCAT('UPDATE wms_inventory_log l 
            LEFT JOIN ', 
            (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
             WHERE TABLE_SCHEMA = DATABASE() 
             AND TABLE_NAME IN ('wms_product', 'product_info', 'product') 
             LIMIT 1),
            ' p ON l.product_id = p.product_id 
            SET 
              l.product_name = COALESCE(p.product_name, CONCAT(\'物品-\', l.product_id)),
              l.product_code = COALESCE(p.product_code, \'\'),
              l.product_spec = COALESCE(p.product_spec, p.specification, \'\'),
              l.product_unit = COALESCE(p.product_unit, p.unit, \'\')
            WHERE l.product_name IS NULL OR l.product_name = \'\' OR l.product_name LIKE \'物品-%\''),
    'SELECT "No alternative product table found" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 为没有物品信息的记录设置默认值
-- ===================================================================

UPDATE wms_inventory_log 
SET 
  product_name = CONCAT('物品-', product_id),
  product_code = CONCAT('CODE-', product_id),
  product_spec = '标准规格',
  product_unit = '个'
WHERE (product_name IS NULL OR product_name = '') 
  AND product_id IS NOT NULL;

-- 8. 处理关联单据信息
-- ===================================================================

-- 确保related_order_id字段存在且类型正确
ALTER TABLE wms_inventory_log 
MODIFY COLUMN related_order_id varchar(100) DEFAULT '' COMMENT '关联单据号';

-- 添加关联单据类型字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'wms_inventory_log' 
     AND COLUMN_NAME = 'related_order_type') > 0,
    'SELECT "Column related_order_type already exists" as message',
    'ALTER TABLE wms_inventory_log ADD COLUMN related_order_type varchar(20) DEFAULT \'\' COMMENT \'关联单据类型\' AFTER related_order_id'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为没有关联单据的记录生成模拟数据（可选）
UPDATE wms_inventory_log 
SET 
  related_order_id = CASE 
    WHEN operation_type = 'IN' THEN CONCAT('IN', DATE_FORMAT(operation_time, '%Y%m%d'), LPAD(log_id, 4, '0'))
    WHEN operation_type = 'OUT' THEN CONCAT('OUT', DATE_FORMAT(operation_time, '%Y%m%d'), LPAD(log_id, 4, '0'))
    WHEN operation_type = 'TRANSFER' THEN CONCAT('TF', DATE_FORMAT(operation_time, '%Y%m%d'), LPAD(log_id, 4, '0'))
    ELSE CONCAT('OP', DATE_FORMAT(operation_time, '%Y%m%d'), LPAD(log_id, 4, '0'))
  END,
  related_order_type = CASE 
    WHEN operation_type = 'IN' THEN 'PURCHASE'
    WHEN operation_type = 'OUT' THEN 'SALES'
    WHEN operation_type = 'TRANSFER' THEN 'TRANSFER'
    ELSE 'OTHER'
  END
WHERE (related_order_id IS NULL OR related_order_id = '') 
  AND operation_time IS NOT NULL;

-- 9. 创建索引优化查询性能
-- ===================================================================

-- 操作类型索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_operation_type') > 0,
    'SELECT "Index idx_operation_type already exists" as message',
    'CREATE INDEX idx_operation_type ON wms_inventory_log(operation_type)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 仓库ID索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_warehouse_id') > 0,
    'SELECT "Index idx_warehouse_id already exists" as message',
    'CREATE INDEX idx_warehouse_id ON wms_inventory_log(warehouse_id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 物品ID索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_product_id') > 0,
    'SELECT "Index idx_product_id already exists" as message',
    'CREATE INDEX idx_product_id ON wms_inventory_log(product_id)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 操作时间索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_operation_time') > 0,
    'SELECT "Index idx_operation_time already exists" as message',
    'CREATE INDEX idx_operation_time ON wms_inventory_log(operation_time)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 复合索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'wms_inventory_log' 
     AND index_name = 'idx_warehouse_operation_time') > 0,
    'SELECT "Index idx_warehouse_operation_time already exists" as message',
    'CREATE INDEX idx_warehouse_operation_time ON wms_inventory_log(warehouse_id, operation_time)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 10. 验证修复结果
-- ===================================================================

-- 检查表结构
SELECT '=== 表结构检查 ===' as '检查项目';
SELECT 
  COLUMN_NAME as '字段名',
  DATA_TYPE as '数据类型',
  IS_NULLABLE as '允许空值',
  COLUMN_DEFAULT as '默认值',
  COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'wms_inventory_log'
  AND COLUMN_NAME IN ('operation_type', 'warehouse_name', 'product_name', 'product_code', 'product_spec', 'product_unit', 'related_order_id', 'related_order_type')
ORDER BY ORDINAL_POSITION;

-- 检查数据完整性
SELECT '=== 数据完整性检查 ===' as '检查项目';
SELECT 
  '总记录数' as 检查项,
  COUNT(*) as 数量,
  '条' as 单位
FROM wms_inventory_log
UNION ALL
SELECT 
  '操作类型为字符串的记录数' as 检查项,
  COUNT(*) as 数量,
  '条' as 单位
FROM wms_inventory_log 
WHERE operation_type IN ('IN', 'OUT', 'TRANSFER', 'ADJUST', 'CHECK')
UNION ALL
SELECT 
  '有仓库名称的记录数' as 检查项,
  COUNT(*) as 数量,
  '条' as 单位
FROM wms_inventory_log 
WHERE warehouse_name IS NOT NULL AND warehouse_name != ''
UNION ALL
SELECT 
  '有物品名称的记录数' as 检查项,
  COUNT(*) as 数量,
  '条' as 单位
FROM wms_inventory_log 
WHERE product_name IS NOT NULL AND product_name != ''
UNION ALL
SELECT 
  '有关联单据的记录数' as 检查项,
  COUNT(*) as 数量,
  '条' as 单位
FROM wms_inventory_log 
WHERE related_order_id IS NOT NULL AND related_order_id != '';

-- 操作类型分布统计
SELECT '=== 操作类型分布 ===' as '检查项目';
SELECT 
  operation_type as '操作类型',
  COUNT(*) as '记录数量',
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM wms_inventory_log), 2) as '占比(%)'
FROM wms_inventory_log 
GROUP BY operation_type
ORDER BY COUNT(*) DESC;

-- 显示修复后的样例数据
SELECT '=== 修复后样例数据 ===' as '检查项目';
SELECT 
  log_id as '日志ID',
  operation_type as '操作类型',
  warehouse_name as '仓库名称',
  product_name as '物品名称',
  product_code as '物品编码',
  product_spec as '物品规格',
  product_unit as '物品单位',
  quantity as '操作数量',
  operator as '操作人员',
  DATE_FORMAT(operation_time, '%Y-%m-%d %H:%i:%s') as '操作时间',
  related_order_id as '关联单据',
  related_order_type as '单据类型'
FROM wms_inventory_log 
ORDER BY operation_time DESC 
LIMIT 10;

-- 检查索引创建情况
SELECT '=== 索引检查 ===' as '检查项目';
SELECT 
  INDEX_NAME as '索引名称',
  COLUMN_NAME as '字段名',
  SEQ_IN_INDEX as '序号'
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'wms_inventory_log'
  AND INDEX_NAME != 'PRIMARY'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

SELECT '库存日志数据修复完成！前端页面应该能正确显示所有字段信息。' as message;