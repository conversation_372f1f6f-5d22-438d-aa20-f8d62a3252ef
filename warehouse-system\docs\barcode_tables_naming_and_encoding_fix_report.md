# 条码表命名规范检查和乱码修复报告

## 1. 概述

本文档记录了对 `wms_barcode` 和 `wms_barcode_template` 两个表的命名规范检查和中文乱码问题修复的过程和结果。

## 2. 表命名规范检查

根据项目命名规范文档 [命名规范.md](./命名规范.md) 中的数据库命名规范：

### 2.1 表命名规范要求

- 使用小写字母，单词间用下划线分隔
- 使用模块前缀区分不同模块的表
- 表名应当使用名词或名词短语
- 表名应当清晰表达表的用途和内容

### 2.2 检查结果

| 表名 | 是否符合规范 | 说明 |
|------|-------------|------|
| `wms_barcode` | ✅ 符合 | 使用小写字母和下划线，表名清晰表达了用途 |
| `wms_barcode_template` | ✅ 符合 | 使用小写字母和下划线，表名清晰表达了用途 |

这两个表的命名符合项目规范，使用了清晰的英文单词描述表的用途，且符合小写字母+下划线的命名约定。

## 3. 中文乱码问题修复

### 3.1 问题描述

在检查数据库表时发现，`wms_barcode` 和 `wms_barcode_template` 表的表注释和字段注释存在中文乱码问题，显示为类似 `鐗╁搧鏉＄爜琛` 的乱码字符。

### 3.2 问题原因

乱码问题是由于数据库字符集设置不当导致的。虽然表结构使用了 `utf8mb4` 字符集，但在执行 DDL 语句时，客户端字符集设置不正确，导致中文注释被错误地编码存储。

### 3.3 修复过程

1. 创建修复脚本 [fix_barcode_table_charset_v3.sql](../../fix_barcode_table_charset_v3.sql)：
   - 设置正确的数据库字符集
   - 重新定义表和字段注释，使用正确的中文字符

2. 使用正确的字符集参数执行修复脚本：
   ```bash
   mysql -u root -p123456 --default-character-set=utf8mb4 -e "source c:/CKGLXT/fix_barcode_table_charset_v3.sql;"
   ```

### 3.4 修复验证

执行修复后，重新检查表结构：

```sql
SHOW CREATE TABLE wms_barcode\G
SHOW CREATE TABLE wms_barcode_template\G
```

确认表注释和字段注释均已正确显示为中文：

#### wms_barcode 表修复后注释：
- 表注释：`物品条码表`
- 字段注释：`条码ID`、`物品ID`、`物品名称`、`条码内容` 等

#### wms_barcode_template 表修复后注释：
- 表注释：`物品条码模板表`
- 字段注释：`模板ID`、`模板名称`、`模板类型`、`模板宽度(mm)` 等

## 4. 结论

1. **命名规范**：两个表的命名均符合项目规范，无需修改。
2. **乱码修复**：通过正确的字符集设置和重新定义注释，已成功修复中文乱码问题。
3. **后续建议**：在执行涉及中文字符的数据库操作时，应始终使用 `--default-character-set=utf8mb4` 参数确保字符集一致性。

## 5. 附件

- 修复脚本：[fix_barcode_table_charset_v3.sql](../../fix_barcode_table_charset_v3.sql)
- 命名规范文档：[命名规范.md](./命名规范.md)