# 仓库管理系统字段定义标准化需求文档

## 项目信息
- **项目路径**: C:\CKGLXT\warehouse-system
- **后端路径**: C:\CKGLXT\warehouse-system\backend
- **前端路径**: C:\CKGLXT\warehouse-system\frontend
- **数据库**: warehouse_system
- **数据库用户**: root
- **数据库密码**: 123456
- **后端端口**: 8080
- **前端端口**: 8081

## 需求概述

本规格文档定义了仓库管理系统中字段定义标准化的需求，重点解决项目中不合理的字段定义问题，特别是状态字段的逻辑不一致问题。通过统一字段定义标准，确保整个系统的数据逻辑一致性和可维护性。

## 核心问题分析

### 当前发现的问题字段

1. **sys_license 表的 status 字段**
   - 当前定义：0=禁用，1=启用
   - 项目规范：0=启用，1=禁用

2. **sys_license_feature 表的 status 字段**
   - 当前定义：0=禁用，1=启用
   - 项目规范：0=启用，1=禁用

3. **WmsOperationLogMapper.xml 中的 operation_status 字段**
   - 当前定义：0=成功，1=失败
   - 项目规范：0=正常，1=异常

4. **system_log_table.sql 中的 operation_status 字段**
   - 需要与项目规范保持一致

## 标准化需求

### 需求1：建立统一的字段定义标准

**用户故事**: 作为开发人员，我希望系统中所有状态字段都遵循统一的定义标准，这样我就可以避免逻辑混乱和理解错误。

#### 验收标准
1. WHEN 定义状态字段时 THEN 必须遵循"0=正常/启用/成功，1=异常/禁用/失败"的标准
2. WHEN 定义布尔类型字段时 THEN 必须遵循"0=否/关闭，1=是/开启"的标准
3. WHEN 创建新表时 THEN 所有状态相关字段都应该使用标准定义
4. WHEN 修改现有字段时 THEN 必须确保与项目标准保持一致
5. WHEN 编写业务代码时 THEN 必须按照标准定义进行逻辑判断

### 需求2：修复sys_license表的status字段

**用户故事**: 作为系统管理员，我希望许可证状态字段的定义与项目标准一致，这样我就可以正确理解和管理许可证状态。

#### 验收标准
1. WHEN 查看许可证状态时 THEN 0应该表示启用状态，1应该表示禁用状态
2. WHEN 更新许可证状态时 THEN 业务逻辑应该正确处理新的字段定义
3. WHEN 查询启用的许可证时 THEN 应该使用WHERE status = '0'条件
4. WHEN 查询禁用的许可证时 THEN 应该使用WHERE status = '1'条件
5. WHEN 数据迁移完成后 THEN 现有数据的状态值应该正确反映实际状态

### 需求3：修复sys_license_feature表的status字段

**用户故事**: 作为系统管理员，我希望许可证功能状态字段的定义与项目标准一致，这样我就可以正确管理功能的启用和禁用。

#### 验收标准
1. WHEN 查看功能状态时 THEN 0应该表示启用状态，1应该表示禁用状态
2. WHEN 启用功能时 THEN 系统应该将status设置为'0'
3. WHEN 禁用功能时 THEN 系统应该将status设置为'1'
4. WHEN 查询可用功能时 THEN 应该使用WHERE status = '0'条件
5. WHEN 数据迁移完成后 THEN 所有功能状态应该正确反映实际可用性

### 需求4：修复操作日志中的operation_status字段

**用户故事**: 作为系统管理员，我希望操作日志中的状态字段定义与项目标准一致，这样我就可以正确分析操作的成功和失败情况。

#### 验收标准
1. WHEN 记录操作成功时 THEN operation_status应该设置为'0'
2. WHEN 记录操作失败时 THEN operation_status应该设置为'1'
3. WHEN 查询成功操作时 THEN 应该使用WHERE operation_status = '0'条件
4. WHEN 查询失败操作时 THEN 应该使用WHERE operation_status = '1'条件
5. WHEN 统计操作成功率时 THEN 计算逻辑应该基于新的字段定义

### 需求5：更新相关业务代码

**用户故事**: 作为开发人员，我希望所有与状态字段相关的业务代码都能正确处理新的字段定义，这样系统就能正常运行而不会出现逻辑错误。

#### 验收标准
1. WHEN 查询启用状态的记录时 THEN 所有相关代码都应该使用status = '0'条件
2. WHEN 设置记录为启用状态时 THEN 所有相关代码都应该设置status = '0'
3. WHEN 处理操作结果时 THEN 成功状态应该使用'0'，失败状态应该使用'1'
4. WHEN 前端显示状态时 THEN 显示逻辑应该正确解释状态值的含义
5. WHEN 执行数据验证时 THEN 验证逻辑应该基于新的字段定义

### 需求6：执行数据迁移

**用户故事**: 作为数据库管理员，我希望能够安全地迁移现有数据以符合新的字段定义标准，这样现有业务数据就不会丢失或出现错误。

#### 验收标准
1. WHEN 执行数据迁移前 THEN 必须创建完整的数据备份
2. WHEN 迁移sys_license表数据时 THEN 原来的0变为1，原来的1变为0
3. WHEN 迁移sys_license_feature表数据时 THEN 原来的0变为1，原来的1变为0
4. WHEN 迁移操作日志数据时 THEN 状态值应该保持原有的成功/失败含义
5. WHEN 数据迁移完成后 THEN 必须验证数据的完整性和正确性

### 需求7：更新数据库表结构和注释

**用户故事**: 作为数据库管理员，我希望数据库表结构和字段注释能够准确反映新的字段定义标准，这样其他开发人员就能正确理解字段含义。

#### 验收标准
1. WHEN 更新表结构时 THEN 所有状态字段的注释都应该明确说明0和1的含义
2. WHEN 创建新表时 THEN 状态字段应该有默认值'0'（表示正常/启用状态）
3. WHEN 修改字段注释时 THEN 注释应该使用标准格式："状态（0正常 1停用）"
4. WHEN 生成数据字典时 THEN 字典应该准确反映字段的标准定义
5. WHEN 开发人员查看表结构时 THEN 应该能够清楚理解每个状态值的含义

### 需求8：建立字段定义规范文档

**用户故事**: 作为项目团队成员，我希望有一个明确的字段定义规范文档，这样我就可以在开发过程中遵循统一的标准。

#### 验收标准
1. WHEN 创建新的状态字段时 THEN 必须参考规范文档确定字段定义
2. WHEN 修改现有字段时 THEN 必须确保符合规范文档的要求
3. WHEN 进行代码审查时 THEN 必须检查字段使用是否符合规范
4. WHEN 新团队成员加入时 THEN 必须学习和遵循字段定义规范
5. WHEN 规范文档更新时 THEN 必须通知所有相关开发人员

### 需求9：验证修复效果

**用户故事**: 作为质量保证人员，我希望能够全面验证字段定义标准化的修复效果，这样我就可以确保修复没有引入新的问题。

#### 验收标准
1. WHEN 执行功能测试时 THEN 所有涉及状态字段的功能都应该正常工作
2. WHEN 查看前端页面时 THEN 状态显示应该正确反映实际状态
3. WHEN 执行数据库查询时 THEN 查询结果应该符合预期
4. WHEN 进行回归测试时 THEN 现有功能不应该受到影响
5. WHEN 测试边界情况时 THEN 系统应该正确处理各种状态值

### 需求10：建立持续监控机制

**用户故事**: 作为系统维护人员，我希望建立持续监控机制来确保字段定义标准的一致性，这样我就可以及时发现和纠正不符合标准的情况。

#### 验收标准
1. WHEN 添加新的状态字段时 THEN 系统应该自动检查是否符合标准
2. WHEN 修改状态相关代码时 THEN 应该有代码审查流程确保符合标准
3. WHEN 发现不符合标准的字段时 THEN 应该有明确的修复流程
4. WHEN 进行定期检查时 THEN 应该能够快速识别所有状态字段的定义
5. WHEN 生成合规报告时 THEN 报告应该显示字段定义标准的遵循情况

## 字段定义标准

### 状态字段标准
- **通用状态字段**: `status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）'`
- **操作状态字段**: `operation_status CHAR(1) DEFAULT '0' COMMENT '操作状态（0成功 1失败）'`
- **审核状态字段**: `audit_status CHAR(1) DEFAULT '0' COMMENT '审核状态（0待审核 1已审核 2已拒绝）'`

### 布尔字段标准
- **是否字段**: `is_xxx CHAR(1) DEFAULT '0' COMMENT '是否xxx（0否 1是）'`
- **开关字段**: `enable_xxx CHAR(1) DEFAULT '0' COMMENT '是否启用xxx（0关闭 1开启）'`

### 删除标记标准
- **删除标记**: `del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）'`

## 技术约束

1. **数据完整性**: 修复过程中必须保证数据完整性，不能丢失业务数据
2. **向后兼容**: 修复后的系统必须能够正确处理历史数据
3. **性能影响**: 修复过程不能显著影响系统性能
4. **业务连续性**: 修复过程中系统应该能够继续提供服务
5. **回滚能力**: 必须具备快速回滚到修复前状态的能力

## 风险评估

### 高风险
- 数据迁移可能导致业务逻辑错误
- 状态值颠倒可能影响权限控制

### 中风险
- 代码修改可能引入新的bug
- 前端显示可能出现状态错误

### 低风险
- 数据库注释更新风险较低
- 规范文档建立风险较低

## 成功标准

1. **逻辑一致性**: 所有状态字段定义符合项目标准
2. **功能正确性**: 所有相关功能正常工作
3. **数据准确性**: 数据迁移后状态值正确反映实际情况
4. **代码质量**: 修复后的代码符合开发规范
5. **文档完整性**: 规范文档完整且易于理解

## 交付物

1. 字段定义标准化规范文档
2. 数据库结构修复脚本
3. 数据迁移脚本
4. 业务代码修复文件
5. 测试用例和测试报告
6. 部署和回滚指南