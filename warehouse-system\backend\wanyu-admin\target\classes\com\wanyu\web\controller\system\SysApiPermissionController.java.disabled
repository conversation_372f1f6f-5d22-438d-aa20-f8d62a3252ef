package com.wanyu.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.SysApiPermission;
import com.wanyu.system.service.ISysApiPermissionService;

/**
 * API权限管理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/api/permission")
public class SysApiPermissionController extends BaseController
{
    @Autowired
    private ISysApiPermissionService apiPermissionService;

    /**
     * 查询API权限列表
     */
    @PreAuthorize("@ss.hasPermi('system:permission:api')")
    @GetMapping("/list")
    public TableDataInfo list(SysApiPermission apiPermission)
    {
        startPage();
        List<SysApiPermission> list = apiPermissionService.selectApiPermissionList(apiPermission);
        return getDataTable(list);
    }

    /**
     * 导出API权限列表
     */
    @Log(title = "API权限管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:permission:api')")
    @GetMapping("/export")
    public AjaxResult export(SysApiPermission apiPermission)
    {
        List<SysApiPermission> list = apiPermissionService.selectApiPermissionList(apiPermission);
        ExcelUtil<SysApiPermission> util = new ExcelUtil<SysApiPermission>(SysApiPermission.class);
        return util.exportExcel(list, "API权限数据");
    }

    /**
     * 根据API权限ID获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:permission:api')")
    @GetMapping(value = "/{apiId}")
    public AjaxResult getInfo(@PathVariable Long apiId)
    {
        return success(apiPermissionService.selectApiPermissionById(apiId));
    }

    /**
     * 新增API权限
     */
    @PreAuthorize("@ss.hasPermi('system:permission:api')")
    @Log(title = "API权限管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysApiPermission apiPermission)
    {
        if (!apiPermissionService.checkApiPathUnique(apiPermission))
        {
            return error("新增API权限'" + apiPermission.getApiPath() + "'失败，API路径已存在");
        }
        apiPermission.setCreateBy(getUsername());
        return toAjax(apiPermissionService.insertApiPermission(apiPermission));
    }

    /**
     * 修改API权限
     */
    @PreAuthorize("@ss.hasPermi('system:permission:api')")
    @Log(title = "API权限管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysApiPermission apiPermission)
    {
        if (!apiPermissionService.checkApiPathUnique(apiPermission))
        {
            return error("修改API权限'" + apiPermission.getApiPath() + "'失败，API路径已存在");
        }
        apiPermission.setUpdateBy(getUsername());
        return toAjax(apiPermissionService.updateApiPermission(apiPermission));
    }

    /**
     * 删除API权限
     */
    @PreAuthorize("@ss.hasPermi('system:permission:api')")
    @Log(title = "API权限管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{apiIds}")
    public AjaxResult remove(@PathVariable Long[] apiIds)
    {
        return toAjax(apiPermissionService.deleteApiPermissionByIds(apiIds));
    }

    /**
     * 获取所有API权限选择框列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<SysApiPermission> apiPermissions = apiPermissionService.selectApiPermissionsAll();
        return success(apiPermissions);
    }
}