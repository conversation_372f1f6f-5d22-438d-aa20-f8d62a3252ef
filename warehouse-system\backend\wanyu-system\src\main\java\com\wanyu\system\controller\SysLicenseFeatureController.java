package com.wanyu.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.system.domain.SysLicenseFeature;
import com.wanyu.system.service.ISysLicenseFeatureService;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;

/**
 * 功能权限Controller
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
@RestController
@RequestMapping("/system/feature")
public class SysLicenseFeatureController extends BaseController
{
    @Autowired
    private ISysLicenseFeatureService sysLicenseFeatureService;

    /**
     * 查询功能权限列表
     */
    @PreAuthorize("@ss.hasPermi('system:feature:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysLicenseFeature sysLicenseFeature)
    {
        startPage();
        List<SysLicenseFeature> list = sysLicenseFeatureService.selectSysLicenseFeatureList(sysLicenseFeature);
        return getDataTable(list);
    }

    /**
     * 查询启用的功能权限列表
     */
    @PreAuthorize("@ss.hasPermi('system:feature:list')")
    @GetMapping("/enabled")
    public AjaxResult listEnabled()
    {
        List<SysLicenseFeature> list = sysLicenseFeatureService.selectEnabledFeatures();
        return AjaxResult.success(list);
    }

    /**
     * 查询核心功能列表
     */
    @PreAuthorize("@ss.hasPermi('system:feature:list')")
    @GetMapping("/core")
    public AjaxResult listCore()
    {
        List<SysLicenseFeature> list = sysLicenseFeatureService.selectCoreFeatures();
        return AjaxResult.success(list);
    }

    /**
     * 根据授权类型查询可用功能
     */
    @PreAuthorize("@ss.hasPermi('system:feature:list')")
    @GetMapping("/license/{licenseType}")
    public AjaxResult listByLicenseType(@PathVariable String licenseType)
    {
        List<SysLicenseFeature> list = sysLicenseFeatureService.selectFeaturesByLicenseType(licenseType);
        return AjaxResult.success(list);
    }

    /**
     * 导出功能权限列表
     */
    @PreAuthorize("@ss.hasPermi('system:feature:export')")
    @Log(title = "功能权限", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLicenseFeature sysLicenseFeature)
    {
        List<SysLicenseFeature> list = sysLicenseFeatureService.selectSysLicenseFeatureList(sysLicenseFeature);
        ExcelUtil<SysLicenseFeature> util = new ExcelUtil<SysLicenseFeature>(SysLicenseFeature.class);
        util.exportExcel(response, list, "功能权限数据");
    }

    /**
     * 获取功能权限详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:feature:query')")
    @GetMapping(value = "/{featureId}")
    public AjaxResult getInfo(@PathVariable("featureId") Long featureId)
    {
        return AjaxResult.success(sysLicenseFeatureService.selectSysLicenseFeatureByFeatureId(featureId));
    }

    /**
     * 根据功能代码获取功能权限详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:feature:query')")
    @GetMapping(value = "/code/{featureCode}")
    public AjaxResult getInfoByCode(@PathVariable("featureCode") String featureCode)
    {
        return AjaxResult.success(sysLicenseFeatureService.selectSysLicenseFeatureByCode(featureCode));
    }

    /**
     * 检查功能是否可用
     */
    @PreAuthorize("@ss.hasPermi('system:feature:query')")
    @GetMapping(value = "/available/{featureCode}")
    public AjaxResult checkAvailable(@PathVariable("featureCode") String featureCode)
    {
        boolean available = sysLicenseFeatureService.isFeatureAvailable(featureCode);
        return AjaxResult.success("功能可用性检查完成", available);
    }

    /**
     * 新增功能权限
     */
    @PreAuthorize("@ss.hasPermi('system:feature:add')")
    @Log(title = "功能权限", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysLicenseFeature sysLicenseFeature)
    {
        return toAjax(sysLicenseFeatureService.insertSysLicenseFeature(sysLicenseFeature));
    }

    /**
     * 修改功能权限
     */
    @PreAuthorize("@ss.hasPermi('system:feature:edit')")
    @Log(title = "功能权限", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysLicenseFeature sysLicenseFeature)
    {
        return toAjax(sysLicenseFeatureService.updateSysLicenseFeature(sysLicenseFeature));
    }

    /**
     * 启用功能
     */
    @PreAuthorize("@ss.hasPermi('system:feature:edit')")
    @Log(title = "启用功能", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{featureId}")
    public AjaxResult enable(@PathVariable Long featureId)
    {
        return toAjax(sysLicenseFeatureService.enableFeature(featureId));
    }

    /**
     * 禁用功能
     */
    @PreAuthorize("@ss.hasPermi('system:feature:edit')")
    @Log(title = "禁用功能", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{featureId}")
    public AjaxResult disable(@PathVariable Long featureId)
    {
        return toAjax(sysLicenseFeatureService.disableFeature(featureId));
    }

    /**
     * 批量启用功能
     */
    @PreAuthorize("@ss.hasPermi('system:feature:edit')")
    @Log(title = "批量启用功能", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public AjaxResult enableBatch(@RequestBody Long[] featureIds)
    {
        return toAjax(sysLicenseFeatureService.enableFeatures(featureIds));
    }

    /**
     * 批量禁用功能
     */
    @PreAuthorize("@ss.hasPermi('system:feature:edit')")
    @Log(title = "批量禁用功能", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public AjaxResult disableBatch(@RequestBody Long[] featureIds)
    {
        return toAjax(sysLicenseFeatureService.disableFeatures(featureIds));
    }

    /**
     * 删除功能权限
     */
    @PreAuthorize("@ss.hasPermi('system:feature:remove')")
    @Log(title = "功能权限", businessType = BusinessType.DELETE)
	@DeleteMapping("/{featureIds}")
    public AjaxResult remove(@PathVariable Long[] featureIds)
    {
        return toAjax(sysLicenseFeatureService.deleteSysLicenseFeatureByFeatureIds(featureIds));
    }
}