package com.wanyu.framework.interceptor.impl;

import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;
import com.wanyu.common.annotation.RepeatSubmit;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.common.utils.http.HttpHelper;
import com.wanyu.common.utils.ip.IpUtils;
import com.wanyu.framework.interceptor.RepeatSubmitInterceptor;
import com.alibaba.fastjson2.JSON;

/**
 * 判断请求url和数据是否和上一次相同，
 * 如果和上次相同，则是重复提交表单。 有效时间为10秒内。
 * 
 * <AUTHOR>
 */
@Component
public class SameUrlDataInterceptor extends RepeatSubmitInterceptor
{
    private static final String REPEAT_PARAMS = "repeatParams";
    private static final String REPEAT_TIME = "repeatTime";

    // 用户缓存请求数据
    private static final Map<String, Object> CACHE_MAP = new HashMap<>();

    public final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    @Override
    public boolean isRepeatSubmit(HttpServletRequest request, RepeatSubmit annotation)
    {
        String nowParams = "";
        if (request.getMethod().equals("GET"))
        {
            nowParams = JSON.toJSONString(request.getParameterMap());
        }
        else
        {
            nowParams = JSON.toJSONString(HttpHelper.getBodyString(request));
        }
        // 请求地址（作为存放cache的key值）
        String url = request.getRequestURI();

        // 唯一值（没有消息头则使用IP）
        String submitKey = IpUtils.getIpAddr(request);

        // 唯一标识（指定key + url + 消息头）
        String cacheRepeatKey = REPEAT_SUBMIT_KEY + url + submitKey;

        Object sessionObj = CACHE_MAP.get(cacheRepeatKey);
        if (sessionObj != null)
        {
            Map<String, Object> sessionMap = (Map<String, Object>) sessionObj;
            if (sessionMap.containsKey(url))
            {
                Map<String, Object> preDataMap = (Map<String, Object>) sessionMap.get(url);
                Map<String, Object> nowDataMap = new HashMap<String, Object>();
                nowDataMap.put(REPEAT_PARAMS, nowParams);
                nowDataMap.put(REPEAT_TIME, System.currentTimeMillis());
                
                if (compareParams(nowDataMap, preDataMap) && compareTime(nowDataMap, preDataMap, annotation.interval()))
                {
                    return true;
                }
            }
        }
        Map<String, Object> nowDataMap = new HashMap<String, Object>();
        nowDataMap.put(REPEAT_PARAMS, nowParams);
        nowDataMap.put(REPEAT_TIME, System.currentTimeMillis());
        Map<String, Object> cacheMap = new HashMap<String, Object>();
        cacheMap.put(url, nowDataMap);
        CACHE_MAP.put(cacheRepeatKey, cacheMap);
        return false;
    }

    /**
     * 判断参数是否相同
     */
    private boolean compareParams(Map<String, Object> nowMap, Map<String, Object> preMap)
    {
        String nowParams = (String) nowMap.get(REPEAT_PARAMS);
        String preParams = (String) preMap.get(REPEAT_PARAMS);
        return nowParams.equals(preParams);
    }

    /**
     * 判断两次间隔时间
     */
    private boolean compareTime(Map<String, Object> nowMap, Map<String, Object> preMap, int interval)
    {
        long time1 = (Long) nowMap.get(REPEAT_TIME);
        long time2 = (Long) preMap.get(REPEAT_TIME);
        if ((time1 - time2) < interval)
        {
            return true;
        }
        return false;
    }
}