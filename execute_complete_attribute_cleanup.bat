@echo off
chcp 65001 >nul
echo ========================================
echo 彻底删除物品属性模块
echo ========================================

echo 1. 执行数据库清理...
mysql -u root -p123456 -D warehouse_system < complete_cleanup_product_attribute.sql
if %errorlevel% neq 0 (
    echo ❌ 数据库清理失败！
    pause
    exit /b 1
) else (
    echo ✅ 数据库清理完成
)

echo.
echo 2. 删除前端文件...

REM 删除前端属性页面文件夹
if exist "warehouse-system\frontend\src\views\product\attribute" (
    rmdir /s /q "warehouse-system\frontend\src\views\product\attribute"
    echo ✅ 删除前端属性页面文件夹
) else (
    echo ℹ️  前端属性页面文件夹不存在
)

REM 删除前端API文件
if exist "warehouse-system\frontend\src\api\product\attribute.js" (
    del "warehouse-system\frontend\src\api\product\attribute.js"
    echo ✅ 删除前端API文件
) else (
    echo ℹ️  前端API文件不存在
)

echo.
echo 3. 删除后端文件...

REM 删除后端product包
if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\product" (
    rmdir /s /q "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\product"
    echo ✅ 删除后端product包
) else (
    echo ℹ️  后端product包不存在
)

REM 删除后端product mapper
if exist "warehouse-system\backend\wanyu-system\src\main\resources\mapper\product" (
    rmdir /s /q "warehouse-system\backend\wanyu-system\src\main\resources\mapper\product"
    echo ✅ 删除后端product mapper
) else (
    echo ℹ️  后端product mapper不存在
)

REM 删除后端product controller
if exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\product" (
    rmdir /s /q "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\product"
    echo ✅ 删除后端product controller
) else (
    echo ℹ️  后端product controller不存在
)

echo.
echo 4. 清理相关脚本和文档文件...

REM 删除属性相关的脚本文件
for %%f in (
    "create_wms_product_attribute_tables.sql"
    "deploy_wms_product_attribute_complete.bat"
    "fix_product_attribute_dict.sql"
    "fix_attribute_display_complete.bat"
    "fix_attribute_display_simple.bat"
    "quick_fix_attribute.bat"
    "test_attribute_display.bat"
    "complete_attribute_fix.bat"
    "update_attribute_backend_code.bat"
    "fix_wms_attribute_table_naming.sql"
    "WMS物品属性功能完成报告.md"
    "物品属性页面显示问题修复报告.md"
    "属性显示问题修复完成.md"
    "remove_product_attribute_module.bat"
    "update_product_router.bat"
    "complete_remove_product_attribute.bat"
) do (
    if exist "%%f" (
        del "%%f"
        echo ✅ 删除文件: %%f
    )
)

echo.
echo 5. 验证清理结果...
mysql -u root -p123456 -D warehouse_system -e "
SELECT '最终验证 - 菜单' as item, COUNT(*) as count FROM sys_menu WHERE menu_name LIKE '%%物品属性%%' OR menu_name LIKE '%%属性%%';
SELECT '最终验证 - 字典类型' as item, COUNT(*) as count FROM sys_dict_type WHERE dict_type LIKE '%%attribute%%';
SELECT '最终验证 - 数据表' as item, COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'warehouse_system' AND table_name LIKE '%%attribute%%';
"

echo.
echo ========================================
echo 🎉 物品属性模块彻底删除完成！
echo ========================================
echo.
echo 已完成以下操作：
echo ✅ 删除所有属性相关菜单项和权限
echo ✅ 删除属性相关字典类型和数据
echo ✅ 删除所有属性相关数据表
echo ✅ 删除前端页面和API文件
echo ✅ 删除后端代码文件
echo ✅ 清理相关脚本和文档文件
echo ✅ 路由配置已更新
echo.
echo 💡 建议操作：
echo 1. 重启后端服务 (端口8080)
echo 2. 重启前端服务 (端口8081)
echo 3. 清除浏览器缓存
echo.
pause