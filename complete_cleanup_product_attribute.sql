-- 彻底删除物品属性模块相关的所有数据库内容
USE warehouse_system;

-- 1. 删除角色菜单关联
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE menu_name LIKE '%物品属性%' OR menu_name LIKE '%属性%'
);

-- 2. 删除所有属性相关菜单项
DELETE FROM sys_menu WHERE menu_id IN (4150, 4373, 4374, 4375, 4376, 4398, 4399, 4400, 4401, 4402);

-- 3. 删除字典数据
DELETE FROM sys_dict_data WHERE dict_type = 'product_attribute_type';
DELETE FROM sys_dict_type WHERE dict_type = 'product_attribute_type';

-- 4. 删除数据表（按依赖关系顺序删除）
DROP TABLE IF EXISTS wms_attribute_option;
DROP TABLE IF EXISTS wms_attribute;
DROP TABLE IF EXISTS wms_product_attribute_backup;
DROP TABLE IF EXISTS wms_product_attribute_option_backup;

-- 5. 验证删除结果
SELECT '菜单删除验证' as check_type, COUNT(*) as remaining_count 
FROM sys_menu WHERE menu_name LIKE '%物品属性%' OR menu_name LIKE '%属性%';

SELECT '字典类型删除验证' as check_type, COUNT(*) as remaining_count 
FROM sys_dict_type WHERE dict_type LIKE '%attribute%';

SELECT '字典数据删除验证' as check_type, COUNT(*) as remaining_count 
FROM sys_dict_data WHERE dict_type LIKE '%attribute%';

SELECT '数据表删除验证' as check_type, COUNT(*) as remaining_count 
FROM information_schema.tables 
WHERE table_schema = 'warehouse_system' AND table_name LIKE '%attribute%';

COMMIT;