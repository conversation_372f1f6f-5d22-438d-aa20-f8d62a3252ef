# 项目相关配置
wanyu:
  # 管理员用户ID
  admin-user-id: 2
  # 名称
  name: WanYu
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2024
  # 文件路径，统一为相对路径 warehouse-system/Pictures
  profile: warehouse-system/Pictures
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证码
  captchaType: math

# 开发环境配置
# 注意: 要启用HTTPS，请使用: --spring.profiles.active=prod,https
# HTTPS将在8443端口运行，需要先执行 generate-ssl-certificate.bat 生成证书
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
    # 设置编码
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.wanyu: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 应用程序名称
  application:
    name: warehouse-system
  # 允许bean定义覆盖，解决Redis配置类中重复定义redisTemplate的问题
  main:
    allow-bean-definition-overriding: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: prod
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # 禁用Redis配置
  # redis:
  #   # 地址
  #   host: localhost
  #   # 端口，默认为6379
  #   port: 6379
  #   # 数据库索引
  #   database: 0
  #   # 密码
  #   password:
  #   # 连接超时时间
  #   timeout: 3s
  #   # 是否启用Redis (自动检测Redis可用性)
  #   enabled: ${REDIS_ENABLED:false}
  #   # 连接池配置
  #   lettuce:
  #     pool:
  #       # 连接池中的最小空闲连接
  #       min-idle: 5
  #       # 连接池中的最大空闲连接
  #       max-idle: 20
  #       # 连接池的最大数据库连接数
  #       max-active: 50
  #       # 连接池最大阻塞等待时间（毫秒）
  #       max-wait: 3000ms
  #   # 添加健康检查
  #   health-check:
  #     enabled: true
  #     interval: 30s
  
  # 缓存配置 (使用内存缓存)
  cache:
    type: simple

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.wanyu.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*