# 后端路由更新总结

## 新增的菜单项及对应的控制器

### 1. 系统监控模块 (menu_id: 108)
- **在线用户** (menu_id: 109) - `/monitor/online`
  - 控制器: `SysUserOnlineController.java` ✅ 已存在
  - 主要功能: 查看在线用户、强制下线

- **定时任务** (menu_id: 110) - `/monitor/job`
  - 控制器: `SysJobController.java` ✅ 已存在并完整实现
  - 主要功能: 定时任务管理、执行、状态控制

- **服务监控** (menu_id: 111) - `/monitor/server`
  - 控制器: `ServerController.java` ✅ 已存在
  - 主要功能: 服务器性能监控

- **缓存监控** (menu_id: 112) - `/monitor/cache`
  - 控制器: `CacheController.java` ✅ 已存在并完整实现
  - 主要功能: 缓存管理、清理、统计

### 2. 物品管理模块扩展
- **物品属性** (menu_id: 4150) - `/product/attribute`
  - 控制器: `ProductAttributeController.java` ✅ 已存在并完整实现
  - 主要功能: 物品属性管理、属性选项管理

### 3. 权限管理模块扩展
- **权限字符列表** (menu_id: 4152) - `/system/permission/perm-list`
  - 控制器: `SysPermissionListController.java` ✅ 新创建
  - 主要功能: 权限字符管理、同步、统计

### 4. 系统管理模块扩展
- **部门仓库权限** (menu_id: 4341) - `/system/dept/warehouse`
  - 控制器: `SysDeptWarehousePermissionController.java` ✅ 新创建
  - 主要功能: 部门仓库权限分配管理

- **字典数据** (menu_id: 4342) - `/system/dict/data`
  - 控制器: `SysDictDataController.java` ✅ 新创建
  - 主要功能: 字典数据管理、缓存刷新

- **登录方式管理** (menu_id: 4343) - `/system/login-style`
  - 控制器: `SysLoginStyleController.java` ✅ 新创建
  - 主要功能: 登录方式配置管理

### 5. 库存管理模块扩展
- **申购管理** (menu_id: 4332) - `/inventory/purchase`
  - 控制器: `WmsPurchaseController.java` ✅ 新创建
  - 主要功能: 申购单管理、审批、打印

### 6. 二维码管理模块扩展
- **二维码工具** (menu_id: 4148) - `/qrcode/tools`
  - 控制器: `QrCodeToolsController.java` ✅ 已创建
  - 主要功能: 二维码工具集合

## 权限配置

所有新增的菜单项都已配置相应的权限字符串：

### 系统监控权限
- `monitor:online:list` - 在线用户查看
- `monitor:online:forceLogout` - 强制下线
- `monitor:job:list` - 定时任务查看
- `monitor:job:add` - 定时任务新增
- `monitor:job:edit` - 定时任务修改
- `monitor:job:remove` - 定时任务删除
- `monitor:job:changeStatus` - 任务状态修改
- `monitor:server:list` - 服务监控查看
- `monitor:cache:list` - 缓存监控查看
- `monitor:cache:clear` - 缓存清理

### 物品属性权限
- `product:attribute:query` - 属性查询
- `product:attribute:add` - 属性新增
- `product:attribute:edit` - 属性修改
- `product:attribute:remove` - 属性删除
- `product:attribute:export` - 属性导出

### 权限管理权限
- `system:permission:perm-list:query` - 权限字符查询
- `system:permission:perm-list:add` - 权限字符新增
- `system:permission:perm-list:edit` - 权限字符修改
- `system:permission:perm-list:remove` - 权限字符删除

### 部门仓库权限
- `system:dept:warehouse:query` - 部门仓库权限查询
- `system:dept:warehouse:add` - 部门仓库权限新增
- `system:dept:warehouse:edit` - 部门仓库权限修改
- `system:dept:warehouse:remove` - 部门仓库权限删除

### 字典数据权限
- `system:dict:data:query` - 字典数据查询
- `system:dict:data:add` - 字典数据新增
- `system:dict:data:edit` - 字典数据修改
- `system:dict:data:remove` - 字典数据删除

### 登录方式权限
- `system:login:style:query` - 登录方式查询
- `system:login:style:add` - 登录方式新增
- `system:login:style:edit` - 登录方式修改
- `system:login:style:remove` - 登录方式删除

### 申购管理权限
- `inventory:purchase:query` - 申购查询
- `inventory:purchase:add` - 申购新增
- `inventory:purchase:edit` - 申购修改
- `inventory:purchase:remove` - 申购删除
- `inventory:purchase:export` - 申购导出
- `inventory:purchase:print` - 申购打印
- `inventory:purchase:approve` - 申购审批

## 数据库脚本

已创建以下SQL脚本来同步菜单数据：
1. `add_missing_menu_items.sql` - 添加基础缺失菜单
2. `add_remaining_menu_items.sql` - 添加剩余菜单项
3. `fix_menu_duplicates_and_routes.sql` - 修复重复菜单和路由

## 注意事项

1. 所有新创建的控制器都包含了基本的CRUD操作
2. 权限注解已正确配置
3. 日志注解已添加到关键操作
4. 控制器继承了BaseController，具备分页等基础功能
5. 部分控制器的具体业务逻辑需要根据实际需求进一步实现

## 下一步工作

1. 根据具体业务需求完善控制器的实现逻辑
2. 创建对应的Service接口和实现类
3. 创建相应的Domain实体类
4. 配置前端路由和页面
5. 测试所有新增功能的权限控制