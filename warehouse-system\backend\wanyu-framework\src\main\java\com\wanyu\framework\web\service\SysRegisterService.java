package com.wanyu.framework.web.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wanyu.common.constant.CacheConstants;
import com.wanyu.common.constant.Constants;
import com.wanyu.common.constant.UserConstants;
import com.wanyu.common.core.domain.entity.SysUser;
import com.wanyu.common.core.domain.model.RegisterBody;
import com.wanyu.common.exception.user.CaptchaException;
import com.wanyu.common.exception.user.CaptchaExpireException;
import com.wanyu.common.utils.MessageUtils;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.framework.manager.AsyncManager;
import com.wanyu.framework.manager.factory.AsyncFactory;
import com.wanyu.system.service.ISysConfigService;
import com.wanyu.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 注册校验方法
 * 支持真实姓名、用户名、手机号注册
 * 
 * <AUTHOR>
 */
@Component
public class SysRegisterService
{
    private static final Logger log = LoggerFactory.getLogger(SysRegisterService.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    // 内存缓存用于存储验证码
    private static final Map<String, String> CAPTCHA_CACHE = new ConcurrentHashMap<>();

    /**
     * 注册
     *
     * @param registerBody 注册信息
     * @return 错误信息，空字符串表示成功
     */
    public String register(RegisterBody registerBody)
    {
        String username = registerBody.getUsername();
        String realName = registerBody.getRealName();
        String phone = registerBody.getPhone();
        String password = registerBody.getPassword();
        String loginType = registerBody.getLoginType();
        String code = registerBody.getCode();
        String uuid = registerBody.getUuid();

        // 根据登录类型设置用户名
        switch (loginType) {
            case "username":
                username = registerBody.getUsername();
                break;
            case "realname":
                username = registerBody.getRealName();
                break;
            case "phone":
                username = registerBody.getPhone();
                break;
            default:
                return "不支持的登录类型";
        }

        // 验证码校验
        if (StringUtils.isEmpty(uuid) || StringUtils.isEmpty(code)) {
            throw new CaptchaException();
        }
        
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        String captcha = CAPTCHA_CACHE.get(verifyKey);
        CAPTCHA_CACHE.remove(verifyKey); // 验证后立即删除
        
        if (captcha == null) {
            throw new CaptchaExpireException();
        }
        
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException();
        }

        SysUser user = new SysUser();
        user.setUserName(username);
        
        if (!userService.checkUserNameUnique(user)) {
            return "保存用户'" + username + "'失败，注册账号已存在";
        }

        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setRealName(realName);
        sysUser.setPhonenumber(phone);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        sysUser.setLoginType(loginType);
        
        return userService.registerUser(sysUser) ? "" : "注册失败";
    }

    /**
     * 设置验证码缓存
     *
     * @param uuid 验证码唯一标识
     * @param code 验证码
     */
    public void setCaptchaCache(String uuid, String code) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;
        CAPTCHA_CACHE.put(verifyKey, code);
    }
}