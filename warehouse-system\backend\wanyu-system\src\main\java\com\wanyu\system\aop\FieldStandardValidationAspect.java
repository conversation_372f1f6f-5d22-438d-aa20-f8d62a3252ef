package com.wanyu.system.aop;

import com.wanyu.common.annotation.ValidateFieldStandard;
import com.wanyu.common.core.domain.BaseEntity;
import com.wanyu.common.exception.ServiceException;
import com.wanyu.common.utils.FieldStandardValidator;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 字段标准验证切面
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Aspect
@Component
public class FieldStandardValidationAspect {
    
    private static final Logger log = LoggerFactory.getLogger(FieldStandardValidationAspect.class);
    
    @Around("@annotation(validateFieldStandard)")
    public Object validateFieldStandard(ProceedingJoinPoint joinPoint, ValidateFieldStandard validateFieldStandard) throws Throwable {
        if (!validateFieldStandard.enabled()) {
            return joinPoint.proceed();
        }
        
        Object[] args = joinPoint.getArgs();
        List<String> validationErrors = new ArrayList<>();
        
        // 验证方法参数中的实体对象
        for (Object arg : args) {
            if (arg instanceof BaseEntity) {
                List<String> errors = validateEntityFields((BaseEntity) arg);
                validationErrors.addAll(errors);
            }
        }
        
        // 如果有验证错误且配置为抛出异常
        if (!validationErrors.isEmpty() && validateFieldStandard.throwException()) {
            String errorMessage = validateFieldStandard.message() + ": " + String.join(", ", validationErrors);
            log.warn("字段标准验证失败: {}", errorMessage);
            throw new ServiceException(errorMessage);
        }
        
        // 记录验证错误但不抛出异常
        if (!validationErrors.isEmpty()) {
            log.warn("字段标准验证发现问题: {}", String.join(", ", validationErrors));
        }
        
        return joinPoint.proceed();
    }
    
    /**
     * 验证实体对象的字段
     */
    private List<String> validateEntityFields(BaseEntity entity) {
        List<String> errors = new ArrayList<>();
        
        try {
            Class<?> clazz = entity.getClass();
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(entity);
                
                if (value instanceof String) {
                    String fieldName = field.getName();
                    String fieldValue = (String) value;
                    
                    // 只验证状态相关字段
                    if (isStatusRelatedField(fieldName)) {
                        FieldStandardValidator.ValidationResult result = 
                            FieldStandardValidator.validateFieldValue(fieldName, fieldValue);
                        
                        if (!result.isValid()) {
                            errors.add(result.getErrorMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("验证实体字段时发生错误", e);
            errors.add("字段验证过程中发生错误: " + e.getMessage());
        }
        
        return errors;
    }
    
    /**
     * 判断是否为状态相关字段
     */
    private boolean isStatusRelatedField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("status") || 
               lowerFieldName.startsWith("is_") || 
               lowerFieldName.startsWith("enable_") ||
               lowerFieldName.endsWith("_flag") ||
               "delFlag".equals(fieldName);
    }
}