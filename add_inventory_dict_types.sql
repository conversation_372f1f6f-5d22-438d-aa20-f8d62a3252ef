-- 添加入库出库相关字典类型到数据库
USE warehouse_system;

-- 1. 添加入库类型字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (105, '入库类型', 'inventory_in_type', '0', 'admin', NOW(), '入库类型列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(15, 1, '采购入库', '1', 'inventory_in_type', '', 'primary', 'Y', '0', 'admin', NOW(), '采购入库'),
(16, 2, '生产入库', '2', 'inventory_in_type', '', 'success', 'N', '0', 'admin', NOW(), '生产入库'),
(17, 3, '调拨入库', '3', 'inventory_in_type', '', 'info', 'N', '0', 'admin', NOW(), '调拨入库'),
(18, 4, '退货入库', '4', 'inventory_in_type', '', 'warning', 'N', '0', 'admin', NOW(), '退货入库');

-- 2. 添加入库状态字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (106, '入库状态', 'inventory_in_status', '0', 'admin', NOW(), '入库状态列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(19, 1, '待审核', '0', 'inventory_in_status', '', 'info', 'Y', '0', 'admin', NOW(), '待审核'),
(20, 2, '已审核', '1', 'inventory_in_status', '', 'primary', 'N', '0', 'admin', NOW(), '已审核'),
(21, 3, '已完成', '2', 'inventory_in_status', '', 'success', 'N', '0', 'admin', NOW(), '已完成'),
(22, 4, '已取消', '3', 'inventory_in_status', '', 'danger', 'N', '0', 'admin', NOW(), '已取消');

-- 3. 添加出库类型字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (107, '出库类型', 'inventory_out_type', '0', 'admin', NOW(), '出库类型列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(23, 1, '销售出库', '1', 'inventory_out_type', '', 'primary', 'Y', '0', 'admin', NOW(), '销售出库'),
(24, 2, '生产出库', '2', 'inventory_out_type', '', 'success', 'N', '0', 'admin', NOW(), '生产出库'),
(25, 3, '调拨出库', '3', 'inventory_out_type', '', 'info', 'N', '0', 'admin', NOW(), '调拨出库'),
(26, 4, '报损出库', '4', 'inventory_out_type', '', 'warning', 'N', '0', 'admin', NOW(), '报损出库');

-- 4. 添加出库状态字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (108, '出库状态', 'inventory_out_status', '0', 'admin', NOW(), '出库状态列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(27, 1, '待审核', '0', 'inventory_out_status', '', 'info', 'Y', '0', 'admin', NOW(), '待审核'),
(28, 2, '已审核', '1', 'inventory_out_status', '', 'primary', 'N', '0', 'admin', NOW(), '已审核'),
(29, 3, '已完成', '2', 'inventory_out_status', '', 'success', 'N', '0', 'admin', NOW(), '已完成'),
(30, 4, '已取消', '3', 'inventory_out_status', '', 'danger', 'N', '0', 'admin', NOW(), '已取消');

-- 5. 添加调拨状态字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (109, '调拨状态', 'inventory_transfer_status', '0', 'admin', NOW(), '调拨状态列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(31, 1, '待审核', '0', 'inventory_transfer_status', '', 'info', 'Y', '0', 'admin', NOW(), '待审核'),
(32, 2, '已审核', '1', 'inventory_transfer_status', '', 'primary', 'N', '0', 'admin', NOW(), '已审核'),
(33, 3, '已完成', '2', 'inventory_transfer_status', '', 'success', 'N', '0', 'admin', NOW(), '已完成'),
(34, 4, '已取消', '3', 'inventory_transfer_status', '', 'danger', 'N', '0', 'admin', NOW(), '已取消');

COMMIT;