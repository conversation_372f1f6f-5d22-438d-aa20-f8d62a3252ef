-- 添加所有缺失的菜单项（不删除任何现有菜单）
-- 基于提供的菜单ID列表

-- 1. 添加系统监控目录 (ID: 4345)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4345, '系统监控', 0, 11, 'monitor', 'Layout', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', NOW(), '系统监控目录');

-- 2. 添加在线用户菜单 (ID: 4346, 父菜单: 4345)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4346, '在线用户', 4345, 1, 'online', 'monitor/online/index', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', NOW(), '在线用户菜单');

-- 3. 添加定时任务菜单 (ID: 4347, 父菜单: 4345)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4347, '定时任务', 4345, 2, 'job', 'monitor/job/index', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', NOW(), '定时任务菜单');

-- 4. 添加服务监控菜单 (ID: 4348, 父菜单: 4345)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4348, '服务监控', 4345, 3, 'server', 'monitor/server/index', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', NOW(), '服务监控菜单');

-- 5. 添加缓存监控菜单 (ID: 4349, 父菜单: 4345)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4349, '缓存监控', 4345, 4, 'cache', 'monitor/cache/index', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', NOW(), '缓存监控菜单');

-- 6. 添加物品属性菜单 (ID: 4350, 父菜单: 17)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4350, '物品属性', 17, 6, 'attribute', 'product/attribute/index', '', 1, 0, 'C', '0', '0', 'product:attribute:list', 'list', 'admin', NOW(), '物品属性菜单');

-- 7. 添加权限字符列表菜单 (ID: 4351, 父菜单: 10)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4351, '权限字符列表', 10, 5, 'perm-list', 'system/permission/perm-list', '', 1, 0, 'C', '0', '0', 'system:permission:perm-list', 'list', 'admin', NOW(), '权限字符列表菜单');

-- 8. 为在线用户添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4368, '在线用户强退', 4346, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', NOW(), ''),
(4381, '在线用户查询', 4346, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', NOW(), ''),
(4382, '批量强退', 4346, 3, '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', NOW(), '');

-- 9. 为定时任务添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4369, '定时任务新增', 4347, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', NOW(), ''),
(4370, '定时任务修改', 4347, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', NOW(), ''),
(4371, '定时任务删除', 4347, 3, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', NOW(), ''),
(4372, '定时任务状态修改', 4347, 4, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', NOW(), ''),
(4383, '定时任务查询', 4347, 5, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', NOW(), ''),
(4384, '定时任务导出', 4347, 6, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', NOW(), ''),
(4385, '任务执行', 4347, 7, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:run', '#', 'admin', NOW(), '');

-- 10. 为服务监控添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4386, '服务监控查询', 4348, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:server:list', '#', 'admin', NOW(), ''),
(4387, '服务监控刷新', 4348, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:server:refresh', '#', 'admin', NOW(), '');

-- 11. 为缓存监控添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4388, '缓存列表', 4349, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:cache:list', '#', 'admin', NOW(), ''),
(4389, '缓存清理', 4349, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:cache:clear', '#', 'admin', NOW(), ''),
(4390, '缓存统计', 4349, 3, '', '', '', 1, 0, 'F', '0', '0', 'monitor:cache:stats', '#', 'admin', NOW(), '');

-- 12. 为物品属性添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4373, '物品属性查询', 4350, 1, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:query', '#', 'admin', NOW(), ''),
(4374, '物品属性新增', 4350, 2, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:add', '#', 'admin', NOW(), ''),
(4375, '物品属性修改', 4350, 3, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:edit', '#', 'admin', NOW(), ''),
(4376, '物品属性删除', 4350, 4, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:remove', '#', 'admin', NOW(), ''),
(4391, '物品属性导出', 4350, 5, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:export', '#', 'admin', NOW(), '');

-- 13. 为权限字符列表添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4377, '权限字符查询', 4351, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:query', '#', 'admin', NOW(), ''),
(4378, '权限字符新增', 4351, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:add', '#', 'admin', NOW(), ''),
(4379, '权限字符修改', 4351, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:edit', '#', 'admin', NOW(), ''),
(4380, '权限字符删除', 4351, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:remove', '#', 'admin', NOW(), ''),
(4392, '权限字符导出', 4351, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:export', '#', 'admin', NOW(), ''),
(4393, '权限字符同步', 4351, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:sync', '#', 'admin', NOW(), '');

-- 14. 为超级管理员角色分配新菜单权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu 
WHERE menu_id IN (4345, 4346, 4347, 4348, 4349, 4350, 4351)
AND NOT EXISTS (
    SELECT 1 FROM sys_role_menu rm 
    WHERE rm.role_id = 1 AND rm.menu_id = sys_menu.menu_id
);

-- 15. 显示新增的菜单信息
SELECT 
    m.menu_id,
    m.menu_name,
    CASE 
        WHEN p.menu_name IS NULL THEN '根目录'
        ELSE p.menu_name
    END AS parent_menu,
    m.order_num,
    m.path,
    m.component,
    CASE 
        WHEN m.menu_type = 'M' THEN '目录'
        WHEN m.menu_type = 'C' THEN '菜单'
        WHEN m.menu_type = 'F' THEN '按钮'
        ELSE m.menu_type
    END AS menu_type_desc,
    m.perms,
    m.status
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_id IN (4345, 4346, 4347, 4348, 4349, 4350, 4351)
ORDER BY m.parent_id, m.order_num;