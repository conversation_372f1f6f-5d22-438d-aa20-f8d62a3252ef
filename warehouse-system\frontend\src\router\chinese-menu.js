export const chineseMenuRoutes = [
  {
    path: '/system',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'ChineseSystem',
    meta: {
      title: '系统管理',
      icon: 'system'
    },
    children: [
      {
        path: 'user',
        component: 'system/user/index',
        name: 'ChineseUser',
        meta: {
          title: '用户管理',
          icon: 'user'
        }
      },
      {
        path: 'role',
        component: 'system/role/index',
        name: 'ChineseRole',
        meta: {
          title: '角色管理',
          icon: 'peoples'
        }
      },
      {
        path: 'menu',
        component: 'system/menu/index',
        name: 'ChineseMenu',
        meta: {
          title: '菜单管理',
          icon: 'tree-table'
        }
      },
      {
        path: 'dept',
        component: 'system/dept/index',
        name: 'ChineseDept',
        meta: {
          title: '部门管理',
          icon: 'tree'
        }
      },

      {
        path: 'dict',
        component: 'system/dict/index',
        name: 'ChineseDict',
        meta: {
          title: '字典管理',
          icon: 'dict'
        }
      },
      {
        path: 'config',
        component: 'system/config/index',
        name: 'ChineseConfig',
        meta: {
          title: '参数设置',
          icon: 'edit'
        }
      },
      {
        path: 'notice',
        component: 'system/notice/index',
        name: 'ChineseNotice',
        meta: {
          title: '通知公告',
          icon: 'message'
        }
      },
      {
        path: 'log',
        component: 'system/log/index',
        name: 'ChineseLog',
        meta: {
          title: '日志管理',
          icon: 'log'
        }
      },
      {
        path: 'permission',
        component: 'system/permission/index',
        name: 'ChinesePermission',
        meta: {
          title: '权限管理',
          icon: 'validCode'
        }
      },
      {
        path: 'user-permission/index',
        component: 'system/permission/userPermission',
        name: 'ChineseUserPermission',
        meta: {
          title: '用户权限管理',
          icon: 'peoples'
        }
      },
      {
        path: 'warehouse',
        component: 'system/warehouse/index',
        name: 'ChineseSystemWarehouseManagement',
        meta: {
          title: '仓库管理',
          icon: 'build'
        }
      },
      {
        path: 'inventory',
        component: 'system/inventory/index',
        name: 'ChineseInventory',
        meta: {
          title: '库存管理',
          icon: 'list'
        }
      },
      {
        path: 'inbound',
        component: 'system/inventory/inbound',
        name: 'ChineseInboundLog',
        meta: {
          title: '入库日志',
          icon: 'log'
        }
      },
      {
        path: 'outbound',
        component: 'system/inventory/outbound',
        name: 'ChineseOutboundLog',
        meta: {
          title: '出库日志',
          icon: 'log'
        }
      },
      {
        path: 'specification',
        component: 'system/specification/index',
        name: 'ChineseSpecification',
        meta: {
          title: '产品规格',
          icon: 'dict'
        }
      },
      {
        path: 'unit',
        component: 'system/unit/index',
        name: 'ChineseUnit',
        meta: {
          title: '产品单位',
          icon: 'dict'
        }
      }
    ]
  },
  {
    path: '/tool',
    component: 'Layout',
    redirect: 'noredirect',
    name: 'ChineseTool',
    meta: {
      title: '系统工具',
      icon: 'tool'
    },
    children: [
      {
        path: 'qrcode',
        component: 'system/qrcode/index',
        name: 'ChineseQrCode',
        meta: {
          title: '二维码工具',
          icon: 'code'
        }
      }
    ]
  }
]