package com.wanyu.system.service;

import java.util.List;
import com.wanyu.system.domain.SysLicenseFeature;

/**
 * 功能权限Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
public interface ISysLicenseFeatureService 
{
    /**
     * 查询功能权限
     * 
     * @param featureId 功能权限主键
     * @return 功能权限
     */
    public SysLicenseFeature selectSysLicenseFeatureByFeatureId(Long featureId);

    /**
     * 查询功能权限列表
     * 
     * @param sysLicenseFeature 功能权限
     * @return 功能权限集合
     */
    public List<SysLicenseFeature> selectSysLicenseFeatureList(SysLicenseFeature sysLicenseFeature);

    /**
     * 查询启用的功能权限列表
     * 
     * @return 启用的功能权限集合
     */
    public List<SysLicenseFeature> selectEnabledFeatures();

    /**
     * 查询禁用的功能权限列表
     * 
     * @return 禁用的功能权限集合
     */
    public List<SysLicenseFeature> selectDisabledFeatures();

    /**
     * 根据功能代码查询功能权限
     * 
     * @param featureCode 功能代码
     * @return 功能权限
     */
    public SysLicenseFeature selectSysLicenseFeatureByCode(String featureCode);

    /**
     * 查询核心功能列表
     * 
     * @return 核心功能集合
     */
    public List<SysLicenseFeature> selectCoreFeatures();

    /**
     * 新增功能权限
     * 
     * @param sysLicenseFeature 功能权限
     * @return 结果
     */
    public int insertSysLicenseFeature(SysLicenseFeature sysLicenseFeature);

    /**
     * 修改功能权限
     * 
     * @param sysLicenseFeature 功能权限
     * @return 结果
     */
    public int updateSysLicenseFeature(SysLicenseFeature sysLicenseFeature);

    /**
     * 批量删除功能权限
     * 
     * @param featureIds 需要删除的功能权限主键集合
     * @return 结果
     */
    public int deleteSysLicenseFeatureByFeatureIds(Long[] featureIds);

    /**
     * 删除功能权限信息
     * 
     * @param featureId 功能权限主键
     * @return 结果
     */
    public int deleteSysLicenseFeatureByFeatureId(Long featureId);

    /**
     * 启用功能
     * 
     * @param featureId 功能权限主键
     * @return 结果
     */
    public int enableFeature(Long featureId);

    /**
     * 禁用功能
     * 
     * @param featureId 功能权限主键
     * @return 结果
     */
    public int disableFeature(Long featureId);

    /**
     * 批量启用功能
     * 
     * @param featureIds 功能权限主键集合
     * @return 结果
     */
    public int enableFeatures(Long[] featureIds);

    /**
     * 批量禁用功能
     * 
     * @param featureIds 功能权限主键集合
     * @return 结果
     */
    public int disableFeatures(Long[] featureIds);

    /**
     * 检查功能是否可用
     * 
     * @param featureCode 功能代码
     * @return 是否可用
     */
    public boolean isFeatureAvailable(String featureCode);

    /**
     * 根据授权类型查询可用功能
     * 
     * @param licenseType 授权类型
     * @return 可用功能集合
     */
    public List<SysLicenseFeature> selectFeaturesByLicenseType(String licenseType);
}