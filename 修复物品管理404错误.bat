@echo off
chcp 65001
echo ========================================
echo 修复物品管理404错误
echo ========================================

echo 步骤1: 检查数据库连接...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL未安装或未在PATH中
    echo 请确保MySQL已安装并可以通过命令行访问
    pause
    exit /b 1
)

echo 步骤2: 验证数据库表是否存在...
mysql -h localhost -P 3306 -u root -p123456 -D warehouse_system -e "SELECT COUNT(*) as table_exists FROM information_schema.tables WHERE table_schema='warehouse_system' AND table_name='wms_product';" 2>nul
if %errorlevel% neq 0 (
    echo ❌ 数据库连接失败或wms_product表不存在
    echo 请检查：
    echo 1. 数据库服务是否运行
    echo 2. warehouse_system数据库是否存在
    echo 3. wms_product表是否存在
    pause
    exit /b 1
)
echo ✅ 数据库表验证通过

echo 步骤3: 检查Java文件是否存在...
if not exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\product\ProductInfoController.java" (
    echo ❌ ProductInfoController文件未找到
    pause
    exit /b 1
)
echo ✅ Java文件检查通过

echo 步骤4: 进入后端目录并编译项目...
cd /d "C:\CKGLXT\warehouse-system\backend"
if exist "pom.xml" (
    echo 正在编译项目...
    call mvn clean compile -q
    if %errorlevel% neq 0 (
        echo ❌ Maven编译失败
        echo 请检查：
        echo 1. Maven是否已安装
        echo 2. 项目依赖是否正确
        echo 3. Java代码是否有语法错误
        pause
        exit /b 1
    )
    echo ✅ Maven编译成功
) else (
    echo ❌ 未找到pom.xml文件
    pause
    exit /b 1
)

echo 步骤5: 测试数据库查询...
mysql -h localhost -P 3306 -u root -p123456 -D warehouse_system -e "SELECT product_id, product_name, product_code FROM wms_product LIMIT 5;" 2>nul
if %errorlevel% neq 0 (
    echo ❌ 数据库查询失败
    pause
    exit /b 1
)
echo ✅ 数据库查询测试通过

echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 接下来请：
echo 1. 重启后端服务（在backend目录下运行）
echo    cd C:\CKGLXT\warehouse-system\backend
echo    java -jar wanyu-admin\target\wanyu-admin.jar
echo.
echo 2. 测试API端点：
echo    GET http://localhost:8080/product/info/list
echo.
echo 3. 检查前端页面是否正常加载
echo.
echo 如果仍有问题，请检查：
echo - 后端服务是否正常启动（端口8080）
echo - 数据库连接是否正常
echo - 权限配置是否正确
echo - 前端请求地址是否正确
echo.
pause