export const menuRoutes = [
  {
    path: '/product',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'ProductManage',
    meta: {
      title: '物品管理',
      icon: 'shopping'
    },
    children: [
      {
        path: 'info',
        component: 'product/info/index',
        name: 'MenuProductInfo',
        meta: {
          title: '物品信息',
          icon: 'list'
        }
      },
      {
        path: 'category',
        component: 'product/category/index',
        name: 'MenuProductCategory',
        meta: {
          title: '物品分类',
          icon: 'tree'
        }
      },
      {
        path: 'unit',
        component: 'product/unit/index',
        name: 'MenuProductUnit',
        meta: {
          title: '物品单位',
          icon: 'component'
        }
      },
      {
        path: 'spec',
        component: 'product/spec/index',
        name: 'MenuProductSpec',
        meta: {
          title: '物品规格',
          icon: 'example'
        }
      },

      {
        path: 'barcode',
        component: 'product/barcode/index',
        name: 'MenuProductBarcode',
        meta: {
          title: '物品条码',
          icon: 'barcode'
        }
      },
      {
        path: 'image',
        component: 'product/image/index',
        name: 'MenuProductImage',
        meta: {
          title: '物品图片',
          icon: 'image'
        }
      }
    ]
  },
  {
    path: '/system',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'SystemManage',
    meta: {
      title: '系统管理',
      icon: 'system'
    },
    children: [
      {
        path: 'user',
        component: 'system/user/index',
        name: 'MenuSystemUser',
        meta: {
          title: '用户管理',
          icon: 'user'
        }
      },
      {
        path: 'role',
        component: 'system/role/index',
        name: 'MenuSystemRole',
        meta: {
          title: '角色管理',
          icon: 'peoples'
        }
      },
      {
        path: 'menu',
        component: 'system/menu/index',
        name: 'MenuSystemMenu',
        meta: {
          title: '菜单管理',
          icon: 'tree-table'
        }
      },
      {
        path: 'dept',
        component: 'system/dept/index',
        name: 'MenuSystemDept',
        meta: {
          title: '部门管理',
          icon: 'tree'
        }
      },
      {
        path: 'dict',
        component: 'system/dict/index',
        name: 'MenuSystemDict',
        meta: {
          title: '字典管理',
          icon: 'dict'
        }
      },
      {
        path: 'config',
        component: 'system/config/index',
        name: 'MenuSystemConfig',
        meta: {
          title: '参数设置',
          icon: 'edit'
        }
      },
      {
        path: 'notice',
        component: 'system/notice/index',
        name: 'MenuSystemNotice',
        meta: {
          title: '通知公告',
          icon: 'message'
        }
      },
      {
        path: 'log',
        component: 'system/log/index',
        name: 'MenuSystemLog',
        meta: {
          title: '日志管理',
          icon: 'log'
        }
      },
      {
        path: 'permission',
        component: 'system/permission/index',
        name: 'MenuSystemPermission',
        meta: {
          title: '权限管理',
          icon: 'validCode'
        }
      },
      {
        path: 'user-permission',
        component: 'system/permission/userPermission',
        name: 'MenuSystemUserPermission',
        meta: {
          title: '用户权限管理',
          icon: 'peoples'
        }
      },
      {
        path: 'warehouse',
        component: 'system/warehouse/index',
        name: 'MenuSystemWarehouse',
        meta: {
          title: '仓库管理',
          icon: 'build'
        }
      },
      {
        path: 'inventory',
        component: 'system/inventory/index',
        name: 'MenuSystemInventory',
        meta: {
          title: '库存管理',
          icon: 'list'
        }
      },
      {
        path: 'inbound',
        component: 'system/inventory/inbound',
        name: 'MenuSystemInboundLog',
        meta: {
          title: '入库日志',
          icon: 'log'
        }
      },
      {
        path: 'outbound',
        component: 'system/inventory/outbound',
        name: 'MenuSystemOutboundLog',
        meta: {
          title: '出库日志',
          icon: 'log'
        }
      }
    ]
  },
  {
    path: '/tool',
    component: 'Layout',
    redirect: 'noredirect',
    name: 'MenuToolManage',
    meta: {
      title: '系统工具',
      icon: 'tool'
    },
    children: [
      {
        path: 'qrcode',
        component: 'system/qrcode/index',
        name: 'MenuToolQrCode',
        meta: {
          title: '二维码工具',
          icon: 'code'
        }
      }
    ]
  },
  {
    path: '/log',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'MenuLogManage',
    meta: {
      title: '日志管理',
      icon: 'log'
    },
    children: [
      {
        path: 'operation',
        component: 'log/operation/index',
        name: 'MenuLogOperation',
        meta: {
          title: '操作日志',
          icon: 'form'
        }
      },
      {
        path: 'inventory',
        component: 'log/inventory/index',
        name: 'MenuLogInventory',
        meta: {
          title: '出入库日志',
          icon: 'documentation'
        }
      },

      {
        path: 'permission',
        component: 'log/permission/index',
        name: 'MenuLogPermission',
        meta: {
          title: '权限日志',
          icon: 'peoples'
        }
      }
    ]
  },
  {
    path: '/report',
    component: 'Layout',
    redirect: 'noredirect',
    alwaysShow: true,
    name: 'ReportStatistics',
    meta: {
      title: '报表统计',
      icon: 'chart'
    },
    children: [
      {
        path: 'stock',
        component: 'report/stock/index',
        name: 'MenuReportStockView',
        meta: {
          title: '库存报表',
          icon: 'documentation'
        }
      },
      {
        path: 'in',
        component: 'report/in/index',
        name: 'MenuReportInView',
        meta: {
          title: '入库报表',
          icon: 'upload'
        }
      },
      {
        path: 'out',
        component: 'report/out/index',
        name: 'MenuReportOutView',
        meta: {
          title: '出库报表',
          icon: 'download'
        }
      }
    ]
  }
]