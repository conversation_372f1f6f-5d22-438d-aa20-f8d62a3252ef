-- 最终菜单路由检查脚本
-- 确保所有菜单项都有对应的后端路由

-- 1. 检查所有主要菜单项的状态
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    p.menu_name as parent_name,
    m.path,
    m.component,
    m.menu_type,
    m.perms,
    m.status,
    CASE 
        WHEN m.status = '0' THEN '正常'
        WHEN m.status = '1' THEN '停用'
        ELSE '未知'
    END as status_desc
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_type IN ('M', 'C')
ORDER BY m.parent_id, m.order_num;

-- 2. 检查权限字符串配置
SELECT 
    menu_name,
    perms,
    CASE 
        WHEN perms IS NULL OR perms = '' THEN '未配置权限'
        ELSE '已配置权限'
    END as perm_status
FROM sys_menu 
WHERE menu_type = 'C'
ORDER BY menu_name;

-- 3. 检查菜单层级结构
WITH RECURSIVE menu_tree AS (
    -- 根节点
    SELECT 
        menu_id,
        menu_name,
        parent_id,
        0 as level,
        CAST(menu_name AS CHAR(1000)) as path
    FROM sys_menu 
    WHERE parent_id = 0
    
    UNION ALL
    
    -- 子节点
    SELECT 
        m.menu_id,
        m.menu_name,
        m.parent_id,
        mt.level + 1,
        CONCAT(mt.path, ' > ', m.menu_name)
    FROM sys_menu m
    INNER JOIN menu_tree mt ON m.parent_id = mt.menu_id
)
SELECT 
    CONCAT(REPEAT('  ', level), menu_name) as menu_hierarchy,
    level,
    path as full_path
FROM menu_tree
ORDER BY path;

-- 4. 检查重复菜单
SELECT 
    menu_name,
    COUNT(*) as count,
    GROUP_CONCAT(menu_id) as menu_ids
FROM sys_menu
GROUP BY menu_name
HAVING COUNT(*) > 1;

-- 5. 检查孤立菜单（父菜单不存在的菜单）
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    '父菜单不存在' as issue
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.parent_id != 0 AND p.menu_id IS NULL;

-- 6. 检查路径冲突
SELECT 
    path,
    COUNT(*) as count,
    GROUP_CONCAT(CONCAT(menu_id, ':', menu_name)) as conflicting_menus
FROM sys_menu
WHERE path IS NOT NULL AND path != ''
GROUP BY path
HAVING COUNT(*) > 1;

-- 7. 统计各模块菜单数量
SELECT 
    CASE 
        WHEN parent_id = 0 THEN menu_name
        ELSE (SELECT menu_name FROM sys_menu WHERE menu_id = m.parent_id)
    END as module_name,
    COUNT(*) as menu_count
FROM sys_menu m
WHERE menu_type IN ('M', 'C')
GROUP BY 
    CASE 
        WHEN parent_id = 0 THEN menu_name
        ELSE (SELECT menu_name FROM sys_menu WHERE menu_id = m.parent_id)
    END
ORDER BY menu_count DESC;

-- 8. 检查按钮权限配置
SELECT 
    p.menu_name as parent_menu,
    m.menu_name as button_name,
    m.perms,
    CASE 
        WHEN m.perms IS NULL OR m.perms = '' THEN '权限未配置'
        ELSE '权限已配置'
    END as perm_status
FROM sys_menu m
INNER JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_type = 'F'
ORDER BY p.menu_name, m.order_num;

-- 9. 检查超级管理员权限分配
SELECT 
    m.menu_name,
    m.menu_type,
    CASE 
        WHEN rm.menu_id IS NOT NULL THEN '已分配'
        ELSE '未分配'
    END as admin_access
FROM sys_menu m
LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id AND rm.role_id = 1
WHERE m.menu_type IN ('M', 'C')
ORDER BY m.parent_id, m.order_num;

-- 10. 最终状态报告
SELECT 
    '菜单总数' as item,
    COUNT(*) as count
FROM sys_menu
UNION ALL
SELECT 
    '目录数量',
    COUNT(*)
FROM sys_menu WHERE menu_type = 'M'
UNION ALL
SELECT 
    '菜单数量',
    COUNT(*)
FROM sys_menu WHERE menu_type = 'C'
UNION ALL
SELECT 
    '按钮数量',
    COUNT(*)
FROM sys_menu WHERE menu_type = 'F'
UNION ALL
SELECT 
    '启用菜单',
    COUNT(*)
FROM sys_menu WHERE status = '0'
UNION ALL
SELECT 
    '停用菜单',
    COUNT(*)
FROM sys_menu WHERE status = '1';