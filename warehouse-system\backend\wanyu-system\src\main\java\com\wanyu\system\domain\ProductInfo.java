package com.wanyu.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;
import java.math.BigDecimal;

/**
 * 物品信息对象 wms_product
 *
 * <AUTHOR>
 */
public class ProductInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 物品ID */
    private Long productId;

    /** 物品名称 */
    @Excel(name = "物品名称")
    private String productName;

    /** 物品编码 */
    @Excel(name = "物品编码")
    private String productCode;

    /** 物品规格 */
    @Excel(name = "物品规格")
    private String productSpec;

    /** 规格ID */
    @Excel(name = "规格ID")
    private Integer specId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    private String specName;

    /** 物品单位 */
    @Excel(name = "物品单位")
    private String productUnit;

    /** 单位ID */
    @Excel(name = "单位ID")
    private Integer unitId;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String unitName;

    /** 物品类别 */
    @Excel(name = "物品类别")
    private String productCategory;

    /** 物品分类ID */
    @Excel(name = "物品分类ID")
    private Long categoryId;

    /** 物品分类名称 */
    @Excel(name = "物品分类名称")
    private String categoryName;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 物品图片URL */
    private String imageUrl;

    public void setProductId(Long productId)
    {
        this.productId = productId;
    }

    public Long getProductId()
    {
        return productId;
    }

    public void setProductName(String productName)
    {
        this.productName = productName;
    }

    public String getProductName()
    {
        return productName;
    }

    public void setProductCode(String productCode)
    {
        this.productCode = productCode;
    }

    public String getProductCode()
    {
        return productCode;
    }

    public void setProductSpec(String productSpec)
    {
        this.productSpec = productSpec;
    }

    public String getProductSpec()
    {
        return productSpec;
    }

    public void setSpecId(Integer specId)
    {
        this.specId = specId;
    }

    public Integer getSpecId()
    {
        return specId;
    }

    public void setSpecName(String specName)
    {
        this.specName = specName;
    }

    public String getSpecName()
    {
        return specName;
    }

    public void setProductUnit(String productUnit)
    {
        this.productUnit = productUnit;
    }

    public String getProductUnit()
    {
        return productUnit;
    }

    public void setUnitId(Integer unitId)
    {
        this.unitId = unitId;
    }

    public Integer getUnitId()
    {
        return unitId;
    }

    public void setUnitName(String unitName)
    {
        this.unitName = unitName;
    }

    public String getUnitName()
    {
        return unitName;
    }

    public void setProductCategory(String productCategory)
    {
        this.productCategory = productCategory;
    }

    public String getProductCategory()
    {
        return productCategory;
    }

    public void setCategoryId(Long categoryId)
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId()
    {
        return categoryId;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setImageUrl(String imageUrl)
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl()
    {
        return imageUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("productId", getProductId())
            .append("productName", getProductName())
            .append("productCode", getProductCode())
            .append("productSpec", getProductSpec())
            .append("specId", getSpecId())
            .append("specName", getSpecName())
            .append("productUnit", getProductUnit())
            .append("unitId", getUnitId())
            .append("unitName", getUnitName())
            .append("productCategory", getProductCategory())
            .append("categoryId", getCategoryId())
            .append("categoryName", getCategoryName())
            .append("price", getPrice())
            .append("status", getStatus())
            .append("imageUrl", getImageUrl())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
