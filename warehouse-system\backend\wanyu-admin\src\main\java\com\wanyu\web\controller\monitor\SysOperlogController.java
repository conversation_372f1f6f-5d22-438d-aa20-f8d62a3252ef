package com.wanyu.web.controller.monitor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.SysOperLog;
import com.wanyu.system.service.ISysLogPermissionService;
import com.wanyu.system.service.ISysOperLogService;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/operlog")
public class SysOperlogController extends BaseController
{
    @Autowired
    private ISysOperLogService operLogService;

    @Autowired
    private ISysLogPermissionService logPermissionService;

    // 兼容前端分页API，支持 GET /monitor/operlog?pageNum=1&pageSize=10
    @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
    @GetMapping("")
    public TableDataInfo listCompat(SysOperLog operLog) {
        // 检查用户是否有权限查看操作日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "operation")) {
            return getDataTable(new ArrayList<>());
        }
        startPage();
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        return getDataTable(list);
    }

    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:export')")
    @GetMapping("/export")
    public void export(HttpServletResponse response, SysOperLog operLog)
    {
        // 检查用户是否有权限查看操作日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "operation"))
        {
            return;
        }

        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        ExcelUtil<SysOperLog> util = new ExcelUtil<SysOperLog>(SysOperLog.class);
        util.exportExcel(response, list, "操作日志");
    }

    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/{operIds}")
    public AjaxResult remove(@PathVariable Long[] operIds)
    {
        // 检查用户是否有权限管理操作日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "operation"))
        {
            return error("您没有权限删除操作日志");
        }

        return toAjax(operLogService.deleteOperLogByIds(operIds));
    }

    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        // 检查用户是否有权限管理操作日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "operation"))
        {
            return error("您没有权限清空操作日志");
        }

        operLogService.cleanOperLog();
        return success();
    }

    /**
     * 获取操作日志统计信息
     */
    @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        // 检查用户是否有权限查看操作日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "operation"))
        {
            return error("您没有权限查看操作日志统计");
        }

        Map<String, Object> statistics = operLogService.getOperLogStatistics();
        return success(statistics);
    }

    /**
     * 获取操作日志趋势数据
     */
    @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
    @GetMapping("/trend")
    public AjaxResult getTrend(@RequestParam(defaultValue = "7") int days)
    {
        // 检查用户是否有权限查看操作日志
        if (!logPermissionService.hasLogPermission(SecurityUtils.getUserId(), "operation"))
        {
            return error("您没有权限查看操作日志趋势");
        }

        List<Map<String, Object>> trendData = operLogService.getOperLogTrend(days);
        return success(trendData);
    }
}
