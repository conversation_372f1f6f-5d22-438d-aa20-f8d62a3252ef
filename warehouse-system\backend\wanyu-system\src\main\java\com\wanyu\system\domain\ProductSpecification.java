package com.wanyu.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanyu.common.annotation.Excel;
import com.wanyu.common.core.domain.BaseEntity;

/**
 * 物品规格对象 wms_specification
 *
 * <AUTHOR>
 */
public class ProductSpecification extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规格ID */
    private Long specId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    private String specName;

    /** 规格编码 */
    @Excel(name = "规格编码")
    private String specCode;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setSpecId(Long specId)
    {
        this.specId = specId;
    }

    public Long getSpecId()
    {
        return specId;
    }
    public void setSpecName(String specName)
    {
        this.specName = specName;
    }

    public String getSpecName()
    {
        return specName;
    }
    public void setSpecCode(String specCode)
    {
        this.specCode = specCode;
    }

    public String getSpecCode()
    {
        return specCode;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("specId", getSpecId())
            .append("specName", getSpecName())
            .append("specCode", getSpecCode())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
