-- 检查并删除其他表中的area_id和rack_id字段
USE warehouse_system;

-- 检查所有表中是否有area_id或rack_id字段
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND (COLUMN_NAME LIKE '%area_id%' OR COLUMN_NAME LIKE '%rack_id%')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 如果发现有相关字段，请根据实际情况执行以下删除语句：

-- 示例：删除产品信息表中的area_id和rack_id字段
-- ALTER TABLE product_info DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE product_info DROP COLUMN IF EXISTS rack_id;

-- 示例：删除库存表中的area_id和rack_id字段
-- ALTER TABLE inventory_info DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE inventory_info DROP COLUMN IF EXISTS rack_id;

-- 示例：删除入库表中的area_id和rack_id字段
-- ALTER TABLE inventory_in DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE inventory_in DROP COLUMN IF EXISTS rack_id;

-- 示例：删除出库表中的area_id和rack_id字段
-- ALTER TABLE inventory_out DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE inventory_out DROP COLUMN IF EXISTS rack_id;

-- 示例：删除库存日志表中的area_id和rack_id字段
-- ALTER TABLE inventory_log DROP COLUMN IF EXISTS area_id;
-- ALTER TABLE inventory_log DROP COLUMN IF EXISTS rack_id;