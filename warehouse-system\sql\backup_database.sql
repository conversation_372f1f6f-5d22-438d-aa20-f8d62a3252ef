-- 数据库备份脚本
-- 用于字段标准化实施前的完整数据备份

-- 创建备份数据库
CREATE DATABASE IF NOT EXISTS warehouse_system_backup;

-- 使用备份数据库
USE warehouse_system_backup;

-- 备份sys_license表
DROP TABLE IF EXISTS sys_license_backup;
CREATE TABLE sys_license_backup AS SELECT * FROM warehouse_system.sys_license;

-- 备份sys_license_feature表
DROP TABLE IF EXISTS sys_license_feature_backup;
CREATE TABLE sys_license_feature_backup AS SELECT * FROM warehouse_system.sys_license_feature;

-- 备份wms_operation_log表（如果存在）
DROP TABLE IF EXISTS wms_operation_log_backup;
CREATE TABLE wms_operation_log_backup AS 
SELECT * FROM warehouse_system.wms_operation_log 
WHERE EXISTS (SELECT 1 FROM information_schema.tables 
              WHERE table_schema = 'warehouse_system' 
              AND table_name = 'wms_operation_log');

-- 备份sys_dict_data表（状态相关字典）
DROP TABLE IF EXISTS sys_dict_data_backup;
CREATE TABLE sys_dict_data_backup AS 
SELECT * FROM warehouse_system.sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status');

-- 创建备份信息表
DROP TABLE IF EXISTS backup_info;
CREATE TABLE backup_info (
    backup_id INT AUTO_INCREMENT PRIMARY KEY,
    backup_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    backup_type VARCHAR(50) DEFAULT 'field_standardization',
    table_name VARCHAR(100),
    record_count INT,
    backup_status VARCHAR(20) DEFAULT 'completed',
    remark TEXT
);

-- 记录备份信息
INSERT INTO backup_info (table_name, record_count, remark) VALUES
('sys_license', (SELECT COUNT(*) FROM sys_license_backup), '许可证表备份'),
('sys_license_feature', (SELECT COUNT(*) FROM sys_license_feature_backup), '许可证功能表备份'),
('wms_operation_log', (SELECT COUNT(*) FROM wms_operation_log_backup), '操作日志表备份'),
('sys_dict_data', (SELECT COUNT(*) FROM sys_dict_data_backup), '数据字典备份');

-- 显示备份结果
SELECT 
    '备份完成' as status,
    backup_time,
    table_name,
    record_count,
    remark
FROM backup_info 
WHERE backup_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY backup_id;

-- 验证备份完整性
SELECT 
    'sys_license' as table_name,
    (SELECT COUNT(*) FROM warehouse_system.sys_license) as original_count,
    (SELECT COUNT(*) FROM sys_license_backup) as backup_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM warehouse_system.sys_license) = (SELECT COUNT(*) FROM sys_license_backup) 
        THEN '备份完整' 
        ELSE '备份不完整' 
    END as backup_status
UNION ALL
SELECT 
    'sys_license_feature' as table_name,
    (SELECT COUNT(*) FROM warehouse_system.sys_license_feature) as original_count,
    (SELECT COUNT(*) FROM sys_license_feature_backup) as backup_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM warehouse_system.sys_license_feature) = (SELECT COUNT(*) FROM sys_license_feature_backup) 
        THEN '备份完整' 
        ELSE '备份不完整' 
    END as backup_status
UNION ALL
SELECT 
    'sys_dict_data' as table_name,
    (SELECT COUNT(*) FROM warehouse_system.sys_dict_data WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status')) as original_count,
    (SELECT COUNT(*) FROM sys_dict_data_backup) as backup_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM warehouse_system.sys_dict_data WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status')) = (SELECT COUNT(*) FROM sys_dict_data_backup) 
        THEN '备份完整' 
        ELSE '备份不完整' 
    END as backup_status;