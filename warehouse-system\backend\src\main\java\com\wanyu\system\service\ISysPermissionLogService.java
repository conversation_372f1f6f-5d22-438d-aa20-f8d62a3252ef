package com.wanyu.system.service;

import java.util.List;
import java.util.Map;
import com.wanyu.system.domain.SysPermissionLog;

/**
 * 权限日志Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ISysPermissionLogService 
{
    /**
     * 查询权限日志
     * 
     * @param logId 权限日志主键
     * @return 权限日志
     */
    public SysPermissionLog selectSysPermissionLogByLogId(Long logId);

    /**
     * 查询权限日志列表
     * 
     * @param sysPermissionLog 权限日志
     * @return 权限日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogList(SysPermissionLog sysPermissionLog);

    /**
     * 新增权限日志
     * 
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    public int insertSysPermissionLog(SysPermissionLog sysPermissionLog);

    /**
     * 修改权限日志
     * 
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    public int updateSysPermissionLog(SysPermissionLog sysPermissionLog);

    /**
     * 批量删除权限日志
     * 
     * @param logIds 需要删除的权限日志主键集合
     * @return 结果
     */
    public int deleteSysPermissionLogByLogIds(Long[] logIds);

    /**
     * 删除权限日志信息
     * 
     * @param logId 权限日志主键
     * @return 结果
     */
    public int deleteSysPermissionLogByLogId(Long logId);

    /**
     * 清空权限日志
     */
    public void cleanSysPermissionLog();

    /**
     * 获取权限统计信息
     * 
     * @param sysPermissionLog 查询条件
     * @return 统计信息
     */
    public Map<String, Object> getPermissionStatistics(SysPermissionLog sysPermissionLog);

    /**
     * 获取权限操作趋势数据
     * 
     * @param sysPermissionLog 查询条件
     * @return 趋势数据
     */
    public List<Map<String, Object>> getPermissionTrend(SysPermissionLog sysPermissionLog);

    /**
     * 记录权限操作日志
     * 
     * @param userName 用户名
     * @param nickName 用户昵称
     * @param permType 权限类型
     * @param permission 权限标识
     * @param operType 操作类型
     * @param status 操作状态
     * @param msg 操作消息
     */
    public void recordPermissionLog(String userName, String nickName, String permType, 
                                  String permission, String operType, String status, String msg);
}