<template>
  <el-row :gutter="20" class="mb20" v-if="visible">
    <el-col :span="24">
      <el-card class="trend-card">
        <div slot="header" class="clearfix">
          <span>{{ title }}</span>
          <el-button-group style="float: right;">
            <el-button 
              size="mini" 
              :type="period === '7d' ? 'primary' : ''" 
              @click="changePeriod('7d')"
            >7天</el-button>
            <el-button 
              size="mini" 
              :type="period === '30d' ? 'primary' : ''" 
              @click="changePeriod('30d')"
            >30天</el-button>
            <el-button 
              size="mini" 
              :type="period === '90d' ? 'primary' : ''" 
              @click="changePeriod('90d')"
            >90天</el-button>
          </el-button-group>
        </div>
        <div ref="trendChart" style="height: 300px;"></div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: "LogTrendChart",
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '趋势分析'
    },
    trendData: {
      type: Array,
      default: () => []
    },
    period: {
      type: String,
      default: '7d'
    },
    chartConfig: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
  watch: {
    trendData: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    visible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.initChart();
        });
      }
    }
  },
  methods: {
    /** 初始化图表 */
    initChart() {
      if (this.$refs.trendChart && !this.chart) {
        this.chart = echarts.init(this.$refs.trendChart);
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
          if (this.chart) {
            this.chart.resize();
          }
        });
        
        this.updateChart();
      }
    },
    /** 更新图表 */
    updateChart() {
      if (!this.chart) return;
      
      // 如果没有数据，显示空状态
      if (!this.trendData.length) {
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'middle',
            textStyle: {
              fontSize: 16,
              color: '#999'
            }
          }
        };
        this.chart.setOption(option);
        return;
      }
      
      const dates = this.trendData.map(item => item.date);
      const series = [];
      
      // 根据配置生成系列数据
      this.chartConfig.series.forEach(seriesConfig => {
        const data = this.trendData.map(item => item[seriesConfig.dataKey] || 0);
        
        const seriesItem = {
          name: seriesConfig.name,
          type: seriesConfig.type || 'line',
          data: data,
          smooth: seriesConfig.smooth !== false,
          itemStyle: {
            color: seriesConfig.color
          }
        };
        
        // 添加面积图样式
        if (seriesConfig.areaStyle) {
          seriesItem.areaStyle = {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: seriesConfig.areaStyle.startColor
              }, {
                offset: 1, color: seriesConfig.areaStyle.endColor
              }]
            }
          };
        }
        
        // 添加线条样式
        if (seriesConfig.lineStyle) {
          seriesItem.lineStyle = seriesConfig.lineStyle;
        }
        
        series.push(seriesItem);
      });
      
      const option = {
        title: {
          text: this.title,
          left: 'center',
          textStyle: {
            fontSize: 16,
            color: '#303133'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(param => {
              result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
            });
            return result;
          }
        },
        legend: {
          data: this.chartConfig.series.map(s => s.name),
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            formatter: function(value) {
              return value.substring(5); // 只显示月-日
            }
          }
        },
        yAxis: {
          type: 'value',
          name: this.chartConfig.yAxisName || '数量',
          nameTextStyle: {
            color: '#666'
          }
        },
        series: series
      };
      
      this.chart.setOption(option);
    },
    /** 切换周期 */
    changePeriod(newPeriod) {
      this.$emit('period-change', newPeriod);
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.trend-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.trend-card .el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.trend-card .el-card__body {
  padding: 20px;
}
</style>