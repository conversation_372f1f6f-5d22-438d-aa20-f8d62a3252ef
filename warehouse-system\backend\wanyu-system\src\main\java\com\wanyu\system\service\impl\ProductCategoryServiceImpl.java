package com.wanyu.system.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.common.core.domain.TreeSelect;
import com.wanyu.common.core.text.Convert;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.system.domain.ProductCategory;
import com.wanyu.system.mapper.ProductCategoryMapper;
import com.wanyu.system.service.IProductCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 物品分类Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ProductCategoryServiceImpl implements IProductCategoryService 
{
    private static final Logger logger = LoggerFactory.getLogger(ProductCategoryServiceImpl.class);
    
    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    /**
     * 查询物品分类
     * 
     * @param categoryId 物品分类主键
     * @return 物品分类
     */
    @Override
    public ProductCategory selectProductCategoryByCategoryId(Long categoryId)
    {
        return productCategoryMapper.selectProductCategoryByCategoryId(categoryId);
    }

    /**
     * 查询物品分类列表
     * 
     * @param productCategory 物品分类
     * @return 物品分类
     */
    @Override
    public List<ProductCategory> selectProductCategoryList(ProductCategory productCategory)
    {
        List<ProductCategory> list = productCategoryMapper.selectProductCategoryList(productCategory);
        logger.info("查询物品分类列表，参数: {}, 结果数量: {}", productCategory, list != null ? list.size() : 0);
        return list;
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param list 物品分类列表
     * @return 树结构列表
     */
    @Override
    public List<ProductCategory> buildCategoryTree(List<ProductCategory> list)
    {
        List<ProductCategory> returnList = new ArrayList<ProductCategory>();
        List<Long> tempList = list.stream().map(ProductCategory::getCategoryId).collect(Collectors.toList());
        for (ProductCategory dept : list)
        {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId()))
            {
                recursionFn(list, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = list;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param list 物品分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<ProductCategory> buildCategoryTreeSelect(List<ProductCategory> list)
    {
        List<ProductCategory> categoryTrees = buildCategoryTree(list);
        return categoryTrees;
    }
    
    /**
     * 查询物品分类树列表
     * 
     * @return 所有物品分类信息
     */
    @Override
    public List<ProductCategory> selectProductCategoryTreeList()
    {
        List<ProductCategory> list = productCategoryMapper.selectProductCategoryTreeList();
        logger.info("查询物品分类树列表，结果数量: {}", list != null ? list.size() : 0);
        return list;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<ProductCategory> list, ProductCategory t)
    {
        // 得到子节点列表
        List<ProductCategory> childList = getChildList(list, t);
        t.setChildren(childList);
        for (ProductCategory tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<ProductCategory> getChildList(List<ProductCategory> list, ProductCategory t)
    {
        List<ProductCategory> tlist = new ArrayList<ProductCategory>();
        Iterator<ProductCategory> it = list.iterator();
        while (it.hasNext())
        {
            ProductCategory n = it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getCategoryId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<ProductCategory> list, ProductCategory t)
    {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 新增物品分类
     * 
     * @param productCategory 物品分类
     * @return 结果
     */
    @Override
    public int insertProductCategory(ProductCategory productCategory)
    {
        productCategory.setCreateTime(DateUtils.getNowDate());
        return productCategoryMapper.insertProductCategory(productCategory);
    }

    /**
     * 修改物品分类
     * 
     * @param productCategory 物品分类
     * @return 结果
     */
    @Override
    public int updateProductCategory(ProductCategory productCategory)
    {
        productCategory.setUpdateTime(DateUtils.getNowDate());
        return productCategoryMapper.updateProductCategory(productCategory);
    }

    /**
     * 批量删除物品分类
     * 
     * @param categoryIds 需要删除的物品分类主键
     * @return 结果
     */
    @Override
    public int deleteProductCategoryByCategoryIds(Long[] categoryIds)
    {
        return productCategoryMapper.deleteProductCategoryByCategoryIds(categoryIds);
    }

    /**
     * 删除物品分类信息
     * 
     * @param categoryId 物品分类主键
     * @return 结果
     */
    @Override
    public int deleteProductCategoryByCategoryId(Long categoryId)
    {
        return productCategoryMapper.deleteProductCategoryByCategoryId(categoryId);
    }
}
