package com.wanyu.system.service;

/**
 * 通知服务接口
 * 用于发送各种类型的通知和告警
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public interface INotificationService {
    
    /**
     * 发送告警通知
     * 
     * @param title 告警标题
     * @param message 告警消息
     */
    void sendAlert(String title, String message);
    
    /**
     * 发送邮件通知
     * 
     * @param to 收件人
     * @param subject 邮件主题
     * @param content 邮件内容
     */
    void sendEmail(String to, String subject, String content);
    
    /**
     * 发送系统通知
     * 
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     */
    void sendSystemNotification(Long userId, String title, String content);
    
    /**
     * 发送短信通知
     * 
     * @param phoneNumber 手机号
     * @param message 短信内容
     */
    void sendSms(String phoneNumber, String message);
}