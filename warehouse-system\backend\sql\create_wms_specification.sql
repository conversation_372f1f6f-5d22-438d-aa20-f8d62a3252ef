-- ----------------------------
-- Table structure for wms_specification
-- ----------------------------
DROP TABLE IF EXISTS `wms_specification`;
CREATE TABLE `wms_specification` (
  `spec_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Specification ID',
  `spec_name` varchar(100) NOT NULL COMMENT 'Specification Name',
  `spec_code` varchar(50) NOT NULL COMMENT 'Specification Code',
  `status` char(1) DEFAULT '0' COMMENT 'Status (0:Normal 1:Disabled)',
  `create_by` varchar(64) DEFAULT '' COMMENT 'Created By',
  `create_time` datetime DEFAULT NULL COMMENT 'Create Time',
  `update_by` varchar(64) DEFAULT '' COMMENT 'Updated By',
  `update_time` datetime DEFAULT NULL COMMENT 'Update Time',
  `remark` varchar(500) DEFAULT NULL COMMENT 'Remark',
  PRIMARY KEY (`spec_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='Product Specification Table';

-- ----------------------------
-- Records of wms_specification
-- ----------------------------
INSERT INTO `wms_specification` VALUES ('1', 'Standard', 'STD', '0', 'admin', '2023-07-01 11:33:00', 'admin', '2023-07-01 11:33:00', 'Standard Specification');
INSERT INTO `wms_specification` VALUES ('2', 'Large', 'L', '0', 'admin', '2023-07-01 11:33:00', 'admin', '2023-07-01 11:33:00', 'Large Specification');
INSERT INTO `wms_specification` VALUES ('3', 'Small', 'S', '0', 'admin', '2023-07-01 11:33:00', 'admin', '2023-07-01 11:33:00', 'Small Specification');
