package com.wanyu.system;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import com.wanyu.system.domain.SysLicense;
import com.wanyu.system.domain.SysLicenseFeature;
import com.wanyu.system.domain.WmsOperationLog;
import com.wanyu.system.mapper.SysLicenseMapper;
import com.wanyu.system.mapper.SysLicenseFeatureMapper;
import com.wanyu.system.mapper.WmsOperationLogMapper;
import com.wanyu.system.service.ISysLicenseService;
import com.wanyu.system.service.ISysLicenseFeatureService;
import com.wanyu.system.service.IWmsOperationLogService;
import com.wanyu.system.service.impl.SysLicenseServiceImpl;
import com.wanyu.system.service.impl.SysLicenseFeatureServiceImpl;
import com.wanyu.system.service.impl.WmsOperationLogServiceImpl;

/**
 * 字段标准化单元测试类
 * 
 * 测试许可证状态标准化和操作日志状态标准化的功能
 * 验证字段定义标准：0=正常/启用/成功，1=异常/禁用/失败
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@DisplayName("字段标准化单元测试")
public class FieldStandardizationTest {

    @Mock
    private SysLicenseMapper licenseMapper;
    
    @Mock
    private SysLicenseFeatureMapper licenseFeatureMapper;
    
    @Mock
    private WmsOperationLogMapper operationLogMapper;
    
    @InjectMocks
    private SysLicenseServiceImpl licenseService;
    
    @InjectMocks
    private SysLicenseFeatureServiceImpl licenseFeatureService;
    
    @InjectMocks
    private WmsOperationLogServiceImpl operationLogService;

    private SysLicense testLicense;
    private SysLicenseFeature testFeature;
    private WmsOperationLog testOperationLog;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testLicense = new SysLicense();
        testLicense.setLicenseId(1L);
        testLicense.setLicenseKey("TEST-LICENSE-KEY");
        testLicense.setCompanyName("测试公司");
        testLicense.setLicenseType("standard");
        testLicense.setStatus("0"); // 新标准：0=启用
        
        testFeature = new SysLicenseFeature();
        testFeature.setFeatureId(1L);
        testFeature.setFeatureCode("INVENTORY_MANAGEMENT");
        testFeature.setFeatureName("库存管理");
        testFeature.setStatus("0"); // 新标准：0=启用
        
        testOperationLog = new WmsOperationLog();
        testOperationLog.setLogId(1L);
        testOperationLog.setOperationType("INVENTORY_IN");
        testOperationLog.setOperationDesc("商品入库操作");
        testOperationLog.setOperationStatus("0"); // 新标准：0=成功
        testOperationLog.setOperationTime(new Date());
        testOperationLog.setOperatorId(1L);
        testOperationLog.setOperatorName("测试用户");
    }

    // ==================== 许可证状态标准化测试 ====================

    @Test
    @DisplayName("测试许可证状态标准化 - 启用状态应为0")
    void testLicenseStatusStandardization_EnabledShouldBeZero() {
        // Given: 创建启用状态的许可证
        testLicense.setStatus("0"); // 新标准：0=启用
        when(licenseMapper.insertSysLicense(any(SysLicense.class))).thenReturn(1);
        
        // When: 插入许可证
        int result = licenseService.insertSysLicense(testLicense);
        
        // Then: 验证插入成功且状态为0
        assertEquals(1, result);
        assertEquals("0", testLicense.getStatus());
        verify(licenseMapper).insertSysLicense(testLicense);
    }

    @Test
    @DisplayName("测试许可证状态标准化 - 禁用状态应为1")
    void testLicenseStatusStandardization_DisabledShouldBeOne() {
        // Given: 创建禁用状态的许可证
        testLicense.setStatus("1"); // 新标准：1=禁用
        when(licenseMapper.updateSysLicense(any(SysLicense.class))).thenReturn(1);
        
        // When: 更新许可证状态为禁用
        int result = licenseService.updateSysLicense(testLicense);
        
        // Then: 验证更新成功且状态为1
        assertEquals(1, result);
        assertEquals("1", testLicense.getStatus());
        verify(licenseMapper).updateSysLicense(testLicense);
    }

    @Test
    @DisplayName("测试查询启用的许可证 - 应使用status='0'条件")
    void testSelectEnabledLicenses_ShouldUseStatusZero() {
        // Given: 模拟查询启用的许可证
        SysLicense queryLicense = new SysLicense();
        queryLicense.setStatus("0"); // 查询启用状态
        
        List<SysLicense> enabledLicenses = Arrays.asList(testLicense);
        when(licenseMapper.selectSysLicenseList(any(SysLicense.class))).thenReturn(enabledLicenses);
        
        // When: 查询启用的许可证
        List<SysLicense> result = licenseService.selectSysLicenseList(queryLicense);
        
        // Then: 验证查询结果正确
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("0", result.get(0).getStatus());
        
        // 验证查询条件使用了正确的状态值
        verify(licenseMapper).selectSysLicenseList(argThat(license -> 
            "0".equals(license.getStatus())
        ));
    }

    @Test
    @DisplayName("测试许可证状态更新 - 启用操作")
    void testUpdateLicenseStatus_EnableOperation() {
        // Given: 准备启用许可证
        Long licenseId = 1L;
        String enableStatus = "0"; // 新标准：0=启用
        when(licenseMapper.updateSysLicense(any(SysLicense.class))).thenReturn(1);
        
        // When: 启用许可证
        int result = licenseService.updateLicenseStatus(licenseId, enableStatus);
        
        // Then: 验证启用成功
        assertEquals(1, result);
        verify(licenseMapper).updateSysLicense(argThat(license -> 
            licenseId.equals(license.getLicenseId()) && "0".equals(license.getStatus())
        ));
    }

    @Test
    @DisplayName("测试许可证状态更新 - 禁用操作")
    void testUpdateLicenseStatus_DisableOperation() {
        // Given: 准备禁用许可证
        Long licenseId = 1L;
        String disableStatus = "1"; // 新标准：1=禁用
        when(licenseMapper.updateSysLicense(any(SysLicense.class))).thenReturn(1);
        
        // When: 禁用许可证
        int result = licenseService.updateLicenseStatus(licenseId, disableStatus);
        
        // Then: 验证禁用成功
        assertEquals(1, result);
        verify(licenseMapper).updateSysLicense(argThat(license -> 
            licenseId.equals(license.getLicenseId()) && "1".equals(license.getStatus())
        ));
    }

    // ==================== 许可证功能状态标准化测试 ====================

    @Test
    @DisplayName("测试许可证功能状态标准化 - 启用功能应为0")
    void testLicenseFeatureStatusStandardization_EnabledShouldBeZero() {
        // Given: 创建启用状态的功能
        testFeature.setStatus("0"); // 新标准：0=启用
        when(licenseFeatureMapper.insertSysLicenseFeature(any(SysLicenseFeature.class))).thenReturn(1);
        
        // When: 插入功能
        int result = licenseFeatureService.insertSysLicenseFeature(testFeature);
        
        // Then: 验证插入成功且状态为0
        assertEquals(1, result);
        assertEquals("0", testFeature.getStatus());
        verify(licenseFeatureMapper).insertSysLicenseFeature(testFeature);
    }

    @Test
    @DisplayName("测试查询启用的功能 - 应使用status='0'条件")
    void testSelectEnabledFeatures_ShouldUseStatusZero() {
        // Given: 模拟查询启用的功能
        List<SysLicenseFeature> enabledFeatures = Arrays.asList(testFeature);
        when(licenseFeatureMapper.selectSysLicenseFeatureList(any(SysLicenseFeature.class))).thenReturn(enabledFeatures);
        
        // When: 查询启用的功能
        List<SysLicenseFeature> result = licenseFeatureService.selectEnabledFeatures();
        
        // Then: 验证查询结果正确
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("0", result.get(0).getStatus());
        
        // 验证查询条件使用了正确的状态值
        verify(licenseFeatureMapper).selectSysLicenseFeatureList(argThat(feature -> 
            "0".equals(feature.getStatus())
        ));
    }

    @Test
    @DisplayName("测试启用功能操作 - 应设置status='0'")
    void testEnableFeature_ShouldSetStatusZero() {
        // Given: 准备启用功能
        Long featureId = 1L;
        when(licenseFeatureMapper.updateSysLicenseFeature(any(SysLicenseFeature.class))).thenReturn(1);
        
        // When: 启用功能
        int result = licenseFeatureService.enableFeature(featureId);
        
        // Then: 验证启用成功
        assertEquals(1, result);
        verify(licenseFeatureMapper).updateSysLicenseFeature(argThat(feature -> 
            featureId.equals(feature.getFeatureId()) && "0".equals(feature.getStatus())
        ));
    }

    @Test
    @DisplayName("测试禁用功能操作 - 应设置status='1'")
    void testDisableFeature_ShouldSetStatusOne() {
        // Given: 准备禁用功能
        Long featureId = 1L;
        when(licenseFeatureMapper.updateSysLicenseFeature(any(SysLicenseFeature.class))).thenReturn(1);
        
        // When: 禁用功能
        int result = licenseFeatureService.disableFeature(featureId);
        
        // Then: 验证禁用成功
        assertEquals(1, result);
        verify(licenseFeatureMapper).updateSysLicenseFeature(argThat(feature -> 
            featureId.equals(feature.getFeatureId()) && "1".equals(feature.getStatus())
        ));
    }

    // ==================== 操作日志状态标准化测试 ====================

    @Test
    @DisplayName("测试操作日志状态标准化 - 成功状态应为0")
    void testOperationLogStatusStandardization_SuccessShouldBeZero() {
        // Given: 创建成功状态的操作日志
        testOperationLog.setOperationStatus("0"); // 新标准：0=成功
        when(operationLogMapper.insertWmsOperationLog(any(WmsOperationLog.class))).thenReturn(1);
        
        // When: 插入操作日志
        int result = operationLogService.insertWmsOperationLog(testOperationLog);
        
        // Then: 验证插入成功且状态为0
        assertEquals(1, result);
        assertEquals("0", testOperationLog.getOperationStatus());
        verify(operationLogMapper).insertWmsOperationLog(testOperationLog);
    }

    @Test
    @DisplayName("测试操作日志状态标准化 - 失败状态应为1")
    void testOperationLogStatusStandardization_FailureShouldBeOne() {
        // Given: 创建失败状态的操作日志
        testOperationLog.setOperationStatus("1"); // 新标准：1=失败
        testOperationLog.setErrorMessage("操作失败：库存不足");
        when(operationLogMapper.insertWmsOperationLog(any(WmsOperationLog.class))).thenReturn(1);
        
        // When: 插入失败的操作日志
        int result = operationLogService.insertWmsOperationLog(testOperationLog);
        
        // Then: 验证插入成功且状态为1
        assertEquals(1, result);
        assertEquals("1", testOperationLog.getOperationStatus());
        assertNotNull(testOperationLog.getErrorMessage());
        verify(operationLogMapper).insertWmsOperationLog(testOperationLog);
    }

    @Test
    @DisplayName("测试查询成功操作 - 应使用operation_status='0'条件")
    void testSelectSuccessOperations_ShouldUseStatusZero() {
        // Given: 模拟查询成功操作
        WmsOperationLog queryLog = new WmsOperationLog();
        queryLog.setOperationStatus("0"); // 查询成功状态
        
        List<WmsOperationLog> successLogs = Arrays.asList(testOperationLog);
        when(operationLogMapper.selectWmsOperationLogList(any(WmsOperationLog.class))).thenReturn(successLogs);
        
        // When: 查询成功操作
        List<WmsOperationLog> result = operationLogService.selectSuccessOperations(queryLog);
        
        // Then: 验证查询结果正确
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("0", result.get(0).getOperationStatus());
        
        // 验证查询条件使用了正确的状态值
        verify(operationLogMapper).selectWmsOperationLogList(argThat(log -> 
            "0".equals(log.getOperationStatus())
        ));
    }

    @Test
    @DisplayName("测试查询失败操作 - 应使用operation_status='1'条件")
    void testSelectFailedOperations_ShouldUseStatusOne() {
        // Given: 模拟查询失败操作
        WmsOperationLog queryLog = new WmsOperationLog();
        queryLog.setOperationStatus("1"); // 查询失败状态
        
        WmsOperationLog failedLog = new WmsOperationLog();
        failedLog.setOperationStatus("1");
        failedLog.setErrorMessage("操作失败");
        
        List<WmsOperationLog> failedLogs = Arrays.asList(failedLog);
        when(operationLogMapper.selectWmsOperationLogList(any(WmsOperationLog.class))).thenReturn(failedLogs);
        
        // When: 查询失败操作
        List<WmsOperationLog> result = operationLogService.selectFailedOperations(queryLog);
        
        // Then: 验证查询结果正确
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("1", result.get(0).getOperationStatus());
        assertNotNull(result.get(0).getErrorMessage());
        
        // 验证查询条件使用了正确的状态值
        verify(operationLogMapper).selectWmsOperationLogList(argThat(log -> 
            "1".equals(log.getOperationStatus())
        ));
    }

    @Test
    @DisplayName("测试记录成功操作 - 应设置operation_status='0'")
    void testLogSuccessOperation_ShouldSetStatusZero() {
        // Given: 准备记录成功操作
        when(operationLogMapper.insertWmsOperationLog(any(WmsOperationLog.class))).thenReturn(1);
        
        // When: 记录成功操作
        int result = operationLogService.logSuccessOperation(
            "INVENTORY_IN", "商品入库成功", 1L, "测试用户", 
            "192.168.1.100", "/api/inventory/in", "POST", 
            "{\"productId\":1}", "{\"success\":true}", 100L
        );
        
        // Then: 验证记录成功
        assertEquals(1, result);
        verify(operationLogMapper).insertWmsOperationLog(argThat(log -> 
            "0".equals(log.getOperationStatus()) && 
            "INVENTORY_IN".equals(log.getOperationType()) &&
            "商品入库成功".equals(log.getOperationDesc())
        ));
    }

    @Test
    @DisplayName("测试记录失败操作 - 应设置operation_status='1'")
    void testLogFailedOperation_ShouldSetStatusOne() {
        // Given: 准备记录失败操作
        when(operationLogMapper.insertWmsOperationLog(any(WmsOperationLog.class))).thenReturn(1);
        
        // When: 记录失败操作
        int result = operationLogService.logFailedOperation(
            "INVENTORY_OUT", "商品出库失败", "库存不足", 1L, "测试用户", 
            "192.168.1.100", "/api/inventory/out", "POST", 
            "{\"productId\":1,\"quantity\":100}", 150L
        );
        
        // Then: 验证记录成功
        assertEquals(1, result);
        verify(operationLogMapper).insertWmsOperationLog(argThat(log -> 
            "1".equals(log.getOperationStatus()) && 
            "INVENTORY_OUT".equals(log.getOperationType()) &&
            "商品出库失败".equals(log.getOperationDesc()) &&
            "库存不足".equals(log.getErrorMessage())
        ));
    }

    // ==================== 字段标准验证测试 ====================

    @Test
    @DisplayName("测试字段标准验证 - 状态字段只能为0或1")
    void testFieldStandardValidation_StatusFieldShouldBeZeroOrOne() {
        // Given & When & Then: 测试有效状态值
        assertTrue(isValidStatusValue("0"), "状态值'0'应该有效");
        assertTrue(isValidStatusValue("1"), "状态值'1'应该有效");
        
        // 测试无效状态值
        assertFalse(isValidStatusValue("2"), "状态值'2'应该无效");
        assertFalse(isValidStatusValue("Y"), "状态值'Y'应该无效");
        assertFalse(isValidStatusValue("N"), "状态值'N'应该无效");
        assertFalse(isValidStatusValue("true"), "状态值'true'应该无效");
        assertFalse(isValidStatusValue("false"), "状态值'false'应该无效");
        assertFalse(isValidStatusValue(null), "状态值null应该无效");
        assertFalse(isValidStatusValue(""), "空状态值应该无效");
    }

    @Test
    @DisplayName("测试字段标准一致性 - 所有状态字段应遵循相同标准")
    void testFieldStandardConsistency_AllStatusFieldsShouldFollowSameStandard() {
        // Given: 创建包含各种状态字段的对象
        SysLicense license = new SysLicense();
        license.setStatus("0"); // 启用状态
        
        SysLicenseFeature feature = new SysLicenseFeature();
        feature.setStatus("0"); // 启用状态
        feature.setIsCore("1"); // 是核心功能
        
        WmsOperationLog log = new WmsOperationLog();
        log.setOperationStatus("0"); // 成功状态
        
        // When & Then: 验证所有状态字段都遵循相同标准
        assertEquals("0", license.getStatus(), "许可证启用状态应为0");
        assertEquals("0", feature.getStatus(), "功能启用状态应为0");
        assertEquals("1", feature.getIsCore(), "核心功能标记应为1");
        assertEquals("0", log.getOperationStatus(), "操作成功状态应为0");
        
        // 验证状态含义的一致性
        assertTrue(isEnabledStatus(license.getStatus()), "许可证状态0应表示启用");
        assertTrue(isEnabledStatus(feature.getStatus()), "功能状态0应表示启用");
        assertTrue(isSuccessStatus(log.getOperationStatus()), "操作状态0应表示成功");
    }

    // ==================== 辅助方法 ====================

    /**
     * 验证状态值是否有效
     */
    private boolean isValidStatusValue(String status) {
        return "0".equals(status) || "1".equals(status);
    }

    /**
     * 判断是否为启用状态
     */
    private boolean isEnabledStatus(String status) {
        return "0".equals(status);
    }

    /**
     * 判断是否为成功状态
     */
    private boolean isSuccessStatus(String status) {
        return "0".equals(status);
    }
}