# 库区和货架组件删除完成指南

## 概述
本指南详细说明了如何完整删除仓库管理系统中的库区和货架相关功能组件。

## 删除内容

### 1. 数据库表
- `wms_warehouse_area` - 库区表
- `wms_warehouse_rack` - 货架表  
- `sys_warehouse_area` - 系统库区表
- `sys_warehouse_rack` - 系统货架表

### 2. 前端组件
- `warehouse-system/frontend/src/views/warehouse/area/` - 库区管理页面
- `warehouse-system/frontend/src/views/warehouse/location/` - 库位管理页面
- `warehouse-system/frontend/src/views/warehouse/rack/` - 货架管理页面

### 3. 后端代码
- Controller层：`WarehouseAreaController.java`, `WarehouseRackController.java`
- Service层：`IWarehouseAreaService.java`, `WarehouseAreaServiceImpl.java`
- Mapper层：`WarehouseAreaMapper.java`, `WarehouseAreaMapper.xml`
- Domain层：`WarehouseArea.java`, `WarehouseRack.java`

### 4. 路由配置
- 已更新 `warehouse-system/frontend/src/router/modules/warehouse.js`
- 删除了库区和库位相关的路由配置

### 5. 菜单权限
- 删除了"仓库区域"、"库位管理"、"货架管理"相关菜单项
- 清理了相关权限配置

## 执行步骤

### 方法一：自动执行（推荐）
```bash
# 执行完整删除脚本
execute_complete_area_location_removal.bat
```

### 方法二：手动执行
1. **检查字段依赖**
   ```sql
   mysql -u root -p123456 warehouse_system < check_and_remove_area_rack_fields.sql
   ```

2. **删除数据库表**
   ```sql
   mysql -u root -p123456 warehouse_system < complete_remove_area_location_components.sql
   ```

3. **删除前端组件**
   ```bash
   rmdir /s /q "warehouse-system\frontend\src\views\warehouse\area"
   rmdir /s /q "warehouse-system\frontend\src\views\warehouse\location"
   ```

4. **删除后端代码**
   - 删除相关的Controller、Service、Mapper、Domain文件

5. **更新路由配置**
   - 已自动更新 `warehouse.js` 文件

## 验证删除结果

### 1. 数据库验证
```sql
-- 检查表是否已删除
SHOW TABLES LIKE '%area%';
SHOW TABLES LIKE '%rack%';

-- 检查其他表中的字段依赖
SELECT TABLE_NAME, COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND (COLUMN_NAME LIKE '%area_id%' OR COLUMN_NAME LIKE '%rack_id%');
```

### 2. 前端验证
- 检查 `/warehouse` 路由下只剩下仓库信息管理
- 确认相关页面组件已删除

### 3. 后端验证
- 重新编译项目，确认没有编译错误
- 检查相关Java文件已删除

## 注意事项

### 1. 数据备份
- 执行删除前请先备份数据库
- 建议备份整个项目代码

### 2. 字段依赖检查
- 检查 `area_rack_fields_check.txt` 文件
- 如果其他表有 `area_id` 或 `rack_id` 字段，需要手动删除

### 3. 重新编译
```bash
# 后端重新编译
cd warehouse-system/backend
mvn clean compile

# 前端重新编译
cd warehouse-system/frontend  
npm run build:prod
```

### 4. 重启服务
```bash
# 重启后端服务
cd C:\CKGLXT\warehouse-system\backend
java -jar wanyu-admin.jar

# 重启前端服务
cd C:\CKGLXT\warehouse-system\frontend
npm run dev
```

## 可能需要手动处理的情况

### 1. 其他表的字段依赖
如果发现其他表有 `area_id` 或 `rack_id` 字段，请执行：
```sql
ALTER TABLE 表名 DROP COLUMN area_id;
ALTER TABLE 表名 DROP COLUMN rack_id;
```

### 2. 业务逻辑依赖
检查以下文件是否有相关引用：
- 库存管理相关的Service和Controller
- 入库出库相关的业务逻辑
- 报表统计相关的查询

### 3. 前端页面引用
检查其他前端页面是否有对库区和货架的引用：
- 下拉选择组件
- 表单字段
- 数据展示

## 完成确认

删除完成后，系统应该：
- ✅ 数据库中不再有库区和货架相关表
- ✅ 前端菜单中不再显示库区和货架管理
- ✅ 后端编译无错误
- ✅ 系统正常启动和运行
- ✅ 其他功能不受影响

## 联系支持
如果在删除过程中遇到问题，请检查：
1. 数据库连接是否正常
2. 文件权限是否足够
3. 是否有其他进程占用相关文件

删除完成后，仓库管理系统将更加简洁，只保留核心的仓库信息管理功能。