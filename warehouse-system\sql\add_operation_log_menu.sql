-- 添加操作日志菜单和权限
-- 符合字段定义标准，使用正确的状态值

-- 1. 添加操作日志菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('操作日志', 108, 4, 'operationLog', 'system/operationLog/index', '', 1, 0, 'C', '0', '0', 'system:operationLog:list', 'log', 'admin', sysdate(), '', null, '操作日志菜单');

-- 获取刚插入的菜单ID
SET @menu_id = LAST_INSERT_ID();

-- 2. 添加操作日志按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('操作日志查询', @menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:operationLog:query', '#', 'admin', sysdate(), '', null, ''),
('操作日志新增', @menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:operationLog:add', '#', 'admin', sysdate(), '', null, ''),
('操作日志修改', @menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:operationLog:edit', '#', 'admin', sysdate(), '', null, ''),
('操作日志删除', @menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:operationLog:remove', '#', 'admin', sysdate(), '', null, ''),
('操作日志导出', @menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:operationLog:export', '#', 'admin', sysdate(), '', null, ''),
('操作日志清理', @menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:operationLog:clean', '#', 'admin', sysdate(), '', null, '');

-- 3. 为管理员角色分配操作日志权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'system:operationLog:%';

-- 4. 验证菜单添加结果
SELECT 
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    menu_type,
    visible,
    status,
    perms,
    icon
FROM sys_menu 
WHERE menu_name LIKE '%操作日志%' OR perms LIKE 'system:operationLog:%'
ORDER BY parent_id, order_num;