package com.wanyu.system.mapper;

import java.util.List;
import com.wanyu.system.domain.WmsBarcode;

/**
 * 物品条码Mapper接口
 *
 * <AUTHOR>
 */
public interface WmsBarcodeMapper
{
    /**
     * 查询物品条码
     *
     * @param barcodeId 物品条码主键
     * @return 物品条码
     */
    public WmsBarcode selectWmsBarcodeByBarcodeId(Long barcodeId);

    /**
     * 查询物品条码列表
     *
     * @param wmsBarcode 物品条码
     * @return 物品条码集合
     */
    public List<WmsBarcode> selectWmsBarcodeList(WmsBarcode wmsBarcode);

    /**
     * 新增物品条码
     *
     * @param wmsBarcode 物品条码
     * @return 结果
     */
    public int insertWmsBarcode(WmsBarcode wmsBarcode);

    /**
     * 修改物品条码
     *
     * @param wmsBarcode 物品条码
     * @return 结果
     */
    public int updateWmsBarcode(WmsBarcode wmsBarcode);

    /**
     * 删除物品条码
     *
     * @param barcodeId 物品条码主键
     * @return 结果
     */
    public int deleteWmsBarcodeByBarcodeId(Long barcodeId);

    /**
     * 批量删除物品条码
     *
     * @param barcodeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWmsBarcodeByBarcodeIds(Long[] barcodeIds);

    /**
     * 根据条码内容查询物品条码
     *
     * @param barcodeContent 条码内容
     * @return 物品条码
     */
    public WmsBarcode selectWmsBarcodeByContent(String barcodeContent);

    /**
     * 根据物品ID查询物品条码列表
     *
     * @param productId 物品ID
     * @return 物品条码集合
     */
    public List<WmsBarcode> selectWmsBarcodeByProductId(Long productId);

    /**
     * 批量新增物品条码
     *
     * @param wmsBarcodeList 物品条码列表
     * @return 结果
     */
    public int batchInsertWmsBarcode(List<WmsBarcode> wmsBarcodeList);
}