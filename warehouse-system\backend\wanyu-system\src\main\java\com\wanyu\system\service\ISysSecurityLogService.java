package com.wanyu.system.service;

import java.util.List;
import com.wanyu.system.domain.SysSecurityLog;

/**
 * 安全日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface ISysSecurityLogService 
{
    /**
     * 查询安全日志
     * 
     * @param logId 安全日志主键
     * @return 安全日志
     */
    public SysSecurityLog selectSysSecurityLogByLogId(Long logId);

    /**
     * 查询安全日志列表
     * 
     * @param sysSecurityLog 安全日志
     * @return 安全日志集合
     */
    public List<SysSecurityLog> selectSysSecurityLogList(SysSecurityLog sysSecurityLog);

    /**
     * 新增安全日志
     * 
     * @param sysSecurityLog 安全日志
     * @return 结果
     */
    public int insertSysSecurityLog(SysSecurityLog sysSecurityLog);

    /**
     * 修改安全日志
     * 
     * @param sysSecurityLog 安全日志
     * @return 结果
     */
    public int updateSysSecurityLog(SysSecurityLog sysSecurityLog);

    /**
     * 批量删除安全日志
     * 
     * @param logIds 需要删除的安全日志主键集合
     * @return 结果
     */
    public int deleteSysSecurityLogByLogIds(Long[] logIds);

    /**
     * 删除安全日志信息
     * 
     * @param logId 安全日志主键
     * @return 结果
     */
    public int deleteSysSecurityLogByLogId(Long logId);

    /**
     * 处理安全事件
     * 
     * @param sysSecurityLog 安全日志
     * @return 结果
     */
    public int handleSecurityEvent(SysSecurityLog sysSecurityLog);

    /**
     * 记录安全事件
     * 
     * @param eventType 事件类型
     * @param eventDesc 事件描述
     * @param riskLevel 风险级别
     * @param userName 用户名
     * @param clientIp 客户端IP
     */
    public void recordSecurityEvent(String eventType, String eventDesc, String riskLevel, String userName, String clientIp);
}
