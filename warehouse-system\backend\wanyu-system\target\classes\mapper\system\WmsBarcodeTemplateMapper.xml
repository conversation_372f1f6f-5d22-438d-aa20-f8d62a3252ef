<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsBarcodeTemplateMapper">
    
    <resultMap type="WmsBarcodeTemplate" id="WmsBarcodeTemplateResult">
        <result property="templateId"        column="template_id"        />
        <result property="templateName"      column="template_name"      />
        <result property="templateType"      column="template_type"      />
        <result property="templateWidth"     column="template_width"     />
        <result property="templateHeight"    column="template_height"    />
        <result property="barcodeWidth"      column="barcode_width"      />
        <result property="barcodeHeight"     column="barcode_height"     />
        <result property="fontSize"          column="font_size"          />
        <result property="showText"          column="show_text"          />
        <result property="showProductName"   column="show_product_name"  />
        <result property="status"            column="status"             />
        <result property="remark"            column="remark"             />
        <result property="createBy"          column="create_by"          />
        <result property="createTime"        column="create_time"        />
        <result property="updateBy"          column="update_by"          />
        <result property="updateTime"        column="update_time"        />
    </resultMap>

    <sql id="selectWmsBarcodeTemplateVo">
        select template_id, template_name, template_type, template_width, template_height, barcode_width, barcode_height, font_size, show_text, show_product_name, status, remark, create_by, create_time, update_by, update_time from wms_barcode_template
    </sql>

    <select id="selectWmsBarcodeTemplateList" parameterType="WmsBarcodeTemplate" resultMap="WmsBarcodeTemplateResult">
        <include refid="selectWmsBarcodeTemplateVo"/>
        <where>  
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="templateType != null  and templateType != ''"> and template_type = #{templateType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWmsBarcodeTemplateByTemplateId" parameterType="Long" resultMap="WmsBarcodeTemplateResult">
        <include refid="selectWmsBarcodeTemplateVo"/>
        where template_id = #{templateId}
    </select>
        
    <insert id="insertWmsBarcodeTemplate" parameterType="WmsBarcodeTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into wms_barcode_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name,</if>
            <if test="templateType != null and templateType != ''">template_type,</if>
            <if test="templateWidth != null">template_width,</if>
            <if test="templateHeight != null">template_height,</if>
            <if test="barcodeWidth != null">barcode_width,</if>
            <if test="barcodeHeight != null">barcode_height,</if>
            <if test="fontSize != null">font_size,</if>
            <if test="showText != null">show_text,</if>
            <if test="showProductName != null">show_product_name,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">#{templateName},</if>
            <if test="templateType != null and templateType != ''">#{templateType},</if>
            <if test="templateWidth != null">#{templateWidth},</if>
            <if test="templateHeight != null">#{templateHeight},</if>
            <if test="barcodeWidth != null">#{barcodeWidth},</if>
            <if test="barcodeHeight != null">#{barcodeHeight},</if>
            <if test="fontSize != null">#{fontSize},</if>
            <if test="showText != null">#{showText},</if>
            <if test="showProductName != null">#{showProductName},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateWmsBarcodeTemplate" parameterType="WmsBarcodeTemplate">
        update wms_barcode_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name = #{templateName},</if>
            <if test="templateType != null and templateType != ''">template_type = #{templateType},</if>
            <if test="templateWidth != null">template_width = #{templateWidth},</if>
            <if test="templateHeight != null">template_height = #{templateHeight},</if>
            <if test="barcodeWidth != null">barcode_width = #{barcodeWidth},</if>
            <if test="barcodeHeight != null">barcode_height = #{barcodeHeight},</if>
            <if test="fontSize != null">font_size = #{fontSize},</if>
            <if test="showText != null">show_text = #{showText},</if>
            <if test="showProductName != null">show_product_name = #{showProductName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where template_id = #{templateId}
    </update>

    <delete id="deleteWmsBarcodeTemplateByTemplateId" parameterType="Long">
        delete from wms_barcode_template where template_id = #{templateId}
    </delete>

    <delete id="deleteWmsBarcodeTemplateByTemplateIds" parameterType="String">
        delete from wms_barcode_template where template_id in 
        <foreach item="templateId" collection="array" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>

</mapper>