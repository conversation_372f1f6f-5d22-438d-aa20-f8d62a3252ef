package com.wanyu.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.system.mapper.SysPermissionLogMapper;
import com.wanyu.system.domain.SysPermissionLog;
import com.wanyu.system.service.ISysPermissionLogService;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.ip.IpUtils;
import com.wanyu.common.utils.ip.AddressUtils;
import com.wanyu.common.utils.ServletUtils;

/**
 * 权限日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class SysPermissionLogServiceImpl implements ISysPermissionLogService 
{
    @Autowired
    private SysPermissionLogMapper sysPermissionLogMapper;

    /**
     * 查询权限日志
     * 
     * @param logId 权限日志主键
     * @return 权限日志
     */
    @Override
    public SysPermissionLog selectSysPermissionLogByLogId(Long logId)
    {
        return sysPermissionLogMapper.selectSysPermissionLogByLogId(logId);
    }

    /**
     * 查询权限日志列表
     * 
     * @param sysPermissionLog 权限日志
     * @return 权限日志
     */
    @Override
    public List<SysPermissionLog> selectSysPermissionLogList(SysPermissionLog sysPermissionLog)
    {
        return sysPermissionLogMapper.selectSysPermissionLogList(sysPermissionLog);
    }

    /**
     * 新增权限日志
     * 
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    @Override
    public int insertSysPermissionLog(SysPermissionLog sysPermissionLog)
    {
        sysPermissionLog.setCreateTime(new Date());
        return sysPermissionLogMapper.insertSysPermissionLog(sysPermissionLog);
    }

    /**
     * 修改权限日志
     * 
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    @Override
    public int updateSysPermissionLog(SysPermissionLog sysPermissionLog)
    {
        return sysPermissionLogMapper.updateSysPermissionLog(sysPermissionLog);
    }

    /**
     * 批量删除权限日志
     * 
     * @param logIds 需要删除的权限日志主键
     * @return 结果
     */
    @Override
    public int deleteSysPermissionLogByLogIds(Long[] logIds)
    {
        return sysPermissionLogMapper.deleteSysPermissionLogByLogIds(logIds);
    }

    /**
     * 删除权限日志信息
     * 
     * @param logId 权限日志主键
     * @return 结果
     */
    @Override
    public int deleteSysPermissionLogByLogId(Long logId)
    {
        return sysPermissionLogMapper.deleteSysPermissionLogByLogId(logId);
    }

    /**
     * 清空权限日志
     */
    @Override
    public void cleanSysPermissionLog()
    {
        sysPermissionLogMapper.cleanSysPermissionLog();
    }

    /**
     * 获取权限统计信息
     * 
     * @param sysPermissionLog 查询条件
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getPermissionStatistics(SysPermissionLog sysPermissionLog)
    {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总权限操作数
        statistics.put("totalPermissions", sysPermissionLogMapper.countTotalPermissions(sysPermissionLog));
        
        // 今日权限操作数
        statistics.put("todayPermissions", sysPermissionLogMapper.countTodayPermissions(sysPermissionLog));
        
        // 授权操作数
        SysPermissionLog grantLog = new SysPermissionLog();
        grantLog.setOperType("GRANT");
        statistics.put("grantedPermissions", sysPermissionLogMapper.countTotalPermissions(grantLog));
        
        // 撤销操作数
        SysPermissionLog revokeLog = new SysPermissionLog();
        revokeLog.setOperType("REVOKE");
        statistics.put("revokedPermissions", sysPermissionLogMapper.countTotalPermissions(revokeLog));
        
        return statistics;
    }

    /**
     * 获取权限操作趋势数据
     * 
     * @param sysPermissionLog 查询条件
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getPermissionTrend(SysPermissionLog sysPermissionLog)
    {
        return sysPermissionLogMapper.getPermissionTrend(sysPermissionLog);
    }

    /**
     * 记录权限操作日志
     * 
     * @param userName 用户名
     * @param nickName 用户昵称
     * @param permType 权限类型
     * @param permission 权限标识
     * @param operType 操作类型
     * @param status 操作状态
     * @param msg 操作消息
     */
    @Override
    public void recordPermissionLog(String userName, String nickName, String permType, 
                                  String permission, String operType, String status, String msg)
    {
        SysPermissionLog log = new SysPermissionLog();
        log.setUserName(userName);
        log.setNickName(nickName);
        log.setPermType(permType);
        log.setPermission(permission);
        log.setOperType(operType);
        log.setStatus(status);
        log.setMsg(msg);
        log.setOperTime(new Date());
        
        // 获取IP和地址信息
        try {
            String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
            log.setOperIp(ip);
            log.setOperLocation(AddressUtils.getRealAddressByIP(ip));
        } catch (Exception e) {
            log.setOperIp("127.0.0.1");
            log.setOperLocation("内网IP");
        }
        
        // 设置创建信息
        log.setCreateBy(SecurityUtils.getUsername());
        log.setCreateTime(new Date());
        
        // 异步记录日志
        try {
            insertSysPermissionLog(log);
        } catch (Exception e) {
            // 记录日志失败不影响主业务
            e.printStackTrace();
        }
    }
}