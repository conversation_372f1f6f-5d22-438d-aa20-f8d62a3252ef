<template>
  <div class="app-container">
    <!-- 移动端结构 -->
    <div v-if="$store.getters.device === 'mobile'" class="mobile-inventory-container">
      <el-collapse v-model="mobileSearchVisible" class="mobile-search">
        <el-collapse-item title="搜索条件" name="search">
          <el-form :model="queryParams" ref="queryForm" size="small" label-width="80px">
            <el-form-item label="出库单号" prop="outCode">
              <el-input v-model="queryParams.outCode" placeholder="请输入出库单号" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="仓库名称" prop="warehouseId">
              <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable style="width: 100%">
                <el-option v-for="item in warehouseOptions" :key="item.warehouseId" :label="item.warehouseName" :value="item.warehouseId" />
              </el-select>
            </el-form-item>
            <el-form-item label="出库类型" prop="outType">
              <el-select v-model="queryParams.outType" placeholder="请选择出库类型" clearable style="width: 100%">
                <el-option v-for="dict in dict.type.inventory_out_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option v-for="dict in dict.type.inventory_out_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="出库时间">
              <el-date-picker v-model="dateRange" style="width: 100%" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <!-- 移动端操作按钮区 -->
      <div class="mobile-actions">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd" v-hasPermi="['inventory:out:add']">新增</el-button>
        <el-button type="success" icon="el-icon-edit" size="small" :disabled="single" @click="handleUpdate" v-hasPermi="['inventory:out:edit']">修改</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple" @click="handleDelete" v-hasPermi="['inventory:out:remove']">删除</el-button>
        <el-dropdown size="small" style="margin-left: 10px;">
          <el-button size="small">
            更多<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="scan" icon="el-icon-camera" v-hasPermi="['inventory:out:add']" @click.native="handleScan">扫码出库</el-dropdown-item>
            <el-dropdown-item command="batchAdd" icon="el-icon-s-grid" v-hasPermi="['inventory:out:add']" @click.native="handleBatchAdd">批量出库</el-dropdown-item>
            <el-dropdown-item command="export" icon="el-icon-download" v-hasPermi="['inventory:out:export']" @click.native="handleExport('excel')">导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <!-- 移动端出库卡片列表 -->
      <div v-loading="loading" class="mobile-out-list">
        <div v-for="item in outList" :key="item.outId" class="out-card">
          <div class="out-card-header">
            <div class="out-title">{{ item.outCode }}</div>
            <el-tag size="mini" :type="item.status === '0' ? 'success' : 'info'" class="out-status">{{ getOutStatusLabel(item.status) }}</el-tag>
          </div>
          <div class="out-card-body">
            <div class="out-detail"><span class="label">仓库:</span><span class="value">{{ item.warehouseName }}</span></div>
            <div class="out-detail"><span class="label">出库类型:</span><span class="value">{{ getOutTypeLabel(item.outType) }}</span></div>
            <div class="out-detail"><span class="label">出库时间:</span><span class="value">{{ item.outTime }}</span></div>
          </div>
          <div class="out-card-actions" @click.stop>
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(item)" v-hasPermi="['inventory:out:query']">查看</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(item)" v-hasPermi="['inventory:out:edit']" v-if="item.status === '0'">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(item)" v-hasPermi="['inventory:out:remove']" v-if="item.status === '0'">删除</el-button>
            <el-button size="mini" type="text" icon="el-icon-check" @click="handleAudit(item)" v-hasPermi="['inventory:out:audit']" v-if="item.status === '0'">审核</el-button>
            <el-button size="mini" type="text" icon="el-icon-printer" @click="handlePrint(item)" v-hasPermi="['inventory:out:print']">打印</el-button>
          </div>
        </div>
      </div>
      <!-- 移动端分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      <!-- 移动端弹窗（新增/编辑） -->
      <el-dialog
        v-if="$store.getters.device === 'mobile'"
        :title="title"
        :visible.sync="open"
        width="100vw"
        top="0"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="仓库" prop="warehouseId">
                <el-select v-model="form.warehouseId" placeholder="请选择仓库">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出库类型" prop="outType">
                <el-select v-model="form.outType" placeholder="请选择出库类型">
                  <el-option
                    v-for="dict in dict.type.inventory_out_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="center">出库明细</el-divider>
          <el-row>
            <el-col :span="24">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddDetail">添加明细</el-button>
              <el-table :data="form.details" style="margin-top: 10px;">
                <el-table-column label="序号" type="index" width="55" align="center" />
                <el-table-column label="物品" prop="productName" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" placeholder="请选择物品" @change="(val) => handleProductChange(val, scope.$index)">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.productId"
                        :label="item.productName"
                        :value="item.productId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" width="150">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.quantity" :min="1" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="单价" prop="price">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.price" :min="0" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="金额" prop="amount" width="150">
                  <template slot-scope="scope">
                    <span>{{ (scope.row.quantity * scope.row.price).toFixed(2) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteDetail(scope.$index)"
                      v-hasPermi="['inventory:out:edit']"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer" style="display:flex;gap:4%;justify-content:space-between">
          <el-button type="primary" @click="submitForm" style="width:48%">确 定</el-button>
          <el-button @click="cancel" style="width:48%">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看出库单对话框 -->
      <el-dialog title="查看出库单" :visible.sync="viewOpen" width="780px" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="出库单号">{{ viewForm.outCode }}</el-descriptions-item>
          <el-descriptions-item label="仓库名称">{{ viewForm.warehouseName }}</el-descriptions-item>
          <el-descriptions-item label="出库类型">
            <dict-tag :options="dict.type.inventory_out_type" :value="viewForm.outType"/>
          </el-descriptions-item>
          <el-descriptions-item label="出库时间">{{ parseTime(viewForm.outTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.inventory_out_status" :value="viewForm.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ viewForm.createBy }}</el-descriptions-item>
          <el-descriptions-item label="审核人" v-if="viewForm.auditBy">{{ viewForm.auditBy }}</el-descriptions-item>
          <el-descriptions-item label="审核时间" v-if="viewForm.auditTime">{{ parseTime(viewForm.auditTime) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
          <el-descriptions-item label="审核意见" :span="2" v-if="viewForm.status === '1' || viewForm.status === '2'">{{ viewForm.auditNote }}</el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="center">出库明细</el-divider>
        <el-table :data="viewForm.details" style="margin-top: 10px;">
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物品名称" prop="productName" min-width="180" />
          <el-table-column label="物品编码" prop="productCode" width="120" />
          <el-table-column label="数量" prop="quantity" width="100" />
          <el-table-column label="单价" prop="price" width="100" />
          <el-table-column label="金额" prop="amount" width="100" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 审核出库单对话框 -->
      <el-dialog title="审核出库单" :visible.sync="auditOpen" width="700px" append-to-body>
        <el-tabs v-model="auditActiveTab">
          <el-tab-pane label="出库单信息" name="info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="出库单号">{{ auditInfo.outCode }}</el-descriptions-item>
              <el-descriptions-item label="仓库名称">{{ auditInfo.warehouseName }}</el-descriptions-item>
              <el-descriptions-item label="出库类型">
                <dict-tag :options="dict.type.inventory_out_type" :value="auditInfo.outType"/>
              </el-descriptions-item>
              <el-descriptions-item label="出库时间">{{ parseTime(auditInfo.outTime) }}</el-descriptions-item>
              <el-descriptions-item label="创建人">{{ auditInfo.createBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ parseTime(auditInfo.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ auditInfo.remark }}</el-descriptions-item>
            </el-descriptions>
            <el-divider content-position="center">出库明细</el-divider>
            <el-table :data="auditInfo.details" style="margin-top: 10px;">
              <el-table-column label="序号" type="index" width="55" align="center" />
              <el-table-column label="物品名称" prop="productName" min-width="180" />
              <el-table-column label="物品编码" prop="productCode" width="120" />
              <el-table-column label="数量" prop="quantity" width="100" />
              <el-table-column label="单价" prop="price" width="100" />
              <el-table-column label="金额" prop="amount" width="100" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="审核操作" name="audit">
            <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
              <el-form-item label="出库单号">{{ auditForm.outCode }}</el-form-item>
              <el-form-item label="审核结果" prop="status">
                <el-radio-group v-model="auditForm.status">
                  <el-radio label="1">通过</el-radio>
                  <el-radio label="2">不通过</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="审核意见" prop="remark">
                <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入审核意见" rows="4" />
              </el-form-item>
              <el-form-item label="审核附注" prop="auditNote">
                <el-input v-model="auditForm.auditNote" type="textarea" placeholder="请输入审核附注（内部记录，不显示给用户）" rows="2" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAudit" :disabled="auditActiveTab !== 'audit'">提交审核</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>

    <div v-else>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="出库单号" prop="outCode">
          <el-input
            v-model="queryParams.outCode"
            placeholder="请输入出库单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库名称" prop="warehouseId">
          <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable>
            <el-option
              v-for="item in warehouseOptions"
              :key="item.warehouseId"
              :label="item.warehouseName"
              :value="item.warehouseId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="出库类型" prop="outType">
          <el-select v-model="queryParams.outType" placeholder="请选择出库类型" clearable>
            <el-option
              v-for="dict in dict.type.inventory_out_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="dict in dict.type.inventory_out_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核人" prop="auditBy">
          <el-input
            v-model="queryParams.auditBy"
            placeholder="请输入审核人"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="出库时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['inventory:out:add']"
          >新增</el-button>
        </el-col>
        <!-- 修改按钮已移除 -->
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inventory:out:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport('excel')"
            v-hasPermi="['inventory:out:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-camera"
            size="mini"
            @click="handleScan"
            v-hasPermi="['inventory:out:add']"
          >扫码出库</el-button>
        </el-col>
        <el-col :span="1.5">
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="outList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="出库ID" align="center" prop="outId" v-if="false" />
        <el-table-column label="出库单号" align="center" prop="outCode" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" :show-overflow-tooltip="true" />
        <el-table-column label="出库类型" align="center" prop="outType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.inventory_out_type" :value="scope.row.outType"/>
          </template>
        </el-table-column>
        <el-table-column label="出库时间" align="center" prop="outTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.outTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.inventory_out_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="审核人" align="center" prop="auditBy" />
        <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.auditTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding operation-column" width="280">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row)"
                v-hasPermi="['inventory:out:query']"
              >查看</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inventory:out:edit']"
                v-if="scope.row.status === '0'"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inventory:out:remove']"
                v-if="scope.row.status === '0'"
              >删除</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-check"
                @click="handleAudit(scope.row)"
                v-hasPermi="['inventory:out:audit']"
                v-if="scope.row.status === '0'"
              >审核</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-printer"
                @click="handlePrint(scope.row)"
                v-hasPermi="['inventory:out:print']"
              >打印</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改出库单对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="仓库" prop="warehouseId">
                <el-select v-model="form.warehouseId" placeholder="请选择仓库">
                  <el-option
                    v-for="item in warehouseOptions"
                    :key="item.warehouseId"
                    :label="item.warehouseName"
                    :value="item.warehouseId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出库类型" prop="outType">
                <el-select v-model="form.outType" placeholder="请选择出库类型">
                  <el-option
                    v-for="dict in dict.type.inventory_out_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="center">出库明细</el-divider>
          <el-row>
            <el-col :span="24">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddDetail">添加明细</el-button>
              <el-table :data="form.details" style="margin-top: 10px;">
                <el-table-column label="序号" type="index" width="55" align="center" />
                <el-table-column label="物品" prop="productName" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.productId" placeholder="请选择物品" @change="(val) => handleProductChange(val, scope.$index)">
                      <el-option
                        v-for="item in productOptions"
                        :key="item.productId"
                        :label="item.productName"
                        :value="item.productId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" width="150">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.quantity" :min="1" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="单价" prop="price">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.price" :min="0" :precision="2" :step="1" style="width: 100%;" />
                  </template>
                </el-table-column>
                <el-table-column label="金额" prop="amount" width="150">
                  <template slot-scope="scope">
                    <span>{{ (scope.row.quantity * scope.row.price).toFixed(2) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteDetail(scope.$index)"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 查看出库单对话框 -->
      <el-dialog title="查看出库单" :visible.sync="viewOpen" width="780px" append-to-body>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="出库单号">{{ viewForm.outCode }}</el-descriptions-item>
          <el-descriptions-item label="仓库名称">{{ viewForm.warehouseName }}</el-descriptions-item>
          <el-descriptions-item label="出库类型">
            <dict-tag :options="dict.type.inventory_out_type" :value="viewForm.outType"/>
          </el-descriptions-item>
          <el-descriptions-item label="出库时间">{{ parseTime(viewForm.outTime) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="dict.type.inventory_out_status" :value="viewForm.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ viewForm.createBy }}</el-descriptions-item>
          <el-descriptions-item label="审核人" v-if="viewForm.auditBy">{{ viewForm.auditBy }}</el-descriptions-item>
          <el-descriptions-item label="审核时间" v-if="viewForm.auditTime">{{ parseTime(viewForm.auditTime) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
          <el-descriptions-item label="审核意见" :span="2" v-if="viewForm.status === '1' || viewForm.status === '2'">{{ viewForm.auditNote }}</el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="center">出库明细</el-divider>
        <el-table :data="viewForm.details" style="margin-top: 10px;">
          <el-table-column label="序号" type="index" width="55" align="center" />
          <el-table-column label="物品名称" prop="productName" min-width="180" />
          <el-table-column label="物品编码" prop="productCode" width="120" />
          <el-table-column label="数量" prop="quantity" width="100" />
          <el-table-column label="单价" prop="price" width="100" />
          <el-table-column label="金额" prop="amount" width="100" />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 审核出库单对话框 -->
      <el-dialog title="审核出库单" :visible.sync="auditOpen" width="700px" append-to-body>
        <el-tabs v-model="auditActiveTab">
          <el-tab-pane label="出库单信息" name="info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="出库单号">{{ auditInfo.outCode }}</el-descriptions-item>
              <el-descriptions-item label="仓库名称">{{ auditInfo.warehouseName }}</el-descriptions-item>
              <el-descriptions-item label="出库类型">
                <dict-tag :options="dict.type.inventory_out_type" :value="auditInfo.outType"/>
              </el-descriptions-item>
              <el-descriptions-item label="出库时间">{{ parseTime(auditInfo.outTime) }}</el-descriptions-item>
              <el-descriptions-item label="创建人">{{ auditInfo.createBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ parseTime(auditInfo.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ auditInfo.remark }}</el-descriptions-item>
            </el-descriptions>
            <el-divider content-position="center">出库明细</el-divider>
            <el-table :data="auditInfo.details" style="margin-top: 10px;">
              <el-table-column label="序号" type="index" width="55" align="center" />
              <el-table-column label="物品名称" prop="productName" min-width="180" />
              <el-table-column label="物品编码" prop="productCode" width="120" />
              <el-table-column label="数量" prop="quantity" width="100" />
              <el-table-column label="单价" prop="price" width="100" />
              <el-table-column label="金额" prop="amount" width="100" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="审核操作" name="audit">
            <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
              <el-form-item label="出库单号">{{ auditForm.outCode }}</el-form-item>
              <el-form-item label="审核结果" prop="status">
                <el-radio-group v-model="auditForm.status">
                  <el-radio label="1">通过</el-radio>
                  <el-radio label="2">不通过</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="审核意见" prop="remark">
                <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入审核意见" rows="4" />
              </el-form-item>
              <el-form-item label="审核附注" prop="auditNote">
                <el-input v-model="auditForm.auditNote" type="textarea" placeholder="请输入审核附注（内部记录，不显示给用户）" rows="2" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAudit" :disabled="auditActiveTab !== 'audit'">提交审核</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </el-dialog>

      <el-dialog title="选择导出字段" :visible.sync="exportFieldDialogVisible" width="400px">
        <el-checkbox-group v-model="selectedExportFields">
          <el-checkbox v-for="col in exportFieldOptions" :key="col.value" :label="col.value">{{ col.label }}</el-checkbox>
        </el-checkbox-group>
        <div slot="footer" class="dialog-footer">
          <el-button @click="exportFieldDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDoExport">导出</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listInventoryOut, getInventoryOut, delInventoryOut, addInventoryOut, updateInventoryOut, auditInventoryOut } from "@/api/inventory/out";
import { listProduct } from "@/api/product/info";
import { optionselect } from "@/api/system/warehouse";
import axios from 'axios';

export default {
  name: "InventoryOut",
  dicts: ['inventory_out_type', 'inventory_out_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 显示审核信息列
      showAuditInfo: true,
      // 总条数
      total: 0,
      // 出库表格数据
      outList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 审核选项卡激活名称
      auditActiveTab: 'info',
      // 日期范围
      dateRange: [],
      // 仓库选项
      warehouseOptions: [],
      // 物品选项
      productOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        outCode: null,
        warehouseId: null,
        outType: null,
        status: null
      },
      // 表单参数
      form: {
        details: []
      },
      // 表单校验
      rules: {
        warehouseId: [
          { required: true, message: "仓库不能为空", trigger: "change" }
        ],
        outType: [
          { required: true, message: "出库类型不能为空", trigger: "change" }
        ]
      },
      // 查看表单参数
      viewForm: {
        details: []
      },
      // 审核信息
      auditInfo: {
        outCode: "",
        warehouseName: "",
        outType: "",
        outTime: null,
        createBy: "",
        createTime: null,
        remark: "",
        details: []
      },
      // 审核表单参数
      auditForm: {
        outId: null,
        outCode: "",
        status: "1",
        remark: "",
        auditNote: ""
      },
      // 审核表单校验
      auditRules: {
        status: [
          { required: true, message: "审核结果不能为空", trigger: "change" }
        ]
      },
      // 移动端搜索折叠面板
      mobileSearchVisible: ['search'],
      exportFieldDialogVisible: false,
      selectedExportFields: [],
      exportFieldOptions: [
        { label: '出库单号', value: 'outCode' },
        { label: '仓库名称', value: 'warehouseName' },
        { label: '出库类型', value: 'outTypeName' },
        { label: '物品名称', value: 'productName' },
        { label: '物品编码', value: 'productCode' },
        { label: '数量', value: 'quantity' },
        { label: '单价', value: 'price' },
        { label: '金额', value: 'amount' },
        { label: '出库时间', value: 'outTime' },
        { label: '操作人员', value: 'operateBy' },
        { label: '状态', value: 'status' },
        { label: '审核人', value: 'auditBy' },
        { label: '审核时间', value: 'auditTime' }
      ],
      exportType: 'excel',
    };
  },
  created() {
    this.getList();
    this.getWarehouseOptions();
    this.getProductOptions();
  },
  methods: {
    /** 查询出库列表 */
    getList() {
      this.loading = true;
      listInventoryOut(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        console.log('出库管理API响应:', response);
        console.log('出库列表数据:', response.rows);
        if (response.rows && response.rows.length > 0) {
          console.log('第一条记录的审核字段:', {
            auditBy: response.rows[0].auditBy,
            auditTime: response.rows[0].auditTime
          });
        }
        this.outList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取仓库选项 */
    getWarehouseOptions() {
      optionselect().then(response => {
        console.log('出库管理页面 - optionselect 接口返回数据:', response);
        this.warehouseOptions = response.data || response.rows || [];
        console.log('出库管理页面 - 最终仓库选项:', this.warehouseOptions);
      }).catch(error => {
        console.error('出库管理页面 - optionselect 接口调用失败:', error);
      });
    },
    /** 获取物品选项 */
    getProductOptions() {
      listProduct().then(response => {
        this.productOptions = response.rows;
      });
    },
    // 物品选择事件
    handleProductChange(value, index) {
      const product = this.productOptions.find(item => item.productId === value);
      if (product) {
        this.form.details[index].productName = product.productName;
        this.form.details[index].productCode = product.productCode;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        outId: null,
        outCode: null,
        warehouseId: null,
        outType: null,
        outTime: null,
        status: "0",
        remark: null,
        details: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.outId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加出库单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      if (row.status !== "0") {
        this.$modal.msgError("已审核的出库单不能修改");
        return;
      }
      this.reset();
      const outId = row.outId || this.ids[0];
      getInventoryOut(outId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改出库单";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getInventoryOut(row.outId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditActiveTab = 'info';
      this.auditForm = {
        outId: row.outId,
        outCode: row.outCode,
        status: "1",
        remark: "",
        auditNote: ""
      };

      // 获取出库单详细信息
      getInventoryOut(row.outId).then(response => {
        this.auditInfo = response.data;
        this.auditOpen = true;
      });
    },
    /** 打印按钮操作 */
    handlePrint(row) {
      this.$router.push({ path: `/inventory/out/print/${row.outId}` });
    },
    /** 扫码出库按钮操作 */
    handleScan() {
      this.$router.push({ path: "/inventory/out-scan/index" });
    },
    /** 报表按钮操作 */
    handleReport() {
      this.$router.push({ path: "/report/out/index" });
    },
    /** 添加明细 */
    handleAddDetail() {
      this.form.details.push({
        productId: null,
        productName: null,
        productCode: null,
        quantity: 1,
        price: 0,
        amount: 0
      });
    },
    /** 删除明细 */
    handleDeleteDetail(index) {
      this.form.details.splice(index, 1);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查明细是否为空
          if (this.form.details.length === 0) {
            this.$modal.msgError("请添加出库明细");
            return;
          }

          // 检查明细是否填写完整
          for (let i = 0; i < this.form.details.length; i++) {
            const detail = this.form.details[i];
            if (!detail.productId) {
              this.$modal.msgError("请选择物品");
              return;
            }
            if (!detail.quantity || detail.quantity <= 0) {
              this.$modal.msgError("请输入正确的数量");
              return;
            }
          }

          if (this.form.outId != null) {
            updateInventoryOut(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventoryOut(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交审核 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          // 提交审核时，包含出库单的详细信息（包括details和warehouseId）
          const submitData = {
            ...this.auditForm,
            warehouseId: this.auditInfo.warehouseId, // 添加仓库ID
            details: this.auditInfo.details // 将明细列表添加到提交数据中
          };
          auditInventoryOut(submitData).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const outIds = row.outId || this.ids;
      this.$modal.confirm('是否确认删除出库单编号为"' + outIds + '"的数据项？').then(() => {
        return delInventoryOut(outIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport(command) {
      this.exportType = command;
      this.exportFieldDialogVisible = true;
      // 默认全选
      if (this.selectedExportFields.length === 0) {
        this.selectedExportFields = this.exportFieldOptions.map(opt => opt.value);
      }
    },
    handleDoExport() {
      if (!this.selectedExportFields.length) {
        this.$modal.msgError('请至少选择一个导出字段');
        return;
      }
      const params = { ...this.queryParams, columns: this.selectedExportFields.join(',') };
      let url = '';
      let fileName = '';
      if (this.exportType === 'excel') {
        url = '/api/v1/reports/export/out/detail/excel';
        fileName = `出库明细报表_${new Date().getTime()}.xlsx`;
      } else if (this.exportType === 'pdf') {
        url = '/api/v1/reports/export/out/detail/pdf';
        fileName = `出库明细报表_${new Date().getTime()}.pdf`;
      }
      this.exportReport(url, params, fileName);
      this.exportFieldDialogVisible = false;
    },
    exportReport(url, params, fileName) {
      this.$modal.loading("正在导出数据，请稍候...");
      const baseUrl = process.env.VUE_APP_BASE_API || '';
      const fullUrl = baseUrl + url;
      axios({
        method: 'get',
        url: fullUrl,
        params: params,
        responseType: 'blob',
        headers: {
          Authorization: this.$store.getters.token ? 'Bearer ' + this.$store.getters.token : undefined
        }
      }).then(response => {
        this.$modal.closeLoading();
        const blob = new Blob([response.data]);
        if (window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(blob, fileName);
        } else {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          window.URL.revokeObjectURL(link.href);
        }
        this.$modal.msgSuccess("导出成功");
      }).catch(() => {
        this.$modal.closeLoading();
        this.$modal.msgError("导出失败，请重试");
      });
    },
    getOutStatusLabel: function(status) {
      var arr = this.dict.type.inventory_out_status;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].value == status) return arr[i].label;
      }
      return status;
    },
    getOutTypeLabel: function(outType) {
      var arr = this.dict.type.inventory_out_type;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].value == outType) return arr[i].label;
      }
      return outType;
    }
  }
};
</script>

<style lang="scss" scoped>
.operation-column {
  .operation-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
  }
}

// 移动端操作按钮样式
@media screen and (max-width: 768px) {
  .operation-column {
    .operation-buttons {
      flex-direction: column;
      align-items: center;
      
      .el-button {
        margin: 2px 0;
        width: 100%;
        text-align: center;
      }
    }
  }
}

// 数字输入框样式优化
:deep(.el-input-number) {
  width: 100%;
  
  .el-input__inner {
    text-align: left;
    padding-right: 50px;
    width: 100%;
  }
  
  .el-input-number__decrease,
  .el-input-number__increase {
    width: 24px;
    height: 24px;
    line-height: 24px;
  }
}

// 移动端数字输入框样式
@media screen and (max-width: 768px) {
  :deep(.el-input-number) {
    width: 100%;
    
    .el-input__inner {
      padding-right: 60px;
      width: 100%;
    }
    
    .el-input-number__decrease,
    .el-input-number__increase {
      width: 28px;
      height: 28px;
      line-height: 28px;
    }
  }
  
  // 确保表格中的数字输入框有足够宽度
  .el-table {
    .el-input-number {
      width: 100%;
      min-width: 120px;
    }
  }
}

.mobile-inventory-container {
  .mobile-search {
    margin-bottom: 10px;
    .el-collapse-item__header {
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      padding: 10px 15px;
      font-size: 14px;
      font-weight: bold;
      color: #303133;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .el-collapse-item__arrow {
        color: #303133;
      }
    }
    .el-collapse-item__content {
      padding: 0 15px 15px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 10px;
        .el-form-item__label {
          font-size: 13px;
          color: #606266;
        }
        .el-form-item__content {
          .el-input__inner, .el-select, .el-date-picker {
            font-size: 13px;
          }
          .el-input__inner {
            padding-right: 10px;
          }
          .el-date-picker .el-range__icon {
            line-height: 24px;
          }
          .el-date-picker .el-range-separator {
            line-height: 24px;
          }
          .el-date-picker .el-range-input {
            line-height: 24px;
          }
        }
      }
      .el-form-item:last-child {
        margin-bottom: 0;
      }
    }
  }
  .mobile-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .el-button {
      font-size: 13px;
      padding: 8px 12px;
      height: auto;
      line-height: 1;
    }
    .el-dropdown {
      .el-button {
        font-size: 13px;
        padding: 8px 12px;
        height: auto;
        line-height: 1;
      }
    }
  }
  .mobile-out-list {
    .out-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0,0,0,0.04);
      margin-bottom: 12px;
      padding: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      &:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }
    }
    .out-card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }
    .out-title {
      font-weight: bold;
      font-size: 16px;
      flex: 1;
    }
    .out-status {
      margin-left: 8px;
    }
    .out-card-body {
      margin-bottom: 8px;
    }
    .out-detail {
      font-size: 13px;
      margin-bottom: 2px;
    }
    .out-detail .label {
      color: #888;
      margin-right: 4px;
    }
    .out-detail .value {
      color: #333;
    }
    .out-card-actions {
      display: flex;
      gap: 8px;
    }
  }
}
</style>