package com.wanyu.common.config;

import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * 缓存健康检查指示器
 * 
 * <AUTHOR>
 */
@Component
public class CacheHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        // 内存缓存健康检查
        return Health.up()
                .withDetail("cache", "Memory cache is available")
                .build();
    }
}