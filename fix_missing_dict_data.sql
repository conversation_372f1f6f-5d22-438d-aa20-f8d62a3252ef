-- 修复缺失的字典数据
USE warehouse_system;

-- 1. 添加产品属性类型字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (100, '产品属性类型', 'product_attribute_type', '0', 'admin', NOW(), '产品属性类型列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(1, 1, '基本属性', '1', 'product_attribute_type', '', 'primary', 'Y', '0', 'admin', NOW(), '产品基本属性'),
(2, 2, '扩展属性', '2', 'product_attribute_type', '', 'info', 'N', '0', 'admin', NOW(), '产品扩展属性'),
(3, 3, '技术参数', '3', 'product_attribute_type', '', 'success', 'N', '0', 'admin', NOW(), '产品技术参数');

-- 2. 添加系统是否字典
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES (101, '系统是否', 'sys_yes_no', '0', 'admin', NOW(), '系统是否列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(4, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', NOW(), '系统默认是'),
(5, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', NOW(), '系统默认否');

-- 3. 添加其他常用字典类型
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) 
VALUES 
(102, '产品状态', 'product_status', '0', 'admin', NOW(), '产品状态列表'),
(103, '产品单位', 'product_unit', '0', 'admin', NOW(), '产品单位列表'),
(104, '库存状态', 'inventory_status', '0', 'admin', NOW(), '库存状态列表');

-- 产品状态字典数据
INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(6, 1, '正常', '0', 'product_status', '', 'primary', 'Y', '0', 'admin', NOW(), '正常状态'),
(7, 2, '停用', '1', 'product_status', '', 'danger', 'N', '0', 'admin', NOW(), '停用状态');

-- 产品单位字典数据
INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(8, 1, '个', 'pcs', 'product_unit', '', 'primary', 'Y', '0', 'admin', NOW(), '个'),
(9, 2, '箱', 'box', 'product_unit', '', 'info', 'N', '0', 'admin', NOW(), '箱'),
(10, 3, '包', 'pack', 'product_unit', '', 'success', 'N', '0', 'admin', NOW(), '包'),
(11, 4, '套', 'set', 'product_unit', '', 'warning', 'N', '0', 'admin', NOW(), '套');

-- 库存状态字典数据
INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(12, 1, '正常', '0', 'inventory_status', '', 'primary', 'Y', '0', 'admin', NOW(), '库存正常'),
(13, 2, '预警', '1', 'inventory_status', '', 'warning', 'N', '0', 'admin', NOW(), '库存预警'),
(14, 3, '缺货', '2', 'inventory_status', '', 'danger', 'N', '0', 'admin', NOW(), '库存缺货');

COMMIT;