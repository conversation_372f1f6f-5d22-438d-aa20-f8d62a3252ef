# 仓库管理系统开发规范与文档

## 目录

1. [项目概述](#1-项目概述)
2. [系统架构](#2-系统架构)
3. [开发规范](#3-开发规范)
   - [3.1 命名规范](#31-命名规范)
   - [3.2 API接口规范](#32-api接口规范)
   - [3.3 代码审查规范](#33-代码审查规范)
   - [3.4 测试规范](#34-测试规范)
4. [数据库设计](#4-数据库设计)
5. [功能模块](#5-功能模块)
6. [用户权限控制](#6-用户权限控制)
7. [实施路线图](#7-实施路线图)
8. [代码审查与质量控制](#8-代码审查与质量控制)
9. [附录](#9-附录)

## 1. 项目概述

仓库管理系统是一个用于管理企业仓库、库存、出入库等业务的综合性系统。系统基于前后端分离架构，前端使用Vue.js框架，后端使用Spring Boot框架，数据库使用MySQL。

### 1.1 系统目标

- 实现仓库基础信息管理
- 实现物品信息管理
- 实现库存管理
- 实现入库管理
- 实现出库管理
- 实现库存调拨管理
- 实现库存盘点管理
- 实现二维码扫描功能
- 实现报表统计功能
- 实现日志管理功能
- 实现用户权限管理

### 1.2 系统特点

- 多级用户授权控制
- 支持二维码扫描操作
- 支持多用户并发操作
- 完善的日志记录功能
- 丰富的报表统计功能

## 2. 系统架构

### 2.1 技术架构

- **前端**：Vue.js + Element UI
- **后端**：Spring Boot + MyBatis
- **数据库**：MySQL
- **缓存**：Redis
- **消息队列**：RabbitMQ（可选）
- **搜索引擎**：Elasticsearch（可选）

### 2.2 系统部署架构

- **开发环境**：单机部署
- **测试环境**：单机部署
- **生产环境**：集群部署（可选）

### 2.3 模块划分

- **基础模块**：用户管理、角色管理、权限管理、系统设置
- **仓库模块**：仓库管理、物品管理
- **库存模块**：库存管理、库存预警
- **出入库模块**：入库管理、出库管理
- **调拨盘点模块**：库存调拨、库存盘点
- **二维码模块**：二维码生成、二维码扫描
- **报表模块**：库存报表、入库报表、出库报表
- **日志模块**：操作日志、出入库日志、系统日志、权限日志

## 3. 开发规范

### 3.1 命名规范

#### 3.1.1 通用规则

- 命名应当具有描述性，避免使用缩写（除非是广泛接受的缩写）
- 命名应当使用英文，避免使用拼音或中英文混合
- 命名应当遵循所使用语言的惯例

#### 3.1.2 Java命名规范

- **包名**：全小写，使用点分隔，如`com.wanyu.system`
- **类名**：大驼峰命名法，如`UserController`
- **接口名**：大驼峰命名法，通常以I开头或以able/ible结尾，如`IUserService`或`Convertible`
- **方法名**：小驼峰命名法，动词开头，如`getUserById`
- **变量名**：小驼峰命名法，如`userId`
- **常量名**：全大写，单词间用下划线分隔，如`MAX_COUNT`
- **枚举名**：同类名，大驼峰命名法
- **枚举值**：同常量名，全大写

#### 3.1.3 JavaScript/TypeScript命名规范

- **变量名**：小驼峰命名法，如`userName`
- **函数名**：小驼峰命名法，如`getUserInfo`
- **类名**：大驼峰命名法，如`UserModel`
- **常量名**：全大写，单词间用下划线分隔，如`API_URL`
- **组件名**：大驼峰命名法，如`UserList`
- **文件名**：小写，单词间用连字符分隔，如`user-list.vue`

#### 3.1.4 数据库命名规范

- **表名**：小写，单词间用下划线分隔，使用前缀区分模块，如`wms_inventory`
- **字段名**：小写，单词间用下划线分隔，如`user_id`
- **主键**：通常命名为`id`或表名加`_id`，如`user_id`
- **外键**：通常命名为关联表名加`_id`，如`role_id`
- **索引名**：`idx_表名_字段名`，如`idx_user_username`
- **唯一索引**：`uk_表名_字段名`，如`uk_user_email`

#### 3.1.5 前端命名规范

- **组件名**：大驼峰命名法，如`UserList`
- **组件文件名**：小写，单词间用连字符分隔，如`user-list.vue`
- **路由名**：小驼峰命名法，如`userList`
- **路由路径**：小写，单词间用连字符分隔，如`/user-list`
- **CSS类名**：小写，单词间用连字符分隔，如`user-avatar`
- **CSS变量名**：小写，单词间用连字符分隔，如`--primary-color`

### 3.2 API接口规范

#### 3.2.1 RESTful API设计原则

- **使用HTTP方法表示操作类型**：
  - GET：获取资源
  - POST：创建资源
  - PUT：更新资源（全量更新）
  - PATCH：更新资源（部分更新）
  - DELETE：删除资源

- **使用URL表示资源，而非操作**：
  - 正确：`/api/v1/users`
  - 错误：`/api/v1/getUsers`

- **使用复数形式表示资源集合**：
  - 正确：`/api/v1/products`
  - 错误：`/api/v1/product`

- **使用HTTP状态码表示请求结果**：
  - 200 OK：请求成功
  - 201 Created：资源创建成功
  - 204 No Content：请求成功，无返回内容
  - 400 Bad Request：请求参数错误
  - 401 Unauthorized：未认证
  - 403 Forbidden：权限不足
  - 404 Not Found：资源不存在
  - 500 Internal Server Error：服务器错误

#### 3.2.2 API路径规范

- **基础路径**：`/api/v1/[资源集合]`
- **获取资源列表**：`GET /api/v1/products`
- **获取单个资源**：`GET /api/v1/products/{id}`
- **创建资源**：`POST /api/v1/products`
- **更新资源**：`PUT /api/v1/products/{id}`
- **部分更新资源**：`PATCH /api/v1/products/{id}`
- **删除资源**：`DELETE /api/v1/products/{id}`
- **资源关联**：`GET /api/v1/warehouses/{id}/stocks`

**正确示例**：
```
/api/v1/products                  # 物品列表
/api/v1/products/{id}             # 单个物品
/api/v1/warehouses                # 仓库列表
/api/v1/warehouses/{id}           # 单个仓库
/api/v1/inventory/stocks          # 库存列表
/api/v1/inventory/ins             # 入库单列表
/api/v1/inventory/outs            # 出库单列表
/api/v1/logs/inventory            # 库存日志
```

#### 3.2.3 API适配器实现

为所有控制器创建对应的API适配器，确保新旧路径都能正常工作：

```java
@RestController
@RequestMapping({"/原路径", "/api/v1/新路径"})
public class XxxController {

    @GetMapping({"/list", ""})
    public TableDataInfo list(Entity entity) {
        // 实现代码
    }

    @GetMapping({"/{id}", "/{id}"})
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        // 实现代码
    }

    // 其他方法
}
```

#### 3.2.4 请求参数规范

- **查询参数**：使用URL参数，如`?page=1&size=10`
- **创建/更新参数**：使用请求体，JSON格式
- **路径参数**：使用URL路径，如`/products/{id}`

#### 3.2.5 响应格式规范

- **统一使用JSON格式**
- **包含状态码、消息和数据三部分**
- **分页数据包含总数和当前页数据**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 数据内容
  }
}
```

分页数据格式：

```json
{
  "code": 200,
  "msg": "操作成功",
  "rows": [
    // 数据列表
  ],
  "total": 100
}
```

#### 3.2.6 错误处理规范

- **使用HTTP状态码表示错误类型**
- **在响应体中提供详细的错误信息**
- **包含错误码、错误消息和错误详情**

```json
{
  "code": 400,
  "msg": "请求参数错误",
  "data": {
    "field": "username",
    "message": "用户名不能为空"
  }
}
```

#### 3.2.7 前端API调用规范

修改所有前端API调用，使用新的API路径：

```javascript
// 正确示例
export function listProduct(query) {
  return request({
    url: '/api/v1/products',
    method: 'get',
    params: query
  })
}

export function getProduct(productId) {
  return request({
    url: `/api/v1/products/${productId}`,
    method: 'get'
  })
}
```

#### 3.2.8 版本控制规范

- **在URL中包含版本号**：`/api/v1/products`
- **主版本号变更**表示不兼容的API变更
- **次版本号变更**表示向后兼容的功能性变更
- **修订版本号变更**表示向后兼容的问题修复

#### 3.2.9 前后端API路径一致性规范

为确保前后端API路径的一致性，避免出现路径不匹配导致的问题，需要遵循以下规范：

1. **前后端API路径必须保持一致**：
   - 前端API调用路径必须与后端Controller中定义的路径完全一致
   - 修改任一端的API路径时，必须同步修改另一端

2. **API路径映射表**：
   - 维护一个API路径映射表，记录所有API的前后端路径
   - 每次修改API路径时，更新映射表

3. **常见API路径示例**：
   ```
   # 仓库管理
   /warehouse/list                # 仓库列表
   /warehouse/{id}                # 单个仓库

   # 物品管理
   /product/list                  # 物品列表
   /product/{id}                  # 单个物品

   # 库存管理
   /inventory/stock/list          # 库存列表
   /inventory/stock/{id}          # 单个库存

   # 入库管理
   /inventory/in/list             # 入库单列表
   /inventory/in/{id}             # 单个入库单

   # 出库管理
   /inventory/out/list            # 出库单列表
   /inventory/out/{id}            # 单个出库单
   ```

4. **API路径检查机制**：
   - 在开发过程中，定期检查前后端API路径是否一致
   - 在代码审查中，将API路径一致性作为检查项目之一
   - 使用自动化工具检查API路径一致性

### 3.3 代码审查规范

#### 3.3.1 代码审查目标

- 确保代码质量
- 发现潜在问题
- 确保代码符合规范
- 知识共享和团队学习

#### 3.3.2 代码审查内容

- 代码正确性：代码是否实现了预期功能
- 代码可读性：代码是否易于理解
- 代码可维护性：代码是否易于维护
- 代码性能：代码是否高效
- 代码安全性：代码是否存在安全漏洞
- 代码规范：代码是否符合规范

#### 3.3.3 代码审查流程

1. 开发人员提交代码审查请求
2. 审查人员进行代码审查
3. 审查人员提出修改建议
4. 开发人员根据建议修改代码
5. 审查人员确认修改
6. 代码合并到主分支

#### 3.3.4 代码审查清单

- [ ] 代码是否符合命名规范
- [ ] 代码是否有充分的注释
- [ ] 代码是否有单元测试
- [ ] 代码是否处理了异常情况
- [ ] 代码是否存在重复
- [ ] 代码是否存在安全漏洞
- [ ] 代码是否存在性能问题
- [ ] 代码是否易于理解和维护

### 3.4 测试规范

#### 3.4.1 测试类型

- 单元测试：测试单个函数或类
- 集成测试：测试多个组件的交互
- 功能测试：测试完整的功能
- 性能测试：测试系统性能
- 安全测试：测试系统安全性

#### 3.4.2 单元测试规范

- 测试应当独立，不依赖于其他测试
- 测试应当自动化，不需要人工干预
- 测试应当快速执行
- 测试应当覆盖正常情况和异常情况
- 测试应当使用断言验证结果

#### 3.4.3 测试命名规范

- 测试类名：被测试类名 + Test，如`UserServiceTest`
- 测试方法名：test + 被测试方法名 + 测试场景，如`testGetUserByIdWithValidId`

#### 3.4.4 测试覆盖率要求

- 单元测试覆盖率：80%以上
- 分支覆盖率：70%以上
- 关键业务逻辑覆盖率：100%

## 4. 数据库设计

### 4.1 数据库命名规范

- **表名**：小写，单词间用下划线分隔，使用前缀区分模块，如`wms_inventory`
- **字段名**：小写，单词间用下划线分隔，如`user_id`
- **主键**：通常命名为`id`或表名加`_id`，如`user_id`
- **外键**：通常命名为关联表名加`_id`，如`role_id`
- **索引名**：`idx_表名_字段名`，如`idx_user_username`
- **唯一索引**：`uk_表名_字段名`，如`uk_user_email`

### 4.2 数据库表设计

#### 4.2.1 用户表（sys_user）

```sql
CREATE TABLE `sys_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';
```

#### 4.2.2 仓库表（sys_warehouse）

```sql
CREATE TABLE `sys_warehouse` (
  `warehouse_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '仓库ID',
  `warehouse_name` varchar(100) NOT NULL COMMENT '仓库名称',
  `warehouse_code` varchar(20) NOT NULL COMMENT '仓库编码',
  `warehouse_address` varchar(200) DEFAULT NULL COMMENT '仓库地址',
  `warehouse_status` char(1) DEFAULT '0' COMMENT '仓库状态（0正常 1停用）',
  `warehouse_capacity` decimal(10,2) DEFAULT NULL COMMENT '仓库容量',
  `warehouse_used` decimal(10,2) DEFAULT '0.00' COMMENT '已使用容量',
  `manager_id` bigint(20) DEFAULT NULL COMMENT '负责人ID',
  `manager_name` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
  `manager_phone` varchar(11) DEFAULT NULL COMMENT '负责人电话',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`warehouse_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='仓库表';
```

#### 4.2.3 物品表（wms_product）

```sql
CREATE TABLE `wms_product` (
  `product_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '物品ID',
  `product_name` varchar(100) NOT NULL COMMENT '物品名称',
  `product_code` varchar(50) NOT NULL COMMENT '物品编码',
  `category_id` bigint(20) NOT NULL COMMENT '物品分类ID',
  `spec_id` bigint(20) NOT NULL COMMENT '规格ID',
  `unit_id` bigint(20) NOT NULL COMMENT '单位ID',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '物品单价',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`product_id`),
  UNIQUE KEY `product_code` (`product_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='物品信息表';
```

#### 4.2.4 库存表（wms_inventory）

```sql
CREATE TABLE `wms_inventory` (
  `inventory_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `product_id` bigint(20) NOT NULL COMMENT '物品ID',
  `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '库存数量',
  `min_quantity` decimal(18,2) DEFAULT '0.00' COMMENT '最小库存量',
  `max_quantity` decimal(18,2) DEFAULT '0.00' COMMENT '最大库存量',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1预警 2缺货）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`inventory_id`),
  UNIQUE KEY `idx_product_warehouse` (`product_id`,`warehouse_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='库存表';
```

#### 4.2.5 入库单表（wms_inventory_in）

```sql
CREATE TABLE `wms_inventory_in` (
  `in_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '入库单ID',
  `in_code` varchar(32) NOT NULL COMMENT '入库单号',
  `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
  `product_id` bigint(20) NOT NULL COMMENT '物品ID',
  `quantity` decimal(18,2) NOT NULL COMMENT '数量',
  `status` char(1) DEFAULT '0' COMMENT '状态（0未审核 1已审核）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`in_id`),
  UNIQUE KEY `uk_in_code` (`in_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='入库单表';
```

#### 4.2.6 出库单表（wms_inventory_out）

```sql
CREATE TABLE `wms_inventory_out` (
  `out_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '出库单ID',
  `out_code` varchar(32) NOT NULL COMMENT '出库单号',
  `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
  `product_id` bigint(20) NOT NULL COMMENT '物品ID',
  `quantity` decimal(18,2) NOT NULL COMMENT '数量',
  `status` char(1) DEFAULT '0' COMMENT '状态（0未审核 1已审核）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`out_id`),
  UNIQUE KEY `uk_out_code` (`out_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='出库单表';
```

#### 4.2.7 出入库日志表（wms_inventory_log）

```sql
CREATE TABLE `wms_inventory_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `oper_type` char(1) NOT NULL COMMENT '操作类型（1入库 2出库 3调拨 4盘点）',
  `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
  `product_id` bigint(20) NOT NULL COMMENT '物品ID',
  `quantity` decimal(18,2) NOT NULL COMMENT '操作数量',
  `before_quantity` decimal(18,2) DEFAULT NULL COMMENT '操作前数量',
  `after_quantity` decimal(18,2) DEFAULT NULL COMMENT '操作后数量',
  `oper_code` varchar(32) DEFAULT NULL COMMENT '操作单号',
  `oper_name` varchar(50) DEFAULT NULL COMMENT '操作人员',
  `oper_ip` varchar(128) DEFAULT NULL COMMENT '操作IP',
  `oper_location` varchar(255) DEFAULT NULL COMMENT '操作地点',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`),
  KEY `idx_warehouse_id` (`warehouse_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_oper_time` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='出入库日志表';
```

## 5. 功能模块

### 5.1 用户管理

- 用户列表：查询、新增、修改、删除用户
- 用户详情：查看用户详细信息
- 用户状态：启用/停用用户
- 用户导入导出：批量导入导出用户
- 用户分配角色：为用户分配角色

### 5.2 角色管理

- 角色列表：查询、新增、修改、删除角色
- 角色详情：查看角色详细信息
- 角色状态：启用/停用角色
- 角色权限分配：为角色分配菜单权限
- 角色数据权限：设置角色数据权限范围

### 5.3 仓库管理

- 仓库列表：查询、新增、修改、删除仓库
- 仓库详情：查看仓库详细信息
- 仓库状态：启用/停用仓库
- 仓库容量管理：设置仓库容量
- 仓库权限管理：设置用户对仓库的访问权限

### 5.4 物品管理

- 物品列表：查询、新增、修改、删除物品
- 物品详情：查看物品详细信息
- 物品状态：启用/停用物品
- 物品分类管理：管理物品分类
- 物品规格管理：管理物品规格
- 物品单位管理：管理物品单位

### 5.5 库存管理

- 库存列表：查询库存信息
- 库存详情：查看库存详细信息
- 库存预警：设置库存预警阈值，显示预警和缺货状态
- 库存调整：手动调整库存数量
- 库存盘点：进行库存盘点，记录盘点结果

### 5.6 入库管理

- 入库单列表：查询、新增、修改、删除入库单
- 入库单详情：查看入库单详细信息
- 入库单审核：审核入库单，更新库存
- 入库单打印：打印入库单
- 扫码入库：使用二维码扫描入库

### 5.7 出库管理

- 出库单列表：查询、新增、修改、删除出库单
- 出库单详情：查看出库单详细信息
- 出库单审核：审核出库单，更新库存
- 出库单打印：打印出库单
- 扫码出库：使用二维码扫描出库

### 5.8 报表统计

- 库存报表：显示库存统计数据和图表
- 入库报表：显示入库统计数据和图表
- 出库报表：显示出库统计数据和图表
- 库存预警报表：显示库存预警和缺货情况
- 物品周转率报表：显示物品周转率统计

### 5.9 日志管理

- 操作日志：记录用户操作日志
- 出入库日志：记录出入库操作日志
- 系统日志：记录系统运行日志
- 权限日志：记录权限变更日志

#### 5.9.1 日志模块路径结构

##### 前端页面模块路径

**审计日志（操作日志）**：
- 路径：[warehouse-system/frontend/src/views/log/operation](file:///c:/CKGLXT/warehouse-system/frontend/src/views/log/operation)
- 文件：index.vue

**数据日志**：
- 路径：[warehouse-system/frontend/src/views/log/data](file:///c:/CKGLXT/warehouse-system/frontend/src/views/log/data)
- 文件：index.vue

**错误日志**：
- 路径：[warehouse-system/frontend/src/views/log/error](file:///c:/CKGLXT/warehouse-system/frontend/src/views/log/error)
- 文件：index.vue

**登录日志**：
- 路径：[warehouse-system/frontend/src/views/log/system](file:///c:/CKGLXT/warehouse-system/frontend/src/views/log/system)
- 文件：index.vue

**安全日志**：
- 路径：[warehouse-system/frontend/src/views/log/security](file:///c:/CKGLXT/warehouse-system/frontend/src/views/log/security)
- 文件：index.vue

##### 后端模块路径

**日志主目录**：[warehouse-system/backend/src/main/java/com/warehouse/log](file:///c:/CKGLXT/warehouse-system/backend/src/main/java/com/warehouse/log)
- controller - 控制器层
- domain - 实体类
- mapper - 数据访问层
- service - 服务层

## 6. 用户权限控制

### 6.1 权限模型

系统采用基于角色的访问控制（RBAC）模型，主要包括以下几个部分：

- **用户（User）**：系统的使用者
- **角色（Role）**：用户的分组，如管理员、普通用户等
- **权限（Permission）**：对资源的操作权限，如查看、新增、修改、删除等
- **资源（Resource）**：系统中的资源，如菜单、按钮、数据等

### 6.2 权限分配

- **角色权限分配**：为角色分配菜单和按钮权限
- **用户角色分配**：为用户分配一个或多个角色
- **数据权限分配**：为角色设置数据访问范围
- **仓库权限分配**：为用户设置可访问的仓库

### 6.3 权限控制实现

#### 6.3.1 菜单权限控制

前端通过指令控制菜单的显示：

```html
<el-menu-item v-hasPermi="['system:user:list']">用户管理</el-menu-item>
```

后端通过注解控制接口的访问：

```java
@PreAuthorize("@ss.hasPermi('system:user:list')")
@GetMapping("/list")
public TableDataInfo list(SysUser user) {
    // 实现代码
}
```

#### 6.3.2 按钮权限控制

前端通过指令控制按钮的显示：

```html
<el-button v-hasPermi="['system:user:add']">新增</el-button>
```

后端通过注解控制接口的访问：

```java
@PreAuthorize("@ss.hasPermi('system:user:add')")
@PostMapping
public AjaxResult add(@RequestBody SysUser user) {
    // 实现代码
}
```

#### 6.3.3 数据权限控制

通过SQL拦截器控制数据的访问范围：

```java
@DataScope(deptAlias = "d", userAlias = "u")
@Override
public List<SysUser> selectUserList(SysUser user) {
    return userMapper.selectUserList(user);
}
```

#### 6.3.4 仓库权限控制

实现自定义注解控制仓库的访问权限：

```java
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface WarehouseScope {
    /**
     * 仓库表的别名
     */
    String warehouseAlias() default "";
}
```

使用示例：

```java
@WarehouseScope(warehouseAlias = "w")
@Override
public List<WmsInventory> selectInventoryList(WmsInventory inventory) {
    return inventoryMapper.selectInventoryList(inventory);
}
```

### 6.4 权限校验流程

1. **前端权限校验**：
   - 登录成功后，获取用户权限信息
   - 根据权限信息生成动态路由
   - 使用指令控制菜单和按钮的显示

2. **后端权限校验**：
   - 请求到达后端，经过认证过滤器验证身份
   - 通过注解或拦截器验证权限
   - 通过SQL拦截器控制数据访问范围

### 6.5 权限安全增强功能

为了提高系统的安全性和可用性，系统实现了以下权限安全增强功能：

#### 6.5.1 权限缓存机制

系统实现了权限缓存机制，将用户权限信息缓存到Redis中，减少数据库查询，提高系统性能。

```java
/**
 * 权限缓存服务
 */
@Component
public class PermissionCacheService {

    @Autowired
    private RedisCache redisCache;

    /**
     * 缓存前缀
     */
    private final String CACHE_PREFIX = "permission:";

    /**
     * 缓存有效期（默认30分钟）
     */
    private final Integer EXPIRATION = 30;

    /**
     * 缓存有效期单位
     */
    private final TimeUnit TIMEUNIT = TimeUnit.MINUTES;

    /**
     * 获取用户权限缓存
     */
    public Set<String> getPermissionCache(String key) {
        String cacheKey = CACHE_PREFIX + key;
        return redisCache.getCacheObject(cacheKey);
    }

    /**
     * 设置用户权限缓存
     */
    public void setPermissionCache(String key, Set<String> permissions) {
        String cacheKey = CACHE_PREFIX + key;
        redisCache.setCacheObject(cacheKey, permissions, EXPIRATION, TIMEUNIT);
    }

    /**
     * 删除用户权限缓存
     */
    public void deletePermissionCache(String key) {
        String cacheKey = CACHE_PREFIX + key;
        redisCache.deleteObject(cacheKey);
    }
}
```

#### 6.5.2 权限日志记录

系统实现了权限操作日志记录功能，记录用户的权限操作，方便审计和问题排查。

```sql
CREATE TABLE `sys_permission_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户名称',
  `operation` varchar(50) NOT NULL COMMENT '操作类型',
  `permission` varchar(100) NOT NULL COMMENT '权限标识',
  `status` char(1) DEFAULT '0' COMMENT '操作状态（0成功 1失败）',
  `ip_addr` varchar(50) DEFAULT '' COMMENT 'IP地址',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='权限操作日志表';
```

#### 6.5.3 权限可视化界面

系统提供了权限可视化界面，方便管理员查看和管理用户权限。

- 权限树形展示：以树形结构展示系统权限
- 角色权限管理：可视化管理角色权限
- 用户权限查看：查看用户拥有的权限

#### 6.5.4 权限模板功能

系统实现了权限模板功能，可以将常用的权限组合保存为模板，方便批量分配权限。

- 模板创建：创建权限模板
- 模板应用：将模板应用到角色
- 模板管理：管理权限模板

## 7. 实施路线图

### 7.1 第一阶段：基础功能实现

- 用户管理
- 角色管理
- 权限管理
- 仓库管理
- 物品管理

### 7.2 第二阶段：核心业务功能实现

- 库存管理
- 入库管理
- 出库管理
- 库存调拨
- 库存盘点

### 7.3 第三阶段：扩展功能实现

- 二维码管理
- 报表统计
- 日志管理
- 系统监控

### 7.4 第四阶段：优化与完善

- 性能优化
- 用户体验优化
- 安全性增强
- 功能完善

## 8. 代码审查与质量控制

### 8.1 代码审查流程

1. **开发人员提交代码审查请求**
   - 完成功能开发或bug修复
   - 进行自测，确保代码可以正常工作
   - 提交代码审查请求，指定审查人员

2. **审查人员进行代码审查**
   - 检查代码是否符合规范
   - 检查代码是否有潜在问题
   - 检查代码是否易于理解和维护
   - 提出修改建议

3. **开发人员根据建议修改代码**
   - 根据审查人员的建议修改代码
   - 对于不同意的建议，提供合理的解释

4. **审查人员确认修改**
   - 检查修改是否符合要求
   - 如果满意，批准代码合并
   - 如果不满意，继续提出修改建议

5. **代码合并到主分支**
   - 合并代码到主分支
   - 确保合并后的代码可以正常工作

### 8.2 代码审查清单

#### 8.2.1 代码规范

- [ ] 代码是否符合命名规范
- [ ] 代码是否有充分的注释
- [ ] 代码是否遵循项目的编码风格
- [ ] 代码是否遵循最佳实践

#### 8.2.2 代码质量

- [ ] 代码是否有单元测试
- [ ] 代码是否处理了异常情况
- [ ] 代码是否存在重复
- [ ] 代码是否存在安全漏洞
- [ ] 代码是否存在性能问题

#### 8.2.3 功能完整性

- [ ] 代码是否实现了所有需求
- [ ] 代码是否处理了边界情况
- [ ] 代码是否有适当的日志记录
- [ ] 代码是否有适当的错误处理

#### 8.2.4 可维护性

- [ ] 代码是否易于理解
- [ ] 代码是否易于修改
- [ ] 代码是否易于测试
- [ ] 代码是否有适当的文档

### 8.3 代码质量控制

#### 8.3.1 静态代码分析

使用静态代码分析工具检查代码质量，如：

- **SonarQube**：检查代码质量、安全漏洞、重复代码等
- **ESLint**：检查JavaScript/TypeScript代码规范
- **PMD**：检查Java代码规范
- **FindBugs**：检查Java代码中的潜在bug

#### 8.3.2 单元测试

- 为所有业务逻辑编写单元测试
- 使用JUnit、Mockito等工具进行单元测试
- 确保测试覆盖率达到要求

#### 8.3.3 集成测试

- 编写集成测试，确保各个模块之间的交互正常
- 使用Spring Boot Test等工具进行集成测试
- 确保测试覆盖所有关键业务流程

#### 8.3.4 性能测试

- 使用JMeter等工具进行性能测试
- 确保系统在高负载下仍能正常工作
- 识别并解决性能瓶颈

### 8.4 持续集成与持续部署

#### 8.4.1 持续集成

- 使用Jenkins、GitLab CI等工具进行持续集成
- 每次代码提交后自动构建和测试
- 发现问题及时通知开发人员

#### 8.4.2 持续部署

- 自动部署到测试环境
- 进行自动化测试
- 通过测试后自动部署到生产环境

## 9. 附录

### 9.1 术语表

- **WMS**：Warehouse Management System，仓库管理系统
- **SKU**：Stock Keeping Unit，库存单位
- **RBAC**：Role-Based Access Control，基于角色的访问控制
- **RESTful API**：一种API设计风格，使用HTTP方法表示操作类型，使用URL表示资源

### 9.2 常见问题

#### 9.2.1 如何处理并发操作？

系统使用乐观锁机制处理并发操作，通过版本号控制数据的更新。

```java
@Version
private Integer version;
```

在更新操作中，检查版本号是否匹配：

```java
@Update("UPDATE wms_inventory SET quantity = #{quantity}, version = version + 1 WHERE inventory_id = #{inventoryId} AND version = #{version}")
int updateWithVersion(WmsInventory inventory);
```

#### 9.2.2 如何确保数据一致性？

系统使用事务机制确保数据一致性，对于涉及多个操作的业务，使用事务保证要么全部成功，要么全部失败。

```java
@Transactional(rollbackFor = Exception.class)
public void processInventoryIn(WmsInventoryIn inventoryIn) {
    // 1. 更新入库单状态
    inventoryInMapper.updateStatus(inventoryIn.getInId(), "1");

    // 2. 更新库存
    WmsInventory inventory = new WmsInventory();
    inventory.setWarehouseId(inventoryIn.getWarehouseId());
    inventory.setProductId(inventoryIn.getProductId());
    inventory.setQuantity(inventoryIn.getQuantity());
    inventoryService.addInventory(inventory);

    // 3. 记录日志
    WmsInventoryLog log = new WmsInventoryLog();
    log.setOperType("1");
    log.setWarehouseId(inventoryIn.getWarehouseId());
    log.setProductId(inventoryIn.getProductId());
    log.setQuantity(inventoryIn.getQuantity());
    log.setOperCode(inventoryIn.getInCode());
    inventoryLogService.insertInventoryLog(log);
}
```

#### 9.2.3 如何处理大数据量？

系统使用分页查询、延迟加载等技术处理大数据量，对于需要处理的大量数据，使用批量操作提高效率。

```java
// 分页查询
@Override
public List<WmsInventory> selectInventoryList(WmsInventory inventory) {
    PageHelper.startPage(inventory.getPageNum(), inventory.getPageSize());
    return inventoryMapper.selectInventoryList(inventory);
}

// 批量操作
@Override
@Transactional(rollbackFor = Exception.class)
public int batchInsertInventory(List<WmsInventory> inventoryList) {
    return inventoryMapper.batchInsertInventory(inventoryList);
}
```

### 9.3 参考资料

- Spring Boot官方文档：https://spring.io/projects/spring-boot
- Vue.js官方文档：https://vuejs.org/
- Element UI官方文档：https://element.eleme.io/
- MyBatis官方文档：https://mybatis.org/mybatis-3/
- MySQL官方文档：https://dev.mysql.com/doc/
