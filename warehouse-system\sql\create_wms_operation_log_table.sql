-- 创建操作日志表
-- 用于记录系统操作日志，符合字段定义标准

DROP TABLE IF EXISTS wms_operation_log;

CREATE TABLE wms_operation_log (
  log_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
  operation_desc VARCHAR(200) DEFAULT '' COMMENT '操作描述',
  operation_status CHAR(1) DEFAULT '0' COMMENT '操作状态（0成功 1失败）',
  error_message TEXT COMMENT '错误信息',
  operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  operator_id BIGINT(20) DEFAULT NULL COMMENT '操作人ID',
  operator_name VARCHAR(50) DEFAULT '' COMMENT '操作人姓名',
  ip_address VARCHAR(50) DEFAULT '' COMMENT 'IP地址',
  user_agent VARCHAR(500) DEFAULT '' COMMENT '用户代理',
  request_url VARCHAR(500) DEFAULT '' COMMENT '请求URL',
  request_method VARCHAR(10) DEFAULT '' COMMENT '请求方法',
  request_params TEXT COMMENT '请求参数',
  response_data TEXT COMMENT '响应数据',
  execution_time BIGINT(20) DEFAULT 0 COMMENT '执行时间(毫秒)',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (log_id),
  KEY idx_operation_status (operation_status),
  KEY idx_operation_time (operation_time),
  KEY idx_operator_id (operator_id),
  KEY idx_operation_type (operation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 插入操作状态字典数据
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('操作状态', 'operation_status', '0', 'admin', sysdate(), '操作日志状态字典') 
ON DUPLICATE KEY UPDATE dict_name = VALUES(dict_name);

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(1, '成功', '0', 'operation_status', '', 'success', 'Y', '0', 'admin', sysdate(), '操作成功'),
(2, '失败', '1', 'operation_status', '', 'danger', 'N', '0', 'admin', sysdate(), '操作失败')
ON DUPLICATE KEY UPDATE 
dict_label = VALUES(dict_label),
css_class = VALUES(css_class),
list_class = VALUES(list_class);

-- 插入操作类型字典数据
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('操作类型', 'operation_type', '0', 'admin', sysdate(), '操作日志类型字典') 
ON DUPLICATE KEY UPDATE dict_name = VALUES(dict_name);

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES 
(1, '新增', 'INSERT', 'operation_type', '', 'primary', 'N', '0', 'admin', sysdate(), '新增操作'),
(2, '修改', 'UPDATE', 'operation_type', '', 'info', 'N', '0', 'admin', sysdate(), '修改操作'),
(3, '删除', 'DELETE', 'operation_type', '', 'danger', 'N', '0', 'admin', sysdate(), '删除操作'),
(4, '查询', 'SELECT', 'operation_type', '', 'success', 'N', '0', 'admin', sysdate(), '查询操作'),
(5, '导出', 'EXPORT', 'operation_type', '', 'warning', 'N', '0', 'admin', sysdate(), '导出操作'),
(6, '导入', 'IMPORT', 'operation_type', '', 'warning', 'N', '0', 'admin', sysdate(), '导入操作'),
(7, '登录', 'LOGIN', 'operation_type', '', 'primary', 'N', '0', 'admin', sysdate(), '登录操作'),
(8, '登出', 'LOGOUT', 'operation_type', '', 'info', 'N', '0', 'admin', sysdate(), '登出操作')
ON DUPLICATE KEY UPDATE 
dict_label = VALUES(dict_label),
css_class = VALUES(css_class),
list_class = VALUES(list_class);