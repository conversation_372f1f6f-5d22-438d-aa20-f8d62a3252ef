# 项目相关配置
wanyu:
  # 名称
  name: WanYu
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2024
  # 实例演示开关
  demoEnabled: false
  # 文件路径，统一为相对路径 warehouse-system/Pictures
  profile: warehouse-system/Pictures
  # 获取ip地址开关
  addressEnabled: false

# 开发环境配置
server:
  # 服务器的HTTP端口
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# Spring配置
spring:
  # 应用程序名称
  application:
    name: warehouse-system
  # 允许bean定义覆盖，解决Redis配置类中重复定义redisTemplate的问题
  main:
    allow-bean-definition-overriding: true
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: root
    password: ${MYSQL_PASSWORD:123456}  # 使用环境变量或默认密码
    druid:
      # 平铺结构配置 - 用于DruidProperties类
      initialSize: 10
      minIdle: 20
      maxActive: 100
      maxWait: 60000
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      # 主库数据源
      master:
        url: **************************************************************************************************************************************************************************************
        username: root
        password: ${MYSQL_PASSWORD:123456}  # 使用环境变量或默认密码
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow: 127.0.0.1
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  
  # 禁用Redis配置
  # redis:
  #   # 地址
  #   host: ${REDIS_HOST:localhost}
  #   # 端口，默认为6379
  #   port: ${REDIS_PORT:6379}
  #   # 数据库索引
  #   database: ${REDIS_DATABASE:0}
  #   # 密码
  #   password: ${REDIS_PASSWORD:}
  #   # 连接超时时间
  #   timeout: 3s
  #   # 是否启用Redis（可以通过环境变量控制）
  #   enabled: ${REDIS_ENABLED:true}
  #   # 连接池配置
  #   lettuce:
  #     pool:
  #       # 连接池中的最小空闲连接
  #       min-idle: 5
  #       # 连接池中的最大空闲连接
  #       max-idle: 20
  #       # 连接池的最大数据库连接数
  #       max-active: 50
  #       # 连接池最大阻塞等待时间（毫秒）
  #       max-wait: 3000ms
  #   # 添加健康检查
  #   health-check:
  #     enabled: ${REDIS_HEALTH_CHECK:true}
  #     interval: 30s

  # 缓存配置 (使用内存缓存)
  cache:
    type: simple

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: ${JWT_SECRET:abcdefghijklmnopqrstuvwxyz}
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.wanyu.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 日志配置
logging:
  level:
    com.wanyu: info
    org.springframework: warn

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false