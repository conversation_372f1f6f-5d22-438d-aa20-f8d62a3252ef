package com.wanyu.common.utils;

import java.util.*;
import java.util.regex.Pattern;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 字段标准验证工具类
 * 用于验证数据库字段定义是否符合项目标准
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public class FieldStandardValidator {
    
    // 标准状态值定义
    public static final String STATUS_ENABLED = "0";  // 启用/正常
    public static final String STATUS_DISABLED = "1"; // 禁用/停用
    public static final String STATUS_DELETED = "2";  // 删除标记
    
    // 操作状态定义
    public static final String OPERATION_SUCCESS = "0"; // 操作成功
    public static final String OPERATION_FAILED = "1";  // 操作失败
    
    // 布尔值定义
    public static final String BOOLEAN_FALSE = "0"; // 否/关闭
    public static final String BOOLEAN_TRUE = "1";  // 是/开启
    
    // 字段命名模式
    private static final Pattern STATUS_FIELD_PATTERN = Pattern.compile("^(.*_)?status$");
    private static final Pattern BOOLEAN_FIELD_PATTERN = Pattern.compile("^(is_|enable_|has_|can_).*");
    private static final Pattern FLAG_FIELD_PATTERN = Pattern.compile("^.*_flag$");
    
    /**
     * 验证状态字段值是否符合标准
     */
    public static boolean isValidStatusValue(String value) {
        return STATUS_ENABLED.equals(value) || STATUS_DISABLED.equals(value) || STATUS_DELETED.equals(value);
    }
    
    /**
     * 验证操作状态字段值是否符合标准
     */
    public static boolean isValidOperationStatus(String value) {
        return OPERATION_SUCCESS.equals(value) || OPERATION_FAILED.equals(value);
    }
    
    /**
     * 验证布尔字段值是否符合标准
     */
    public static boolean isValidBooleanValue(String value) {
        return BOOLEAN_FALSE.equals(value) || BOOLEAN_TRUE.equals(value);
    }
    
    /**
     * 根据字段名判断字段类型
     */
    public static FieldType getFieldType(String fieldName) {
        if (fieldName == null) {
            return FieldType.UNKNOWN;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        
        if ("del_flag".equals(lowerFieldName)) {
            return FieldType.DELETE_FLAG;
        }
        
        if (STATUS_FIELD_PATTERN.matcher(lowerFieldName).matches()) {
            if (lowerFieldName.contains("operation")) {
                return FieldType.OPERATION_STATUS;
            }
            return FieldType.STATUS;
        }
        
        if (BOOLEAN_FIELD_PATTERN.matcher(lowerFieldName).matches()) {
            return FieldType.BOOLEAN;
        }
        
        if (FLAG_FIELD_PATTERN.matcher(lowerFieldName).matches()) {
            return FieldType.FLAG;
        }
        
        return FieldType.UNKNOWN;
    } 
   
    /**
     * 验证字段值是否符合标准
     */
    public static ValidationResult validateFieldValue(String fieldName, String value) {
        FieldType fieldType = getFieldType(fieldName);
        ValidationResult result = new ValidationResult();
        result.setFieldName(fieldName);
        result.setFieldType(fieldType);
        result.setValue(value);
        
        switch (fieldType) {
            case STATUS:
                result.setValid(isValidStatusValue(value));
                result.setExpectedValues(Arrays.asList(STATUS_ENABLED, STATUS_DISABLED, STATUS_DELETED));
                break;
            case OPERATION_STATUS:
                result.setValid(isValidOperationStatus(value));
                result.setExpectedValues(Arrays.asList(OPERATION_SUCCESS, OPERATION_FAILED));
                break;
            case BOOLEAN:
                result.setValid(isValidBooleanValue(value));
                result.setExpectedValues(Arrays.asList(BOOLEAN_FALSE, BOOLEAN_TRUE));
                break;
            case DELETE_FLAG:
                result.setValid(STATUS_ENABLED.equals(value) || STATUS_DELETED.equals(value));
                result.setExpectedValues(Arrays.asList(STATUS_ENABLED, STATUS_DELETED));
                break;
            case FLAG:
                result.setValid(isValidBooleanValue(value));
                result.setExpectedValues(Arrays.asList(BOOLEAN_FALSE, BOOLEAN_TRUE));
                break;
            default:
                result.setValid(true); // 未知类型不验证
                result.setExpectedValues(Collections.emptyList());
        }
        
        if (!result.isValid()) {
            result.setErrorMessage(String.format(
                "字段 %s 的值 '%s' 不符合标准，期望值: %s", 
                fieldName, value, result.getExpectedValues()
            ));
        }
        
        return result;
    }
    
    /**
     * 批量验证字段值
     */
    public static List<ValidationResult> validateFields(Map<String, String> fieldValues) {
        List<ValidationResult> results = new ArrayList<>();
        
        for (Map.Entry<String, String> entry : fieldValues.entrySet()) {
            ValidationResult result = validateFieldValue(entry.getKey(), entry.getValue());
            results.add(result);
        }
        
        return results;
    }
    
    /**
     * 检查是否所有字段都符合标准
     */
    public static boolean isAllFieldsValid(Map<String, String> fieldValues) {
        return validateFields(fieldValues).stream().allMatch(ValidationResult::isValid);
    }
    
    /**
     * 获取不符合标准的字段
     */
    public static List<ValidationResult> getInvalidFields(Map<String, String> fieldValues) {
        return validateFields(fieldValues).stream()
                .filter(result -> !result.isValid())
                .collect(ArrayList::new, (list, item) -> list.add(item), ArrayList::addAll);
    }
    
    /**
     * 检查数据库表字段定义是否符合标准
     */
    public static TableStandardResult checkTableStandard(String tableName) {
        TableStandardResult result = new TableStandardResult();
        result.setTableName(tableName);
        result.setCheckTime(LocalDateTime.now());
        
        List<FieldDefinitionResult> fieldResults = new ArrayList<>();
        List<String> issues = new ArrayList<>();
        
        try {
            // 这里应该通过数据源获取连接，为了演示使用默认配置
            String url = "*********************************************************************************************************************************************************";
            String username = "root";
            String password = "123456";
            
            try (Connection conn = DriverManager.getConnection(url, username, password)) {
                fieldResults = checkTableFieldDefinitions(conn, tableName);
                
                // 分析字段定义问题
                for (FieldDefinitionResult fieldResult : fieldResults) {
                    if (!fieldResult.isCompliant()) {
                        issues.addAll(fieldResult.getIssues());
                    }
                }
            }
        } catch (SQLException e) {
            issues.add("数据库连接失败: " + e.getMessage());
        }
        
        result.setFieldResults(fieldResults);
        result.setIssues(issues);
        result.setCompliant(issues.isEmpty());
        
        return result;
    }
    
    /**
     * 检查表的字段定义
     */
    private static List<FieldDefinitionResult> checkTableFieldDefinitions(Connection conn, String tableName) throws SQLException {
        List<FieldDefinitionResult> results = new ArrayList<>();
        
        String sql = "SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, IS_NULLABLE, COLUMN_COMMENT " +
                    "FROM INFORMATION_SCHEMA.COLUMNS " +
                    "WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? " +
                    "ORDER BY ORDINAL_POSITION";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, conn.getCatalog());
            stmt.setString(2, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String columnName = rs.getString("COLUMN_NAME");
                    String dataType = rs.getString("DATA_TYPE");
                    String columnDefault = rs.getString("COLUMN_DEFAULT");
                    String isNullable = rs.getString("IS_NULLABLE");
                    String columnComment = rs.getString("COLUMN_COMMENT");
                    
                    FieldDefinitionResult fieldResult = checkFieldDefinition(
                        columnName, dataType, columnDefault, isNullable, columnComment
                    );
                    results.add(fieldResult);
                }
            }
        }
        
        return results;
    }
    
    /**
     * 检查单个字段定义
     */
    private static FieldDefinitionResult checkFieldDefinition(String columnName, String dataType, 
                                                            String columnDefault, String isNullable, String columnComment) {
        FieldDefinitionResult result = new FieldDefinitionResult();
        result.setColumnName(columnName);
        result.setDataType(dataType);
        result.setColumnDefault(columnDefault);
        result.setIsNullable(isNullable);
        result.setColumnComment(columnComment);
        
        FieldType fieldType = getFieldType(columnName);
        result.setFieldType(fieldType);
        
        List<String> issues = new ArrayList<>();
        
        // 检查字段类型相关的标准
        if (fieldType != FieldType.UNKNOWN) {
            // 检查数据类型
            if (!"char".equalsIgnoreCase(dataType) && !"varchar".equalsIgnoreCase(dataType)) {
                issues.add(String.format("字段 %s 应该使用 CHAR(1) 类型，当前类型: %s", columnName, dataType));
            }
            
            // 检查默认值
            String expectedDefault = getExpectedDefaultValue(fieldType);
            if (expectedDefault != null && !expectedDefault.equals(columnDefault)) {
                issues.add(String.format("字段 %s 默认值应该为 '%s'，当前默认值: %s", 
                    columnName, expectedDefault, columnDefault));
            }
            
            // 检查注释格式
            String expectedCommentPattern = getExpectedCommentPattern(fieldType);
            if (expectedCommentPattern != null && (columnComment == null || !columnComment.matches(expectedCommentPattern))) {
                issues.add(String.format("字段 %s 注释格式不符合标准，期望格式: %s，当前注释: %s", 
                    columnName, getExpectedCommentExample(fieldType), columnComment));
            }
        }
        
        result.setIssues(issues);
        result.setCompliant(issues.isEmpty());
        
        return result;
    }
    
    /**
     * 获取字段类型的期望默认值
     */
    private static String getExpectedDefaultValue(FieldType fieldType) {
        switch (fieldType) {
            case STATUS:
            case OPERATION_STATUS:
            case BOOLEAN:
            case FLAG:
            case DELETE_FLAG:
                return "0";
            default:
                return null;
        }
    }
    
    /**
     * 获取字段类型的期望注释模式
     */
    private static String getExpectedCommentPattern(FieldType fieldType) {
        switch (fieldType) {
            case STATUS:
                return ".*状态.*\\(.*0.*正常.*1.*停用.*\\).*";
            case OPERATION_STATUS:
                return ".*状态.*\\(.*0.*成功.*1.*失败.*\\).*";
            case BOOLEAN:
                return ".*\\(.*0.*否.*1.*是.*\\).*";
            case DELETE_FLAG:
                return ".*删除.*\\(.*0.*存在.*2.*删除.*\\).*";
            case FLAG:
                return ".*\\(.*0.*.*1.*\\).*";
            default:
                return null;
        }
    }
    
    /**
     * 获取字段类型的期望注释示例
     */
    private static String getExpectedCommentExample(FieldType fieldType) {
        switch (fieldType) {
            case STATUS:
                return "状态（0正常 1停用）";
            case OPERATION_STATUS:
                return "操作状态（0成功 1失败）";
            case BOOLEAN:
                return "是否xxx（0否 1是）";
            case DELETE_FLAG:
                return "删除标志（0代表存在 2代表删除）";
            case FLAG:
                return "xxx标记（0关闭 1开启）";
            default:
                return "标准注释格式";
        }
    }
    
    /**
     * 生成字段标准合规性报告
     */
    public static ComplianceReport generateComplianceReport(List<String> tableNames) {
        ComplianceReport report = new ComplianceReport();
        report.setReportTime(LocalDateTime.now());
        report.setReportId("FIELD_STANDARD_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        
        List<TableStandardResult> tableResults = new ArrayList<>();
        int totalTables = tableNames.size();
        int compliantTables = 0;
        int totalIssues = 0;
        
        for (String tableName : tableNames) {
            TableStandardResult tableResult = checkTableStandard(tableName);
            tableResults.add(tableResult);
            
            if (tableResult.isCompliant()) {
                compliantTables++;
            } else {
                totalIssues += tableResult.getIssues().size();
            }
        }
        
        report.setTableResults(tableResults);
        report.setTotalTables(totalTables);
        report.setCompliantTables(compliantTables);
        report.setNonCompliantTables(totalTables - compliantTables);
        report.setTotalIssues(totalIssues);
        report.setComplianceRate((double) compliantTables / totalTables * 100);
        
        return report;
    }
    
    /**
     * 生成字段标准检查SQL脚本
     */
    public static String generateFieldStandardCheckScript(String databaseName) {
        StringBuilder script = new StringBuilder();
        script.append("-- 字段标准检查脚本\n");
        script.append("-- 生成时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");
        
        script.append("-- 检查状态字段定义\n");
        script.append("SELECT \n");
        script.append("    TABLE_NAME,\n");
        script.append("    COLUMN_NAME,\n");
        script.append("    DATA_TYPE,\n");
        script.append("    COLUMN_DEFAULT,\n");
        script.append("    COLUMN_COMMENT,\n");
        script.append("    CASE \n");
        script.append("        WHEN COLUMN_NAME LIKE '%status' AND DATA_TYPE != 'char' THEN '数据类型不符合标准'\n");
        script.append("        WHEN COLUMN_NAME LIKE '%status' AND COLUMN_DEFAULT != '0' THEN '默认值不符合标准'\n");
        script.append("        WHEN COLUMN_NAME LIKE '%status' AND (COLUMN_COMMENT IS NULL OR COLUMN_COMMENT NOT LIKE '%0%正常%1%停用%') THEN '注释格式不符合标准'\n");
        script.append("        WHEN COLUMN_NAME LIKE 'is_%' AND DATA_TYPE != 'char' THEN '数据类型不符合标准'\n");
        script.append("        WHEN COLUMN_NAME LIKE 'is_%' AND COLUMN_DEFAULT != '0' THEN '默认值不符合标准'\n");
        script.append("        WHEN COLUMN_NAME = 'del_flag' AND COLUMN_DEFAULT != '0' THEN '默认值不符合标准'\n");
        script.append("        ELSE '符合标准'\n");
        script.append("    END as COMPLIANCE_STATUS\n");
        script.append("FROM INFORMATION_SCHEMA.COLUMNS \n");
        script.append("WHERE TABLE_SCHEMA = '").append(databaseName).append("'\n");
        script.append("    AND (COLUMN_NAME LIKE '%status' OR COLUMN_NAME LIKE 'is_%' OR COLUMN_NAME LIKE '%_flag')\n");
        script.append("ORDER BY TABLE_NAME, COLUMN_NAME;\n\n");
        
        script.append("-- 检查不符合标准的字段\n");
        script.append("SELECT \n");
        script.append("    TABLE_NAME,\n");
        script.append("    COLUMN_NAME,\n");
        script.append("    '字段定义不符合标准' as ISSUE_TYPE\n");
        script.append("FROM INFORMATION_SCHEMA.COLUMNS \n");
        script.append("WHERE TABLE_SCHEMA = '").append(databaseName).append("'\n");
        script.append("    AND (\n");
        script.append("        (COLUMN_NAME LIKE '%status' AND (DATA_TYPE != 'char' OR COLUMN_DEFAULT != '0'))\n");
        script.append("        OR (COLUMN_NAME LIKE 'is_%' AND (DATA_TYPE != 'char' OR COLUMN_DEFAULT != '0'))\n");
        script.append("        OR (COLUMN_NAME = 'del_flag' AND COLUMN_DEFAULT != '0')\n");
        script.append("    );\n");
        
        return script.toString();
    }
    
    /**
     * 表标准检查结果类
     */
    public static class TableStandardResult {
        private String tableName;
        private LocalDateTime checkTime;
        private boolean compliant;
        private List<FieldDefinitionResult> fieldResults;
        private List<String> issues;
        
        // Constructors
        public TableStandardResult() {
            this.fieldResults = new ArrayList<>();
            this.issues = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }
        
        public LocalDateTime getCheckTime() { return checkTime; }
        public void setCheckTime(LocalDateTime checkTime) { this.checkTime = checkTime; }
        
        public boolean isCompliant() { return compliant; }
        public void setCompliant(boolean compliant) { this.compliant = compliant; }
        
        public List<FieldDefinitionResult> getFieldResults() { return fieldResults; }
        public void setFieldResults(List<FieldDefinitionResult> fieldResults) { this.fieldResults = fieldResults; }
        
        public List<String> getIssues() { return issues; }
        public void setIssues(List<String> issues) { this.issues = issues; }
    }
    
    /**
     * 字段定义检查结果类
     */
    public static class FieldDefinitionResult {
        private String columnName;
        private String dataType;
        private String columnDefault;
        private String isNullable;
        private String columnComment;
        private FieldType fieldType;
        private boolean compliant;
        private List<String> issues;
        
        // Constructors
        public FieldDefinitionResult() {
            this.issues = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        
        public String getDataType() { return dataType; }
        public void setDataType(String dataType) { this.dataType = dataType; }
        
        public String getColumnDefault() { return columnDefault; }
        public void setColumnDefault(String columnDefault) { this.columnDefault = columnDefault; }
        
        public String getIsNullable() { return isNullable; }
        public void setIsNullable(String isNullable) { this.isNullable = isNullable; }
        
        public String getColumnComment() { return columnComment; }
        public void setColumnComment(String columnComment) { this.columnComment = columnComment; }
        
        public FieldType getFieldType() { return fieldType; }
        public void setFieldType(FieldType fieldType) { this.fieldType = fieldType; }
        
        public boolean isCompliant() { return compliant; }
        public void setCompliant(boolean compliant) { this.compliant = compliant; }
        
        public List<String> getIssues() { return issues; }
        public void setIssues(List<String> issues) { this.issues = issues; }
    }
    
    /**
     * 合规性报告类
     */
    public static class ComplianceReport {
        private String reportId;
        private LocalDateTime reportTime;
        private int totalTables;
        private int compliantTables;
        private int nonCompliantTables;
        private int totalIssues;
        private double complianceRate;
        private List<TableStandardResult> tableResults;
        
        // Constructors
        public ComplianceReport() {
            this.tableResults = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getReportId() { return reportId; }
        public void setReportId(String reportId) { this.reportId = reportId; }
        
        public LocalDateTime getReportTime() { return reportTime; }
        public void setReportTime(LocalDateTime reportTime) { this.reportTime = reportTime; }
        
        public int getTotalTables() { return totalTables; }
        public void setTotalTables(int totalTables) { this.totalTables = totalTables; }
        
        public int getCompliantTables() { return compliantTables; }
        public void setCompliantTables(int compliantTables) { this.compliantTables = compliantTables; }
        
        public int getNonCompliantTables() { return nonCompliantTables; }
        public void setNonCompliantTables(int nonCompliantTables) { this.nonCompliantTables = nonCompliantTables; }
        
        public int getTotalIssues() { return totalIssues; }
        public void setTotalIssues(int totalIssues) { this.totalIssues = totalIssues; }
        
        public double getComplianceRate() { return complianceRate; }
        public void setComplianceRate(double complianceRate) { this.complianceRate = complianceRate; }
        
        public List<TableStandardResult> getTableResults() { return tableResults; }
        public void setTableResults(List<TableStandardResult> tableResults) { this.tableResults = tableResults; }
        
        /**
         * 生成报告摘要
         */
        public String generateSummary() {
            StringBuilder summary = new StringBuilder();
            summary.append("字段标准合规性报告摘要\n");
            summary.append("===================\n");
            summary.append("报告ID: ").append(reportId).append("\n");
            summary.append("生成时间: ").append(reportTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            summary.append("检查表数: ").append(totalTables).append("\n");
            summary.append("合规表数: ").append(compliantTables).append("\n");
            summary.append("不合规表数: ").append(nonCompliantTables).append("\n");
            summary.append("总问题数: ").append(totalIssues).append("\n");
            summary.append("合规率: ").append(String.format("%.2f%%", complianceRate)).append("\n\n");
            
            if (nonCompliantTables > 0) {
                summary.append("不合规表详情:\n");
                for (TableStandardResult tableResult : tableResults) {
                    if (!tableResult.isCompliant()) {
                        summary.append("- ").append(tableResult.getTableName())
                               .append(" (").append(tableResult.getIssues().size()).append("个问题)\n");
                    }
                }
            }
            
            return summary.toString();
        }
    }
    
    /**
     * 字段验证结果类
     */
    public static class ValidationResult {
        private String fieldName;
        private FieldType fieldType;
        private String value;
        private boolean valid;
        private List<String> expectedValues;
        private String errorMessage;
        
        // Constructors
        public ValidationResult() {
            this.expectedValues = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }
        
        public FieldType getFieldType() { return fieldType; }
        public void setFieldType(FieldType fieldType) { this.fieldType = fieldType; }
        
        public String getValue() { return value; }
        public void setValue(String value) { this.value = value; }
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public List<String> getExpectedValues() { return expectedValues; }
        public void setExpectedValues(List<String> expectedValues) { this.expectedValues = expectedValues; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    /**
     * 字段类型枚举
     */
    public enum FieldType {
        STATUS("状态字段"),
        OPERATION_STATUS("操作状态字段"),
        BOOLEAN("布尔字段"),
        DELETE_FLAG("删除标记字段"),
        FLAG("标记字段"),
        UNKNOWN("未知类型");
        
        private final String description;
        
        FieldType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}