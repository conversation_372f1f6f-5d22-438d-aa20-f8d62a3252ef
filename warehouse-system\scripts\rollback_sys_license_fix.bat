@echo off
chcp 65001 >nul
echo =====================================================
echo sys_license表字段标准化修复回滚脚本
echo =====================================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo [WARNING] 即将回滚sys_license表字段标准化修复
echo [WARNING] 这将恢复到修复前的状态：status字段 0=禁用，1=启用
echo.
set /p confirm="确认执行回滚操作？(y/N): "
if /i not "%confirm%"=="y" (
    echo [INFO] 回滚操作已取消
    pause
    exit /b 0
)

echo.
echo [INFO] 开始执行回滚操作...
echo [INFO] 数据库: %DB_NAME%
echo [INFO] 时间: %date% %time%
echo.

echo [STEP 1] 执行回滚脚本...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% %DB_NAME% < "%~dp0..\sql\rollback_sys_license_fix.sql"

if %errorlevel% neq 0 (
    echo [ERROR] 回滚脚本执行失败！
    echo [INFO] 请检查数据库连接和备份表是否存在
    pause
    exit /b 1
)

echo [SUCCESS] 回滚脚本执行成功！
echo.

echo [STEP 2] 验证回滚结果...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT COLUMN_DEFAULT, COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'warehouse_system' AND TABLE_NAME = 'sys_license' AND COLUMN_NAME = 'status';"

echo.
echo =====================================================
echo sys_license表字段标准化回滚完成
echo =====================================================
echo.
echo [INFO] 回滚内容：
echo   - status字段定义：0=禁用，1=启用（原始状态）
echo   - 默认值：'1'（启用状态）
echo   - 数据已恢复到修复前状态
echo.
echo [INFO] 修复后备份：sys_license_backup_after_fix
echo [INFO] 如需重新修复，请执行：execute_sys_license_fix.bat
echo.

pause