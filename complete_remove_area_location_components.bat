@echo off
chcp 65001 >nul
echo ========================================
echo 完整删除库区和货架相关组件
echo ========================================

echo.
echo 1. 删除前端库区和货架相关页面...
if exist "warehouse-system\frontend\src\views\warehouse\area" (
    rmdir /s /q "warehouse-system\frontend\src\views\warehouse\area"
    echo    已删除: warehouse-system\frontend\src\views\warehouse\area
)

if exist "warehouse-system\frontend\src\views\warehouse\location" (
    rmdir /s /q "warehouse-system\frontend\src\views\warehouse\location"
    echo    已删除: warehouse-system\frontend\src\views\warehouse\location
)

if exist "warehouse-system\frontend\src\views\warehouse\rack" (
    rmdir /s /q "warehouse-system\frontend\src\views\warehouse\rack"
    echo    已删除: warehouse-system\frontend\src\views\warehouse\rack
)

echo.
echo 2. 删除后端库区和货架相关Java文件...
if exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseAreaController.java" (
    del "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseAreaController.java"
    echo    已删除: WarehouseAreaController.java
)

if exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseRackController.java" (
    del "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseRackController.java"
    echo    已删除: WarehouseRackController.java
)

if exist "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseLocationController.java" (
    del "warehouse-system\backend\wanyu-admin\src\main\java\com\wanyu\web\controller\warehouse\WarehouseLocationController.java"
    echo    已删除: WarehouseLocationController.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\IWarehouseAreaService.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\IWarehouseAreaService.java"
    echo    已删除: IWarehouseAreaService.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\IWarehouseRackService.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\IWarehouseRackService.java"
    echo    已删除: IWarehouseRackService.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\impl\WarehouseAreaServiceImpl.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\impl\WarehouseAreaServiceImpl.java"
    echo    已删除: WarehouseAreaServiceImpl.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\impl\WarehouseRackServiceImpl.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\service\impl\WarehouseRackServiceImpl.java"
    echo    已删除: WarehouseRackServiceImpl.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\mapper\WarehouseAreaMapper.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\mapper\WarehouseAreaMapper.java"
    echo    已删除: WarehouseAreaMapper.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\mapper\WarehouseRackMapper.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\mapper\WarehouseRackMapper.java"
    echo    已删除: WarehouseRackMapper.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\domain\WarehouseArea.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\domain\WarehouseArea.java"
    echo    已删除: WarehouseArea.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\domain\WarehouseRack.java" (
    del "warehouse-system\backend\wanyu-system\src\main\java\com\wanyu\system\domain\WarehouseRack.java"
    echo    已删除: WarehouseRack.java
)

if exist "warehouse-system\backend\wanyu-system\src\main\resources\mapper\system\WarehouseAreaMapper.xml" (
    del "warehouse-system\backend\wanyu-system\src\main\resources\mapper\system\WarehouseAreaMapper.xml"
    echo    已删除: WarehouseAreaMapper.xml
)

if exist "warehouse-system\backend\wanyu-system\src\main\resources\mapper\system\WarehouseRackMapper.xml" (
    del "warehouse-system\backend\wanyu-system\src\main\resources\mapper\system\WarehouseRackMapper.xml"
    echo    已删除: WarehouseRackMapper.xml
)

echo.
echo 3. 执行数据库清理...
mysql -h localhost -P 3306 -u root -p123456 warehouse_system < complete_remove_area_location_components.sql
if %errorlevel% equ 0 (
    echo    数据库清理完成
) else (
    echo    数据库清理失败，请检查连接
)

echo.
echo 4. 更新前端路由配置...
echo 正在更新 warehouse.js 路由文件...

echo.
echo ========================================
echo 库区和货架组件删除完成！
echo ========================================
echo.
echo 接下来需要手动执行以下步骤：
echo 1. 重新编译后端项目
echo 2. 重新启动前端项目  
echo 3. 检查系统功能是否正常
echo.
pause