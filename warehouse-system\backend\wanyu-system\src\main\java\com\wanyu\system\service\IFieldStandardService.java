package com.wanyu.system.service;

import com.wanyu.common.utils.FieldStandardValidator;
import com.wanyu.system.task.FieldStandardMonitor;

import java.util.List;

/**
 * 字段标准服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public interface IFieldStandardService {
    
    /**
     * 检查表字段标准合规性
     * 
     * @param tableName 表名
     * @return 检查结果
     */
    FieldStandardValidator.TableStandardResult checkTableStandard(String tableName);
    
    /**
     * 生成字段标准合规性报告
     * 
     * @param tableNames 表名列表
     * @return 合规性报告
     */
    FieldStandardValidator.ComplianceReport generateComplianceReport(List<String> tableNames);
    
    /**
     * 验证字段值是否符合标准
     * 
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @return 验证结果
     */
    FieldStandardValidator.ValidationResult validateFieldValue(String fieldName, String fieldValue);
    
    /**
     * 获取监控统计信息
     * 
     * @return 监控统计信息
     */
    FieldStandardMonitor.MonitoringStats getMonitoringStats();
    
    /**
     * 手动触发字段标准检查
     * 
     * @return 检查报告
     */
    FieldStandardValidator.ComplianceReport manualCheckStandards();
    
    /**
     * 生成字段标准检查SQL脚本
     * 
     * @param databaseName 数据库名
     * @return SQL脚本
     */
    String generateFieldStandardCheckScript(String databaseName);
    
    /**
     * 检查字段值违规情况
     * 
     * @return 违规记录列表
     */
    List<FieldStandardMonitor.FieldValueViolation> checkFieldValueViolations();
}