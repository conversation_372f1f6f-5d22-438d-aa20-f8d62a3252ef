<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.ProductSpecificationMapper">

    <resultMap type="ProductSpecification" id="ProductSpecificationResult">
        <result property="specId"    column="spec_id"    />
        <result property="specName"    column="spec_name"    />
        <result property="specCode"    column="spec_code"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProductSpecificationVo">
        select spec_id, spec_name, spec_code, status, remark, create_by, create_time, update_by, update_time from wms_specification
    </sql>

    <select id="selectProductSpecificationList" parameterType="ProductSpecification" resultMap="ProductSpecificationResult">
        <include refid="selectProductSpecificationVo"/>
        <where>
            <if test="specName != null  and specName != ''"> and spec_name like concat('%', #{specName}, '%')</if>
            <if test="specCode != null  and specCode != ''"> and spec_code like concat('%', #{specCode}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectProductSpecificationBySpecId" parameterType="Long" resultMap="ProductSpecificationResult">
        <include refid="selectProductSpecificationVo"/>
        where spec_id = #{specId}
    </select>

    <insert id="insertProductSpecification" parameterType="ProductSpecification" useGeneratedKeys="true" keyProperty="specId">
        insert into wms_specification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="specName != null">spec_name,</if>
            <if test="specCode != null">spec_code,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="specName != null">#{specName},</if>
            <if test="specCode != null">#{specCode},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProductSpecification" parameterType="ProductSpecification">
        update wms_specification
        <trim prefix="SET" suffixOverrides=",">
            <if test="specName != null">spec_name = #{specName},</if>
            <if test="specCode != null">spec_code = #{specCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where spec_id = #{specId}
    </update>

    <delete id="deleteProductSpecificationBySpecId" parameterType="Long">
        delete from wms_specification where spec_id = #{specId}
    </delete>

    <delete id="deleteProductSpecificationBySpecIds" parameterType="String">
        delete from wms_specification where spec_id in
        <foreach item="specId" collection="array" open="(" separator="," close=")">
            #{specId}
        </foreach>
    </delete>
</mapper>
