@echo off
chcp 65001 >nul
echo ========================================
echo 清理并重新插入条码字典数据
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo 🔧 清理重复数据并重新插入...
echo.

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < clean_and_fix_barcode_dict.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 字典数据清理并重新插入完成！
    echo.
    echo 📊 最终验证结果:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT dict_sort, dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'wms_barcode_type' ORDER BY dict_sort;"
    echo.
    echo ========================================
    echo ✅ 修复完成！
    echo ========================================
    echo.
) else (
    echo.
    echo ❌ 修复失败！
    echo.
)

echo 按任意键退出...
pause >nul