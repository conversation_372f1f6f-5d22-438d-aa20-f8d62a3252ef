-- 修复菜单重复项并确保路由正确
-- 基于提供的菜单列表进行检查和修复

-- 1. 检查当前菜单状态
SELECT 
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    menu_type,
    status
FROM sys_menu 
WHERE menu_name IN (
    '定时任务', '服务监控', '缓存监控', '物品属性', '权限字符列表',
    '部门仓库权限', '字典数据', '登录方式管理', '系统监控', '在线用户'
)
ORDER BY menu_name, menu_id;

-- 2. 删除重复的系统监控菜单（保留ID为108的，删除4345）
DELETE FROM sys_menu WHERE menu_id = 4345 AND menu_name = '系统监控';
DELETE FROM sys_role_menu WHERE menu_id = 4345;

-- 3. 更新子菜单的parent_id，将4345改为108
UPDATE sys_menu SET parent_id = 108 WHERE parent_id = 4345;

-- 4. 确保系统监控目录存在且正确
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(108, '系统监控', 0, 10, 'monitor', 'Layout', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', NOW(), '系统监控目录');

-- 5. 确保监控子菜单存在且parent_id正确
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(109, '在线用户', 108, 1, 'online', 'monitor/online/index', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', NOW(), '在线用户菜单'),
(110, '定时任务', 108, 2, 'job', 'monitor/job/index', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', NOW(), '定时任务菜单'),
(111, '服务监控', 108, 3, 'server', 'monitor/server/index', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', NOW(), '服务监控菜单'),
(112, '缓存监控', 108, 4, 'cache', 'monitor/cache/index', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', NOW(), '缓存监控菜单');

-- 6. 删除重复的物品属性菜单（保留ID为4150的）
DELETE FROM sys_menu WHERE menu_id = 4350 AND menu_name = '物品属性';
DELETE FROM sys_role_menu WHERE menu_id = 4350;

-- 7. 删除重复的权限字符列表菜单（保留ID为4152的，删除4351）
DELETE FROM sys_menu WHERE menu_id = 4351 AND menu_name = '权限字符列表';
DELETE FROM sys_role_menu WHERE menu_id = 4351;

-- 8. 更新权限字符列表的parent_id为权限管理(9)而不是权限定义(10)
UPDATE sys_menu SET parent_id = 9, order_num = 6 WHERE menu_id = 4152;

-- 9. 确保物品属性菜单正确
UPDATE sys_menu SET 
    parent_id = 17, 
    order_num = 5, 
    path = 'attribute', 
    component = 'product/attribute/index',
    perms = 'product:attribute:list',
    icon = 'list'
WHERE menu_id = 4150;

-- 10. 检查并修复菜单路径和组件
UPDATE sys_menu SET 
    path = 'dept-warehouse',
    component = 'system/dept/warehouse-permission'
WHERE menu_id = 4341;

UPDATE sys_menu SET 
    path = 'dict/data',
    component = 'system/dict/data'
WHERE menu_id = 4342;

UPDATE sys_menu SET 
    path = 'login-style',
    component = 'system/login/style'
WHERE menu_id = 4343;

-- 11. 为超级管理员分配所有新菜单权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu 
WHERE menu_id IN (108, 109, 110, 111, 112, 4150, 4152, 4341, 4342, 4343)
AND NOT EXISTS (
    SELECT 1 FROM sys_role_menu rm 
    WHERE rm.role_id = 1 AND rm.menu_id = sys_menu.menu_id
);

-- 12. 显示最终的菜单结构
SELECT 
    m.menu_id,
    m.menu_name,
    CASE 
        WHEN p.menu_name IS NULL THEN '根目录'
        ELSE p.menu_name
    END AS parent_menu,
    m.order_num,
    m.path,
    m.component,
    CASE 
        WHEN m.menu_type = 'M' THEN '目录'
        WHEN m.menu_type = 'C' THEN '菜单'
        WHEN m.menu_type = 'F' THEN '按钮'
        ELSE m.menu_type
    END AS menu_type_desc,
    m.perms,
    m.status
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_name IN (
    '系统监控', '在线用户', '定时任务', '服务监控', '缓存监控', 
    '物品属性', '权限字符列表', '部门仓库权限', '字典数据', '登录方式管理'
)
ORDER BY m.parent_id, m.order_num;