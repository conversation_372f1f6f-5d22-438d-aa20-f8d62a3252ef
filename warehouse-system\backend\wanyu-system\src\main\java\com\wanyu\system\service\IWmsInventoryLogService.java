package com.wanyu.system.service;

import java.util.List;
import java.util.Map;
import com.wanyu.system.domain.WmsInventoryLog;

/**
 * 出入库日志Service接口
 * 
 * <AUTHOR>
 */
public interface IWmsInventoryLogService 
{
    /**
     * 查询出入库日志
     * 
     * @param logId 出入库日志ID
     * @return 出入库日志
     */
    public WmsInventoryLog selectWmsInventoryLogByLogId(Long logId);

    /**
     * 查询出入库日志列表
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 出入库日志集合
     */
    public List<WmsInventoryLog> selectWmsInventoryLogList(WmsInventoryLog wmsInventoryLog);

    /**
     * 根据用户仓库权限查询出入库日志列表
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 出入库日志集合
     */
    public List<WmsInventoryLog> selectWmsInventoryLogListWithAuth(WmsInventoryLog wmsInventoryLog);

    /**
     * 新增出入库日志
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 结果
     */
    public int insertWmsInventoryLog(WmsInventoryLog wmsInventoryLog);

    /**
     * 修改出入库日志
     * 
     * @param wmsInventoryLog 出入库日志
     * @return 结果
     */
    public int updateWmsInventoryLog(WmsInventoryLog wmsInventoryLog);

    /**
     * 批量删除出入库日志
     * 
     * @param logIds 需要删除的出入库日志ID
     * @return 结果
     */
    public int deleteWmsInventoryLogByLogIds(Long[] logIds);

    /**
     * 删除出入库日志信息
     * 
     * @param logId 出入库日志ID
     * @return 结果
     */
    public int deleteWmsInventoryLogByLogId(Long logId);

    /**
     * 清空出入库日志
     *
     * @return 结果
     */
    public int cleanWmsInventoryLog();

    /**
     * 获取库存统计信息
     * 
     * @param params 查询参数
     * @return 统计信息
     */
    public Map<String, Object> getInventoryStatistics(Map<String, Object> params);

    /**
     * 获取库存操作趋势数据
     * 
     * @param params 查询参数
     * @return 趋势数据
     */
    public List<Map<String, Object>> getInventoryTrend(Map<String, Object> params);
    
    /**
     * 记录库存变动日志
     * 
     * @param warehouseId 仓库ID
     * @param productId 产品ID
     * @param operationType 操作类型（IN-入库，OUT-出库，TRANSFER-调拨，CHECK-盘点）
     * @param quantity 操作数量
     * @param beforeQuantity 操作前数量
     * @param afterQuantity 操作后数量
     * @param relatedOrderId 关联单据号
     * @param relatedOrderType 关联单据类型
     * @param operator 操作人
     * @param remark 备注
     * @return 结果
     */
    public int recordInventoryLog(Long warehouseId, Long productId, String operationType,
            java.math.BigDecimal quantity, java.math.BigDecimal beforeQuantity, java.math.BigDecimal afterQuantity,
            String relatedOrderId, String relatedOrderType, String operator, String remark);

    /**
     * 记录库存操作日志（增强版）
     * 
     * @param operationType 操作类型
     * @param warehouseId 仓库ID
     * @param productId 物品ID
     * @param quantity 操作数量
     * @param beforeQuantity 操作前数量
     * @param afterQuantity 操作后数量
     * @param relatedOrderId 关联单据号
     * @param reason 操作原因
     * @param remark 备注
     */
    public void recordInventoryLogEnhanced(String operationType, Long warehouseId, Long productId, 
                                 java.math.BigDecimal quantity, java.math.BigDecimal beforeQuantity, 
                                 java.math.BigDecimal afterQuantity, String relatedOrderId, 
                                 String reason, String remark);

    /**
     * 根据库存ID查询出入库日志列表
     * @param inventoryId 库存ID
     * @return 日志集合
     */
    public List<WmsInventoryLog> selectWmsInventoryLogListByInventoryId(Long inventoryId);
    
    /**
     * 按物品类别统计库存操作
     * 
     * @param params 查询参数
     * @return 统计数据
     */
    public List<Map<String, Object>> getInventoryStatsByCategory(Map<String, Object> params);


}