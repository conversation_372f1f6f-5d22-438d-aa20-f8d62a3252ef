package com.wanyu.framework.web.service;

import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.wanyu.common.constant.CacheConstants;
import com.wanyu.common.core.domain.entity.SysUser;
import com.wanyu.common.exception.user.UserPasswordNotMatchException;
import com.wanyu.common.exception.user.UserPasswordRetryLimitExceedException;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.framework.security.context.AuthenticationContextHolder;

/**
 * 登录密码方法
 * 
 * <AUTHOR>
 */
@Component
public class SysPasswordService
{
    // 内存缓存用于存储密码错误次数
    private static final Map<String, Integer> PASSWORD_ERROR_COUNT = new HashMap<>();

    @Value(value = "${user.password.maxRetryCount}")
    private int maxRetryCount;

    @Value(value = "${user.password.lockTime}")
    private int lockTime;

    /**
     * 登录账户密码错误次数缓存键名
     * 
     * @param username 用户名
     * @return 缓存键key
     */
    private String getCacheKey(String username)
    {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    public void validate(SysUser user)
    {
        Authentication usernamePasswordAuthenticationToken = AuthenticationContextHolder.getContext();
        String username = usernamePasswordAuthenticationToken.getName();
        String password = usernamePasswordAuthenticationToken.getCredentials().toString();

        Integer retryCount = PASSWORD_ERROR_COUNT.get(getCacheKey(username));

        if (retryCount == null) {
            retryCount = 0;
        }

        if (retryCount >= maxRetryCount) {
            throw new UserPasswordRetryLimitExceedException(maxRetryCount, lockTime);
        }

        if (!SecurityUtils.matchesPassword(password, user.getPassword())) {
            retryCount++;
            PASSWORD_ERROR_COUNT.put(getCacheKey(username), retryCount);
            throw new UserPasswordNotMatchException();
        } else {
            clearLoginRecordCache(username);
        }
    }

    public void clearLoginRecordCache(String loginName) {
        PASSWORD_ERROR_COUNT.remove(getCacheKey(loginName));
    }
}
