<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsInventoryOutDetailMapper">
    
    <resultMap type="WmsInventoryOutDetail" id="WmsInventoryOutDetailResult">
        <result property="detailId"       column="detail_id"       />
        <result property="outId"          column="out_id"          />
        <result property="productId"      column="product_id"      />
        <result property="productName"    column="product_name"    />
        <result property="productCode"    column="product_code"    />
        <result property="quantity"       column="quantity"        />
        <result property="price"          column="price"           />
        <result property="amount"         column="amount"          />
        <result property="remark"         column="remark"          />
    </resultMap>

    <sql id="selectWmsInventoryOutDetailVo">
        select d.detail_id, d.out_id, d.product_id, p.product_name, p.product_code, d.quantity, d.price, d.amount, d.remark
        from wms_inventory_out_detail d
        left join wms_product p on d.product_id = p.product_id
    </sql>

    <select id="selectWmsInventoryOutDetailList" parameterType="WmsInventoryOutDetail" resultMap="WmsInventoryOutDetailResult">
        <include refid="selectWmsInventoryOutDetailVo"/>
        <where>  
            <if test="outId != null "> and d.out_id = #{outId}</if>
            <if test="productId != null "> and d.product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and p.product_code like concat('%', #{productCode}, '%')</if>
        </where>
    </select>
    
    <select id="selectWmsInventoryOutDetailByDetailId" parameterType="Long" resultMap="WmsInventoryOutDetailResult">
        <include refid="selectWmsInventoryOutDetailVo"/>
        where d.detail_id = #{detailId}
    </select>
    
    <select id="selectWmsInventoryOutDetailByOutId" parameterType="Long" resultMap="WmsInventoryOutDetailResult">
        <include refid="selectWmsInventoryOutDetailVo"/>
        where d.out_id = #{outId}
    </select>
        
    <insert id="insertWmsInventoryOutDetail" parameterType="WmsInventoryOutDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into wms_inventory_out_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="outId != null">out_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="price != null">price,</if>
            <if test="amount != null">amount,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="outId != null">#{outId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="price != null">#{price},</if>
            <if test="amount != null">#{amount},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertWmsInventoryOutDetail">
        insert into wms_inventory_out_detail(out_id, product_id, quantity, price, amount, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.outId}, #{item.productId}, #{item.quantity}, #{item.price}, #{item.amount}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateWmsInventoryOutDetail" parameterType="WmsInventoryOutDetail">
        update wms_inventory_out_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="outId != null">out_id = #{outId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="price != null">price = #{price},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteWmsInventoryOutDetailByDetailId" parameterType="Long">
        delete from wms_inventory_out_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteWmsInventoryOutDetailByDetailIds" parameterType="String">
        delete from wms_inventory_out_detail where detail_id in 
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
    
    <delete id="deleteWmsInventoryOutDetailByOutId" parameterType="Long">
        delete from wms_inventory_out_detail where out_id = #{outId}
    </delete>
</mapper>
