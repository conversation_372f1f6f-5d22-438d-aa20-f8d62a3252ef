package com.wanyu.system.mapper;

import java.util.List;
import com.wanyu.system.domain.SysLicenseFeature;
import org.apache.ibatis.annotations.Param;

/**
 * 功能权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
public interface SysLicenseFeatureMapper 
{
    /**
     * 查询功能权限
     * 
     * @param featureId 功能权限主键
     * @return 功能权限
     */
    public SysLicenseFeature selectSysLicenseFeatureByFeatureId(Long featureId);

    /**
     * 查询功能权限列表
     * 
     * @param sysLicenseFeature 功能权限
     * @return 功能权限集合
     */
    public List<SysLicenseFeature> selectSysLicenseFeatureList(SysLicenseFeature sysLicenseFeature);

    /**
     * 根据状态查询功能权限列表
     * 
     * @param status 状态（0正常 1停用）
     * @return 功能权限集合
     */
    public List<SysLicenseFeature> selectSysLicenseFeatureByStatus(@Param("status") String status);

    /**
     * 根据功能代码查询功能权限
     * 
     * @param featureCode 功能代码
     * @return 功能权限
     */
    public SysLicenseFeature selectSysLicenseFeatureByCode(@Param("featureCode") String featureCode);

    /**
     * 根据是否核心功能查询列表
     * 
     * @param isCore 是否核心功能（0否 1是）
     * @return 功能权限集合
     */
    public List<SysLicenseFeature> selectSysLicenseFeatureByCore(@Param("isCore") String isCore);

    /**
     * 根据授权类型查询可用功能
     * 
     * @param licenseType 授权类型
     * @return 功能权限集合
     */
    public List<SysLicenseFeature> selectFeaturesByLicenseType(@Param("licenseType") String licenseType);

    /**
     * 新增功能权限
     * 
     * @param sysLicenseFeature 功能权限
     * @return 结果
     */
    public int insertSysLicenseFeature(SysLicenseFeature sysLicenseFeature);

    /**
     * 修改功能权限
     * 
     * @param sysLicenseFeature 功能权限
     * @return 结果
     */
    public int updateSysLicenseFeature(SysLicenseFeature sysLicenseFeature);

    /**
     * 删除功能权限
     * 
     * @param featureId 功能权限主键
     * @return 结果
     */
    public int deleteSysLicenseFeatureByFeatureId(Long featureId);

    /**
     * 批量删除功能权限
     * 
     * @param featureIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysLicenseFeatureByFeatureIds(Long[] featureIds);

    /**
     * 更新功能状态
     * 
     * @param featureId 功能权限主键
     * @param status 状态（0正常 1停用）
     * @return 结果
     */
    public int updateFeatureStatus(@Param("featureId") Long featureId, @Param("status") String status);

    /**
     * 批量更新功能状态
     * 
     * @param featureIds 功能权限主键集合
     * @param status 状态（0正常 1停用）
     * @return 结果
     */
    public int updateFeaturesStatus(@Param("featureIds") Long[] featureIds, @Param("status") String status);
}