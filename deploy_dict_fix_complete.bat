@echo off
echo 部署字典功能完整修复...
echo.

echo 1. 执行数据库修复...
mysql -h localhost -u root -p123456 warehouse_system < fix_missing_dict_data.sql
if %errorlevel% neq 0 (
    echo 数据库修复失败！
    pause
    exit /b 1
)

echo 2. 添加入库出库字典类型...
mysql -h localhost -u root -p123456 warehouse_system < add_inventory_dict_types.sql
if %errorlevel% neq 0 (
    echo 字典类型添加失败！
    pause
    exit /b 1
)

echo 3. 停止后端服务...
cd /d "C:\CKGLXT\warehouse-system\backend"
taskkill /f /im java.exe 2>nul
timeout /t 3 >nul

echo 4. 启动后端服务...
start "后端服务" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=dev"

echo 5. 等待服务启动...
timeout /t 15 >nul

echo 6. 测试字典API...
cd /d "C:\CKGLXT"
call test_dict_api.bat

echo.
echo ========================================
echo 字典功能修复部署完成！
echo.
echo 修复内容：
echo - 启用了字典数据Controller
echo - 添加了入库类型字典数据
echo - 添加了入库状态字典数据  
echo - 添加了出库类型字典数据
echo - 添加了出库状态字典数据
echo - 添加了调拨状态字典数据
echo.
echo 现在前端应该可以正常获取字典数据了！
echo ========================================
pause