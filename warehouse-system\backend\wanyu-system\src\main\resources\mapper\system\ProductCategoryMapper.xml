<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.ProductCategoryMapper">
    
    <resultMap type="ProductCategory" id="ProductCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProductCategoryVo">
        select category_id, parent_id, ancestors, category_name, category_code, order_num, status, del_flag, create_by, create_time, update_by, update_time, remark from wms_category
    </sql>

    <select id="selectProductCategoryList" parameterType="ProductCategory" resultMap="ProductCategoryResult">
        <include refid="selectProductCategoryVo"/>
        <where>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="categoryCode != null  and categoryCode != ''"> and category_code = #{categoryCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by parent_id, order_num
    </select>
    
    <select id="selectProductCategoryByCategoryId" parameterType="Long" resultMap="ProductCategoryResult">
        <include refid="selectProductCategoryVo"/>
        where category_id = #{categoryId} and del_flag = '0'
    </select>
    
    <select id="selectProductCategoryTreeList" resultMap="ProductCategoryResult">
        select category_id, parent_id, ancestors, category_name, category_code, order_num, status from wms_category where del_flag = '0' order by parent_id, order_num
    </select>
    
    <select id="hasChildByCategoryId" parameterType="Long" resultType="Integer">
        select count(1) from wms_category where parent_id = #{categoryId} and del_flag = '0'
    </select>
        
    <insert id="insertProductCategory" parameterType="ProductCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into wms_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="categoryName != null">category_name,</if>
            <if test="categoryCode != null">category_code,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="categoryName != null">#{categoryName},</if>
            <if test="categoryCode != null">#{categoryCode},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProductCategory" parameterType="ProductCategory">
        update wms_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="categoryName != null">category_name = #{categoryName},</if>
            <if test="categoryCode != null">category_code = #{categoryCode},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <update id="deleteProductCategoryByCategoryId" parameterType="Long">
        update wms_category set del_flag = '2' where category_id = #{categoryId}
    </update>

    <update id="deleteProductCategoryByCategoryIds" parameterType="String">
        update wms_category set del_flag = '2' where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </update>

    <delete id="doPhysicalDeleteProductCategoryByCategoryId" parameterType="Long">
        delete from wms_category where category_id = #{categoryId}
    </delete>

    <delete id="doPhysicalDeleteProductCategoryByCategoryIds" parameterType="String">
        delete from wms_category where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="checkCategoryCodeUnique" parameterType="String" resultType="int">
        select count(1) from wms_category where category_code = #{categoryCode} and del_flag = '0'
    </select>
</mapper>