package com.wanyu.web.controller.product;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.system.domain.ProductUnit;
import com.wanyu.system.service.IProductUnitService;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.common.core.page.TableDataInfo;

/**
 * 物品单位Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@RestController
@RequestMapping("/product/unit")
public class ProductUnitController extends BaseController
{
    @Autowired
    private IProductUnitService productUnitService;

    /**
     * 查询物品单位列表
     */
    @PreAuthorize("@ss.hasPermi('product:unit:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductUnit productUnit)
    {
        startPage();
        List<ProductUnit> list = productUnitService.selectProductUnitList(productUnit);
        return getDataTable(list);
    }

    /**
     * 导出物品单位列表
     */
    @PreAuthorize("@ss.hasPermi('product:unit:export')")
    @Log(title = "物品单位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductUnit productUnit)
    {
        List<ProductUnit> list = productUnitService.selectProductUnitList(productUnit);
        ExcelUtil<ProductUnit> util = new ExcelUtil<ProductUnit>(ProductUnit.class);
        util.exportExcel(response, list, "物品单位数据");
    }

    /**
     * 获取物品单位详细信息
     */
    @PreAuthorize("@ss.hasPermi('product:unit:query')")
    @GetMapping(value = "/{unitId}")
    public AjaxResult getInfo(@PathVariable("unitId") Long unitId)
    {
        return success(productUnitService.selectProductUnitByUnitId(unitId));
    }

    /**
     * 新增物品单位
     */
    @PreAuthorize("@ss.hasPermi('product:unit:add')")
    @Log(title = "物品单位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProductUnit productUnit)
    {
        return toAjax(productUnitService.insertProductUnit(productUnit));
    }

    /**
     * 修改物品单位
     */
    @PreAuthorize("@ss.hasPermi('product:unit:edit')")
    @Log(title = "物品单位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductUnit productUnit)
    {
        return toAjax(productUnitService.updateProductUnit(productUnit));
    }

    /**
     * 删除物品单位
     */
    @PreAuthorize("@ss.hasPermi('product:unit:remove')")
    @Log(title = "物品单位", businessType = BusinessType.DELETE)
	@DeleteMapping("/{unitIds}")
    public AjaxResult remove(@PathVariable Long[] unitIds)
    {
        return toAjax(productUnitService.deleteProductUnitByUnitIds(unitIds));
    }
}