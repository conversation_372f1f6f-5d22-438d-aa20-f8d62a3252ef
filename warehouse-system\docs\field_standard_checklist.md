# 字段标准检查清单

## 快速检查清单

### 数据库设计阶段

#### 字段定义检查
- [ ] 状态字段使用 `CHAR(1)` 类型
- [ ] 状态字段默认值为 `'0'`（正常/启用状态）
- [ ] 字段注释格式: `状态（0正常 1停用）`
- [ ] 布尔字段使用 `is_xxx` 或 `enable_xxx` 命名
- [ ] 删除标记使用 `del_flag CHAR(1) DEFAULT '0'`

#### 命名规范检查
- [ ] 通用状态字段命名为 `status`
- [ ] 特定状态字段命名为 `xxx_status`
- [ ] 布尔字段使用 `is_`、`enable_`、`has_` 前缀
- [ ] 标记字段使用 `_flag`、`_mark` 后缀

#### 索引和约束检查
- [ ] 为状态字段创建索引
- [ ] 为删除标记字段创建索引
- [ ] 主键字段使用 `BIGINT(20) AUTO_INCREMENT`

### 代码开发阶段

#### Java代码检查
- [ ] 查询启用状态使用 `WHERE status = '0'`
- [ ] 设置启用状态使用 `setStatus("0")`
- [ ] 设置禁用状态使用 `setStatus("1")`
- [ ] 记录成功操作使用 `setOperationStatus("0")`
- [ ] 记录失败操作使用 `setOperationStatus("1")`
- [ ] 使用常量定义而非硬编码状态值

#### 实体类检查
- [ ] 状态字段默认值为 `"0"`
- [ ] 添加了适当的验证注解
- [ ] 字段注释清晰准确
- [ ] 继承了BaseEntity（如适用）

#### Service层检查
- [ ] 启用/禁用方法逻辑正确
- [ ] 查询方法使用正确的状态值
- [ ] 操作日志记录状态正确
- [ ] 异常处理适当

### 前端开发阶段

#### Vue组件检查
- [ ] 状态显示使用正确的字典组件
- [ ] 操作按钮状态判断逻辑正确
- [ ] 启用操作设置 `status: "0"`
- [ ] 禁用操作设置 `status: "1"`
- [ ] 表单验证规则正确

#### 字典配置检查
- [ ] 字典类型命名规范
- [ ] 字典值符合标准（0=正常，1=停用）
- [ ] 样式配置合理（success/danger）
- [ ] 默认值设置正确

### 测试验证阶段

#### 功能测试检查
- [ ] 启用/禁用功能正常工作
- [ ] 状态查询过滤正确
- [ ] 前端状态显示符合预期
- [ ] 操作日志记录正确

#### 数据一致性检查
- [ ] 数据库中状态值符合标准
- [ ] 前后端状态值一致
- [ ] 字典配置与代码逻辑一致
- [ ] 历史数据迁移正确

## 详细检查项目

### 1. 数据库表结构检查

```sql
-- 检查状态字段定义
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND COLUMN_NAME LIKE '%status%'
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 检查字段注释格式
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_COMMENT,
    CASE 
        WHEN COLUMN_COMMENT LIKE '%（0%1%）%' THEN '✓ 格式正确'
        WHEN COLUMN_COMMENT LIKE '%(0%1%)%' THEN '✓ 格式正确'
        ELSE '⚠️ 需要检查注释格式'
    END as comment_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND COLUMN_NAME LIKE '%status%';
```

### 2. 代码规范检查

#### Java代码检查脚本
```bash
# 检查硬编码状态值
grep -r "status.*=.*[\"']1[\"']" src/main/java/
grep -r "setStatus.*[\"']1[\"']" src/main/java/

# 检查操作状态使用
grep -r "operationStatus.*=.*[\"']0[\"']" src/main/java/
grep -r "setOperationStatus" src/main/java/

# 检查常量使用
grep -r "StatusConstants" src/main/java/
```

#### 前端代码检查
```bash
# 检查Vue组件中的状态判断
grep -r "status.*===.*[\"']0[\"']" src/
grep -r "status.*===.*[\"']1[\"']" src/

# 检查字典组件使用
grep -r "dict-tag" src/
grep -r "sys_normal_disable" src/
```

### 3. 数据字典检查

```sql
-- 检查字典配置
SELECT 
    dict_type,
    dict_label,
    dict_value,
    list_class,
    is_default,
    CASE 
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '0' AND dict_label LIKE '%正常%' THEN '✓'
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '1' AND dict_label LIKE '%停用%' THEN '✓'
        WHEN dict_type = 'operation_status' AND dict_value = '0' AND dict_label LIKE '%成功%' THEN '✓'
        WHEN dict_type = 'operation_status' AND dict_value = '1' AND dict_label LIKE '%失败%' THEN '✓'
        ELSE '⚠️'
    END as standard_check
FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status')
ORDER BY dict_type, dict_value;
```

### 4. 业务逻辑检查

#### 状态切换逻辑检查
- [ ] 启用操作: `status '1' -> '0'`
- [ ] 禁用操作: `status '0' -> '1'`
- [ ] 状态查询: 启用状态查询 `status = '0'`
- [ ] 默认状态: 新记录默认 `status = '0'`

#### 操作日志检查
- [ ] 成功操作: `operation_status = '0'`
- [ ] 失败操作: `operation_status = '1'`
- [ ] 日志查询: 成功日志查询 `operation_status = '0'`
- [ ] 统计分析: 基于正确的状态值进行统计

### 5. 前端显示检查

#### 状态显示检查
- [ ] 启用状态显示为"正常"或"启用"
- [ ] 禁用状态显示为"停用"或"禁用"
- [ ] 成功状态显示为绿色
- [ ] 失败状态显示为红色

#### 操作按钮检查
- [ ] 启用状态记录显示"禁用"按钮
- [ ] 禁用状态记录显示"启用"按钮
- [ ] 按钮点击后状态正确切换
- [ ] 页面刷新后状态显示正确

## 常见错误和修复

### 错误1: 状态值颠倒
```java
// 错误写法
if (entity.getStatus().equals("1")) {
    // 处理启用状态 - 错误！
}

// 正确写法
if (entity.getStatus().equals("0")) {
    // 处理启用状态 - 正确！
}
```

### 错误2: 硬编码状态值
```java
// 错误写法
entity.setStatus("1"); // 硬编码

// 正确写法
entity.setStatus(StatusConstants.STATUS_DISABLE);
```

### 错误3: 前端状态判断错误
```vue
<!-- 错误写法 -->
<el-button v-if="scope.row.status === '0'" @click="handleEnable">启用</el-button>

<!-- 正确写法 -->
<el-button v-if="scope.row.status === '1'" @click="handleEnable">启用</el-button>
```

### 错误4: 字典配置错误
```sql
-- 错误配置
INSERT INTO sys_dict_data VALUES (..., '启用', '1', 'sys_normal_disable', ...);

-- 正确配置
INSERT INTO sys_dict_data VALUES (..., '正常', '0', 'sys_normal_disable', ...);
```

## 验证工具

### 自动检查脚本
```bash
#!/bin/bash
# field_standard_check.sh

echo "=== 字段标准检查 ==="

echo "1. 检查数据库字段定义..."
mysql -u root -p123456 warehouse_system -e "
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_DEFAULT,
    CASE 
        WHEN DATA_TYPE = 'char' AND CHARACTER_MAXIMUM_LENGTH = 1 AND COLUMN_DEFAULT = '0' THEN '✓'
        ELSE '⚠️'
    END as standard_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND COLUMN_NAME LIKE '%status%';"

echo "2. 检查字典配置..."
mysql -u root -p123456 warehouse_system -e "
SELECT 
    dict_type,
    dict_value,
    dict_label,
    CASE 
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '0' THEN '✓'
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '1' THEN '✓'
        ELSE '⚠️'
    END as check_result
FROM sys_dict_data 
WHERE dict_type = 'sys_normal_disable';"

echo "3. 检查Java代码..."
find src/main/java -name "*.java" -exec grep -l "setStatus.*\"1\"" {} \; | head -5

echo "检查完成！"
```

### 手动验证步骤
1. 运行数据库检查SQL
2. 执行代码检查脚本
3. 测试前端状态显示
4. 验证业务功能正常
5. 检查操作日志记录

## 修复指南

### 数据库修复
1. 备份相关表数据
2. 执行字段定义修复脚本
3. 更新字段注释
4. 验证数据完整性

### 代码修复
1. 更新硬编码状态值
2. 添加常量定义
3. 修复业务逻辑
4. 更新单元测试

### 前端修复
1. 更新状态判断逻辑
2. 修复字典组件配置
3. 调整样式显示
4. 测试用户交互

### 数据字典修复
1. 更新字典类型定义
2. 修复字典数据值
3. 调整样式配置
4. 验证前端显示

---

**使用说明**: 
1. 在开发新功能前，参考此清单确保符合标准
2. 在代码审查时，使用此清单进行检查
3. 在测试阶段，验证所有检查项目
4. 定期运行自动检查脚本，确保持续合规