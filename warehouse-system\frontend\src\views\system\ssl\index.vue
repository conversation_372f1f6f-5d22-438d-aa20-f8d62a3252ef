<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold;">
          <i class="el-icon-lock"></i> SSL证书管理
        </span>
      </div>

      <!-- HTTPS状态信息 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="24">
          <el-alert
            :title="httpsStatus.title"
            :type="httpsStatus.type"
            :description="httpsStatus.description"
            show-icon
            :closable="false">
          </el-alert>
        </el-col>
      </el-row>

      <!-- 访问地址信息 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="12">
          <el-card shadow="hover">
            <div slot="header">
              <i class="el-icon-link"></i> 访问地址
            </div>
            <div v-if="httpsInfo.isHttps">
              <p><strong>当前HTTPS地址:</strong></p>
              <el-input
                :value="httpsInfo.httpsUrl"
                readonly
                size="small"
                style="margin-bottom: 10px;">
                <el-button
                  slot="append"
                  icon="el-icon-copy-document"
                  @click="copyToClipboard(httpsInfo.httpsUrl)">
                  复制
                </el-button>
              </el-input>
              
              <p><strong>局域网访问地址:</strong></p>
              <el-input
                :value="httpsInfo.localHttpsUrl"
                readonly
                size="small">
                <el-button
                  slot="append"
                  icon="el-icon-copy-document"
                  @click="copyToClipboard(httpsInfo.localHttpsUrl)">
                  复制
                </el-button>
              </el-input>
            </div>
            <div v-else>
              <p>当前正在使用HTTP连接</p>
              <p><strong>HTTPS地址:</strong> {{ httpsInfo.httpsUrl }}</p>
              <p><strong>局域网HTTPS地址:</strong> {{ httpsInfo.localHttpsUrl }}</p>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card shadow="hover">
            <div slot="header">
              <i class="el-icon-download"></i> 证书下载
            </div>
            <div style="text-align: center;">
              <el-button
                type="primary"
                icon="el-icon-download"
                @click="downloadCert('windows')"
                :loading="downloading.windows"
                style="margin-bottom: 10px; width: 100%;">
                下载Windows证书 (.cer)
              </el-button>
              
              <el-button
                type="success"
                icon="el-icon-download"
                @click="downloadCert('linux')"
                :loading="downloading.linux"
                style="margin-bottom: 10px; width: 100%;">
                下载Linux/Mac证书 (.pem)
              </el-button>
              
              <el-button
                type="info"
                icon="el-icon-question"
                @click="showInstallGuide"
                style="width: 100%;">
                查看安装指南
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 证书状态信息 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover">
            <div slot="header">
              <i class="el-icon-info"></i> 证书信息
            </div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="HTTPS状态">
                <el-tag :type="httpsInfo.isHttps ? 'success' : 'warning'">
                  {{ httpsInfo.isHttps ? '已启用' : '未启用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="服务器地址">
                {{ httpsInfo.serverName }}:{{ httpsInfo.serverPort }}
              </el-descriptions-item>
              <el-descriptions-item label="本机IP地址">
                {{ httpsInfo.localIp }}
              </el-descriptions-item>
              <el-descriptions-item label="协议类型">
                {{ httpsInfo.scheme.toUpperCase() }}
              </el-descriptions-item>
              <el-descriptions-item label="证书文件状态">
                <el-tag :type="httpsInfo.certificateExists ? 'success' : 'danger'">
                  {{ httpsInfo.certificateExists ? '存在' : '不存在' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="PEM证书状态">
                <el-tag :type="httpsInfo.pemCertificateExists ? 'success' : 'danger'">
                  {{ httpsInfo.pemCertificateExists ? '存在' : '不存在' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 安装指南对话框 -->
    <el-dialog
      title="SSL证书安装指南"
      :visible.sync="guideDialog"
      width="70%"
      center>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="Windows" name="windows">
          <el-steps :active="8" direction="vertical" size="small">
            <el-step
              v-for="(step, index) in installGuide.windows"
              :key="index"
              :title="`步骤 ${index + 1}`"
              :description="step">
            </el-step>
          </el-steps>
        </el-tab-pane>
        
        <el-tab-pane label="Linux" name="linux">
          <el-steps :active="4" direction="vertical" size="small">
            <el-step
              v-for="(step, index) in installGuide.linux"
              :key="index"
              :title="`步骤 ${index + 1}`"
              :description="step">
            </el-step>
          </el-steps>
        </el-tab-pane>
        
        <el-tab-pane label="Mac" name="mac">
          <el-steps :active="5" direction="vertical" size="small">
            <el-step
              v-for="(step, index) in installGuide.mac"
              :key="index"
              :title="`步骤 ${index + 1}`"
              :description="step">
            </el-step>
          </el-steps>
        </el-tab-pane>
        
        <el-tab-pane label="注意事项" name="notes">
          <el-alert
            v-for="(note, index) in installGuide.notes"
            :key="index"
            :title="note"
            type="info"
            :closable="false"
            style="margin-bottom: 10px;">
          </el-alert>
        </el-tab-pane>
      </el-tabs>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="guideDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getHttpsInfo, downloadCertificate, downloadCertificatePem, getCertificateInstallGuide, downloadFile } from '@/api/system/ssl'

export default {
  name: 'SslCertificate',
  data() {
    return {
      httpsInfo: {
        isHttps: false,
        serverName: '',
        serverPort: 80,
        scheme: 'http',
        localIp: '',
        httpsUrl: '',
        localHttpsUrl: '',
        certificateExists: false,
        pemCertificateExists: false
      },
      downloading: {
        windows: false,
        linux: false
      },
      guideDialog: false,
      activeTab: 'windows',
      installGuide: {
        windows: [],
        linux: [],
        mac: [],
        notes: []
      }
    }
  },
  computed: {
    httpsStatus() {
      if (this.httpsInfo.isHttps) {
        return {
          title: 'HTTPS已启用',
          type: 'success',
          description: '当前正在使用安全的HTTPS连接访问系统'
        }
      } else {
        return {
          title: 'HTTPS未启用',
          type: 'warning',
          description: '当前正在使用HTTP连接，建议切换到HTTPS以确保数据传输安全'
        }
      }
    }
  },
  created() {
    this.fetchHttpsInfo()
    this.fetchInstallGuide()
  },
  methods: {
    // 获取HTTPS信息
    async fetchHttpsInfo() {
      try {
        const response = await getHttpsInfo()
        if (response.code === 200) {
          this.httpsInfo = response.data
        }
      } catch (error) {
        console.error('获取HTTPS信息失败:', error)
        this.$message.error('获取HTTPS信息失败')
      }
    },
    
    // 获取安装指南
    async fetchInstallGuide() {
      try {
        const response = await getCertificateInstallGuide()
        if (response.code === 200) {
          this.installGuide = response.data
        }
      } catch (error) {
        console.error('获取安装指南失败:', error)
      }
    },
    
    // 下载证书
    async downloadCert(type) {
      try {
        this.downloading[type] = true
        
        let response, filename
        if (type === 'windows') {
          response = await downloadCertificate()
          filename = 'warehouse-system.cer'
        } else {
          response = await downloadCertificatePem()
          filename = 'warehouse-system.pem'
        }
        
        downloadFile(response, filename)
        this.$message.success(`${type === 'windows' ? 'Windows' : 'Linux/Mac'}证书下载成功`)
      } catch (error) {
        console.error('证书下载失败:', error)
        this.$message.error('证书下载失败，请检查网络连接')
      } finally {
        this.downloading[type] = false
      }
    },
    
    // 显示安装指南
    showInstallGuide() {
      this.guideDialog = true
    },
    
    // 复制到剪贴板
    async copyToClipboard(text) {
      try {
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(text)
        } else {
          // 兼容老版本浏览器
          const textArea = document.createElement('textarea')
          textArea.value = text
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }
        this.$message.success('地址已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败，请手动复制')
      }
    }
  }
}
</script>

<style scoped>
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}

.box-card {
  margin: 20px;
}

.el-card {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: center;
}
</style>