-- 菜单结构检查脚本
-- 设置连接字符集
SET NAMES utf8mb4;

-- 选择数据库
USE warehouse_system;

-- 创建临时表来存储异常菜单
CREATE TEMPORARY TABLE IF NOT EXISTS temp_abnormal_menus (
    menu_id BIGINT,
    menu_name VARCHAR(50),
    parent_id BIGINT,
    menu_type CHAR(1),
    issue_description VARCHAR(200)
);

-- 插入按钮类型(F)的菜单父级不是菜单类型(C)的异常记录
INSERT INTO temp_abnormal_menus (menu_id, menu_name, parent_id, menu_type, issue_description)
SELECT m.menu_id, m.menu_name, m.parent_id, m.menu_type, '按钮类型(F)的菜单父级不是菜单类型(C)'
FROM sys_menu m
WHERE m.menu_type = 'F' 
  AND m.parent_id != 0 
  AND m.parent_id NOT IN (
    SELECT menu_id FROM sys_menu WHERE menu_type = 'C'
  );

-- 插入菜单类型(C)的菜单父级不是目录类型(M)且不是顶级菜单的异常记录
INSERT INTO temp_abnormal_menus (menu_id, menu_name, parent_id, menu_type, issue_description)
SELECT m.menu_id, m.menu_name, m.parent_id, m.menu_type, '菜单类型(C)的菜单父级不是目录类型(M)且不是顶级菜单'
FROM sys_menu m
WHERE m.menu_type = 'C' 
  AND m.parent_id != 0 
  AND m.parent_id NOT IN (
    SELECT menu_id FROM sys_menu WHERE menu_type = 'M'
  );

-- 查询异常菜单
SELECT 
    m.menu_id AS '菜单ID',
    m.menu_name AS '菜单名称',
    m.parent_id AS '父级ID',
    CASE m.menu_type
        WHEN 'M' THEN '目录'
        WHEN 'C' THEN '菜单'
        WHEN 'F' THEN '按钮'
        ELSE '未知'
    END AS '菜单类型',
    p.menu_name AS '父级菜单名称',
    CASE p.menu_type
        WHEN 'M' THEN '目录'
        WHEN 'C' THEN '菜单'
        WHEN 'F' THEN '按钮'
        ELSE '未知'
    END AS '父级菜单类型',
    am.issue_description AS '问题描述',
    CASE 
        WHEN m.menu_type = 'F' THEN 
            CONCAT('UPDATE sys_menu SET parent_id = (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_type = ''C'' LIMIT 1) AS temp) WHERE menu_id = ', m.menu_id, ';')
        WHEN m.menu_type = 'C' THEN 
            CONCAT('UPDATE sys_menu SET parent_id = (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_type = ''M'' LIMIT 1) AS temp) WHERE menu_id = ', m.menu_id, ';')
        ELSE ''
    END AS '修复SQL'
FROM temp_abnormal_menus am
JOIN sys_menu m ON am.menu_id = m.menu_id
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
ORDER BY m.menu_type, m.menu_id;

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS temp_abnormal_menus;

-- 提交事务
COMMIT;
