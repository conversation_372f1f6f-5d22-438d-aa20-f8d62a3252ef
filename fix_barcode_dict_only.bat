@echo off
chcp 65001 >nul
echo ========================================
echo 修复条码字典数据 - 解决前端404错误
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456

echo 🔧 修复条码类型字典数据...
echo.

mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < fix_barcode_dict_only.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 字典数据修复完成！
    echo.
    echo 📊 验证结果:
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'wms_barcode_type' ORDER BY dict_sort;"
    echo.
    echo ========================================
    echo ✅ 修复完成！现在可以重启后端服务
    echo ========================================
    echo.
    echo 🔄 下一步:
    echo   1. 重启后端服务
    echo   2. 刷新前端页面
    echo   3. 测试条码功能
    echo.
) else (
    echo.
    echo ❌ 字典数据修复失败！
    echo.
)

echo 按任意键退出...
pause >nul