-- Field Definition Standardization - Dictionary Data Update
-- Standard: 0=normal/enabled/success, 1=abnormal/disabled/failed

START TRANSACTION;

-- Backup current dictionary data
CREATE TABLE IF NOT EXISTS sys_dict_data_backup_standardization AS 
SELECT * FROM sys_dict_data WHERE dict_type IN (
    'sys_normal_disable', 'operation_status'
);

-- Update sys_normal_disable dictionary
UPDATE sys_dict_data SET 
    dict_label = 'Normal',
    dict_value = '0',
    css_class = '',
    list_class = 'primary',
    is_default = 'Y',
    remark = 'Normal Status'
WHERE dict_type = 'sys_normal_disable' AND dict_value = '0';

UPDATE sys_dict_data SET 
    dict_label = 'Disabled',
    dict_value = '1',
    css_class = '',
    list_class = 'danger',
    is_default = 'N',
    remark = 'Disabled Status'
WHERE dict_type = 'sys_normal_disable' AND dict_value = '1';

-- Create operation_status dictionary type if not exists
INSERT IGNORE INTO sys_dict_type (
    dict_name, dict_type, status, create_by, create_time, remark
) VALUES (
    'Operation Status', 'operation_status', '0', 'admin', NOW(), 'Operation execution status dictionary'
);

-- Delete existing operation_status dictionary data
DELETE FROM sys_dict_data WHERE dict_type = 'operation_status';

-- Insert standard operation_status dictionary data
INSERT INTO sys_dict_data (
    dict_sort, dict_label, dict_value, dict_type, 
    css_class, list_class, is_default, status, 
    create_by, create_time, remark
) VALUES 
(1, 'Success', '0', 'operation_status', '', 'success', 'Y', '0', 'admin', NOW(), 'Operation executed successfully'),
(2, 'Failed', '1', 'operation_status', '', 'danger', 'N', '0', 'admin', NOW(), 'Operation execution failed');

-- Verify the updates
SELECT 
    dict_type,
    dict_label,
    dict_value,
    CASE 
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '0' AND dict_label = 'Normal' THEN 'OK'
        WHEN dict_type = 'sys_normal_disable' AND dict_value = '1' AND dict_label = 'Disabled' THEN 'OK'
        WHEN dict_type = 'operation_status' AND dict_value = '0' AND dict_label = 'Success' THEN 'OK'
        WHEN dict_type = 'operation_status' AND dict_value = '1' AND dict_label = 'Failed' THEN 'OK'
        ELSE 'CHECK'
    END as validation_result
FROM sys_dict_data 
WHERE dict_type IN ('sys_normal_disable', 'operation_status')
ORDER BY dict_type, dict_sort;

COMMIT;

SELECT 'Dictionary data standardization completed' as status;