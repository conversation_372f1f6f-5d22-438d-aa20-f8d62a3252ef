-- 备份验证脚本
-- 用于验证数据备份的完整性和正确性

USE warehouse_system_backup;

-- 1. 验证备份表是否存在
SELECT 
    'backup_tables_check' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system_backup' AND table_name = 'sys_license_backup')
        AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system_backup' AND table_name = 'sys_license_feature_backup')
        AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system_backup' AND table_name = 'sys_dict_data_backup')
        AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'warehouse_system_backup' AND table_name = 'backup_info')
        THEN '所有备份表存在'
        ELSE '备份表缺失'
    END as result;

-- 2. 验证备份数据完整性
SELECT 
    'data_integrity_check' as check_type,
    table_name,
    original_count,
    backup_count,
    CASE 
        WHEN original_count = backup_count THEN '数据完整'
        ELSE CONCAT('数据不完整，差异：', ABS(original_count - backup_count), '条记录')
    END as result
FROM (
    SELECT 
        'sys_license' as table_name,
        (SELECT COUNT(*) FROM warehouse_system.sys_license) as original_count,
        (SELECT COUNT(*) FROM sys_license_backup) as backup_count
    UNION ALL
    SELECT 
        'sys_license_feature' as table_name,
        (SELECT COUNT(*) FROM warehouse_system.sys_license_feature) as original_count,
        (SELECT COUNT(*) FROM sys_license_feature_backup) as backup_count
    UNION ALL
    SELECT 
        'sys_dict_data' as table_name,
        (SELECT COUNT(*) FROM warehouse_system.sys_dict_data WHERE dict_type IN ('sys_normal_disable', 'operation_status', 'audit_status')) as original_count,
        (SELECT COUNT(*) FROM sys_dict_data_backup) as backup_count
) as backup_comparison;

-- 3. 验证关键字段数据分布
SELECT 
    'sys_license_status_distribution' as check_type,
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sys_license_backup), 2) as percentage
FROM sys_license_backup 
GROUP BY status
ORDER BY status;

SELECT 
    'sys_license_feature_status_distribution' as check_type,
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sys_license_feature_backup), 2) as percentage
FROM sys_license_feature_backup 
GROUP BY status
ORDER BY status;

-- 4. 验证数据字典备份
SELECT 
    'dict_data_backup_check' as check_type,
    dict_type,
    COUNT(*) as count
FROM sys_dict_data_backup 
GROUP BY dict_type
ORDER BY dict_type;

-- 5. 检查备份时间和状态
SELECT 
    'backup_info_check' as check_type,
    backup_time,
    table_name,
    record_count,
    backup_status,
    remark
FROM backup_info 
ORDER BY backup_id;

-- 6. 验证数据一致性（抽样检查）
-- 检查sys_license表前10条记录的一致性
SELECT 
    'sys_license_sample_check' as check_type,
    'sample_records' as detail,
    COUNT(*) as matching_records
FROM (
    SELECT o.license_id
    FROM warehouse_system.sys_license o
    INNER JOIN sys_license_backup b ON o.license_id = b.license_id 
        AND o.license_name = b.license_name 
        AND o.status = b.status
        AND o.create_time = b.create_time
    LIMIT 10
) as sample_check;

-- 7. 检查字段结构一致性
SELECT 
    'table_structure_check' as check_type,
    'sys_license' as table_name,
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'warehouse_system' AND table_name = 'sys_license') =
             (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'warehouse_system_backup' AND table_name = 'sys_license_backup')
        THEN '表结构一致'
        ELSE '表结构不一致'
    END as result;

-- 8. 生成备份验证报告
SELECT 
    '=== 备份验证报告 ===' as report_section,
    NOW() as verification_time;

SELECT 
    '备份状态' as item,
    CASE 
        WHEN (SELECT COUNT(*) FROM backup_info WHERE backup_status = 'completed') = 
             (SELECT COUNT(DISTINCT table_name) FROM backup_info)
        THEN '所有表备份成功'
        ELSE '存在备份失败的表'
    END as status;

SELECT 
    '数据完整性' as item,
    CASE 
        WHEN (SELECT COUNT(*) FROM warehouse_system.sys_license) = (SELECT COUNT(*) FROM sys_license_backup)
        AND (SELECT COUNT(*) FROM warehouse_system.sys_license_feature) = (SELECT COUNT(*) FROM sys_license_feature_backup)
        THEN '数据完整'
        ELSE '数据不完整'
    END as status;

SELECT 
    '备份可用性' as item,
    CASE 
        WHEN EXISTS (SELECT 1 FROM sys_license_backup LIMIT 1)
        AND EXISTS (SELECT 1 FROM sys_license_feature_backup LIMIT 1)
        AND EXISTS (SELECT 1 FROM sys_dict_data_backup LIMIT 1)
        THEN '备份可用'
        ELSE '备份不可用'
    END as status;

-- 9. 输出验证结论
SELECT 
    '验证结论' as conclusion,
    CASE 
        WHEN (SELECT COUNT(*) FROM backup_info WHERE backup_status = 'completed') = 
             (SELECT COUNT(DISTINCT table_name) FROM backup_info)
        AND (SELECT COUNT(*) FROM warehouse_system.sys_license) = (SELECT COUNT(*) FROM sys_license_backup)
        AND (SELECT COUNT(*) FROM warehouse_system.sys_license_feature) = (SELECT COUNT(*) FROM sys_license_feature_backup)
        THEN '备份验证通过，可以进行字段标准化操作'
        ELSE '备份验证失败，请重新执行备份'
    END as result;