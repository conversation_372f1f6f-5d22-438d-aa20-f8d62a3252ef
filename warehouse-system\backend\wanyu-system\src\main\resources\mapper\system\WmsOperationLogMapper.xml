<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsOperationLogMapper">
    
    <resultMap type="WmsOperationLog" id="WmsOperationLogResult">
        <result property="logId"    column="log_id"    />
        <result property="operationType"    column="operation_type"    />
        <result property="operationDesc"    column="operation_desc"    />
        <result property="operationStatus"    column="operation_status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="userAgent"    column="user_agent"    />
        <result property="requestUrl"    column="request_url"    />
        <result property="requestMethod"    column="request_method"    />
        <result property="requestParams"    column="request_params"    />
        <result property="responseData"    column="response_data"    />
        <result property="executionTime"    column="execution_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectWmsOperationLogVo">
        select log_id, operation_type, operation_desc, operation_status, error_message, operation_time, operator_id, operator_name, ip_address, user_agent, request_url, request_method, request_params, response_data, execution_time, create_time from wms_operation_log
    </sql>

    <select id="selectWmsOperationLogList" parameterType="WmsOperationLog" resultMap="WmsOperationLogResult">
        <include refid="selectWmsOperationLogVo"/>
        <where>  
            <if test="operationType != null  and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="operationDesc != null  and operationDesc != ''"> and operation_desc like concat('%', #{operationDesc}, '%')</if>
            <if test="operationStatus != null  and operationStatus != ''"> and operation_status = #{operationStatus}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            <if test="requestUrl != null  and requestUrl != ''"> and request_url like concat('%', #{requestUrl}, '%')</if>
            <if test="requestMethod != null  and requestMethod != ''"> and request_method = #{requestMethod}</if>
            <if test="params.beginOperationTime != null and params.beginOperationTime != ''"><!-- 开始操作时间 -->
                and date_format(operation_time,'%y%m%d') &gt;= date_format(#{params.beginOperationTime},'%y%m%d')
            </if>
            <if test="params.endOperationTime != null and params.endOperationTime != ''"><!-- 结束操作时间 -->
                and date_format(operation_time,'%y%m%d') &lt;= date_format(#{params.endOperationTime},'%y%m%d')
            </if>
        </where>
        order by operation_time desc
    </select>
    
    <select id="selectWmsOperationLogByLogId" parameterType="Long" resultMap="WmsOperationLogResult">
        <include refid="selectWmsOperationLogVo"/>
        where log_id = #{logId}
    </select>

    <!-- 查询成功操作日志 -->
    <select id="selectSuccessOperations" parameterType="WmsOperationLog" resultMap="WmsOperationLogResult">
        <include refid="selectWmsOperationLogVo"/>
        <where>
            operation_status = '0'
            <if test="operationType != null and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="operatorId != null"> and operator_id = #{operatorId}</if>
            <if test="params.beginOperationTime != null and params.beginOperationTime != ''">
                and date_format(operation_time,'%y%m%d') &gt;= date_format(#{params.beginOperationTime},'%y%m%d')
            </if>
            <if test="params.endOperationTime != null and params.endOperationTime != ''">
                and date_format(operation_time,'%y%m%d') &lt;= date_format(#{params.endOperationTime},'%y%m%d')
            </if>
        </where>
        order by operation_time desc
    </select>

    <!-- 查询失败操作日志 -->
    <select id="selectFailedOperations" parameterType="WmsOperationLog" resultMap="WmsOperationLogResult">
        <include refid="selectWmsOperationLogVo"/>
        <where>
            operation_status = '1'
            <if test="operationType != null and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="operatorId != null"> and operator_id = #{operatorId}</if>
            <if test="params.beginOperationTime != null and params.beginOperationTime != ''">
                and date_format(operation_time,'%y%m%d') &gt;= date_format(#{params.beginOperationTime},'%y%m%d')
            </if>
            <if test="params.endOperationTime != null and params.endOperationTime != ''">
                and date_format(operation_time,'%y%m%d') &lt;= date_format(#{params.endOperationTime},'%y%m%d')
            </if>
        </where>
        order by operation_time desc
    </select>

    <!-- 统计操作状态 -->
    <select id="countByOperationStatus" resultType="java.util.Map">
        select 
            operation_status,
            count(*) as count
        from wms_operation_log
        <where>
            <if test="operationType != null and operationType != ''"> and operation_type = #{operationType}</if>
            <if test="operatorId != null"> and operator_id = #{operatorId}</if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(operation_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(operation_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        group by operation_status
    </select>
        
    <insert id="insertWmsOperationLog" parameterType="WmsOperationLog" useGeneratedKeys="true" keyProperty="logId">
        insert into wms_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="operationDesc != null and operationDesc != ''">operation_desc,</if>
            <if test="operationStatus != null">operation_status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null and operatorName != ''">operator_name,</if>
            <if test="ipAddress != null and ipAddress != ''">ip_address,</if>
            <if test="userAgent != null and userAgent != ''">user_agent,</if>
            <if test="requestUrl != null and requestUrl != ''">request_url,</if>
            <if test="requestMethod != null and requestMethod != ''">request_method,</if>
            <if test="requestParams != null">request_params,</if>
            <if test="responseData != null">response_data,</if>
            <if test="executionTime != null">execution_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="operationDesc != null and operationDesc != ''">#{operationDesc},</if>
            <if test="operationStatus != null">#{operationStatus},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},</if>
            <if test="ipAddress != null and ipAddress != ''">#{ipAddress},</if>
            <if test="userAgent != null and userAgent != ''">#{userAgent},</if>
            <if test="requestUrl != null and requestUrl != ''">#{requestUrl},</if>
            <if test="requestMethod != null and requestMethod != ''">#{requestMethod},</if>
            <if test="requestParams != null">#{requestParams},</if>
            <if test="responseData != null">#{responseData},</if>
            <if test="executionTime != null">#{executionTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateWmsOperationLog" parameterType="WmsOperationLog">
        update wms_operation_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
            <if test="operationDesc != null and operationDesc != ''">operation_desc = #{operationDesc},</if>
            <if test="operationStatus != null">operation_status = #{operationStatus},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">operator_name = #{operatorName},</if>
            <if test="ipAddress != null and ipAddress != ''">ip_address = #{ipAddress},</if>
            <if test="userAgent != null and userAgent != ''">user_agent = #{userAgent},</if>
            <if test="requestUrl != null and requestUrl != ''">request_url = #{requestUrl},</if>
            <if test="requestMethod != null and requestMethod != ''">request_method = #{requestMethod},</if>
            <if test="requestParams != null">request_params = #{requestParams},</if>
            <if test="responseData != null">response_data = #{responseData},</if>
            <if test="executionTime != null">execution_time = #{executionTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteWmsOperationLogByLogId" parameterType="Long">
        delete from wms_operation_log where log_id = #{logId}
    </delete>

    <delete id="deleteWmsOperationLogByLogIds" parameterType="String">
        delete from wms_operation_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

    <!-- 清理过期日志 -->
    <delete id="cleanExpiredLogs" parameterType="int">
        delete from wms_operation_log 
        where operation_time &lt; date_sub(now(), interval #{days} day)
    </delete>
</mapper>