#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# 配置
BASE_URL = "http://localhost:8080"
PRODUCT_LIST_URL = f"{BASE_URL}/product/info/list"

def test_product_api():
    """测试物品管理API"""
    
    print("========================================")
    print("测试物品管理API")
    print("========================================")
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 测试物品列表API
        print("1. 测试物品列表API...")
        response = session.get(f"{PRODUCT_LIST_URL}?pageNum=1&pageSize=10")
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功！")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查数据结构
            if 'rows' in data:
                print(f"✅ 数据格式正确，共 {len(data['rows'])} 条记录")
                if data['rows']:
                    print("✅ 数据不为空")
                    print(f"第一条记录: {json.dumps(data['rows'][0], indent=2, ensure_ascii=False)}")
                else:
                    print("⚠️  数据为空，但API正常")
            else:
                print("⚠️  响应格式可能不正确")
                
        elif response.status_code == 401:
            print("❌ 需要登录认证")
            print("请确保：")
            print("1. 后端服务已启动")
            print("2. 用户已登录或API不需要认证")
        elif response.status_code == 404:
            print("❌ API端点未找到")
            print("请确保：")
            print("1. WmsProductController已正确部署")
            print("2. 后端服务已重启")
            print("3. 路径映射正确 (/product/info/list)")
        elif response.status_code == 500:
            print("❌ 服务器内部错误")
            print("请检查：")
            print("1. 数据库连接是否正常")
            print("2. Mapper配置是否正确")
            print("3. 后端日志中的错误信息")
            print(f"错误响应: {response.text}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
        print("请确保后端服务已启动在 http://localhost:8080")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_database_direct():
    """直接测试数据库连接"""
    import subprocess
    
    print("\n2. 测试数据库连接...")
    try:
        cmd = [
            'mysql', 
            '-h', 'localhost', 
            '-P', '3306', 
            '-u', 'root', 
            '-p123456', 
            '-D', 'warehouse_system', 
            '-e', 'SELECT COUNT(*) as count FROM wms_product;'
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 数据库连接成功")
            print(f"查询结果: {result.stdout}")
        else:
            print("❌ 数据库连接失败")
            print(f"错误信息: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("❌ 数据库连接超时")
    except FileNotFoundError:
        print("⚠️  MySQL客户端未找到，跳过数据库测试")
    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")

if __name__ == "__main__":
    test_product_api()
    test_database_direct()
    
    print("\n========================================")
    print("测试完成")
    print("========================================")
    input("按回车键退出...")