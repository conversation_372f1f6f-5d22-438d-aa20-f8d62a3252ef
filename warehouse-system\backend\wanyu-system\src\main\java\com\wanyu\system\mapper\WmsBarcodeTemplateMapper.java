package com.wanyu.system.mapper;

import java.util.List;
import com.wanyu.system.domain.WmsBarcodeTemplate;

/**
 * 物品条码模板Mapper接口
 *
 * <AUTHOR>
 */
public interface WmsBarcodeTemplateMapper
{
    /**
     * 查询物品条码模板
     *
     * @param templateId 物品条码模板主键
     * @return 物品条码模板
     */
    public WmsBarcodeTemplate selectWmsBarcodeTemplateByTemplateId(Long templateId);

    /**
     * 查询物品条码模板列表
     *
     * @param wmsBarcodeTemplate 物品条码模板
     * @return 物品条码模板集合
     */
    public List<WmsBarcodeTemplate> selectWmsBarcodeTemplateList(WmsBarcodeTemplate wmsBarcodeTemplate);

    /**
     * 新增物品条码模板
     *
     * @param wmsBarcodeTemplate 物品条码模板
     * @return 结果
     */
    public int insertWmsBarcodeTemplate(WmsBarcodeTemplate wmsBarcodeTemplate);

    /**
     * 修改物品条码模板
     *
     * @param wmsBarcodeTemplate 物品条码模板
     * @return 结果
     */
    public int updateWmsBarcodeTemplate(WmsBarcodeTemplate wmsBarcodeTemplate);

    /**
     * 删除物品条码模板
     *
     * @param templateId 物品条码模板主键
     * @return 结果
     */
    public int deleteWmsBarcodeTemplateByTemplateId(Long templateId);

    /**
     * 批量删除物品条码模板
     *
     * @param templateIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWmsBarcodeTemplateByTemplateIds(Long[] templateIds);
}