@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: 字段定义标准化数据库部署脚本 (仅数据库)
:: 版本: 1.0
:: 描述: 仅执行数据库字段标准化，不涉及代码编译
:: ========================================

echo.
echo ========================================
echo 字段定义标准化数据库部署脚本
echo (仅数据库修复，不包含代码编译)
echo ========================================
echo.

:: 设置项目路径和配置
set "PROJECT_ROOT=C:\CKGLXT\warehouse-system"
set "DB_HOST=localhost"
set "DB_PORT=3306"
set "DB_NAME=warehouse_system"
set "DB_USER=root"
set "DB_PASSWORD=123456"
set "BACKUP_DIR=%PROJECT_ROOT%\backups"
set "LOG_FILE=%PROJECT_ROOT%\database_deployment.log"

:: 创建日志函数
call :log "开始字段定义标准化数据库部署流程"

:: 检查必要的工具和环境
call :log "检查数据库部署环境"
call :check_db_prerequisites
if !errorlevel! neq 0 (
    call :log "ERROR: 环境检查失败，部署终止"
    goto :error_exit
)

:: 第一阶段：创建备份
call :log "阶段1: 创建数据库备份"
call :create_backup
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库备份失败，部署终止"
    goto :error_exit
)

:: 第二阶段：执行数据库修复
call :log "阶段2: 执行数据库字段标准化修复"
call :execute_database_fixes
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库修复失败，开始回滚"
    call :rollback_database
    goto :error_exit
)

:: 第三阶段：验证数据库修复结果
call :log "阶段3: 验证数据库修复结果"
call :verify_database_fixes
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库验证失败，开始回滚"
    call :rollback_database
    goto :error_exit
)

:: 部署成功
call :log "SUCCESS: 字段定义标准化数据库部署成功完成"
echo.
echo ========================================
echo 数据库部署成功完成！
echo ========================================
echo.
echo 完成的操作：
echo ✓ 数据库备份已创建
echo ✓ sys_license表status字段已标准化
echo ✓ sys_license_feature表status字段已标准化
echo ✓ 数据字典已更新
echo ✓ 数据完整性验证通过
echo.
echo 备份位置: %BACKUP_DIR%
echo 日志文件: %LOG_FILE%
echo.
echo 注意：此脚本仅修复了数据库，如需更新应用代码，
echo 请安装Maven后运行完整部署脚本 deploy_field_standardization.bat
echo.
pause
goto :end

:: ========================================
:: 函数定义
:: ========================================

:check_db_prerequisites
call :log "检查数据库部署环境"

:: 检查MySQL客户端
mysql --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: MySQL客户端未安装或不在PATH中"
    echo.
    echo 错误：MySQL客户端未找到
    echo 解决方案：请安装MySQL客户端并确保mysql命令可用
    echo.
    pause
    exit /b 1
)
call :log "MySQL客户端检查通过"

:: 检查数据库连接
call :log "测试数据库连接..."
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库连接失败"
    echo.
    echo 错误：无法连接到数据库
    echo 数据库配置：
    echo   主机: %DB_HOST%:%DB_PORT%
    echo   数据库: %DB_NAME%
    echo   用户: %DB_USER%
    echo.
    echo 解决方案：
    echo 1. 确认MySQL服务已启动
    echo 2. 检查数据库连接参数
    echo 3. 确认数据库 %DB_NAME% 存在
    echo 4. 验证用户名和密码
    echo.
    pause
    exit /b 1
)
call :log "数据库连接测试通过"

:: 创建备份目录
if not exist "%BACKUP_DIR%" (
    mkdir "%BACKUP_DIR%" 2>nul
    if !errorlevel! neq 0 (
        call :log "ERROR: 无法创建备份目录"
        exit /b 1
    )
    call :log "创建备份目录: %BACKUP_DIR%"
)

call :log "数据库环境检查通过"
exit /b 0

:create_backup
call :log "开始创建数据库备份"

:: 生成备份文件名
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set backup_date=%%d%%b%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set backup_time=%%a%%b
set "BACKUP_FILE=%BACKUP_DIR%\warehouse_system_backup_%backup_date%_%backup_time%.sql"

:: 执行数据库备份
call :log "备份文件: %BACKUP_FILE%"
mysqldump -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% > "%BACKUP_FILE%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库备份失败"
    exit /b 1
)

:: 验证备份文件
if not exist "%BACKUP_FILE%" (
    call :log "ERROR: 备份文件未生成"
    exit /b 1
)

:: 检查备份文件大小
for %%A in ("%BACKUP_FILE%") do set backup_size=%%~zA
if !backup_size! lss 1000 (
    call :log "ERROR: 备份文件过小，可能备份失败"
    exit /b 1
)

call :log "数据库备份成功，文件大小: !backup_size! 字节"
exit /b 0

:execute_database_fixes
call :log "执行数据库字段标准化修复"

:: 创建修复脚本
set "FIX_SCRIPT=%TEMP%\field_standardization_fix.sql"
call :create_fix_script "%FIX_SCRIPT%"

:: 执行修复脚本
call :log "执行修复脚本: %FIX_SCRIPT%"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%FIX_SCRIPT%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库修复脚本执行失败"
    exit /b 1
)

call :log "数据库修复脚本执行成功"
exit /b 0

:create_fix_script
set "script_file=%~1"
call :log "创建数据库修复脚本: %script_file%"

(
echo -- 字段定义标准化修复脚本
echo -- 生成时间: %date% %time%
echo.
echo -- 开始事务
echo START TRANSACTION;
echo.
echo -- 1. 备份关键表到临时表
echo CREATE TABLE sys_license_temp_backup AS SELECT * FROM sys_license;
echo CREATE TABLE sys_license_feature_temp_backup AS SELECT * FROM sys_license_feature;
echo.
echo -- 2. 修复sys_license表的status字段
echo -- 添加临时字段
echo ALTER TABLE sys_license ADD COLUMN status_new CHAR^(1^) DEFAULT '0' COMMENT '状态（0正常 1停用）';
echo.
echo -- 数据转换（颠倒原有值）
echo UPDATE sys_license SET status_new = CASE 
echo     WHEN status = '0' THEN '1'  -- 原来禁用^(0^) -^> 新标准禁用^(1^)
echo     WHEN status = '1' THEN '0'  -- 原来启用^(1^) -^> 新标准启用^(0^)
echo     ELSE '0' 
echo END;
echo.
echo -- 删除原字段，重命名新字段
echo ALTER TABLE sys_license DROP COLUMN status;
echo ALTER TABLE sys_license CHANGE COLUMN status_new status CHAR^(1^) DEFAULT '0' COMMENT '状态（0正常 1停用）';
echo.
echo -- 3. 修复sys_license_feature表的status字段
echo -- 添加临时字段
echo ALTER TABLE sys_license_feature ADD COLUMN status_new CHAR^(1^) DEFAULT '0' COMMENT '状态（0正常 1停用）';
echo.
echo -- 数据转换（颠倒原有值）
echo UPDATE sys_license_feature SET status_new = CASE 
echo     WHEN status = '0' THEN '1'  -- 原来禁用^(0^) -^> 新标准禁用^(1^)
echo     WHEN status = '1' THEN '0'  -- 原来启用^(1^) -^> 新标准启用^(0^)
echo     ELSE '0' 
echo END;
echo.
echo -- 删除原字段，重命名新字段
echo ALTER TABLE sys_license_feature DROP COLUMN status;
echo ALTER TABLE sys_license_feature CHANGE COLUMN status_new status CHAR^(1^) DEFAULT '0' COMMENT '状态（0正常 1停用）';
echo.
echo -- 4. 更新数据字典
echo UPDATE sys_dict_data SET dict_label = '正常', dict_value = '0' 
echo WHERE dict_type = 'sys_normal_disable' AND dict_value = '0';
echo.
echo UPDATE sys_dict_data SET dict_label = '停用', dict_value = '1' 
echo WHERE dict_type = 'sys_normal_disable' AND dict_value = '1';
echo.
echo -- 5. 确保操作状态字典正确
echo INSERT IGNORE INTO sys_dict_data ^(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark^) 
echo VALUES 
echo ^(1, '成功', '0', 'operation_status', '', 'success', 'Y', '0', 'admin', NOW^(^), '操作成功'^),
echo ^(2, '失败', '1', 'operation_status', '', 'danger', 'N', '0', 'admin', NOW^(^), '操作失败'^);
echo.
echo -- 提交事务
echo COMMIT;
) > "%script_file%"

exit /b 0

:verify_database_fixes
call :log "验证数据库修复结果"

:: 创建验证脚本
set "VERIFY_SCRIPT=%TEMP%\field_standardization_verify.sql"
call :create_verify_script "%VERIFY_SCRIPT%"

:: 执行验证脚本
set "VERIFY_RESULT=%TEMP%\verify_result.txt"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%VERIFY_SCRIPT%" > "%VERIFY_RESULT%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库验证脚本执行失败"
    exit /b 1
)

:: 检查验证结果
findstr "ERROR\|FAIL" "%VERIFY_RESULT%" >nul
if !errorlevel! equ 0 (
    call :log "ERROR: 数据库验证发现问题"
    type "%VERIFY_RESULT%" >> "%LOG_FILE%"
    exit /b 1
)

call :log "数据库修复验证通过"
exit /b 0

:create_verify_script
set "script_file=%~1"
call :log "创建数据库验证脚本: %script_file%"

(
echo -- 字段定义标准化验证脚本
echo SELECT 'sys_license表验证' as test_name,
echo     COUNT^(*^) as total_records,
echo     SUM^(CASE WHEN status = '0' THEN 1 ELSE 0 END^) as enabled_count,
echo     SUM^(CASE WHEN status = '1' THEN 1 ELSE 0 END^) as disabled_count,
echo     SUM^(CASE WHEN status NOT IN ^('0', '1'^) THEN 1 ELSE 0 END^) as invalid_count
echo FROM sys_license;
echo.
echo SELECT 'sys_license_feature表验证' as test_name,
echo     COUNT^(*^) as total_records,
echo     SUM^(CASE WHEN status = '0' THEN 1 ELSE 0 END^) as enabled_count,
echo     SUM^(CASE WHEN status = '1' THEN 1 ELSE 0 END^) as disabled_count,
echo     SUM^(CASE WHEN status NOT IN ^('0', '1'^) THEN 1 ELSE 0 END^) as invalid_count
echo FROM sys_license_feature;
) > "%script_file%"

exit /b 0

:rollback_database
call :log "开始数据库回滚"

:: 查找最新的备份文件
for /f "delims=" %%i in ('dir /b /o-d "%BACKUP_DIR%\warehouse_system_backup_*.sql" 2^>nul') do (
    set "LATEST_BACKUP=%BACKUP_DIR%\%%i"
    goto :found_backup
)

call :log "ERROR: 未找到备份文件，无法回滚"
exit /b 1

:found_backup
call :log "使用备份文件回滚: !LATEST_BACKUP!"

:: 删除当前数据库
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "DROP DATABASE IF EXISTS %DB_NAME%;" 2>>"%LOG_FILE%"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE %DB_NAME%;" 2>>"%LOG_FILE%"

:: 恢复备份
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "!LATEST_BACKUP!" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库回滚失败"
    exit /b 1
)

call :log "数据库回滚成功"
exit /b 0

:log
set "timestamp=%date% %time%"
echo [%timestamp%] %~1
echo [%timestamp%] %~1 >> "%LOG_FILE%"
exit /b 0

:error_exit
echo.
echo ========================================
echo 数据库部署失败！
echo ========================================
echo.
echo 请检查日志文件: %LOG_FILE%
echo 备份位置: %BACKUP_DIR%
echo.
echo 如需回滚，可运行: rollback_deployment.bat
echo.
pause
exit /b 1

:end
endlocal