package com.wanyu.system.service;

import java.util.List;
import com.wanyu.system.domain.SysPermissionLog;
import com.wanyu.system.domain.SysPermissionTemplateLog;

/**
 * 权限日志Service接口
 *
 * <AUTHOR>
 */
public interface ISysPermissionLogService
{
    /**
     * 查询权限日志
     *
     * @param logId 权限日志主键
     * @return 权限日志
     */
    public SysPermissionLog selectSysPermissionLogByLogId(Long logId);

    /**
     * 查询权限日志列表
     *
     * @param sysPermissionLog 权限日志
     * @return 权限日志集合
     */
    public List<SysPermissionLog> selectSysPermissionLogList(SysPermissionLog sysPermissionLog);

    /**
     * 新增权限日志
     *
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    public int insertSysPermissionLog(SysPermissionLog sysPermissionLog);

    /**
     * 修改权限日志
     *
     * @param sysPermissionLog 权限日志
     * @return 结果
     */
    public int updateSysPermissionLog(SysPermissionLog sysPermissionLog);

    /**
     * 批量删除权限日志
     *
     * @param logIds 需要删除的权限日志主键集合
     * @return 结果
     */
    public int deleteSysPermissionLogByLogIds(Long[] logIds);

    /**
     * 删除权限日志信息
     *
     * @param logId 权限日志主键
     * @return 结果
     */
    public int deleteSysPermissionLogByLogId(Long logId);

    /**
     * 清空权限日志
     */
    public void cleanSysPermissionLog();

    /**
     * 记录权限验证日志
     *
     * @param permissionType 权限类型
     * @param permission 权限标识
     * @param method 请求方法
     * @param url 请求URL
     * @param result 验证结果
     * @param failReason 失败原因
     */
    public void recordPermissionLog(String permissionType, String permission, String method, String url, String result, String failReason);

    /**
     * 记录权限操作日志
     *
     * @param userId 用户ID
     * @param userName 用户名称
     * @param operation 操作类型
     * @param permission 权限标识
     * @param status 操作状态（0成功 1失败）
     * @return 结果
     */
    public int insertPermissionLog(Long userId, String userName, String operation, String permission, String status);

    /**
     * 记录权限模板应用日志
     *
     * @param templateId 模板ID
     * @param templateName 模板名称
     * @param targetType 目标类型（1角色 2用户）
     * @param targetId 目标ID
     * @param targetName 目标名称
     * @param applyType 应用类型（1追加 2替换）
     * @param status 状态（0成功 1失败）
     * @param createBy 创建者
     * @param remark 备注
     * @return 结果
     */
    public int insertTemplateLog(Long templateId, String templateName, String targetType, Long targetId, String targetName, String applyType, String status, String createBy, String remark);

    /**
     * 查询权限模板应用日志列表
     *
     * @param templateLog 权限模板应用日志
     * @return 权限模板应用日志集合
     */
    public List<SysPermissionTemplateLog> selectTemplateLogList(SysPermissionTemplateLog templateLog);
}
