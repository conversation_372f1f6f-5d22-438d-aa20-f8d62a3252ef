@echo off
chcp 65001
echo ========================================
echo 修复物品信息图片显示问题
echo ========================================

echo 步骤1: 创建图片上传目录...
mkdir "C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13" 2>nul
if exist "C:\CKGLXT\warehouse-system\Pictures\upload\2025\08\13" (
    echo ✅ 图片目录创建成功
) else (
    echo ❌ 图片目录创建失败
    pause
    exit /b 1
)

echo 步骤2: 检查后端服务是否运行...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8080/profile/upload/2025/08/13/test.png
echo.

echo 步骤3: 测试图片API访问...
echo 正在测试图片访问路径...
echo 数据库中的图片路径: /profile/upload/2025/08/13/屏幕截图 2025-05-15 171140_20250813231048A002.png
echo 完整访问路径: http://localhost:8080/profile/upload/2025/08/13/屏幕截图 2025-05-15 171140_20250813231048A002.png

echo 步骤4: 检查物品信息API...
curl -X GET "http://localhost:8080/product/info/list?pageNum=1&pageSize=5" -H "Content-Type: application/json"
echo.

echo ========================================
echo 问题诊断完成
echo ========================================
echo.
echo 可能的问题：
echo 1. 图片文件不存在于指定路径
echo 2. 后端静态资源映射配置问题
echo 3. 前端图片URL拼接问题
echo 4. 文件权限问题
echo.
echo 解决方案：
echo 1. 确保图片文件存在于 C:\CKGLXT\warehouse-system\Pictures\upload\ 目录下
echo 2. 检查后端服务是否正常启动
echo 3. 验证前端 getImageUrl 方法是否正确
echo.
pause