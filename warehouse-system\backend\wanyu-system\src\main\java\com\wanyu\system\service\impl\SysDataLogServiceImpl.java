package com.wanyu.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanyu.system.mapper.SysDataLogMapper;
import com.wanyu.system.domain.SysDataLog;
import com.wanyu.system.service.ISysDataLogService;

/**
 * 数据日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class SysDataLogServiceImpl implements ISysDataLogService 
{
    @Autowired
    private SysDataLogMapper dataLogMapper;

    /**
     * 查询数据日志
     * 
     * @param logId 数据日志主键
     * @return 数据日志
     */
    @Override
    public SysDataLog selectDataLogById(Long logId)
    {
        return dataLogMapper.selectDataLogById(logId);
    }

    /**
     * 查询数据日志列表
     * 
     * @param dataLog 数据日志
     * @return 数据日志
     */
    @Override
    public List<SysDataLog> selectDataLogList(SysDataLog dataLog)
    {
        return dataLogMapper.selectDataLogList(dataLog);
    }

    /**
     * 新增数据日志
     * 
     * @param dataLog 数据日志
     * @return 结果
     */
    @Override
    public int insertDataLog(SysDataLog dataLog)
    {
        return dataLogMapper.insertDataLog(dataLog);
    }

    /**
     * 修改数据日志
     * 
     * @param dataLog 数据日志
     * @return 结果
     */
    @Override
    public int updateDataLog(SysDataLog dataLog)
    {
        return dataLogMapper.updateDataLog(dataLog);
    }

    /**
     * 批量删除数据日志
     * 
     * @param logIds 需要删除的数据日志主键
     * @return 结果
     */
    @Override
    public int deleteDataLogByIds(Long[] logIds)
    {
        return dataLogMapper.deleteDataLogByIds(logIds);
    }

    /**
     * 删除数据日志信息
     * 
     * @param logId 数据日志主键
     * @return 结果
     */
    @Override
    public int deleteDataLogById(Long logId)
    {
        return dataLogMapper.deleteDataLogById(logId);
    }

    /**
     * 清空数据日志
     */
    @Override
    public void cleanDataLog()
    {
        dataLogMapper.cleanDataLog();
    }
}