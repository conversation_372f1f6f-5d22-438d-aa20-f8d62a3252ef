-- 完整删除岗位相关的数据库表和字段
-- 执行前请先备份数据库

USE warehouse_system;

-- 1. 删除岗位表 (sys_post)
DROP TABLE IF EXISTS `sys_post`;

-- 2. 删除用户岗位关联表 (sys_user_post)
DROP TABLE IF EXISTS `sys_user_post`;

-- 3. 删除岗位相关菜单项
DELETE FROM sys_menu WHERE menu_name LIKE '%岗位%' OR perms LIKE '%post%';

-- 4. 删除岗位相关权限
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE menu_name LIKE '%岗位%' OR perms LIKE '%post%'
);

-- 5. 检查其他表中是否有post_id字段的引用
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND COLUMN_NAME LIKE '%post%'
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 6. 如果用户表有岗位相关字段，删除它们
-- ALTER TABLE sys_user DROP COLUMN IF EXISTS post_id;
-- ALTER TABLE sys_user DROP COLUMN IF EXISTS post_ids;

-- 7. 清理相关的数据字典项
DELETE FROM sys_dict_type WHERE dict_type LIKE '%post%';
DELETE FROM sys_dict_data WHERE dict_type LIKE '%post%';

COMMIT;