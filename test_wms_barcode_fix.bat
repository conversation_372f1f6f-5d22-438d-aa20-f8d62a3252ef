@echo off
chcp 65001 >nul
echo ========================================
echo 物品条码功能测试脚本
echo ========================================
echo.

set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=warehouse_system
set DB_USER=root
set DB_PASS=123456
set BACKEND_URL=http://localhost:8080

echo 🧪 开始测试物品条码功能...
echo.

echo 📋 1. 测试数据库表结构...
echo.
echo 检查 wms_barcode 表:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SHOW CREATE TABLE wms_barcode\G" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ wms_barcode 表不存在或无法访问
    goto :error
)

echo.
echo 检查 wms_barcode_template 表:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SHOW CREATE TABLE wms_barcode_template\G" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ wms_barcode_template 表不存在或无法访问
    goto :error
)

echo.
echo ✅ 数据库表结构正常

echo.
echo 📊 2. 测试数据完整性...
echo.
echo 条码模板数据:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT template_id, template_name, template_type, status FROM wms_barcode_template;"

echo.
echo 条码类型字典数据:
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT dict_label, dict_value FROM sys_dict_data WHERE dict_type = 'wms_barcode_type' ORDER BY dict_sort;"

echo.
echo ✅ 数据完整性检查通过

echo.
echo 🔧 3. 测试后端编译...
echo.
cd /d "C:\CKGLXT\warehouse-system\backend"
if not exist "pom.xml" (
    echo ❌ 未找到后端项目目录
    goto :error
)

echo 执行 Maven 编译...
call mvn clean compile -q -DskipTests
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 后端编译失败
    echo 请检查代码是否有语法错误
    goto :error
)

echo ✅ 后端编译成功

echo.
echo 🌐 4. 测试后端服务连接...
echo.
echo 检查后端服务是否运行在端口 8080...
netstat -an | findstr ":8080" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ 后端服务正在运行
    
    echo.
    echo 测试条码相关API端点...
    
    echo 测试条码列表API:
    curl -s -o nul -w "HTTP状态码: %%{http_code}\n" "%BACKEND_URL%/product/barcode/list" 2>nul
    
    echo 测试条码模板列表API:
    curl -s -o nul -w "HTTP状态码: %%{http_code}\n" "%BACKEND_URL%/product/barcode/template/list" 2>nul
    
) else (
    echo ⚠️  后端服务未运行
    echo 请先启动后端服务进行完整测试
)

echo.
echo ========================================
echo ✅ 物品条码功能测试完成！
echo ========================================
echo.
echo 📝 测试结果总结:
echo   1. ✅ 数据库表结构正常
echo   2. ✅ 数据完整性检查通过
echo   3. ✅ 后端代码编译成功
echo   4. ⚠️  API测试需要后端服务运行
echo.
echo 🔄 建议下一步操作:
echo   1. 启动后端服务: start-system.bat
echo   2. 访问前端页面测试条码功能
echo   3. 测试条码生成和管理功能
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ 测试过程中发现问题！
echo ========================================
echo.
echo 🔧 建议修复步骤:
echo   1. 重新运行修复脚本: fix_wms_barcode_table_complete.bat
echo   2. 检查数据库连接配置
echo   3. 检查后端代码语法错误
echo   4. 查看详细错误日志
echo.

:end
echo 按任意键退出...
pause >nul