-- 添加缺少的菜单项到项目中
-- 基于sys_menu.csv文件分析，以下是项目中缺少的重要菜单项

-- 获取当前最大的menu_id
SET @max_menu_id = (SELECT MAX(menu_id) FROM sys_menu);

-- 1. 添加首页菜单 (如果不存在)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(1, '首页', 0, 1, 'index', 'Layout', '', 1, 0, 'M', '0', '0', '', 'dashboard', 'admin', NOW(), '首页目录'),
(2, '首页', 1, 1, 'index', 'index', '', 1, 0, 'C', '0', '0', '', 'dashboard', 'admin', NOW(), '首页菜单');

-- 2. 添加系统监控目录 (如果不存在)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(108, '系统监控', 0, 10, 'monitor', 'Layout', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', NOW(), '系统监控目录');

-- 3. 添加系统监控子菜单
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(109, '在线用户', 108, 1, 'online', 'monitor/online/index', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', NOW(), '在线用户菜单'),
(110, '定时任务', 108, 2, 'job', 'monitor/job/index', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', NOW(), '定时任务菜单'),
(111, '服务监控', 108, 3, 'server', 'monitor/server/index', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', NOW(), '服务监控菜单'),
(112, '缓存监控', 108, 4, 'cache', 'monitor/cache/index', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', NOW(), '缓存监控菜单');

-- 4. 添加登录日志菜单 (如果不存在)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(37, '登录日志', 34, 5, 'logininfor', 'monitor/logininfor/index', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', NOW(), '登录日志菜单');

-- 5. 添加物品属性菜单 (如果不存在)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4150, '物品属性', 17, 5, 'attribute', 'product/attribute/index', '', 1, 0, 'C', '0', '0', 'product:attribute:list', 'list', 'admin', NOW(), '物品属性菜单');

-- 6. 添加二维码工具菜单 (在二维码管理下)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4148, '二维码工具', 22, 5, 'qr-tools', 'qrcode/tools/index', '', 1, 0, 'C', '0', '0', 'qrcode:tools:list', 'tool', 'admin', NOW(), '二维码工具菜单');

-- 7. 添加权限字符列表菜单 (在权限管理下)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4152, '权限字符列表', 9, 6, 'permission-list', 'system/permission/permissionList', '', 1, 0, 'C', '0', '0', 'system:permission:list', 'list', 'admin', NOW(), '权限字符列表菜单');

-- 8. 添加分配仓库菜单 (在用户管理下)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4147, '分配仓库', 4, 12, 'assign-warehouse', 'system/user/assignWarehouse', '', 1, 0, 'C', '0', '0', 'system:user:assignWarehouse', 'warehouse', 'admin', NOW(), '分配仓库菜单');

-- 9. 为在线用户添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 1, '在线查询', 109, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', NOW(), ''),
(@max_menu_id + 2, '批量强退', 109, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', NOW(), ''),
(@max_menu_id + 3, '单条强退', 109, 3, '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', NOW(), '');

-- 10. 为定时任务添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 4, '任务查询', 110, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', NOW(), ''),
(@max_menu_id + 5, '任务新增', 110, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', NOW(), ''),
(@max_menu_id + 6, '任务修改', 110, 3, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', NOW(), ''),
(@max_menu_id + 7, '任务删除', 110, 4, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', NOW(), ''),
(@max_menu_id + 8, '状态修改', 110, 5, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', NOW(), ''),
(@max_menu_id + 9, '任务导出', 110, 6, '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', NOW(), '');

-- 11. 为服务监控添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 10, '服务监控', 111, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:server:list', '#', 'admin', NOW(), '');

-- 12. 为缓存监控添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 11, '缓存列表', 112, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:cache:list', '#', 'admin', NOW(), ''),
(@max_menu_id + 12, '缓存清理', 112, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:cache:clear', '#', 'admin', NOW(), '');

-- 13. 为登录日志添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 13, '登录查询', 37, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', NOW(), ''),
(@max_menu_id + 14, '登录删除', 37, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', NOW(), ''),
(@max_menu_id + 15, '日志导出', 37, 3, '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', NOW(), ''),
(@max_menu_id + 16, '账户解锁', 37, 4, '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', NOW(), ''),
(@max_menu_id + 17, '日志清空', 37, 5, '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:clean', '#', 'admin', NOW(), '');

-- 14. 为物品属性添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 18, '属性查询', 4150, 1, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:query', '#', 'admin', NOW(), ''),
(@max_menu_id + 19, '属性新增', 4150, 2, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:add', '#', 'admin', NOW(), ''),
(@max_menu_id + 20, '属性修改', 4150, 3, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:edit', '#', 'admin', NOW(), ''),
(@max_menu_id + 21, '属性删除', 4150, 4, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:remove', '#', 'admin', NOW(), ''),
(@max_menu_id + 22, '属性导出', 4150, 5, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:export', '#', 'admin', NOW(), '');

-- 15. 为二维码工具添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 23, '工具查询', 4148, 1, '', '', '', 1, 0, 'F', '0', '0', 'qrcode:tools:query', '#', 'admin', NOW(), ''),
(@max_menu_id + 24, '工具生成', 4148, 2, '', '', '', 1, 0, 'F', '0', '0', 'qrcode:tools:generate', '#', 'admin', NOW(), ''),
(@max_menu_id + 25, '工具扫描', 4148, 3, '', '', '', 1, 0, 'F', '0', '0', 'qrcode:tools:scan', '#', 'admin', NOW(), '');

-- 16. 为权限字符列表添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 26, '权限查询', 4152, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:query', '#', 'admin', NOW(), ''),
(@max_menu_id + 27, '权限新增', 4152, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:add', '#', 'admin', NOW(), ''),
(@max_menu_id + 28, '权限修改', 4152, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:edit', '#', 'admin', NOW(), ''),
(@max_menu_id + 29, '权限删除', 4152, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:remove', '#', 'admin', NOW(), '');

-- 17. 为分配仓库添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(@max_menu_id + 30, '仓库查询', 4147, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:user:warehouse:query', '#', 'admin', NOW(), ''),
(@max_menu_id + 31, '仓库分配', 4147, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:user:warehouse:assign', '#', 'admin', NOW(), ''),
(@max_menu_id + 32, '仓库取消', 4147, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:user:warehouse:cancel', '#', 'admin', NOW(), '');

-- 为超级管理员角色分配新菜单权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu 
WHERE menu_id IN (1, 2, 108, 109, 110, 111, 112, 37, 4150, 4148, 4152, 4147)
AND NOT EXISTS (
    SELECT 1 FROM sys_role_menu rm 
    WHERE rm.role_id = 1 AND rm.menu_id = sys_menu.menu_id
);

-- 显示添加的菜单信息
SELECT 
    m.menu_id,
    m.menu_name,
    CASE 
        WHEN p.menu_name IS NULL THEN '根目录'
        ELSE p.menu_name
    END AS parent_menu,
    m.order_num,
    m.path,
    CASE 
        WHEN m.menu_type = 'M' THEN '目录'
        WHEN m.menu_type = 'C' THEN '菜单'
        WHEN m.menu_type = 'F' THEN '按钮'
        ELSE m.menu_type
    END AS menu_type_desc,
    m.perms
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_id IN (1, 2, 108, 109, 110, 111, 112, 37, 4150, 4148, 4152, 4147)
ORDER BY m.parent_id, m.order_num;