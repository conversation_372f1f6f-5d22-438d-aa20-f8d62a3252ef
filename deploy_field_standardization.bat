@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: 字段定义标准化自动化部署脚本
:: 版本: 1.0
:: 创建时间: %date% %time%
:: 描述: 自动化执行数据库修复、代码部署和验证
:: ========================================

echo.
echo ========================================
echo 字段定义标准化部署脚本
echo ========================================
echo.

:: 设置项目路径和配置
set "PROJECT_ROOT=C:\CKGLXT\warehouse-system"
set "BACKEND_PATH=%PROJECT_ROOT%\backend"
set "FRONTEND_PATH=%PROJECT_ROOT%\frontend"
set "DB_HOST=localhost"
set "DB_PORT=3306"
set "DB_NAME=warehouse_system"
set "DB_USER=root"
set "DB_PASSWORD=123456"
set "BACKUP_DIR=%PROJECT_ROOT%\backups"
set "LOG_FILE=%PROJECT_ROOT%\deployment.log"

:: 创建日志函数
call :log "开始字段定义标准化部署流程"

:: 检查必要的工具和环境
call :check_prerequisites
if !errorlevel! neq 0 (
    call :log "ERROR: 环境检查失败，部署终止"
    goto :error_exit
)

:: 第一阶段：创建备份
call :log "阶段1: 创建数据库备份"
call :create_backup
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库备份失败，部署终止"
    goto :error_exit
)

:: 第二阶段：停止服务
call :log "阶段2: 停止应用服务"
call :stop_services
if !errorlevel! neq 0 (
    call :log "WARNING: 服务停止可能不完整，继续执行"
)

:: 第三阶段：执行数据库修复
call :log "阶段3: 执行数据库字段标准化修复"
call :execute_database_fixes
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库修复失败，开始回滚"
    call :rollback_database
    goto :error_exit
)

:: 第四阶段：验证数据库修复结果
call :log "阶段4: 验证数据库修复结果"
call :verify_database_fixes
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库验证失败，开始回滚"
    call :rollback_database
    goto :error_exit
)

:: 第五阶段：编译和部署后端代码
call :log "阶段5: 编译和部署后端代码"
call :deploy_backend
if !errorlevel! neq 0 (
    call :log "ERROR: 后端部署失败，开始回滚"
    call :rollback_database
    goto :error_exit
)

:: 第六阶段：部署前端代码
call :log "阶段6: 部署前端代码"
call :deploy_frontend
if !errorlevel! neq 0 (
    call :log "ERROR: 前端部署失败"
    goto :error_exit
)

:: 第七阶段：启动服务
call :log "阶段7: 启动应用服务"
call :start_services
if !errorlevel! neq 0 (
    call :log "ERROR: 服务启动失败"
    goto :error_exit
)

:: 第八阶段：执行部署后验证
call :log "阶段8: 执行部署后验证"
call :verify_deployment
if !errorlevel! neq 0 (
    call :log "ERROR: 部署验证失败"
    goto :error_exit
)

:: 部署成功
call :log "SUCCESS: 字段定义标准化部署成功完成"
echo.
echo ========================================
echo 部署成功完成！
echo 备份位置: %BACKUP_DIR%
echo 日志文件: %LOG_FILE%
echo ========================================
echo.
pause
goto :end

:: ========================================
:: 函数定义
:: ========================================

:check_prerequisites
call :log "检查部署环境和必要工具"

:: 检查MySQL客户端
call :log "检查MySQL客户端..."
mysql --version >nul 2>&1
set mysql_result=!errorlevel!
if !mysql_result! neq 0 (
    call :log "ERROR: MySQL客户端未安装或不在PATH中"
    call :log "请安装MySQL客户端并确保mysql命令在PATH中可用"
    echo.
    echo 错误详情：MySQL客户端检查失败
    echo 解决方案：
    echo 1. 安装MySQL客户端
    echo 2. 将MySQL bin目录添加到系统PATH
    echo 3. 重新打开命令提示符
    echo.
    pause
    exit /b 1
)
call :log "MySQL客户端检查通过"

:: 检查Java环境
call :log "检查Java环境..."
java -version >nul 2>&1
set java_result=!errorlevel!
if !java_result! neq 0 (
    call :log "ERROR: Java环境未配置"
    call :log "请安装Java JDK并配置JAVA_HOME环境变量"
    echo.
    echo 错误详情：Java环境检查失败
    echo 解决方案：
    echo 1. 安装Java JDK 8或11
    echo 2. 配置JAVA_HOME环境变量
    echo 3. 将Java bin目录添加到系统PATH
    echo.
    pause
    exit /b 1
)
call :log "Java环境检查通过"

:: 检查Maven
call :log "检查Maven..."
mvn --version >nul 2>&1
set maven_result=!errorlevel!
if !maven_result! neq 0 (
    call :log "ERROR: Maven未安装或不在PATH中"
    call :log "请安装Apache Maven并配置环境变量"
    echo.
    echo 错误详情：Maven检查失败
    echo 解决方案：
    echo 1. 下载并安装Apache Maven
    echo 2. 配置MAVEN_HOME环境变量
    echo 3. 将Maven bin目录添加到系统PATH
    echo.
    pause
    exit /b 1
)
call :log "Maven检查通过"

:: 检查Node.js (前端需要)
call :log "检查Node.js..."
node --version >nul 2>&1
set node_result=!errorlevel!
if !node_result! neq 0 (
    call :log "WARNING: Node.js未安装，前端部署可能失败"
    call :log "如需前端部署，请安装Node.js"
) else (
    call :log "Node.js检查通过"
)

:: 检查项目目录
call :log "检查项目目录结构..."
if not exist "%PROJECT_ROOT%" (
    call :log "ERROR: 项目根目录不存在: %PROJECT_ROOT%"
    echo.
    echo 错误详情：项目根目录不存在
    echo 当前配置的项目路径：%PROJECT_ROOT%
    echo 解决方案：
    echo 1. 确认项目路径是否正确
    echo 2. 修改脚本中的PROJECT_ROOT变量
    echo.
    pause
    exit /b 1
)
call :log "项目根目录存在: %PROJECT_ROOT%"

if not exist "%BACKEND_PATH%" (
    call :log "ERROR: 后端目录不存在: %BACKEND_PATH%"
    echo.
    echo 错误详情：后端目录不存在
    echo 当前配置的后端路径：%BACKEND_PATH%
    echo 解决方案：
    echo 1. 确认后端项目路径是否正确
    echo 2. 检查项目结构
    echo.
    pause
    exit /b 1
)
call :log "后端目录存在: %BACKEND_PATH%"

:: 创建备份目录
if not exist "%BACKUP_DIR%" (
    mkdir "%BACKUP_DIR%" 2>nul
    if !errorlevel! neq 0 (
        call :log "ERROR: 无法创建备份目录: %BACKUP_DIR%"
        echo.
        echo 错误详情：备份目录创建失败
        echo 解决方案：
        echo 1. 检查磁盘空间
        echo 2. 检查目录权限
        echo 3. 手动创建备份目录
        echo.
        pause
        exit /b 1
    )
    call :log "创建备份目录: %BACKUP_DIR%"
) else (
    call :log "备份目录已存在: %BACKUP_DIR%"
)

call :log "环境检查通过"
exit /b 0

:create_backup
call :log "开始创建数据库备份"

:: 生成备份文件名
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set backup_date=%%d%%b%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set backup_time=%%a%%b
set "BACKUP_FILE=%BACKUP_DIR%\warehouse_system_backup_%backup_date%_%backup_time%.sql"

:: 执行数据库备份
call :log "备份文件: %BACKUP_FILE%"
mysqldump -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% > "%BACKUP_FILE%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库备份失败"
    exit /b 1
)

:: 验证备份文件
if not exist "%BACKUP_FILE%" (
    call :log "ERROR: 备份文件未生成"
    exit /b 1
)

:: 检查备份文件大小
for %%A in ("%BACKUP_FILE%") do set backup_size=%%~zA
if !backup_size! lss 1000 (
    call :log "ERROR: 备份文件过小，可能备份失败"
    exit /b 1
)

call :log "数据库备份成功，文件大小: !backup_size! 字节"
exit /b 0

:stop_services
call :log "停止应用服务"

:: 停止Java进程
tasklist | findstr "java.exe" >nul
if !errorlevel! equ 0 (
    call :log "发现Java进程，正在停止..."
    taskkill /F /IM java.exe >nul 2>&1
    timeout /t 3 >nul
)

:: 停止Node.js进程（如果前端在运行）
tasklist | findstr "node.exe" >nul
if !errorlevel! equ 0 (
    call :log "发现Node.js进程，正在停止..."
    taskkill /F /IM node.exe >nul 2>&1
    timeout /t 2 >nul
)

call :log "服务停止完成"
exit /b 0

:execute_database_fixes
call :log "执行数据库字段标准化修复"

:: 创建修复脚本
set "FIX_SCRIPT=%TEMP%\field_standardization_fix.sql"
call :create_fix_script "%FIX_SCRIPT%"

:: 执行修复脚本
call :log "执行修复脚本: %FIX_SCRIPT%"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%FIX_SCRIPT%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库修复脚本执行失败"
    exit /b 1
)

call :log "数据库修复脚本执行成功"
exit /b 0

:create_fix_script
set "script_file=%~1"
call :log "创建数据库修复脚本: %script_file%"

(
echo -- 字段定义标准化修复脚本
echo -- 生成时间: %date% %time%
echo.
echo -- 开始事务
echo START TRANSACTION;
echo.
echo -- 1. 备份关键表到临时表
echo CREATE TABLE sys_license_temp_backup AS SELECT * FROM sys_license;
echo CREATE TABLE sys_license_feature_temp_backup AS SELECT * FROM sys_license_feature;
echo.
echo -- 2. 修复sys_license表的status字段
echo -- 添加临时字段
echo ALTER TABLE sys_license ADD COLUMN status_new CHAR^(1^) DEFAULT '0' COMMENT '状态（0正常 1停用）';
echo.
echo -- 数据转换（颠倒原有值）
echo UPDATE sys_license SET status_new = CASE 
echo     WHEN status = '0' THEN '1'  -- 原来禁用^(0^) -^> 新标准禁用^(1^)
echo     WHEN status = '1' THEN '0'  -- 原来启用^(1^) -^> 新标准启用^(0^)
echo     ELSE '0' 
echo END;
echo.
echo -- 删除原字段，重命名新字段
echo ALTER TABLE sys_license DROP COLUMN status;
echo ALTER TABLE sys_license CHANGE COLUMN status_new status CHAR^(1^) DEFAULT '0' COMMENT '状态（0正常 1停用）';
echo.
echo -- 3. 修复sys_license_feature表的status字段
echo -- 添加临时字段
echo ALTER TABLE sys_license_feature ADD COLUMN status_new CHAR^(1^) DEFAULT '0' COMMENT '状态（0正常 1停用）';
echo.
echo -- 数据转换（颠倒原有值）
echo UPDATE sys_license_feature SET status_new = CASE 
echo     WHEN status = '0' THEN '1'  -- 原来禁用^(0^) -^> 新标准禁用^(1^)
echo     WHEN status = '1' THEN '0'  -- 原来启用^(1^) -^> 新标准启用^(0^)
echo     ELSE '0' 
echo END;
echo.
echo -- 删除原字段，重命名新字段
echo ALTER TABLE sys_license_feature DROP COLUMN status;
echo ALTER TABLE sys_license_feature CHANGE COLUMN status_new status CHAR^(1^) DEFAULT '0' COMMENT '状态（0正常 1停用）';
echo.
echo -- 4. 更新数据字典
echo UPDATE sys_dict_data SET dict_label = '正常', dict_value = '0' 
echo WHERE dict_type = 'sys_normal_disable' AND dict_value = '0';
echo.
echo UPDATE sys_dict_data SET dict_label = '停用', dict_value = '1' 
echo WHERE dict_type = 'sys_normal_disable' AND dict_value = '1';
echo.
echo -- 5. 确保操作状态字典正确
echo INSERT IGNORE INTO sys_dict_data ^(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark^) 
echo VALUES 
echo ^(1, '成功', '0', 'operation_status', '', 'success', 'Y', '0', 'admin', NOW^(^), '操作成功'^),
echo ^(2, '失败', '1', 'operation_status', '', 'danger', 'N', '0', 'admin', NOW^(^), '操作失败'^);
echo.
echo -- 提交事务
echo COMMIT;
echo.
echo -- 记录修复完成
echo INSERT INTO sys_operation_log ^(title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time^)
echo VALUES ^('字段定义标准化', 0, 'deploy_field_standardization.bat', 'BATCH', 1, 'system', '系统', '/deploy/field-standardization', '127.0.0.1', '本地', '字段定义标准化部署', '部署成功', 0, NULL, NOW^(^)^);
) > "%script_file%"

exit /b 0

:verify_database_fixes
call :log "验证数据库修复结果"

:: 创建验证脚本
set "VERIFY_SCRIPT=%TEMP%\field_standardization_verify.sql"
call :create_verify_script "%VERIFY_SCRIPT%"

:: 执行验证脚本
set "VERIFY_RESULT=%TEMP%\verify_result.txt"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%VERIFY_SCRIPT%" > "%VERIFY_RESULT%" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库验证脚本执行失败"
    exit /b 1
)

:: 检查验证结果
findstr "ERROR\|FAIL" "%VERIFY_RESULT%" >nul
if !errorlevel! equ 0 (
    call :log "ERROR: 数据库验证发现问题"
    type "%VERIFY_RESULT%" >> "%LOG_FILE%"
    exit /b 1
)

call :log "数据库修复验证通过"
exit /b 0

:create_verify_script
set "script_file=%~1"
call :log "创建数据库验证脚本: %script_file%"

(
echo -- 字段定义标准化验证脚本
echo SELECT 'sys_license表验证' as test_name,
echo     COUNT^(*^) as total_records,
echo     SUM^(CASE WHEN status = '0' THEN 1 ELSE 0 END^) as enabled_count,
echo     SUM^(CASE WHEN status = '1' THEN 1 ELSE 0 END^) as disabled_count,
echo     SUM^(CASE WHEN status NOT IN ^('0', '1'^) THEN 1 ELSE 0 END^) as invalid_count
echo FROM sys_license;
echo.
echo SELECT 'sys_license_feature表验证' as test_name,
echo     COUNT^(*^) as total_records,
echo     SUM^(CASE WHEN status = '0' THEN 1 ELSE 0 END^) as enabled_count,
echo     SUM^(CASE WHEN status = '1' THEN 1 ELSE 0 END^) as disabled_count,
echo     SUM^(CASE WHEN status NOT IN ^('0', '1'^) THEN 1 ELSE 0 END^) as invalid_count
echo FROM sys_license_feature;
echo.
echo SELECT 'sys_dict_data验证' as test_name,
echo     COUNT^(*^) as total_records
echo FROM sys_dict_data 
echo WHERE dict_type = 'sys_normal_disable' 
echo   AND ^(^(dict_value = '0' AND dict_label = '正常'^) OR ^(dict_value = '1' AND dict_label = '停用'^)^);
) > "%script_file%"

exit /b 0

:deploy_backend
call :log "开始后端代码编译和部署"

cd /d "%BACKEND_PATH%"
if !errorlevel! neq 0 (
    call :log "ERROR: 无法切换到后端目录"
    exit /b 1
)

:: 清理和编译
call :log "执行Maven清理和编译"
mvn clean package -DskipTests -q >>"%LOG_FILE%" 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: Maven编译失败"
    exit /b 1
)

:: 检查编译产物
if not exist "wanyu-admin\target\wanyu-admin.jar" (
    call :log "ERROR: 编译产物不存在"
    exit /b 1
)

call :log "后端编译成功"
exit /b 0

:deploy_frontend
call :log "开始前端代码部署"

cd /d "%FRONTEND_PATH%"
if !errorlevel! neq 0 (
    call :log "WARNING: 无法切换到前端目录，跳过前端部署"
    exit /b 0
)

:: 检查是否需要安装依赖
if not exist "node_modules" (
    call :log "安装前端依赖"
    npm install >>"%LOG_FILE%" 2>&1
    if !errorlevel! neq 0 (
        call :log "WARNING: 前端依赖安装失败"
        exit /b 0
    )
)

:: 构建前端
call :log "构建前端代码"
npm run build:prod >>"%LOG_FILE%" 2>&1
if !errorlevel! neq 0 (
    call :log "WARNING: 前端构建失败"
    exit /b 0
)

call :log "前端部署完成"
exit /b 0

:start_services
call :log "启动应用服务"

cd /d "%BACKEND_PATH%"

:: 启动后端服务
call :log "启动后端服务"
start "Warehouse System Backend" java -jar wanyu-admin\target\wanyu-admin.jar

:: 等待服务启动
call :log "等待服务启动..."
timeout /t 30 >nul

:: 检查服务是否启动成功
call :check_service_health
if !errorlevel! neq 0 (
    call :log "ERROR: 服务启动失败或健康检查失败"
    exit /b 1
)

call :log "服务启动成功"
exit /b 0

:check_service_health
call :log "执行服务健康检查"

:: 尝试连接后端服务
for /l %%i in (1,1,10) do (
    curl -s http://localhost:8080/actuator/health >nul 2>&1
    if !errorlevel! equ 0 (
        call :log "服务健康检查通过"
        exit /b 0
    )
    call :log "等待服务启动... (%%i/10)"
    timeout /t 6 >nul
)

call :log "服务健康检查失败"
exit /b 1

:verify_deployment
call :log "执行部署后验证"

:: 验证数据库连接
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库连接验证失败"
    exit /b 1
)

:: 验证关键功能
call :log "验证许可证管理功能"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT COUNT(*) FROM sys_license WHERE status IN ('0', '1');" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: 许可证表验证失败"
    exit /b 1
)

call :log "验证许可证功能表"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT COUNT(*) FROM sys_license_feature WHERE status IN ('0', '1');" %DB_NAME% >nul 2>&1
if !errorlevel! neq 0 (
    call :log "ERROR: 许可证功能表验证失败"
    exit /b 1
)

call :log "部署验证通过"
exit /b 0

:rollback_database
call :log "开始数据库回滚"

:: 查找最新的备份文件
for /f "delims=" %%i in ('dir /b /o-d "%BACKUP_DIR%\warehouse_system_backup_*.sql" 2^>nul') do (
    set "LATEST_BACKUP=%BACKUP_DIR%\%%i"
    goto :found_backup
)

call :log "ERROR: 未找到备份文件，无法回滚"
exit /b 1

:found_backup
call :log "使用备份文件回滚: !LATEST_BACKUP!"

:: 删除当前数据库
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "DROP DATABASE IF EXISTS %DB_NAME%;" 2>>"%LOG_FILE%"
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE %DB_NAME%;" 2>>"%LOG_FILE%"

:: 恢复备份
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < "!LATEST_BACKUP!" 2>>"%LOG_FILE%"
if !errorlevel! neq 0 (
    call :log "ERROR: 数据库回滚失败"
    exit /b 1
)

call :log "数据库回滚成功"
exit /b 0

:log
set "timestamp=%date% %time%"
echo [%timestamp%] %~1
echo [%timestamp%] %~1 >> "%LOG_FILE%"
exit /b 0

:error_exit
echo.
echo ========================================
echo 部署失败！
echo 请检查日志文件: %LOG_FILE%
echo 备份位置: %BACKUP_DIR%
echo ========================================
echo.
pause
exit /b 1

:end
endlocal