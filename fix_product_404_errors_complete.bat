@echo off
chcp 65001 >nul
echo ========================================
echo 修复物品管理404错误 - 完整解决方案
echo ========================================

echo 问题分析：
echo 1. Vue组件中缺少handleExport方法
echo 2. 后端缺少product相关的Controller
echo 3. 后端缺少对应的Service和Domain类
echo.

echo 1. 已修复前端组件方法缺失问题...
echo ✅ 物品分类页面handleExport方法已添加

echo.
echo 2. 已创建后端Controller文件...
echo ✅ ProductInfoController.java
echo ✅ ProductCategoryController.java  
echo ✅ ProductUnitController.java
echo ✅ ProductSpecController.java

echo.
echo 3. 检查后端服务状态...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8080') do (
    echo 后端服务正在运行，PID: %%a
    goto :backend_running
)

echo 后端服务未运行，正在启动...
cd /d "warehouse-system\backend"
start "后端服务" cmd /c "mvn spring-boot:run -Dspring-boot.run.profiles=dev"
echo 等待后端服务启动...
timeout /t 20 /nobreak >nul
cd /d "..\..\"

:backend_running
echo.
echo 4. 重启前端服务...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8081') do (
    echo 停止前端服务，PID: %%a
    taskkill /f /pid %%a >nul 2>&1
)

cd /d "warehouse-system\frontend"
start "前端服务" cmd /c "npm run dev"
cd /d "..\..\"

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo ⚠️  注意事项：
echo 1. 后端Controller已创建，但还需要对应的Service和Domain类
echo 2. 需要确保数据库表结构正确
echo 3. 需要配置相应的权限菜单
echo.
echo 🌐 访问地址：
echo 前端: http://localhost:8081
echo 后端: http://localhost:8080
echo.
echo 📋 下一步操作：
echo 1. 等待服务启动完成
echo 2. 测试物品管理各个功能
echo 3. 如仍有404错误，需要创建对应的Service类
echo.
pause