package com.wanyu.web.controller.monitor;

import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.system.domain.SysCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 缓存监控
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/cache")
public class CacheController extends BaseController
{
    @Autowired
    private CacheManager cacheManager;

    private final static List<SysCache> caches = new ArrayList<SysCache>();
    {
        caches.add(new SysCache("userWarehouse", "用户仓库权限缓存"));
        caches.add(new SysCache("roleWarehouse", "角色仓库权限缓存"));
        caches.add(new SysCache("product", "产品信息缓存"));
        caches.add(new SysCache("inventory", "库存信息缓存"));
        caches.add(new SysCache("menu", "菜单缓存"));
        caches.add(new SysCache("permission", "权限缓存"));
        caches.add(new SysCache("dict", "字典缓存"));
        caches.add(new SysCache("config", "配置缓存"));
        caches.add(new SysCache("deptTemplate", "部门模板缓存"));
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping()
    public String cache()
    {
        return "monitor/cache";
    }

    /**
     * 获取缓存列表
     * @return
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/list")
    public TableDataInfo list()
    {
        return getDataTable(caches);
    }

    /**
     * 获取缓存键名列表
     * @param cacheName
     * @return
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getNames/{cacheName}")
    public AjaxResult getCacheNames(@PathVariable String cacheName)
    {
        Set<String> cacheNames = (Set<String>) cacheManager.getCache(cacheName).getNativeCache();
        return AjaxResult.success(cacheNames);
    }

    /**
     * 获取缓存键值列表
     * @param cacheName
     * @param cacheKey
     * @return
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getValue/{cacheName}/{cacheKey}")
    public AjaxResult getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey)
    {
        Object cacheValue = cacheManager.getCache(cacheName).get(cacheKey, Object.class);
        return AjaxResult.success(cacheValue);
    }

    /**
     * 清理缓存名称
     * @param cacheName
     * @return
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clear/{cacheName}")
    public AjaxResult clearCacheName(@PathVariable String cacheName)
    {
        cacheManager.getCache(cacheName).clear();
        return AjaxResult.success();
    }

    /**
     * 清理缓存键名
     * @param cacheName
     * @param cacheKey
     * @return
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clear/{cacheName}/{cacheKey}")
    public AjaxResult clearCacheKey(@PathVariable String cacheName, @PathVariable String cacheKey)
    {
        cacheManager.getCache(cacheName).evict(cacheKey);
        return AjaxResult.success();
    }

    /**
     * 清理全部缓存
     * @return
     */
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearAll")
    public AjaxResult clearAll()
    {
        Collection<String> cacheNames = cacheManager.getCacheNames();
        for (String cacheName : cacheNames)
        {
            cacheManager.getCache(cacheName).clear();
        }
        return AjaxResult.success();
    }
}