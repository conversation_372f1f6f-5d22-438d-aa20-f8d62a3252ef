import axios from 'axios'
import { Notification, MessageBox, Message, Loading } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { tansParams, blobValidate } from "@/utils/wanyu";
import cache from '@/plugins/cache'
import { saveAs } from 'file-saver'

let downloadLoadingInstance;
// 是否显示重新登录
export let isRelogin = { show: false };

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  // 超时
  timeout: 10000
})

// 打印环境变量，帮助调试
console.log("环境变量:", {
  VUE_APP_BASE_API: process.env.VUE_APP_BASE_API,
  VUE_APP_SERVER_URL: process.env.VUE_APP_SERVER_URL,
  NODE_ENV: process.env.NODE_ENV
});

// request拦截器
service.interceptors.request.use(config => {
  // 打印请求信息，帮助调试
  console.log("发送请求:", {
    url: config.url,
    method: config.method,
    baseURL: config.baseURL,
    fullURL: config.baseURL + config.url,
    headers: config.headers,
    params: config.params,
    data: config.data,
    transformedData: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data
  });
  
  // 调试特定API
  if (config.url.includes('assignUserDataPermission')) {
    console.debug('权限分配API请求详情:', {
      rawData: config.data,
      stringifiedData: JSON.stringify(config.data),
      headers: config.headers
    });
  }

  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  // 是否需要防止数据重复提交
  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
  if (getToken() && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
  }
  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params);
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }
  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
    const requestObj = {
      url: config.url,
      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      time: new Date().getTime()
    }
    const requestSize = Object.keys(JSON.stringify(requestObj)).length; // 请求数据大小
    const limitSize = 5 * 1024 * 1024; // 限制存放数据5M
    if (requestSize >= limitSize) {
      console.warn(`[${config.url}]: ` + '请求数据大小超出允许的5M限制，无法进行防重复提交验证。')
      return config;
    }
    const sessionObj = cache.session.getJSON('sessionObj')
    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
      cache.session.setJSON('sessionObj', requestObj)
    } else {
      const s_url = sessionObj.url;                  // 请求地址
      const s_data = sessionObj.data;                // 请求数据
      const s_time = sessionObj.time;                // 请求时间
      const interval = 1000;                         // 间隔时间(ms)，小于此时间视为重复提交
      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
        const message = '数据正在处理，请勿重复提交';
        console.warn(`[${s_url}]: ` + message)
        return Promise.reject(new Error(message))
      } else {
        cache.session.setJSON('sessionObj', requestObj)
      }
    }
  }
  return config
}, error => {
    console.log(error)
    Promise.reject(error)
})

// 响应拦截器
service.interceptors.response.use(res => {
    // 打印响应信息，帮助调试
    console.log("收到响应:", {
      url: res.config.url,
      method: res.config.method,
      status: res.status,
      data: res.data
    });
    
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200;
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default']
    // 二进制数据则直接返回
    if (res.request.responseType ===  'blob' || res.request.responseType ===  'arraybuffer') {
      return res.data
    }
    if (code === 401) {
      if (!isRelogin.show) {
        isRelogin.show = true;
        MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', { confirmButtonText: '重新登录', cancelButtonText: '取消', type: 'warning' }).then(() => {
          isRelogin.show = false;
          store.dispatch('LogOut').then(() => {
            location.href = '/index';
          })
      }).catch(() => {
        isRelogin.show = false;
      });
    }
      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
    } else if (code === 500) {
      Message({ message: msg, type: 'error' })
      return Promise.reject(new Error(msg))
    } else if (code === 601) {
      // 验证码错误或失效时，使用更友好的提示
      Message({ 
        message: msg || '验证码错误或已失效，请重新输入', 
        type: 'warning',
        duration: 3000,
        showClose: true
      });
      // 触发一个自定义事件，让组件可以响应验证码失效
      if (window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('captchaInvalid', {
          detail: { message: msg || '验证码已失效' }
        }));
      }
      return Promise.reject(new Error('验证码已失效'));
    } else if (code !== 200) {
      Notification.error({ title: msg })
      return Promise.reject('error')
    } else {
      return res.data
    }
  },
  error => {
    console.log('请求错误:', error);
    console.log('请求配置:', error.config);
    if (error.response) {
      console.log('响应状态码:', error.response.status);
      console.log('响应头:', error.response.headers);
      console.log('响应数据:', error.response.data);
    }

    let { message } = error;
    if (message == "Network Error") {
      message = "后端接口连接异常";
    } else if (message.includes("timeout")) {
      message = "系统接口请求超时";
    } else if (message.includes("Request failed with status code")) {
      const statusCode = message.substr(message.length - 3);
      message = "系统接口" + statusCode + "异常";

      // 针对404错误提供更详细的信息
      if (statusCode === "404") {
        message = "请求的接口不存在，请检查API路径是否正确";
        console.error("404错误 - 请求URL:", error.config.url);
        console.error("404错误 - 完整URL:", error.config.baseURL + error.config.url);
      }
    }
    Message({ message: message, type: 'error', duration: 5 * 1000 })
    return Promise.reject(error)
  }
)

// 通用下载方法
export function download(url, params, filename, config) {
  // 兜底：如果 params 只有一层 params，则自动解包
  if (params && typeof params === 'object' && Object.keys(params).length === 1 && params.params) {
    params = params.params;
  }
  // 终极兜底：强制删除 params.params 字段，防止类型转换错误
  if (params && typeof params === 'object' && params.params) {
    delete params.params;
  }
  downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })

  // 默认使用GET方法
  const method = config && config.method ? config.method : 'get';

  if (method.toLowerCase() === 'get') {
    // 构建查询字符串
    let queryUrl = process.env.VUE_APP_BASE_API + url;
    if (params) {
      const queryString = Object.keys(params)
        .filter(key => params[key] !== null && params[key] !== undefined && params[key] !== '')
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

      if (queryString) {
        queryUrl += (queryUrl.includes('?') ? '&' : '?') + queryString;
      }
    }

    // 发送GET请求
    return axios({
      method: 'get',
      url: queryUrl,
      responseType: 'blob',
      headers: { 'Authorization': 'Bearer ' + getToken() },
      ...config
    }).then(async (response) => {
      const isBlob = blobValidate(response.data);
      if (isBlob) {
        const blob = new Blob([response.data])
        saveAs(blob, filename)
      } else {
        const resText = await response.data.text();
        const rspObj = JSON.parse(resText);
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        Message.error(errMsg);
      }
      downloadLoadingInstance.close();
    }).catch((r) => {
      console.error(r)
      Message.error('下载文件出现错误，请联系管理员！')
      downloadLoadingInstance.close();
    });
  } else {
    // 原有的POST请求逻辑
    return service.post(url, params, {
      transformRequest: [(params) => { return tansParams(params) }],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob',
      ...config
    }).then(async (data) => {
      const isBlob = blobValidate(data);
      if (isBlob) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        Message.error(errMsg);
      }
      downloadLoadingInstance.close();
    }).catch((r) => {
      console.error(r)
      Message.error('下载文件出现错误，请联系管理员！')
      downloadLoadingInstance.close();
    });
  }
}

// 暴露service到window便于调试
if (process.env.NODE_ENV === 'development') {
  window.__apiService = service
}

export default service