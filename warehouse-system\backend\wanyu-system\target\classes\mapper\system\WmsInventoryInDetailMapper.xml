<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanyu.system.mapper.WmsInventoryInDetailMapper">
    
    <resultMap type="WmsInventoryInDetail" id="WmsInventoryInDetailResult">
        <result property="detailId"       column="detail_id"       />
        <result property="inId"           column="in_id"           />
        <result property="productId"      column="product_id"      />
        <result property="productName"    column="product_name"    />
        <result property="productCode"    column="product_code"    />
        <result property="quantity"       column="quantity"        />
        <result property="price"          column="price"           />
        <result property="amount"         column="amount"          />
        <result property="remark"         column="remark"          />
    </resultMap>

    <sql id="selectWmsInventoryInDetailVo">
        select d.detail_id, d.in_id, d.product_id, p.product_name, p.product_code, d.quantity, d.price, d.amount, d.remark
        from wms_inventory_in_detail d
        left join wms_product p on d.product_id = p.product_id
    </sql>

    <select id="selectWmsInventoryInDetailList" parameterType="WmsInventoryInDetail" resultMap="WmsInventoryInDetailResult">
        <include refid="selectWmsInventoryInDetailVo"/>
        <where>  
            <if test="inId != null "> and d.in_id = #{inId}</if>
            <if test="productId != null "> and d.product_id = #{productId}</if>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productCode != null  and productCode != ''"> and p.product_code like concat('%', #{productCode}, '%')</if>
        </where>
    </select>
    
    <select id="selectWmsInventoryInDetailByDetailId" parameterType="Long" resultMap="WmsInventoryInDetailResult">
        <include refid="selectWmsInventoryInDetailVo"/>
        where d.detail_id = #{detailId}
    </select>
    
    <select id="selectWmsInventoryInDetailByInId" parameterType="Long" resultMap="WmsInventoryInDetailResult">
        <include refid="selectWmsInventoryInDetailVo"/>
        where d.in_id = #{inId}
    </select>
        
    <insert id="insertWmsInventoryInDetail" parameterType="WmsInventoryInDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into wms_inventory_in_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inId != null">in_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="price != null">price,</if>
            <if test="amount != null">amount,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inId != null">#{inId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="price != null">#{price},</if>
            <if test="amount != null">#{amount},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertWmsInventoryInDetail">
        insert into wms_inventory_in_detail(in_id, product_id, quantity, price, amount, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.inId}, #{item.productId}, #{item.quantity}, #{item.price}, #{item.amount}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateWmsInventoryInDetail" parameterType="WmsInventoryInDetail">
        update wms_inventory_in_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="inId != null">in_id = #{inId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="price != null">price = #{price},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteWmsInventoryInDetailByDetailId" parameterType="Long">
        delete from wms_inventory_in_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteWmsInventoryInDetailByDetailIds" parameterType="String">
        delete from wms_inventory_in_detail where detail_id in 
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
    
    <delete id="deleteWmsInventoryInDetailByInId" parameterType="Long">
        delete from wms_inventory_in_detail where in_id = #{inId}
    </delete>
</mapper>
