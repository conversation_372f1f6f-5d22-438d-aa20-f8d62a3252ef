-- 添加剩余遗漏的菜单项
-- 基于sys_menu.csv文件的完整分析

-- 获取当前最大的menu_id
SET @max_menu_id = (SELECT MAX(menu_id) FROM sys_menu);

-- 1. 添加部门仓库权限菜单 (在部门管理下)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4341, '部门仓库权限', 7, 5, 'dept-warehouse', 'system/dept/dept-warehouse-permission', '', 1, 0, 'C', '0', '0', 'system:dept:warehouse', 'warehouse', 'admin', NOW(), '部门仓库权限菜单');

-- 2. 添加字典数据菜单 (在字典管理下)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4342, '字典数据', 4139, 2, 'dict-data', 'system/dict/data', '', 1, 0, 'C', '0', '0', 'system:dict:data', 'dict', 'admin', NOW(), '字典数据菜单');

-- 3. 添加登录方式管理菜单 (在系统管理下)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4343, '登录方式管理', 3, 11, 'login-style', 'system/login-style/index', '', 1, 0, 'C', '0', '0', 'system:login:style', 'login', 'admin', NOW(), '登录方式管理菜单');

-- 4. 添加权限字符列表菜单 (在权限定义下)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4351, '权限字符列表', 10, 5, 'perm-list', 'system/permission/perm-list', '', 1, 0, 'C', '0', '0', 'system:permission:perm-list', 'list', 'admin', NOW(), '权限字符列表菜单');

-- 5. 为部门仓库权限添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4352, '部门仓库查询', 4341, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:warehouse:query', '#', 'admin', NOW(), ''),
(4353, '部门仓库新增', 4341, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:warehouse:add', '#', 'admin', NOW(), ''),
(4354, '部门仓库修改', 4341, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:warehouse:edit', '#', 'admin', NOW(), ''),
(4355, '部门仓库删除', 4341, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:warehouse:remove', '#', 'admin', NOW(), '');

-- 6. 为字典数据添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4356, '字典数据查询', 4342, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:dict:data:query', '#', 'admin', NOW(), ''),
(4357, '字典数据新增', 4342, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:dict:data:add', '#', 'admin', NOW(), ''),
(4358, '字典数据修改', 4342, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:dict:data:edit', '#', 'admin', NOW(), ''),
(4359, '字典数据删除', 4342, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:dict:data:remove', '#', 'admin', NOW(), '');

-- 7. 为登录方式管理添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4360, '登录方式查询', 4343, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:login:style:query', '#', 'admin', NOW(), ''),
(4361, '登录方式新增', 4343, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:login:style:add', '#', 'admin', NOW(), ''),
(4362, '登录方式修改', 4343, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:login:style:edit', '#', 'admin', NOW(), ''),
(4363, '登录方式删除', 4343, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:login:style:remove', '#', 'admin', NOW(), '');

-- 8. 为权限字符列表添加按钮权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4377, '权限字符查询', 4351, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:query', '#', 'admin', NOW(), ''),
(4378, '权限字符新增', 4351, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:add', '#', 'admin', NOW(), ''),
(4379, '权限字符修改', 4351, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:edit', '#', 'admin', NOW(), ''),
(4380, '权限字符删除', 4351, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:permission:perm-list:remove', '#', 'admin', NOW(), '');

-- 9. 为物品属性添加按钮权限 (如果不存在)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4373, '物品属性查询', 4150, 1, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:query', '#', 'admin', NOW(), ''),
(4374, '物品属性新增', 4150, 2, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:add', '#', 'admin', NOW(), ''),
(4375, '物品属性修改', 4150, 3, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:edit', '#', 'admin', NOW(), ''),
(4376, '物品属性删除', 4150, 4, '', '', '', 1, 0, 'F', '0', '0', 'product:attribute:remove', '#', 'admin', NOW(), '');

-- 10. 为申购管理添加按钮权限 (如果不存在)
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) 
VALUES 
(4333, '申购管理查询', 4332, 1, '', '', '', 1, 0, 'F', '0', '0', 'inventory:purchase:query', '#', 'admin', NOW(), ''),
(4334, '申购管理新增', 4332, 2, '', '', '', 1, 0, 'F', '0', '0', 'inventory:purchase:add', '#', 'admin', NOW(), ''),
(4335, '申购管理修改', 4332, 3, '', '', '', 1, 0, 'F', '0', '0', 'inventory:purchase:edit', '#', 'admin', NOW(), ''),
(4336, '申购管理删除', 4332, 4, '', '', '', 1, 0, 'F', '0', '0', 'inventory:purchase:remove', '#', 'admin', NOW(), ''),
(4337, '申购管理导出', 4332, 5, '', '', '', 1, 0, 'F', '0', '0', 'inventory:purchase:export', '#', 'admin', NOW(), ''),
(4338, '申购管理打印', 4332, 6, '', '', '', 1, 0, 'F', '0', '0', 'inventory:purchase:print', '#', 'admin', NOW(), ''),
(4339, '申购管理审批', 4332, 7, '', '', '', 1, 0, 'F', '0', '0', 'inventory:purchase:approve', '#', 'admin', NOW(), '');

-- 为超级管理员角色分配新菜单权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu 
WHERE menu_id IN (4341, 4342, 4343, 4351)
AND NOT EXISTS (
    SELECT 1 FROM sys_role_menu rm 
    WHERE rm.role_id = 1 AND rm.menu_id = sys_menu.menu_id
);

-- 显示添加的菜单信息
SELECT 
    m.menu_id,
    m.menu_name,
    CASE 
        WHEN p.menu_name IS NULL THEN '根目录'
        ELSE p.menu_name
    END AS parent_menu,
    m.order_num,
    m.path,
    CASE 
        WHEN m.menu_type = 'M' THEN '目录'
        WHEN m.menu_type = 'C' THEN '菜单'
        WHEN m.menu_type = 'F' THEN '按钮'
        ELSE m.menu_type
    END AS menu_type_desc,
    m.perms
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_id IN (4341, 4342, 4343, 4351)
ORDER BY m.parent_id, m.order_num;