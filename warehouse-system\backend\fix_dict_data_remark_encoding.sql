```
-- 修复sys_dict_data表中remark字段的乱码问题
-- 修复时间：2025-08-21

-- 修复wms_barcode_type类型的乱码
UPDATE sys_dict_data 
SET remark = 'CODE128条码' 
WHERE dict_type = 'wms_barcode_type' AND dict_value = 'CODE128';

UPDATE sys_dict_data 
SET remark = 'EAN13条码' 
WHERE dict_type = 'wms_barcode_type' AND dict_value = 'EAN13';

UPDATE sys_dict_data 
SET remark = 'EAN8条码' 
WHERE dict_type = 'wms_barcode_type' AND dict_value = 'EAN8';

UPDATE sys_dict_data 
SET remark = 'UPC-A条码' 
WHERE dict_type = 'wms_barcode_type' AND dict_value = 'UPC_A';

UPDATE sys_dict_data 
SET remark = 'QR二维码' 
WHERE dict_type = 'wms_barcode_type' AND dict_value = 'QR_CODE';

-- 修复inventory_in_type类型的乱码
UPDATE sys_dict_data 
SET remark = '申购入库' 
WHERE dict_type = 'inventory_in_type' AND dict_value = '5';

-- 查询验证修复结果
SELECT dict_code, dict_label, dict_value, dict_type, remark
FROM sys_dict_data 
WHERE dict_type IN ('wms_barcode_type', 'inventory_in_type');
```