# 仓库管理系统字段定义标准规范

## 文档信息
- **文档名称**: 字段定义标准规范
- **版本**: 1.0
- **创建日期**: 2025-01-30
- **适用系统**: 仓库管理系统 (warehouse-system)
- **维护团队**: 开发团队

## 1. 概述

本文档定义了仓库管理系统中所有数据库字段的标准定义规范，确保整个系统的数据逻辑一致性和可维护性。所有开发人员在创建新表、修改现有字段或编写业务代码时，必须严格遵循本规范。

### 1.1 标准化目标
- 统一字段定义逻辑，消除歧义
- 提高代码可读性和可维护性
- 减少开发错误和理解偏差
- 建立可持续的开发规范

### 1.2 适用范围
- 数据库表结构设计
- 业务逻辑代码编写
- 前端页面状态显示
- API接口数据处理
- 数据字典配置

## 2. 字段定义标准

### 2.1 核心原则

**统一标准**: 0 = 正常/启用/成功/是/开启，1 = 异常/禁用/失败/否/关闭

这个原则适用于所有状态相关字段，确保整个系统的逻辑一致性。

### 2.2 状态字段标准

#### 2.2.1 通用状态字段
```sql
-- 标准定义
status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）'

-- 使用场景
-- 用户状态、角色状态、菜单状态、部门状态等通用启用/禁用状态
```

**取值说明**:
- `'0'`: 正常/启用状态（默认值）
- `'1'`: 停用/禁用状态

**业务逻辑**:
```java
// 查询启用状态的记录
WHERE status = '0'

// 设置为启用状态
entity.setStatus("0");

// 设置为禁用状态
entity.setStatus("1");
```

#### 2.2.2 操作状态字段
```sql
-- 标准定义
operation_status CHAR(1) DEFAULT '0' COMMENT '操作状态（0成功 1失败）'

-- 使用场景
-- 操作日志、任务执行状态、接口调用结果等
```

**取值说明**:
- `'0'`: 操作成功（默认值）
- `'1'`: 操作失败

#### 2.2.3 审核状态字段
```sql
-- 标准定义
audit_status CHAR(1) DEFAULT '0' COMMENT '审核状态（0待审核 1已审核 2已拒绝）'

-- 使用场景
-- 申请单审核、数据审核、流程审批等
```

**取值说明**:
- `'0'`: 待审核（默认值）
- `'1'`: 已审核/已通过
- `'2'`: 已拒绝

#### 2.2.4 业务流程状态字段
```sql
-- 入库状态
inventory_in_status CHAR(1) DEFAULT '0' COMMENT '入库状态（0待入库 1已入库 2入库异常）'

-- 出库状态
inventory_out_status CHAR(1) DEFAULT '0' COMMENT '出库状态（0待出库 1已出库 2出库异常）'

-- 调拨状态
transfer_status CHAR(1) DEFAULT '0' COMMENT '调拨状态（0待调拨 1已调拨 2调拨异常）'
```

### 2.3 布尔字段标准

#### 2.3.1 是否类型字段
```sql
-- 标准定义
is_xxx CHAR(1) DEFAULT '0' COMMENT '是否xxx（0否 1是）'

-- 示例
is_default CHAR(1) DEFAULT '0' COMMENT '是否默认（0否 1是）'
is_enabled CHAR(1) DEFAULT '0' COMMENT '是否启用（0否 1是）'
is_required CHAR(1) DEFAULT '0' COMMENT '是否必填（0否 1是）'
```

**取值说明**:
- `'0'`: 否/关闭（默认值）
- `'1'`: 是/开启

#### 2.3.2 开关类型字段
```sql
-- 标准定义
enable_xxx CHAR(1) DEFAULT '0' COMMENT '是否启用xxx（0关闭 1开启）'

-- 示例
enable_cache CHAR(1) DEFAULT '0' COMMENT '是否启用缓存（0关闭 1开启）'
enable_log CHAR(1) DEFAULT '0' COMMENT '是否启用日志（0关闭 1开启）'
```

### 2.4 删除标记字段

```sql
-- 标准定义（遵循RuoYi框架规范）
del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）'
```

**取值说明**:
- `'0'`: 记录存在（默认值）
- `'2'`: 记录已删除（逻辑删除）

**注意**: 删除标记使用 `'2'` 而不是 `'1'`，这是为了与RuoYi框架保持一致。

## 3. 字段命名规范

### 3.1 命名约定

#### 3.1.1 状态类字段
```sql
status          -- 通用状态
xxx_status      -- 特定状态（如audit_status, operation_status）
```

#### 3.1.2 布尔类字段
```sql
is_xxx          -- 是否类型（如is_enabled, is_default）
enable_xxx      -- 启用类型（如enable_cache, enable_log）
has_xxx         -- 拥有类型（如has_permission, has_children）
```

#### 3.1.3 标记类字段
```sql
xxx_flag        -- 标记类型（如del_flag, sync_flag）
xxx_mark        -- 标记类型（如read_mark, check_mark）
```

### 3.2 命名规则
1. 使用小写字母和下划线
2. 避免使用缩写，保持语义清晰
3. 状态字段统一使用 `status` 后缀
4. 布尔字段使用 `is_`、`enable_`、`has_` 前缀
5. 标记字段使用 `_flag`、`_mark` 后缀

## 4. 数据字典配置标准

### 4.1 字典类型定义

#### 4.1.1 通用状态字典
```sql
-- 字典类型
dict_type: 'sys_normal_disable'
dict_name: '系统状态'

-- 字典数据
dict_value: '0', dict_label: '正常', list_class: 'primary'
dict_value: '1', dict_label: '停用', list_class: 'danger'
```

#### 4.1.2 操作状态字典
```sql
-- 字典类型
dict_type: 'operation_status'
dict_name: '操作状态'

-- 字典数据
dict_value: '0', dict_label: '成功', list_class: 'success'
dict_value: '1', dict_label: '失败', list_class: 'danger'
```

#### 4.1.3 是否状态字典
```sql
-- 字典类型
dict_type: 'sys_yes_no'
dict_name: '是否状态'

-- 字典数据
dict_value: '0', dict_label: '否', list_class: 'info'
dict_value: '1', dict_label: '是', list_class: 'success'
```

### 4.2 字典配置规范
1. 字典类型名称使用小写字母和下划线
2. 正常/成功状态使用绿色系样式 (`success`, `primary`)
3. 异常/失败状态使用红色系样式 (`danger`)
4. 中性状态使用蓝色系样式 (`info`)
5. 默认状态设置 `is_default = 'Y'`

## 5. 代码编写规范

### 5.1 Java代码规范

#### 5.1.1 实体类定义
```java
public class BaseEntity {
    /** 状态（0正常 1停用） */
    private String status = "0";
    
    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag = "0";
    
    /** 是否默认（0否 1是） */
    private String isDefault = "0";
}
```

#### 5.1.2 业务逻辑代码
```java
// 查询启用状态的记录
public List<Entity> selectEnabledEntities() {
    Entity query = new Entity();
    query.setStatus("0"); // 0=启用
    return mapper.selectEntityList(query);
}

// 启用记录
public int enableEntity(Long id) {
    Entity entity = new Entity();
    entity.setId(id);
    entity.setStatus("0"); // 0=启用
    return mapper.updateEntity(entity);
}

// 禁用记录
public int disableEntity(Long id) {
    Entity entity = new Entity();
    entity.setId(id);
    entity.setStatus("1"); // 1=禁用
    return mapper.updateEntity(entity);
}

// 记录操作成功
public void logSuccess(String operation) {
    OperationLog log = new OperationLog();
    log.setOperation(operation);
    log.setOperationStatus("0"); // 0=成功
    logMapper.insertLog(log);
}
```

#### 5.1.3 常量定义
```java
public class StatusConstants {
    /** 正常状态 */
    public static final String STATUS_NORMAL = "0";
    
    /** 停用状态 */
    public static final String STATUS_DISABLE = "1";
    
    /** 操作成功 */
    public static final String OPERATION_SUCCESS = "0";
    
    /** 操作失败 */
    public static final String OPERATION_FAILED = "1";
    
    /** 删除标志-存在 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志-删除 */
    public static final String DEL_FLAG_DELETE = "2";
}
```

### 5.2 SQL查询规范

#### 5.2.1 查询条件
```sql
-- 查询启用状态的记录
SELECT * FROM table_name WHERE status = '0';

-- 查询禁用状态的记录
SELECT * FROM table_name WHERE status = '1';

-- 查询成功的操作
SELECT * FROM operation_log WHERE operation_status = '0';

-- 查询失败的操作
SELECT * FROM operation_log WHERE operation_status = '1';

-- 查询未删除的记录
SELECT * FROM table_name WHERE del_flag = '0';
```

#### 5.2.2 更新操作
```sql
-- 启用记录
UPDATE table_name SET status = '0' WHERE id = ?;

-- 禁用记录
UPDATE table_name SET status = '1' WHERE id = ?;

-- 逻辑删除
UPDATE table_name SET del_flag = '2' WHERE id = ?;
```

### 5.3 前端代码规范

#### 5.3.1 Vue组件状态显示
```vue
<template>
  <!-- 状态显示 -->
  <el-table-column label="状态" align="center" prop="status">
    <template slot-scope="scope">
      <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
    </template>
  </el-table-column>
  
  <!-- 操作按钮 -->
  <el-table-column label="操作" align="center">
    <template slot-scope="scope">
      <el-button
        v-if="scope.row.status === '1'"
        size="mini"
        type="text"
        @click="handleEnable(scope.row)"
      >启用</el-button>
      <el-button
        v-if="scope.row.status === '0'"
        size="mini"
        type="text"
        @click="handleDisable(scope.row)"
      >禁用</el-button>
    </template>
  </el-table-column>
</template>

<script>
export default {
  methods: {
    // 启用记录
    handleEnable(row) {
      const data = {
        id: row.id,
        status: "0" // 0=启用
      };
      updateEntity(data).then(() => {
        this.$modal.msgSuccess("启用成功");
        this.getList();
      });
    },
    
    // 禁用记录
    handleDisable(row) {
      const data = {
        id: row.id,
        status: "1" // 1=禁用
      };
      updateEntity(data).then(() => {
        this.$modal.msgSuccess("禁用成功");
        this.getList();
      });
    }
  }
}
</script>
```

#### 5.3.2 表单验证
```javascript
// 状态字段验证规则
const statusRules = {
  status: [
    { required: true, message: "状态不能为空", trigger: "change" },
    { pattern: /^[01]$/, message: "状态值必须为0或1", trigger: "change" }
  ]
};

// 操作状态验证规则
const operationStatusRules = {
  operationStatus: [
    { required: true, message: "操作状态不能为空", trigger: "change" },
    { pattern: /^[01]$/, message: "操作状态值必须为0或1", trigger: "change" }
  ]
};
```

## 6. 数据库设计规范

### 6.1 表创建模板

```sql
-- 标准表创建模板
CREATE TABLE example_table (
  -- 主键
  id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 业务字段
  name VARCHAR(100) NOT NULL DEFAULT '' COMMENT '名称',
  description TEXT COMMENT '描述',
  
  -- 状态字段
  status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  
  -- 布尔字段
  is_default CHAR(1) DEFAULT '0' COMMENT '是否默认（0否 1是）',
  is_enabled CHAR(1) DEFAULT '0' COMMENT '是否启用（0否 1是）',
  
  -- 删除标记
  del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  
  -- 审计字段
  create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 主键约束
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例表';

-- 创建索引
CREATE INDEX idx_status ON example_table(status);
CREATE INDEX idx_del_flag ON example_table(del_flag);
CREATE INDEX idx_create_time ON example_table(create_time);
```

### 6.2 字段定义规范

#### 6.2.1 数据类型规范
- 状态字段: `CHAR(1)`
- 主键字段: `BIGINT(20) NOT NULL AUTO_INCREMENT`
- 名称字段: `VARCHAR(100)` 或根据业务需要调整长度
- 描述字段: `TEXT` 或 `VARCHAR(500)`
- 时间字段: `DATETIME`

#### 6.2.2 默认值规范
- 状态字段默认值: `'0'` (正常/启用状态)
- 布尔字段默认值: `'0'` (否/关闭状态)
- 删除标记默认值: `'0'` (存在状态)
- 字符串字段默认值: `''` (空字符串)

#### 6.2.3 注释规范
- 必须为每个字段添加清晰的中文注释
- 状态字段注释必须明确说明每个值的含义
- 格式: `字段名称（值1含义 值2含义）`
- 示例: `状态（0正常 1停用）`

## 7. 字段标准检查清单

### 7.1 数据库设计检查

- [ ] 所有状态字段使用 `CHAR(1)` 类型
- [ ] 状态字段默认值为 `'0'`
- [ ] 字段注释清晰说明每个值的含义
- [ ] 遵循统一的命名规范
- [ ] 创建了必要的索引

### 7.2 代码实现检查

- [ ] 查询启用状态使用 `status = '0'`
- [ ] 设置启用状态使用 `setStatus("0")`
- [ ] 操作成功记录使用 `operationStatus = '0'`
- [ ] 前端状态显示逻辑正确
- [ ] 使用了统一的常量定义

### 7.3 数据字典检查

- [ ] 字典类型命名规范
- [ ] 字典数据值符合标准
- [ ] 样式配置合理
- [ ] 默认值设置正确

### 7.4 测试验证检查

- [ ] 状态切换功能正常
- [ ] 查询过滤功能正确
- [ ] 前端显示符合预期
- [ ] 数据一致性验证通过

## 8. 最佳实践

### 8.1 开发建议

1. **使用常量**: 避免在代码中硬编码状态值，使用常量定义
2. **统一验证**: 在实体类中添加字段验证注解
3. **文档同步**: 修改字段定义时同步更新文档
4. **代码审查**: 在代码审查中检查字段使用是否符合规范

### 8.2 维护建议

1. **定期检查**: 定期检查系统中的字段定义是否符合标准
2. **培训新人**: 确保新团队成员了解和遵循字段定义规范
3. **工具支持**: 开发工具或脚本自动检查字段定义合规性
4. **版本控制**: 规范文档纳入版本控制，跟踪变更历史

### 8.3 异常处理

1. **数据迁移**: 修改字段定义时制定详细的数据迁移计划
2. **向下兼容**: 考虑现有数据的兼容性问题
3. **回滚方案**: 准备字段修改的回滚方案
4. **影响评估**: 评估字段修改对现有功能的影响

## 9. 常见问题和解决方案

### 9.1 常见问题

**Q1: 为什么删除标记使用 '2' 而不是 '1'？**
A1: 这是为了与RuoYi框架保持一致，RuoYi框架中删除标记使用 '0'=存在，'2'=删除。

**Q2: 如何处理多状态的业务场景？**
A2: 对于超过两个状态的场景，可以使用 '0', '1', '2' 等多个值，但要确保 '0' 始终表示正常/初始状态。

**Q3: 现有系统如何迁移到新标准？**
A3: 制定详细的数据迁移计划，包括备份、转换、验证和回滚步骤。

### 9.2 解决方案

1. **渐进式迁移**: 分模块、分阶段进行字段标准化
2. **充分测试**: 在测试环境充分验证后再应用到生产环境
3. **监控告警**: 建立监控机制，及时发现不符合标准的字段使用
4. **文档更新**: 及时更新相关文档和培训材料

## 10. 附录

### 10.1 相关文档
- 数据库设计规范
- 代码开发规范
- 前端开发规范
- 测试规范

### 10.2 工具和脚本
- 字段标准检查脚本
- 数据迁移脚本
- 验证测试脚本

### 10.3 联系方式
- 技术负责人: 开发团队
- 文档维护: 架构师
- 问题反馈: 项目经理

---

**文档版本历史**
- v1.0 (2025-01-30): 初始版本，建立字段定义标准规范

**审核状态**: 已审核
**生效日期**: 2025-01-30
**下次审核**: 2025-07-30