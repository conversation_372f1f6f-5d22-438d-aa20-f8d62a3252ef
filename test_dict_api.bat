@echo off
echo 测试字典API功能...
echo.

echo 1. 测试入库类型字典数据
curl -X GET "http://localhost:8080/system/dict/data/type/inventory_in_type" -H "Content-Type: application/json"
echo.
echo.

echo 2. 测试入库状态字典数据
curl -X GET "http://localhost:8080/system/dict/data/type/inventory_in_status" -H "Content-Type: application/json"
echo.
echo.

echo 3. 测试出库类型字典数据
curl -X GET "http://localhost:8080/system/dict/data/type/inventory_out_type" -H "Content-Type: application/json"
echo.
echo.

echo 4. 测试出库状态字典数据
curl -X GET "http://localhost:8080/system/dict/data/type/inventory_out_status" -H "Content-Type: application/json"
echo.
echo.

echo 5. 测试调拨状态字典数据
curl -X GET "http://localhost:8080/system/dict/data/type/inventory_transfer_status" -H "Content-Type: application/json"
echo.
echo.

echo 字典API测试完成！
pause