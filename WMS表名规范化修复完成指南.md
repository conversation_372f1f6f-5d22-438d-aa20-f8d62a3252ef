# WMS表名规范化修复完成指南

## 概述

本指南详细说明了WMS系统中物品相关数据库表名规范化的修复过程，统一使用`wms_`前缀和简化的表名规范。

## 修复内容

### 表名变更对照

| 原表名 | 新表名 | 说明 |
|--------|--------|------|
| `product_barcode` | `wms_barcode` | 物品条码表 |
| - | `wms_barcode_template` | 物品条码模板表（新增） |
| `wms_product_category` | `wms_category` | 物品分类表 |
| `wms_product_specification` | `wms_specification` | 物品规格表 |
| `wms_product_unit` | `wms_unit` | 物品单位表 |

### 修复的代码文件

#### 后端实体类
- ✅ `WmsBarcode.java` - 新创建的物品条码实体类
- ✅ `WmsBarcodeTemplate.java` - 新创建的条码模板实体类
- ✅ `ProductCategory.java` - 更新注释中的表名
- ✅ `ProductSpecification.java` - 已使用正确表名
- ✅ `ProductUnit.java` - 已使用正确表名

#### 数据访问层
- ✅ `WmsBarcodeMapper.java` + `WmsBarcodeMapper.xml` - 新创建
- ✅ `WmsBarcodeTemplateMapper.java` + `WmsBarcodeTemplateMapper.xml` - 新创建
- ✅ `ProductCategoryMapper.xml` - 已使用 `wms_category`
- ✅ `ProductSpecificationMapper.xml` - 已使用 `wms_specification`
- ✅ `ProductUnitMapper.xml` - 已使用 `wms_unit`

#### 业务逻辑层
- ✅ `IWmsBarcodeService.java` - 新创建的条码服务接口
- ✅ `WmsBarcodeServiceImpl.java` - 新创建的条码服务实现

#### 控制器层
- ✅ `ProductBarcodeController.java` - 已更新使用新的WmsBarcode实体

## 数据库表结构

### 1. wms_barcode（物品条码表）
```sql
CREATE TABLE `wms_barcode` (
  `barcode_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '条码ID',
  `product_id` bigint(20) NOT NULL COMMENT '物品ID',
  `product_name` varchar(100) DEFAULT NULL COMMENT '物品名称',
  `barcode_content` varchar(200) NOT NULL COMMENT '条码内容',
  `barcode_type` varchar(20) NOT NULL DEFAULT 'CODE128' COMMENT '条码类型',
  `barcode_image` varchar(500) DEFAULT NULL COMMENT '条码图片路径',
  `template_id` bigint(20) DEFAULT NULL COMMENT '条码模板ID',
  `is_main` char(1) DEFAULT '0' COMMENT '是否主条码(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`barcode_id`),
  UNIQUE KEY `uk_barcode_content` (`barcode_content`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_barcode_type` (`barcode_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品条码表';
```

### 2. wms_barcode_template（物品条码模板表）
```sql
CREATE TABLE `wms_barcode_template` (
  `template_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_type` varchar(20) NOT NULL COMMENT '模板类型',
  `template_width` int(11) DEFAULT 100 COMMENT '模板宽度(mm)',
  `template_height` int(11) DEFAULT 50 COMMENT '模板高度(mm)',
  `barcode_width` int(11) DEFAULT 80 COMMENT '条码宽度(mm)',
  `barcode_height` int(11) DEFAULT 30 COMMENT '条码高度(mm)',
  `font_size` int(11) DEFAULT 12 COMMENT '字体大小',
  `show_text` char(1) DEFAULT '1' COMMENT '是否显示文字(0否 1是)',
  `show_product_name` char(1) DEFAULT '1' COMMENT '是否显示物品名称(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品条码模板表';
```

### 3. wms_category（物品分类表）
```sql
CREATE TABLE `wms_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `category_name` varchar(30) NOT NULL COMMENT '分类名称',
  `category_code` varchar(30) DEFAULT NULL COMMENT '分类编码',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_code` (`category_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品分类表';
```

### 4. wms_specification（物品规格表）
```sql
CREATE TABLE `wms_specification` (
  `spec_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `spec_name` varchar(50) NOT NULL COMMENT '规格名称',
  `spec_code` varchar(30) DEFAULT NULL COMMENT '规格编码',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`spec_id`),
  UNIQUE KEY `uk_spec_code` (`spec_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品规格表';
```

### 5. wms_unit（物品单位表）
```sql
CREATE TABLE `wms_unit` (
  `unit_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '单位ID',
  `unit_name` varchar(50) NOT NULL COMMENT '单位名称',
  `unit_code` varchar(30) DEFAULT NULL COMMENT '单位编码',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`unit_id`),
  UNIQUE KEY `uk_unit_code` (`unit_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品单位表';
```

## 执行修复

### 方法一：综合修复（推荐）
```bash
# 执行综合修复脚本
fix_all_wms_tables_complete.bat

# 验证修复结果
test_all_wms_tables_fix.bat
```

### 方法二：分步修复
```bash
# 1. 修复条码表
fix_wms_barcode_table_complete.bat
test_wms_barcode_fix.bat

# 2. 修复其他表
fix_wms_table_names_complete.bat
test_wms_table_names_fix.bat
```

### 重新编译和启动
```bash
# 重新编译后端项目
cd C:\CKGLXT\warehouse-system\backend
mvn clean compile

# 重启后端服务
cd C:\CKGLXT\warehouse-system
start-system.bat
```

## API接口说明

### 物品条码相关接口

#### 基础CRUD
- `GET /product/barcode/list` - 查询条码列表
- `GET /product/barcode/{barcodeId}` - 获取条码详情
- `POST /product/barcode` - 新增条码
- `PUT /product/barcode` - 修改条码
- `DELETE /product/barcode/{barcodeIds}` - 删除条码

#### 高级功能
- `POST /product/barcode/generate` - 生成单个条码
- `POST /product/barcode/batch/generate` - 批量生成条码
- `GET /product/barcode/image/{barcodeId}` - 获取条码图片
- `GET /product/barcode/print/{barcodeId}` - 打印条码
- `GET /product/barcode/template/list` - 查询条码模板列表
- `GET /product/barcode/export` - 导出条码数据

### 物品分类相关接口
- `GET /product/category/list` - 查询分类列表
- `GET /product/category/{categoryId}` - 获取分类详情
- `POST /product/category` - 新增分类
- `PUT /product/category` - 修改分类
- `DELETE /product/category/{categoryIds}` - 删除分类

### 物品规格相关接口
- `GET /product/specification/list` - 查询规格列表
- `GET /product/specification/{specId}` - 获取规格详情
- `POST /product/specification` - 新增规格
- `PUT /product/specification` - 修改规格
- `DELETE /product/specification/{specIds}` - 删除规格

### 物品单位相关接口
- `GET /product/unit/list` - 查询单位列表
- `GET /product/unit/{unitId}` - 获取单位详情
- `POST /product/unit` - 新增单位
- `PUT /product/unit` - 修改单位
- `DELETE /product/unit/{unitIds}` - 删除单位

## 默认数据

### 条码模板
- 标准模板 (CODE128, 100x50mm)
- 小型模板 (CODE128, 60x30mm)
- 二维码模板 (QR_CODE, 50x50mm)

### 物品分类
- 电子产品
  - 手机
  - 电脑
- 办公用品
  - 文具
  - 打印用品
- 生活用品

### 物品规格
- 标准规格、小号、中号、大号、特大号
- 迷你、加长、加宽

### 物品单位
- 个、台、箱、包、袋、瓶、支、张、本、套
- 米、千克、升、吨

### 条码类型字典
- CODE128（默认）
- EAN13
- EAN8
- UPC_A
- QR_CODE（二维码）

## 功能特性

### 物品条码管理
- ✅ 条码生成（单个/批量）
- ✅ 条码模板管理
- ✅ 多种条码类型支持
- ✅ 条码打印功能
- ✅ 条码图片生成
- ✅ 条码导出功能

### 物品分类管理
- ✅ 树形结构分类
- ✅ 分类层级管理
- ✅ 分类编码唯一性
- ✅ 软删除机制

### 物品规格管理
- ✅ 规格信息维护
- ✅ 规格编码唯一性
- ✅ 状态管理

### 物品单位管理
- ✅ 单位信息维护
- ✅ 单位编码唯一性
- ✅ 状态管理

## 数据关联

### 外键关系
- `wms_product.category_id` → `wms_category.category_id`
- `wms_product.spec_id` → `wms_specification.spec_id`
- `wms_product.unit_id` → `wms_unit.unit_id`
- `wms_barcode.product_id` → `wms_product.product_id`
- `wms_barcode.template_id` → `wms_barcode_template.template_id`

### 数据一致性
修复脚本会自动更新`wms_product`表中的外键引用，确保数据关联的正确性。

## 权限配置

### 条码相关权限
- `product:barcode:list` - 查看条码列表
- `product:barcode:query` - 查看条码详情
- `product:barcode:add` - 新增条码
- `product:barcode:edit` - 修改条码
- `product:barcode:remove` - 删除条码
- `product:barcode:generate` - 生成条码
- `product:barcode:print` - 打印条码
- `product:barcode:export` - 导出条码

### 分类相关权限
- `product:category:list` - 查看分类列表
- `product:category:query` - 查看分类详情
- `product:category:add` - 新增分类
- `product:category:edit` - 修改分类
- `product:category:remove` - 删除分类

### 规格相关权限
- `product:specification:list` - 查看规格列表
- `product:specification:query` - 查看规格详情
- `product:specification:add` - 新增规格
- `product:specification:edit` - 修改规格
- `product:specification:remove` - 删除规格

### 单位相关权限
- `product:unit:list` - 查看单位列表
- `product:unit:query` - 查看单位详情
- `product:unit:add` - 新增单位
- `product:unit:edit` - 修改单位
- `product:unit:remove` - 删除单位

## 故障排除

### 1. 编译错误
**症状**: Maven编译失败
**解决方案**:
- 检查实体类导入是否正确
- 确认Mapper接口和XML文件匹配
- 验证服务接口实现是否完整

### 2. 数据库连接错误
**症状**: 无法连接数据库
**解决方案**:
- 检查数据库服务状态
- 验证连接参数（主机、端口、用户名、密码）
- 确认数据库权限设置

### 3. API调用失败
**症状**: 接口返回404或500错误
**解决方案**:
- 确认后端服务正常启动
- 检查权限配置是否正确
- 验证请求参数格式

### 4. 数据迁移问题
**症状**: 旧数据未正确迁移
**解决方案**:
- 重新运行修复脚本
- 检查旧表是否存在
- 手动验证数据完整性

## 测试验证

### 数据库测试
```sql
-- 验证表结构
DESCRIBE wms_barcode;
DESCRIBE wms_category;
DESCRIBE wms_specification;
DESCRIBE wms_unit;

-- 验证数据
SELECT COUNT(*) FROM wms_barcode;
SELECT COUNT(*) FROM wms_category WHERE del_flag = '0';
SELECT COUNT(*) FROM wms_specification;
SELECT COUNT(*) FROM wms_unit;

-- 验证关联
SELECT COUNT(*) FROM wms_product p 
INNER JOIN wms_category c ON p.category_id = c.category_id;
```

### API测试
使用Postman或curl测试各个API接口的功能。

### 功能测试
1. 登录系统
2. 测试物品条码管理功能
3. 测试物品分类管理功能
4. 测试物品规格管理功能
5. 测试物品单位管理功能
6. 测试物品信息与各表的关联

## 总结

通过本次WMS表名规范化修复，实现了以下目标：

### ✅ 完成的工作
1. **表名规范化**: 统一使用`wms_`前缀和简化的表名
2. **代码更新**: 创建了完整的实体类、服务和控制器
3. **数据迁移**: 安全地迁移了旧表数据
4. **默认数据**: 插入了完整的默认数据和字典数据
5. **关联更新**: 更新了外键引用确保数据一致性

### 🚀 系统优势
1. **命名规范**: 遵循统一的命名规范，提高代码可维护性
2. **功能完整**: 支持条码生成、模板管理、分类管理等完整功能
3. **数据安全**: 包含完整的数据备份和迁移机制
4. **扩展性强**: 易于扩展新功能和新的条码类型

### 📋 后续维护
- 定期备份数据库
- 监控系统性能
- 根据业务需求扩展功能
- 保持代码和文档的同步更新

如有任何问题，请参考故障排除部分或联系开发团队。