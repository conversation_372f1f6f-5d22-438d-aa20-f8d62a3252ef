package com.wanyu.web.controller.system;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.SysPermissionTemplate;
import com.wanyu.system.service.ISysPermissionTemplateService;

/**
 * 权限模板Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/template")
public class SysPermissionTemplateController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(SysPermissionTemplateController.class);
    
    @Autowired
    private ISysPermissionTemplateService sysPermissionTemplateService;

    /**
     * 查询权限模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:template:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysPermissionTemplate sysPermissionTemplate)
    {
        startPage();
        List<SysPermissionTemplate> list = sysPermissionTemplateService.selectSysPermissionTemplateList(sysPermissionTemplate);
        return getDataTable(list);
    }

    /**
     * 导出权限模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:template:export')")
    @Log(title = "权限模板", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, SysPermissionTemplate sysPermissionTemplate)
    {
        List<SysPermissionTemplate> list = sysPermissionTemplateService.selectSysPermissionTemplateList(sysPermissionTemplate);
        ExcelUtil<SysPermissionTemplate> util = new ExcelUtil<SysPermissionTemplate>(SysPermissionTemplate.class);
        util.exportExcel(response, list, "权限模板数据");
    }

    /**
     * 获取权限模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:template:query')")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId)
    {
        SysPermissionTemplate template = sysPermissionTemplateService.selectSysPermissionTemplateByTemplateId(templateId);
        // 如果是菜单权限类型，获取菜单ID列表
        if (template != null && "1".equals(template.getTemplateType())) {
            List<Long> menuIds = sysPermissionTemplateService.selectTemplateMenuIds(templateId);
            template.setMenuIds(menuIds.toArray(new Long[menuIds.size()]));
        }
        return success(template);
    }

    /**
     * 新增权限模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "权限模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysPermissionTemplate sysPermissionTemplate)
    {
        return toAjax(sysPermissionTemplateService.insertSysPermissionTemplate(sysPermissionTemplate));
    }

    /**
     * 修改权限模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "权限模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysPermissionTemplate sysPermissionTemplate)
    {
        return toAjax(sysPermissionTemplateService.updateSysPermissionTemplate(sysPermissionTemplate));
    }

    /**
     * 删除权限模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:remove')")
    @Log(title = "权限模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds)
    {
        return toAjax(sysPermissionTemplateService.deleteSysPermissionTemplateByTemplateIds(templateIds));
    }
    
    /**
     * 获取权限模板权限
     */
    @PreAuthorize("@ss.hasPermi('system:template:query')")
    @GetMapping(value = "/permissions/{templateId}")
    public AjaxResult getPermissions(@PathVariable("templateId") Long templateId)
    {
        List<String> permissions = sysPermissionTemplateService.selectPermissionsByTemplateId(templateId);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("permissions", permissions.toArray(new String[permissions.size()]));
        return ajax;
    }
    
    /**
     * 应用权限模板到角色
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "权限模板", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/apply/role/{templateId}/{roleId}")
    public AjaxResult applyToRole(@PathVariable("templateId") Long templateId, @PathVariable("roleId") Long roleId)
    {
        return toAjax(sysPermissionTemplateService.applyTemplateToRole(templateId, roleId));
    }
    
    /**
     * 应用权限模板到用户
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "权限模板", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/apply/user/{templateId}/{userId}")
    public AjaxResult applyToUser(@PathVariable("templateId") Long templateId, @PathVariable("userId") Long userId)
    {
        return toAjax(sysPermissionTemplateService.applyTemplateToUser(templateId, userId));
    }
    
    /**
     * 批量应用权限模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "批量应用权限模板", businessType = BusinessType.UPDATE)
    @PostMapping("/batchApply")
    public AjaxResult batchApply(@RequestBody Map<String, Object> request)
    {
        try {
            Long templateId = Long.valueOf(request.get("templateId").toString());
            String targetType = request.get("targetType").toString(); // 1: 角色, 2: 用户
            
            // 处理 targetIds 的类型转换，从 Integer 转换为 Long
            @SuppressWarnings("unchecked")
            List<Object> targetIdsRaw = (List<Object>) request.get("targetIds");
            List<Long> targetIds = new ArrayList<>();
            if (targetIdsRaw != null) {
                for (Object id : targetIdsRaw) {
                    if (id instanceof Integer) {
                        targetIds.add(((Integer) id).longValue());
                    } else if (id instanceof Long) {
                        targetIds.add((Long) id);
                    } else {
                        targetIds.add(Long.valueOf(id.toString()));
                    }
                }
            }
            
            String applyType = request.get("applyType").toString(); // 1: 追加, 2: 替换
            
            if (targetIds == null || targetIds.isEmpty()) {
                return error("请选择要应用的目标对象");
            }
            
            int successCount = 0;
            
            if ("1".equals(targetType)) {
                // 应用到角色
                for (Long roleId : targetIds) {
                    successCount += sysPermissionTemplateService.applyTemplateToRole(templateId, roleId);
                }
            } else if ("2".equals(targetType)) {
                // 应用到用户
                for (Long userId : targetIds) {
                    successCount += sysPermissionTemplateService.applyTemplateToUser(templateId, userId);
                }
            }
            
            String message = String.format("成功应用到 %d 个%s", successCount, "1".equals(targetType) ? "角色" : "用户");
            return success(message);
            
        } catch (Exception e) {
            logger.error("批量应用权限模板失败", e);
            return error("批量应用权限模板失败: " + e.getMessage());
        }
    }
}