package com.wanyu.system.service.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.wanyu.common.constant.Constants;
import com.wanyu.common.core.domain.entity.SysRole;
import com.wanyu.common.core.domain.entity.SysUser;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.system.domain.SysPermissionLog;
import com.wanyu.system.mapper.SysMenuMapper;
import com.wanyu.system.mapper.SysPermissionLogMapper;
import com.wanyu.system.mapper.SysRoleMapper;
import com.wanyu.system.mapper.SysUserMapper;
import com.wanyu.system.mapper.SysUserRoleMapper;
import com.wanyu.system.service.ISysPermissionTemplateService;
import com.wanyu.system.service.IUnifiedPermissionService;

/**
 * 统一权限管理服务实现
 *
 * <AUTHOR>
 */
@Service
public class UnifiedPermissionServiceImpl implements IUnifiedPermissionService {

    private static final Logger log = LoggerFactory.getLogger(UnifiedPermissionServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysPermissionLogMapper permissionLogMapper;

    /**
     * 获取用户的所有权限（菜单权限、按钮权限）
     */
    @Override
    public Set<String> getUserPermissions(Long userId) {
        Set<String> permissions = new HashSet<>();

        // 管理员拥有所有权限
        if (SysUser.isAdmin(userId)) {
            permissions.add("*:*:*");
        } else {
            // 获取用户角色
            List<SysRole> roles = roleMapper.selectRolePermissionByUserId(userId);
            // 获取角色权限
            for (SysRole role : roles) {
                List<String> rolePerms = menuMapper.selectMenuPermsByRoleId(role.getRoleId());
                if (rolePerms != null) {
                    permissions.addAll(rolePerms);
                }
            }
        }

        return permissions;
    }

    /**
     * 获取用户的菜单权限
     */
    @Override
    public Set<String> getMenuPermissions(Long userId) {
        Set<String> permissions = getUserPermissions(userId);
        Set<String> menuPermissions = new HashSet<>();

        for (String permission : permissions) {
            if (permission.endsWith(":list") || permission.endsWith(":query")) {
                menuPermissions.add(permission);
            }
        }

        return menuPermissions;
    }

    /**
     * 获取用户的按钮权限
     */
    @Override
    public Set<String> getButtonPermissions(Long userId) {
        Set<String> permissions = getUserPermissions(userId);
        Set<String> buttonPermissions = new HashSet<>();

        for (String permission : permissions) {
            if (permission.endsWith(":add") || permission.endsWith(":edit") ||
                permission.endsWith(":remove") || permission.endsWith(":export") ||
                permission.endsWith(":import")) {
                buttonPermissions.add(permission);
            }
        }

        return buttonPermissions;
    }

    /**
     * 获取用户的数据权限
     */
    @Override
    public Set<String> getDataPermissions(Long userId) {
        return new HashSet<>();
    }

    /**
     * 获取用户的API权限
     */
    @Override
    public Set<String> getApiPermissions(Long userId) {
        return new HashSet<>();
    }

    /**
     * 获取用户的仓库权限
     */
    @Override
    public Set<Long> getWarehousePermissions(Long userId) {
        return new HashSet<>();
    }

    /**
     * 检查用户是否具有指定权限
     */
    @Override
    public boolean hasPermission(Long userId, String permission) {
        if (userId == null || StringUtils.isEmpty(permission)) {
            log.error("参数校验失败: userId或permission为空");
            return false;
        }
        try {
        logPermissionOperation(userId, "CHECK", permission, null);
        Set<String> permissions = getUserPermissions(userId);
        boolean hasPermission = hasPermissions(permissions, permission);
        logPermissionOperation(userId, "CHECK", permission, hasPermission ? "0" : "1");
        return hasPermission;
        } catch (Exception e) {
            log.error("权限校验异常", e);
            logPermissionOperation(userId, "CHECK", permission, "1");
            return false;
        }
    }

    /**
     * 判断是否包含权限
     *
     * @param permissions 权限列表
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    private boolean hasPermissions(Set<String> permissions, String permission) {
        return permissions.contains(Constants.ALL_PERMISSION) || permissions.contains(permission);
    }

    /**
     * 检查用户是否具有指定数据权限
     */
    @Override
    public boolean hasDataPermission(Long userId, String dataPermission) {
        return false;
    }

    /**
     * 检查用户是否具有指定API权限
     */
    @Override
    public boolean hasApiPermission(Long userId, String apiPermission) {
        return false;
    }

    /**
     * 检查用户是否具有指定仓库的权限
     */
    @Override
    public boolean hasWarehousePermission(Long userId, Long warehouseId) {
        return false;
    }

    /**
     * 应用权限模板到用户
     */
    @Override
    @Transactional
    public int applyTemplateToUser(Long templateId, Long userId) {
        return 0;
    }

    /**
     * 应用权限模板到角色
     */
    @Override
    @Transactional
    public int applyTemplateToRole(Long templateId, Long roleId) {
        return 0;
    }

    /**
     * 获取用户的权限概览
     */
    @Override
    public Map<String, Object> getUserPermissionOverview(Long userId) {
        Map<String, Object> overview = new HashMap<>();

        // 获取用户信息
        SysUser user = userMapper.selectUserById(userId);
        if (user == null) {
            return overview;
        }

        // 获取用户角色
        List<SysRole> roles = roleMapper.selectRolePermissionByUserId(userId);

        // 获取用户权限
        Set<String> permissions = getUserPermissions(userId);

        // 组装概览数据
        overview.put("user", user);
        overview.put("roles", roles);
        overview.put("permissions", permissions);

        return overview;
    }

    /**
     * 获取角色的权限概览
     */
    @Override
    public Map<String, Object> getRolePermissionOverview(Long roleId) {
        return new HashMap<>();
    }

    /**
     * 刷新用户权限缓存
     */
    @Override
    public void refreshUserPermissionCache(Long userId) {
        // 空实现
    }

    /**
     * 刷新所有用户权限缓存
     */
    @Override
    public void refreshAllUserPermissionCache() {
        // 空实现
    }

    /**
     * 记录权限操作日志
     */
    @Override
    public void logPermissionOperation(Long userId, String operation, String permission, String result) {
        try {
            SysPermissionLog log = new SysPermissionLog();
            log.setUserId(userId);
            log.setPermissionType("1"); // 默认为菜单权限
            log.setPermission(permission);
            log.setMethod(operation);
            log.setResult(result != null ? result : "0");
            log.setOperTime(new java.util.Date());

            permissionLogMapper.insertSysPermissionLog(log);
        } catch (Exception e) {
            log.error("记录权限操作日志失败", e);
        }
    }
}
