@echo off
chcp 65001 >nul
echo ========================================
echo 修复字典数据404错误
echo ========================================

echo.
echo 1. 执行字典数据修复...
mysql -h localhost -P 3306 -u root -p123456 warehouse_system < fix_missing_dict_data.sql
if %errorlevel% equ 0 (
    echo 字典数据修复完成
) else (
    echo 字典数据修复失败，请检查连接
    pause
    exit /b 1
)

echo.
echo 2. 验证字典数据...
mysql -h localhost -P 3306 -u root -p123456 -e "USE warehouse_system; SELECT dict_type, dict_name FROM sys_dict_type WHERE dict_type IN ('product_attribute_type', 'sys_yes_no', 'product_status', 'product_unit', 'inventory_status');"

echo.
echo 3. 检查字典数据项...
mysql -h localhost -P 3306 -u root -p123456 -e "USE warehouse_system; SELECT COUNT(*) as dict_data_count FROM sys_dict_data WHERE dict_type IN ('product_attribute_type', 'sys_yes_no', 'product_status', 'product_unit', 'inventory_status');"

echo.
echo ========================================
echo 字典数据404错误修复完成！
echo ========================================
echo.
echo 现在可以重新访问前端页面，字典数据应该正常加载了。
echo.
pause