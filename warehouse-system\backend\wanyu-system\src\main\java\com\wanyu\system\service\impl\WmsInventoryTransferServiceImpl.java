package com.wanyu.system.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.wanyu.common.exception.ServiceException;
import com.wanyu.common.utils.DateUtils;
import com.wanyu.common.utils.SecurityUtils;
import com.wanyu.common.utils.StringUtils;
import com.wanyu.common.utils.spring.SpringUtils;
import com.wanyu.system.mapper.WmsInventoryTransferMapper;
import com.wanyu.system.mapper.WmsInventoryTransferDetailMapper;
import com.wanyu.system.domain.WmsInventory;
import com.wanyu.system.domain.WmsInventoryTransfer;
import com.wanyu.system.domain.WmsInventoryTransferDetail;
import com.wanyu.system.service.IWmsInventoryTransferService;
import com.wanyu.system.service.IWmsInventoryService;
import com.wanyu.system.service.IWmsInventoryLogService;
import com.wanyu.common.annotation.WarehouseScope;
import com.wanyu.common.core.domain.model.LoginUser;

/**
 * 库存调拨Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class WmsInventoryTransferServiceImpl implements IWmsInventoryTransferService
{
    @Autowired
    private WmsInventoryTransferMapper wmsInventoryTransferMapper;

    @Autowired
    private WmsInventoryTransferDetailMapper wmsInventoryTransferDetailMapper;

    @Autowired
    private IWmsInventoryService wmsInventoryService;

    @Autowired
    private com.wanyu.system.service.ISysUserService userService;

    /**
     * 查询库存调拨
     *
     * @param transferId 库存调拨主键
     * @return 库存调拨
     */
    @Override
    public WmsInventoryTransfer selectWmsInventoryTransferByTransferId(Long transferId)
    {
        WmsInventoryTransfer wmsInventoryTransfer = wmsInventoryTransferMapper.selectWmsInventoryTransferByTransferId(transferId);
        if (wmsInventoryTransfer != null)
        {
            List<WmsInventoryTransferDetail> details = wmsInventoryTransferDetailMapper.selectWmsInventoryTransferDetailByTransferId(transferId);
            wmsInventoryTransfer.setDetails(details);
        }
        return wmsInventoryTransfer;
    }

    /**
     * 查询库存调拨列表
     *
     * @param wmsInventoryTransfer 库存调拨
     * @return 库存调拨
     */
    @Override
    @WarehouseScope(warehouseAlias = "w")
    public List<WmsInventoryTransfer> selectWmsInventoryTransferList(WmsInventoryTransfer wmsInventoryTransfer)
    {
        // 获取当前登录用户，加容错，导出接口无token时不过滤
        boolean isAdmin = true;
        Long userId = null;
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && loginUser.getUser() != null) {
                userId = loginUser.getUserId();
                isAdmin = userId == 1L
                    || loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()));
            }
        } catch (Exception e) {
            // 获取不到用户信息，默认超级管理员逻辑（不过滤）
            isAdmin = true;
        }
        if (!isAdmin && userId != null) {
            // 非超级管理员，只返回授权仓库的调拨单（调出或调入仓库都在授权范围内）
            wmsInventoryTransfer.getParams().put("warehouseScope", " AND (t.from_warehouse_id IN (SELECT warehouse_id FROM sys_user_warehouse WHERE user_id = " + userId + ") OR t.to_warehouse_id IN (SELECT warehouse_id FROM sys_user_warehouse WHERE user_id = " + userId + ")) ");
        }
        List<WmsInventoryTransfer> transferList = wmsInventoryTransferMapper.selectWmsInventoryTransferList(wmsInventoryTransfer);
        // 为每个调拨单填充明细并转换用户名
        if (transferList != null && !transferList.isEmpty()) {
            for (WmsInventoryTransfer transfer : transferList) {
                List<WmsInventoryTransferDetail> details = wmsInventoryTransferDetailMapper.selectWmsInventoryTransferDetailByTransferId(transfer.getTransferId());
                transfer.setDetails(details);
                convertUserNames(transfer);
            }
        }
        return transferList;
    }

    /**
     * 新增库存调拨
     *
     * @param wmsInventoryTransfer 库存调拨
     * @return 结果
     */
    @Transactional
    @Override
    public int insertWmsInventoryTransfer(WmsInventoryTransfer wmsInventoryTransfer)
    {
        // 插入调拨单前，校验每个明细的调出仓库库存
        if (StringUtils.isNotNull(wmsInventoryTransfer.getDetails()) && wmsInventoryTransfer.getDetails().size() > 0)
        {
            List<WmsInventoryTransferDetail> details = wmsInventoryTransfer.getDetails();
            for (WmsInventoryTransferDetail detail : details)
            {
                if (!wmsInventoryService.checkStockAvailable(detail.getProductId(), wmsInventoryTransfer.getFromWarehouseId(), detail.getQuantity())) {
                    BigDecimal currentStock = wmsInventoryService.getCurrentStock(detail.getProductId(), wmsInventoryTransfer.getFromWarehouseId());
                    throw new RuntimeException("调出仓库库存不足，物品[" + detail.getProductName() + "]，当前库存：" + currentStock + "，需要调拨：" + detail.getQuantity());
                }
            }
        }
        // 生成调拨单号
        String transferCode = generateTransferCode();
        wmsInventoryTransfer.setTransferCode(transferCode);

        // 设置调拨时间
        if (wmsInventoryTransfer.getTransferTime() == null)
        {
            wmsInventoryTransfer.setTransferTime(DateUtils.getNowDate());
        }

        // 设置状态为未审核（如果没有指定状态）
        if (wmsInventoryTransfer.getStatus() == null) {
            wmsInventoryTransfer.setStatus("0");
        }

        // 如果状态为已审核，自动设置审核信息
        if ("1".equals(wmsInventoryTransfer.getStatus())) {
            wmsInventoryTransfer.setAuditBy(SecurityUtils.getUsername());
            wmsInventoryTransfer.setAuditTime(DateUtils.getNowDate());
        }

        // 设置创建者和创建时间
        wmsInventoryTransfer.setCreateBy(SecurityUtils.getUsername());
        wmsInventoryTransfer.setCreateTime(DateUtils.getNowDate());

        // 插入调拨单
        int rows = wmsInventoryTransferMapper.insertWmsInventoryTransfer(wmsInventoryTransfer);

        // 插入调拨单明细
        if (StringUtils.isNotNull(wmsInventoryTransfer.getDetails()) && wmsInventoryTransfer.getDetails().size() > 0)
        {
            List<WmsInventoryTransferDetail> details = wmsInventoryTransfer.getDetails();
            for (WmsInventoryTransferDetail detail : details)
            {
                detail.setTransferId(wmsInventoryTransfer.getTransferId());
            }
            wmsInventoryTransferDetailMapper.batchInsertWmsInventoryTransferDetail(details);
        }

        return rows;
    }

    /**
     * 修改库存调拨
     *
     * @param wmsInventoryTransfer 库存调拨
     * @return 结果
     */
    @Transactional
    @Override
    public int updateWmsInventoryTransfer(WmsInventoryTransfer wmsInventoryTransfer)
    {
        // 严格检查调拨单状态
        WmsInventoryTransfer originalTransfer = wmsInventoryTransferMapper.selectWmsInventoryTransferByTransferId(wmsInventoryTransfer.getTransferId());
        if (originalTransfer == null) {
            throw new ServiceException("调拨单不存在");
        }
        if (!"0".equals(originalTransfer.getStatus())) {
            throw new ServiceException("已审核的调拨单不能修改");
        }
        
        // 强制重置状态为未审核
        wmsInventoryTransfer.setStatus("0");
        
        // 防止修改关键字段
        wmsInventoryTransfer.setFromWarehouseId(originalTransfer.getFromWarehouseId());
        wmsInventoryTransfer.setToWarehouseId(originalTransfer.getToWarehouseId());
        wmsInventoryTransfer.setTransferTime(originalTransfer.getTransferTime());

        // 设置更新者和更新时间
        wmsInventoryTransfer.setUpdateBy(SecurityUtils.getUsername());
        wmsInventoryTransfer.setUpdateTime(DateUtils.getNowDate());

        // 更新调拨单
        int rows = wmsInventoryTransferMapper.updateWmsInventoryTransfer(wmsInventoryTransfer);

        // 删除原有明细
        wmsInventoryTransferDetailMapper.deleteWmsInventoryTransferDetailByTransferId(wmsInventoryTransfer.getTransferId());

        // 插入新的明细
        if (StringUtils.isNotNull(wmsInventoryTransfer.getDetails()) && wmsInventoryTransfer.getDetails().size() > 0)
        {
            List<WmsInventoryTransferDetail> details = wmsInventoryTransfer.getDetails();
            for (WmsInventoryTransferDetail detail : details)
            {
                detail.setTransferId(wmsInventoryTransfer.getTransferId());
            }
            wmsInventoryTransferDetailMapper.batchInsertWmsInventoryTransferDetail(details);
        }

        return rows;
    }

    /**
     * 批量删除库存调拨
     *
     * @param transferIds 需要删除的库存调拨主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteWmsInventoryTransferByTransferIds(Long[] transferIds)
    {
        // 删除调拨单明细
        for (Long transferId : transferIds)
        {
            wmsInventoryTransferDetailMapper.deleteWmsInventoryTransferDetailByTransferId(transferId);
        }

        // 删除调拨单
        return wmsInventoryTransferMapper.deleteWmsInventoryTransferByTransferIds(transferIds);
    }

    /**
     * 删除库存调拨信息
     *
     * @param transferId 库存调拨主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteWmsInventoryTransferByTransferId(Long transferId)
    {
        // 删除调拨单明细
        wmsInventoryTransferDetailMapper.deleteWmsInventoryTransferDetailByTransferId(transferId);

        // 删除调拨单
        return wmsInventoryTransferMapper.deleteWmsInventoryTransferByTransferId(transferId);
    }

    /**
     * 审核库存调拨
     *
     * @param wmsInventoryTransfer 库存调拨
     * @return 结果
     */
    @Transactional
    @Override
    public int auditWmsInventoryTransfer(WmsInventoryTransfer wmsInventoryTransfer)
    {
        // 设置更新者和更新时间
        wmsInventoryTransfer.setUpdateBy(SecurityUtils.getUsername());
        wmsInventoryTransfer.setUpdateTime(DateUtils.getNowDate());

        // 设置审核人和审核时间
        wmsInventoryTransfer.setAuditBy(SecurityUtils.getUsername());
        wmsInventoryTransfer.setAuditTime(DateUtils.getNowDate());

        // 如果审核通过，则更新库存
        if ("1".equals(wmsInventoryTransfer.getStatus()))
        {
            // 查询调拨单明细
            List<WmsInventoryTransferDetail> details = wmsInventoryTransferDetailMapper.selectWmsInventoryTransferDetailByTransferId(wmsInventoryTransfer.getTransferId());

            // 查询调拨单
            WmsInventoryTransfer transfer = wmsInventoryTransferMapper.selectWmsInventoryTransferByTransferId(wmsInventoryTransfer.getTransferId());

            // 检查调出仓库是否有足够的库存
            for (WmsInventoryTransferDetail detail : details)
            {
                // 查询调出仓库的库存
                WmsInventory inventory = wmsInventoryService.selectWmsInventoryByProductIdAndWarehouseId(
                    detail.getProductId(), transfer.getFromWarehouseId());

                if (inventory == null || inventory.getQuantity().compareTo(detail.getQuantity()) < 0)
                {
                    String productName = detail.getProductName() != null ? detail.getProductName() : "未知物品";
                    String errorMsg = String.format("调出仓库库存不足，物品：%s，需要数量：%s，当前库存：%s",
                        productName, detail.getQuantity(), inventory != null ? inventory.getQuantity() : "0");
                    throw new ServiceException(errorMsg);
                }
            }

            // 更新库存
            for (WmsInventoryTransferDetail detail : details)
            {
                // 调出仓库减少库存
                wmsInventoryService.outStock(detail.getProductId(), transfer.getFromWarehouseId(), detail.getQuantity());

                // 调入仓库增加库存
                wmsInventoryService.inStock(detail.getProductId(), transfer.getToWarehouseId(), detail.getQuantity());
            }

            // 记录库存变动日志
            recordTransferLog(transfer, details);
        }

        // 更新调拨单状态
        return wmsInventoryTransferMapper.updateWmsInventoryTransfer(wmsInventoryTransfer);
    }

    /**
     * 记录库存调拨日志
     *
     * @param transfer 调拨单
     * @param details 调拨单明细
     */
    private void recordTransferLog(WmsInventoryTransfer transfer, List<WmsInventoryTransferDetail> details)
    {
        // 注入库存日志服务
        IWmsInventoryLogService inventoryLogService = 
            SpringUtils.getBean(IWmsInventoryLogService.class);
        
        String operator = SecurityUtils.getUsername();
        String transferCode = transfer.getTransferCode();
        
        for (WmsInventoryTransferDetail detail : details) {
            // 记录调出仓库的日志（出库）
            inventoryLogService.recordInventoryLogEnhanced(
                "TRANSFER", // 操作类型
                transfer.getFromWarehouseId(), // 调出仓库ID
                detail.getProductId(), // 物品ID
                detail.getQuantity().negate(), // 调拨数量(出库为负数)
                null, // 操作前数量（由服务层查询）
                null, // 操作后数量（由服务层计算）
                transferCode, // 关联单据号
                "调拨出库", // 操作原因
                "调拨单：" + transferCode + "，调拨至：" + transfer.getToWarehouseName() // 备注
            );
            
            // 记录调入仓库的日志（入库）
            inventoryLogService.recordInventoryLogEnhanced(
                "TRANSFER", // 操作类型
                transfer.getToWarehouseId(), // 调入仓库ID
                detail.getProductId(), // 物品ID
                detail.getQuantity(), // 调拨数量
                null, // 操作前数量（由服务层查询）
                null, // 操作后数量（由服务层计算）
                transferCode, // 关联单据号
                "调拨入库", // 操作原因
                "调拨单：" + transferCode + "，调拨自：" + transfer.getFromWarehouseName() // 备注
            );
        }
    }

    /**
     * 生成调拨单号
     *
     * @return 调拨单号
     */
    private String generateTransferCode()
    {
        // 生成格式：TR + 年月日 + 时分秒
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        String timeStr = DateUtils.dateTimeNow("HHmmss");
        String transferCode = "TR" + dateStr + timeStr;

        return transferCode;
    }

    /**
     * 转换用户名为真实姓名
     *
     * @param transfer 调拨单
     */
    private void convertUserNames(WmsInventoryTransfer transfer) {
        if (transfer == null) {
            return;
        }

        // 转换创建人
        if (transfer.getCreateBy() != null && !transfer.getCreateBy().isEmpty()) {
            transfer.setCreateBy(getRealName(transfer.getCreateBy()));
        }

        // 转换更新人
        if (transfer.getUpdateBy() != null && !transfer.getUpdateBy().isEmpty()) {
            transfer.setUpdateBy(getRealName(transfer.getUpdateBy()));
        }

        // 转换审核人
        if (transfer.getAuditBy() != null && !transfer.getAuditBy().isEmpty()) {
            transfer.setAuditBy(getRealName(transfer.getAuditBy()));
        }
    }

    /**
     * 根据用户名获取真实姓名
     */
    private String getRealName(String username) {
        if (username == null || username.isEmpty()) {
            return username;
        }
        try {
            com.wanyu.common.core.domain.entity.SysUser user = userService.selectUserByUserName(username);
            if (user != null) {
                // 优先使用真实姓名，其次使用昵称
                if (user.getRealName() != null && !user.getRealName().isEmpty()) {
                    return user.getRealName();
                } else if (user.getNickName() != null && !user.getNickName().isEmpty()) {
                    return user.getNickName();
                }
            }
        } catch (Exception e) {
            // 查询失败时返回原用户名
            System.err.println("查询用户失败: " + username + ", 错误: " + e.getMessage());
        }
        return username;
    }
}
