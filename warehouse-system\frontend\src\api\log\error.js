import request from '@/utils/request'

// 查询错误日志列表
export function listErrorLog(query) {
  return request({
    url: '/api/v1/logs/error/list',
    method: 'get',
    params: query
  }).then(res => {
    // 确保返回的数据格式正确
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        rows: res.rows || [],
        total: res.total || 0
      };
    }
    return res;
  }).catch(err => {
    console.error("查询错误日志列表出错:", err);
    return {
      code: 500,
      msg: "查询错误日志列表失败",
      rows: [],
      total: 0
    };
  });
}

// 查询错误日志详细
export function getErrorLog(logId) {
  return request({
    url: `/api/v1/logs/error/${logId}`,
    method: 'get'
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        data: res.data || res
      };
    }
    return res;
  }).catch(err => {
    console.error("查询错误日志详细出错:", err);
    return {
      code: 500,
      msg: "查询错误日志详细失败"
    };
  });
}

// 删除错误日志
export function delErrorLog(logIds) {
  return request({
    url: `/api/v1/logs/error/${logIds}`,
    method: 'delete'
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "删除成功"
      };
    }
    return res;
  }).catch(err => {
    console.error("删除错误日志出错:", err);
    return {
      code: 500,
      msg: "删除错误日志失败"
    };
  });
}

// 批量删除错误日志
export function batchDelErrorLog(logIds) {
  return request({
    url: '/api/v1/logs/error/batch',
    method: 'delete',
    data: { logIds: logIds }
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "批量删除成功"
      };
    }
    return res;
  }).catch(err => {
    console.error("批量删除错误日志出错:", err);
    return {
      code: 500,
      msg: "批量删除错误日志失败"
    };
  });
}

// 清空错误日志
export function cleanErrorLog() {
  return request({
    url: '/api/v1/logs/error/clean',
    method: 'delete'
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "清空成功"
      };
    }
    return res;
  }).catch(err => {
    console.error("清空错误日志出错:", err);
    return {
      code: 500,
      msg: "清空错误日志失败"
    };
  });
}

// 导出错误日志
export function exportErrorLog(query) {
  return request({
    url: '/api/v1/logs/error/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  }).then(res => {
    return res;
  }).catch(err => {
    console.error("导出错误日志出错:", err);
    throw err;
  });
}

// 标记错误日志为已处理
export function markErrorLogAsHandled(logIds) {
  return request({
    url: '/api/v1/logs/error/mark-handled',
    method: 'put',
    data: { logIds: logIds }
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "标记成功"
      };
    }
    return res;
  }).catch(err => {
    console.error("标记错误日志出错:", err);
    return {
      code: 500,
      msg: "标记错误日志失败"
    };
  });
}

// 获取错误统计信息
export function getErrorStatistics(query) {
  return request({
    url: '/api/v1/logs/error/statistics',
    method: 'get',
    params: query
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        data: res.data || res
      };
    }
    return res;
  }).catch(err => {
    console.error("获取错误统计信息出错:", err);
    return {
      code: 500,
      msg: "获取错误统计信息失败",
      data: {
        totalErrors: 0,
        todayErrors: 0,
        unhandledErrors: 0,
        criticalErrors: 0
      }
    };
  });
}

// 获取错误趋势数据
export function getErrorTrend(query) {
  return request({
    url: '/api/v1/logs/error/trend',
    method: 'get',
    params: query
  }).then(res => {
    if (res.code === undefined) {
      return {
        code: 200,
        msg: "操作成功",
        data: res.data || res
      };
    }
    return res;
  }).catch(err => {
    console.error("获取错误趋势数据出错:", err);
    return {
      code: 500,
      msg: "获取错误趋势数据失败",
      data: []
    };
  });
}