import request from '@/utils/request'

// 登录方法
export function login(loginForm) {
  const { username, realName, phone, password, code, uuid, loginType } = loginForm;

  // 根据登录类型构建数据
  const data = {
    password,
    code,
    uuid,
    loginType
  };

  // 根据登录类型添加对应的字段
  if (loginType === 'username') {
    data.username = username;
  } else if (loginType === 'realname') {
    data.realName = realName;
  } else if (loginType === 'phone') {
    data.phone = phone;
  }

  console.log('发送登录请求:', {
    url: '/login',
    data: {
      ...data,
      password: password ? '******' : null // 避免在日志中暴露密码
    }
  });

  return request({
    url: '/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false,
      repeatSubmit: false // 添加repeatSubmit配置，避免请求被拦截
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  console.log('获取验证码请求发送');
  
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false,
      repeatSubmit: false // 添加repeatSubmit配置，避免请求被拦截
    },
    method: 'get',
    timeout: 20000
  })
}