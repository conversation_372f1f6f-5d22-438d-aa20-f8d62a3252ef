package com.wanyu.framework.security.service;

import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wanyu.common.constant.CacheConstants;
import com.wanyu.common.utils.StringUtils;

/**
 * 权限缓存服务
 *
 * <AUTHOR>
 */
@Service("permissionCacheManager")
public class PermissionCacheService
{
    /**
     * 缓存有效期（默认5分钟）- 减少缓存时间，确保权限变更能够及时生效
     */
    private final Integer EXPIRATION = 5;

    /**
     * 缓存有效期单位
     */
    private final TimeUnit TIMEUNIT = TimeUnit.MINUTES;

    /**
     * 内存缓存用于存储权限数据
     */
    private static final Map<String, Object> PERMISSION_CACHE = new HashMap<>();

    /**
     * 获取用户权限缓存键名
     *
     * @param userId 用户ID
     * @return 缓存键名
     */
    private String getUserPermissionKey(Long userId)
    {
        return CacheConstants.USER_PERMISSION_KEY + userId;
    }

    /**
     * 获取角色权限缓存键名
     *
     * @param roleId 角色ID
     * @return 缓存键名
     */
    private String getRolePermissionKey(Long roleId)
    {
        return CacheConstants.ROLE_PERMISSION_KEY + roleId;
    }

    /**
     * 获取数据权限缓存键名
     *
     * @param userId 用户ID
     * @return 缓存键名
     */
    private String getDataScopeKey(Long userId)
    {
        return CacheConstants.DATA_SCOPE_KEY + userId;
    }

    /**
     * 获取仓库权限缓存键名
     *
     * @param userId 用户ID
     * @return 缓存键名
     */
    private String getWarehousePermissionKey(Long userId)
    {
        return CacheConstants.WAREHOUSE_PERMISSION_KEY + userId;
    }

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> getUserPermissions(Long userId)
    {
        String cacheKey = getUserPermissionKey(userId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存用户权限列表
     *
     * @param userId 用户ID
     * @param permissions 权限列表
     */
    public void setUserPermissions(Long userId, Set<String> permissions)
    {
        String cacheKey = getUserPermissionKey(userId);
        PERMISSION_CACHE.put(cacheKey, permissions);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    /**
     * 删除用户权限缓存
     *
     * @param userId 用户ID
     */
    public void deleteUserPermissions(Long userId)
    {
        String cacheKey = getUserPermissionKey(userId);
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取角色权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    public Set<String> getRolePermissions(Long roleId)
    {
        String cacheKey = getRolePermissionKey(roleId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存角色权限列表
     *
     * @param roleId 角色ID
     * @param permissions 权限列表
     */
    public void setRolePermissions(Long roleId, Set<String> permissions)
    {
        String cacheKey = getRolePermissionKey(roleId);
        PERMISSION_CACHE.put(cacheKey, permissions);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    /**
     * 删除角色权限缓存
     *
     * @param roleId 角色ID
     */
    public void deleteRolePermissions(Long roleId)
    {
        String cacheKey = getRolePermissionKey(roleId);
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取数据权限列表
     *
     * @param userId 用户ID
     * @return 数据权限列表
     */
    public Set<String> getDataPermissions(Long userId)
    {
        String cacheKey = getDataScopeKey(userId);
        return (Set<String>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存数据权限列表
     *
     * @param userId 用户ID
     * @param dataPermissions 数据权限列表
     */
    public void setDataPermissions(Long userId, Set<String> dataPermissions)
    {
        String cacheKey = getDataScopeKey(userId);
        PERMISSION_CACHE.put(cacheKey, dataPermissions);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    /**
     * 删除数据权限缓存
     *
     * @param userId 用户ID
     */
    public void deleteDataPermissions(Long userId)
    {
        String cacheKey = getDataScopeKey(userId);
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 获取用户仓库权限列表
     *
     * @param userId 用户ID
     * @return 仓库ID列表
     */
    public List<Long> getWarehousePermissions(Long userId)
    {
        String cacheKey = getWarehousePermissionKey(userId);
        return (List<Long>) PERMISSION_CACHE.get(cacheKey);
    }

    /**
     * 缓存用户仓库权限列表
     *
     * @param userId 用户ID
     * @param warehousePermissions 仓库权限列表
     */
    public void setWarehousePermissions(Long userId, List<Long> warehousePermissions)
    {
        String cacheKey = getWarehousePermissionKey(userId);
        PERMISSION_CACHE.put(cacheKey, warehousePermissions);
        
        // 启动一个定时任务在指定时间后删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(EXPIRATION * 60 * 1000); // 转换为毫秒
                PERMISSION_CACHE.remove(cacheKey);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    /**
     * 删除仓库权限缓存
     *
     * @param userId 用户ID
     */
    public void deleteWarehousePermissions(Long userId)
    {
        String cacheKey = getWarehousePermissionKey(userId);
        PERMISSION_CACHE.remove(cacheKey);
    }

    /**
     * 清空所有权限缓存
     */
    public void clearAllPermissionCache()
    {
        PERMISSION_CACHE.clear();
    }
}