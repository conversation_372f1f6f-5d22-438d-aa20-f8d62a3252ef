# 物品条码表修复完成指南

## 概述

本指南详细说明了物品条码数据库表名从 `product_barcode` 更改为 `wms_barcode` 的修复过程和使用方法。

## 修复内容

### 1. 数据库表结构

#### 主表：wms_barcode
```sql
CREATE TABLE `wms_barcode` (
  `barcode_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '条码ID',
  `product_id` bigint(20) NOT NULL COMMENT '物品ID',
  `product_name` varchar(100) DEFAULT NULL COMMENT '物品名称',
  `barcode_content` varchar(200) NOT NULL COMMENT '条码内容',
  `barcode_type` varchar(20) NOT NULL DEFAULT 'CODE128' COMMENT '条码类型',
  `barcode_image` varchar(500) DEFAULT NULL COMMENT '条码图片路径',
  `template_id` bigint(20) DEFAULT NULL COMMENT '条码模板ID',
  `is_main` char(1) DEFAULT '0' COMMENT '是否主条码(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`barcode_id`),
  UNIQUE KEY `uk_barcode_content` (`barcode_content`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_barcode_type` (`barcode_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品条码表';
```

#### 模板表：wms_barcode_template
```sql
CREATE TABLE `wms_barcode_template` (
  `template_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_type` varchar(20) NOT NULL COMMENT '模板类型',
  `template_width` int(11) DEFAULT 100 COMMENT '模板宽度(mm)',
  `template_height` int(11) DEFAULT 50 COMMENT '模板高度(mm)',
  `barcode_width` int(11) DEFAULT 80 COMMENT '条码宽度(mm)',
  `barcode_height` int(11) DEFAULT 30 COMMENT '条码高度(mm)',
  `font_size` int(11) DEFAULT 12 COMMENT '字体大小',
  `show_text` char(1) DEFAULT '1' COMMENT '是否显示文字(0否 1是)',
  `show_product_name` char(1) DEFAULT '1' COMMENT '是否显示物品名称(0否 1是)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品条码模板表';
```

### 2. 后端代码结构

#### 实体类
- `WmsBarcode.java` - 物品条码实体类
- `WmsBarcodeTemplate.java` - 物品条码模板实体类

#### 数据访问层
- `WmsBarcodeMapper.java` - 条码数据访问接口
- `WmsBarcodeMapper.xml` - 条码SQL映射文件
- `WmsBarcodeTemplateMapper.java` - 模板数据访问接口
- `WmsBarcodeTemplateMapper.xml` - 模板SQL映射文件

#### 业务逻辑层
- `IWmsBarcodeService.java` - 条码服务接口
- `WmsBarcodeServiceImpl.java` - 条码服务实现类

#### 控制器层
- `ProductBarcodeController.java` - 条码控制器（已更新）

### 3. 字典数据

#### 条码类型字典
- 字典类型：`wms_barcode_type`
- 支持的条码类型：
  - CODE128（默认）
  - EAN13
  - EAN8
  - UPC_A
  - QR_CODE（二维码）

## 执行修复

### 1. 运行数据库修复脚本
```bash
fix_wms_barcode_table_complete.bat
```

### 2. 验证修复结果
```bash
test_wms_barcode_fix.bat
```

### 3. 重新编译后端项目
```bash
cd C:\CKGLXT\warehouse-system\backend
mvn clean compile
```

### 4. 重启后端服务
```bash
cd C:\CKGLXT\warehouse-system
start-system.bat
```

## API接口说明

### 基础CRUD操作

#### 1. 查询条码列表
```http
GET /product/barcode/list
```

#### 2. 获取条码详情
```http
GET /product/barcode/{barcodeId}
```

#### 3. 新增条码
```http
POST /product/barcode
Content-Type: application/json

{
  "productId": 1,
  "productName": "测试物品",
  "barcodeContent": "TEST001",
  "barcodeType": "CODE128",
  "templateId": 1,
  "isMain": "0",
  "status": "0"
}
```

#### 4. 修改条码
```http
PUT /product/barcode
Content-Type: application/json

{
  "barcodeId": 1,
  "productId": 1,
  "productName": "测试物品",
  "barcodeContent": "TEST001",
  "barcodeType": "CODE128",
  "templateId": 1,
  "isMain": "0",
  "status": "0"
}
```

#### 5. 删除条码
```http
DELETE /product/barcode/{barcodeIds}
```

### 高级功能

#### 1. 生成单个条码
```http
POST /product/barcode/generate
Content-Type: application/json

{
  "productId": 1,
  "productName": "测试物品",
  "barcodeType": "CODE128",
  "templateId": 1
}
```

#### 2. 批量生成条码
```http
POST /product/barcode/batch/generate
Content-Type: application/json

{
  "productId": 1,
  "productName": "测试物品",
  "barcodeType": "CODE128",
  "templateId": 1,
  "prefix": "TEST",
  "startNum": 1,
  "numDigits": 4,
  "count": 10
}
```

#### 3. 获取条码图片
```http
GET /product/barcode/image/{barcodeId}
```

#### 4. 打印条码
```http
GET /product/barcode/print/{barcodeId}
```

#### 5. 查询条码模板列表
```http
GET /product/barcode/template/list
```

#### 6. 导出条码数据
```http
GET /product/barcode/export
```

## 权限配置

### 所需权限字符
- `product:barcode:list` - 查看条码列表
- `product:barcode:query` - 查看条码详情
- `product:barcode:add` - 新增条码
- `product:barcode:edit` - 修改条码
- `product:barcode:remove` - 删除条码
- `product:barcode:generate` - 生成条码
- `product:barcode:print` - 打印条码
- `product:barcode:export` - 导出条码

## 使用示例

### 1. 创建条码模板
首先需要在 `wms_barcode_template` 表中创建条码模板，或使用默认模板。

### 2. 生成物品条码
```java
// 单个条码生成
WmsBarcode barcode = new WmsBarcode();
barcode.setProductId(1L);
barcode.setProductName("测试物品");
barcode.setBarcodeType("CODE128");
barcode.setTemplateId(1L);

WmsBarcode result = wmsBarcodeService.generateWmsBarcode(barcode);
```

### 3. 批量生成条码
```java
// 批量条码生成
List<WmsBarcode> barcodes = new ArrayList<>();
for (int i = 1; i <= 10; i++) {
    WmsBarcode barcode = new WmsBarcode();
    barcode.setProductId(1L);
    barcode.setProductName("测试物品");
    barcode.setBarcodeContent("TEST" + String.format("%04d", i));
    barcode.setBarcodeType("CODE128");
    barcode.setTemplateId(1L);
    barcodes.add(barcode);
}

List<WmsBarcode> results = wmsBarcodeService.batchGenerateWmsBarcode(barcodes);
```

## 故障排除

### 1. 编译错误
如果出现编译错误，请检查：
- 实体类是否正确导入
- Mapper接口是否存在
- 服务接口是否正确实现

### 2. 数据库连接错误
如果出现数据库连接错误，请检查：
- 数据库服务是否运行
- 连接参数是否正确
- 用户权限是否足够

### 3. API调用失败
如果API调用失败，请检查：
- 后端服务是否正常启动
- 权限配置是否正确
- 请求参数是否符合要求

## 测试验证

### 1. 数据库测试
```sql
-- 查询条码数据
SELECT * FROM wms_barcode LIMIT 10;

-- 查询模板数据
SELECT * FROM wms_barcode_template;

-- 查询字典数据
SELECT * FROM sys_dict_data WHERE dict_type = 'wms_barcode_type';
```

### 2. API测试
使用Postman或其他API测试工具测试上述API接口。

### 3. 功能测试
1. 登录系统
2. 进入物品条码管理页面
3. 测试条码的增删改查功能
4. 测试条码生成和打印功能

## 总结

通过本次修复，物品条码功能已经完全迁移到使用 `wms_barcode` 表的新架构。新架构具有以下优势：

1. **命名规范统一**：使用 `wms_` 前缀，符合项目命名规范
2. **功能完整**：支持条码生成、批量操作、模板管理等功能
3. **扩展性强**：支持多种条码类型，易于扩展新功能
4. **数据安全**：包含完整的数据迁移和验证机制

如有任何问题，请参考故障排除部分或联系开发团队。